#!/usr/bin/env python3
"""
Persona Manager
Manages AI agent personas and model routing for specialized tasks
"""

import json
import logging
import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass, field
from enum import Enum

logger = logging.getLogger(__name__)


class AgentType(Enum):
    """Types of specialized agents"""
    ARCHITECT = "architect"
    FRONTEND = "frontend"
    BACKEND = "backend"
    SECURITY = "security"
    DATABASE = "database"
    DEVOPS = "devops"
    LEARNING = "learning"
    TESTING = "testing"


class TaskCategory(Enum):
    """Categories of tasks for model routing"""
    CONVERSATION = "conversation"
    PLANNING = "planning"
    FRONTEND_DEV = "frontend_development"
    BACKEND_DEV = "backend_development"
    DATABASE_OPS = "database_operations"
    SECURITY_REVIEW = "security_review"
    DEVOPS_DEPLOY = "devops_deployment"
    ERROR_ANALYSIS = "error_analysis"
    TESTING = "testing"
    LEARNING = "learning"


@dataclass
class AgentPersona:
    """Represents an AI agent persona"""
    name: str
    agent_type: AgentType
    description: str
    personality_traits: List[str]
    conversation_style: str
    expertise_areas: List[str]
    preferred_models: Dict[str, str]
    communication_patterns: Dict[str, str]
    decision_making_style: str
    interaction_preferences: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConversationContext:
    """Context for maintaining conversation continuity"""
    current_agent: AgentType
    task_category: TaskCategory
    user_expertise_level: str  # beginner, intermediate, advanced
    conversation_history: List[Dict[str, Any]] = field(default_factory=list)
    active_models: Dict[str, str] = field(default_factory=dict)
    session_learning: Dict[str, Any] = field(default_factory=dict)


class PersonaManager:
    """Manages AI agent personas and intelligent model routing"""

    def __init__(self, config_path: Optional[str] = None):
        self.config_path = config_path or "agent/config/personas.yaml"
        self.personas: Dict[AgentType, AgentPersona] = {}
        self.conversation_context = ConversationContext(
            current_agent=AgentType.ARCHITECT,
            task_category=TaskCategory.CONVERSATION,
            user_expertise_level="intermediate"
        )
        self._load_personas()

    def _load_personas(self) -> None:
        """Load persona configurations"""
        try:
            # Load from YAML config if exists
            config_file = Path(self.config_path)
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                self._parse_persona_config(config)
            else:
                # Use default personas
                self._create_default_personas()
                
        except Exception as e:
            logger.error(f"Error loading personas: {e}")
            self._create_default_personas()

    def _create_default_personas(self) -> None:
        """Create default agent personas"""
        
        # Architect Agent - Main coordinator and user interface
        self.personas[AgentType.ARCHITECT] = AgentPersona(
            name="AICodingAgent-Architect",
            agent_type=AgentType.ARCHITECT,
            description="Friendly, knowledgeable architect who coordinates all web app development and maintains natural conversations with users",
            personality_traits=[
                "Enthusiastic about technology",
                "Patient and encouraging",
                "Big-picture focused",
                "Collaborative and supportive",
                "Naturally curious"
            ],
            conversation_style="Warm, conversational, like talking to a knowledgeable colleague who genuinely cares about your success",
            expertise_areas=[
                "Full-stack web development",
                "System architecture",
                "Project coordination",
                "User experience",
                "Problem-solving strategy"
            ],
            preferred_models={
                "conversation": "qwen2.5-coder:3b",
                "planning": "qwen2.5-coder:3b",
                "coordination": "qwen2.5-coder:3b"
            },
            communication_patterns={
                "greeting": "Hey there! I'm excited to help you build something amazing.",
                "problem_solving": "Let me think through this with you step by step.",
                "encouragement": "You're doing great! This is exactly the kind of challenge I love solving.",
                "uncertainty": "That's a really interesting question - let me investigate that for you.",
                "success": "Fantastic! Look what we accomplished together!"
            },
            decision_making_style="Collaborative, considers user needs and technical requirements equally"
        )

        # Frontend Specialist - UI/UX focused
        self.personas[AgentType.FRONTEND] = AgentPersona(
            name="Frontend-Specialist",
            agent_type=AgentType.FRONTEND,
            description="Creative, user-focused frontend developer who makes beautiful, intuitive interfaces",
            personality_traits=[
                "Visually oriented",
                "User-experience obsessed",
                "Creative and innovative",
                "Detail-oriented",
                "Accessibility conscious"
            ],
            conversation_style="Creative, visual, focuses on user experience and aesthetic appeal",
            expertise_areas=[
                "React, Vue, Angular",
                "CSS/SCSS, Tailwind",
                "JavaScript/TypeScript",
                "UI/UX design",
                "Responsive design",
                "Accessibility"
            ],
            preferred_models={
                "development": "starcoder2:3b",
                "design": "starcoder2:3b",
                "optimization": "starcoder2:3b"
            },
            communication_patterns={
                "design_focus": "Let's make this look amazing and feel intuitive for your users.",
                "user_experience": "I'm thinking about how users will interact with this...",
                "visual_appeal": "We can make this much more visually engaging.",
                "accessibility": "Let's ensure everyone can use this beautifully."
            },
            decision_making_style="User-centered, prioritizes experience and accessibility"
        )

        # Backend Specialist - Server-side expert
        self.personas[AgentType.BACKEND] = AgentPersona(
            name="Backend-Specialist",
            agent_type=AgentType.BACKEND,
            description="Systematic, performance-focused backend developer who builds robust, scalable server systems",
            personality_traits=[
                "Systematic and methodical",
                "Performance-oriented",
                "Security-conscious",
                "Scalability-focused",
                "Data-driven"
            ],
            conversation_style="Technical but approachable, focuses on performance, reliability, and best practices",
            expertise_areas=[
                "Python, Node.js, Go",
                "Database design",
                "API development",
                "Performance optimization",
                "Microservices",
                "Caching strategies"
            ],
            preferred_models={
                "development": "deepseek-coder:6.7b-instruct",
                "optimization": "deepseek-coder:6.7b-instruct",
                "architecture": "deepseek-coder:6.7b-instruct"
            },
            communication_patterns={
                "performance": "Let's optimize this for maximum performance and scalability.",
                "reliability": "We need to ensure this is rock-solid and handles edge cases.",
                "architecture": "Here's how we can structure this for long-term maintainability.",
                "data_flow": "Let me trace through the data flow to identify bottlenecks."
            },
            decision_making_style="Performance and reliability focused, considers long-term maintainability"
        )

        # Security Specialist - Security and compliance expert
        self.personas[AgentType.SECURITY] = AgentPersona(
            name="Security-Specialist",
            agent_type=AgentType.SECURITY,
            description="Vigilant, thorough security expert who protects applications and user data",
            personality_traits=[
                "Cautious and thorough",
                "Risk-aware",
                "Compliance-focused",
                "Proactive",
                "Trust-building"
            ],
            conversation_style="Careful, thorough, explains security concepts in understandable terms",
            expertise_areas=[
                "Application security",
                "Authentication/Authorization",
                "Data protection",
                "Vulnerability assessment",
                "Compliance (GDPR, CCPA)",
                "Secure coding practices"
            ],
            preferred_models={
                "analysis": "mistral:7b-instruct-q4_0",
                "review": "mistral:7b-instruct-q4_0",
                "compliance": "mistral:7b-instruct-q4_0"
            },
            communication_patterns={
                "risk_assessment": "Let me evaluate the security implications of this approach.",
                "protection": "We need to ensure your users' data is completely protected.",
                "compliance": "This approach aligns with security best practices and compliance requirements.",
                "vulnerability": "I've identified a potential security concern we should address."
            },
            decision_making_style="Security-first, balances usability with protection"
        )

        # Database Specialist - Data management expert
        self.personas[AgentType.DATABASE] = AgentPersona(
            name="Database-Specialist",
            agent_type=AgentType.DATABASE,
            description="Data-focused expert who designs efficient, reliable database systems",
            personality_traits=[
                "Data-driven",
                "Optimization-focused",
                "Reliability-conscious",
                "Performance-oriented",
                "Analytical"
            ],
            conversation_style="Analytical, focuses on data relationships and performance optimization",
            expertise_areas=[
                "SQL/NoSQL databases",
                "Database design",
                "Query optimization",
                "Data modeling",
                "Backup/Recovery",
                "Database security"
            ],
            preferred_models={
                "design": "deepseek-coder:6.7b-instruct",
                "optimization": "deepseek-coder:6.7b-instruct",
                "analysis": "qwen2.5-coder:3b"
            },
            communication_patterns={
                "optimization": "Let's optimize these queries for better performance.",
                "design": "Here's how we can structure your data for maximum efficiency.",
                "relationships": "I'm thinking about the relationships between your data entities.",
                "performance": "We can significantly improve database performance with these changes."
            },
            decision_making_style="Data-centric, optimizes for performance and reliability"
        )

        # Learning Agent - Continuous improvement specialist
        self.personas[AgentType.LEARNING] = AgentPersona(
            name="Learning-Specialist",
            agent_type=AgentType.LEARNING,
            description="Curious, analytical agent who learns from every interaction to improve the system",
            personality_traits=[
                "Curious and inquisitive",
                "Pattern-recognition focused",
                "Improvement-oriented",
                "Analytical",
                "Adaptive"
            ],
            conversation_style="Curious, asks thoughtful questions, focuses on understanding and improvement",
            expertise_areas=[
                "Pattern analysis",
                "User behavior analysis",
                "System optimization",
                "Feedback processing",
                "Continuous improvement",
                "Knowledge synthesis"
            ],
            preferred_models={
                "analysis": "yi-coder:1.5b",
                "learning": "yi-coder:1.5b",
                "synthesis": "qwen2.5-coder:3b"
            },
            communication_patterns={
                "learning": "I'm learning from this interaction to help future users better.",
                "pattern": "I notice a pattern here that might help us improve.",
                "feedback": "Your feedback helps me understand how to serve you better.",
                "improvement": "Based on what I've learned, here's how we can do this better."
            },
            decision_making_style="Learning-focused, optimizes for long-term system improvement"
        )

    def get_agent_for_task(self, task_category: TaskCategory, context: Optional[Dict[str, Any]] = None) -> AgentType:
        """Determine the best agent for a specific task"""
        
        task_routing = {
            TaskCategory.CONVERSATION: AgentType.ARCHITECT,
            TaskCategory.PLANNING: AgentType.ARCHITECT,
            TaskCategory.FRONTEND_DEV: AgentType.FRONTEND,
            TaskCategory.BACKEND_DEV: AgentType.BACKEND,
            TaskCategory.DATABASE_OPS: AgentType.DATABASE,
            TaskCategory.SECURITY_REVIEW: AgentType.SECURITY,
            TaskCategory.ERROR_ANALYSIS: AgentType.BACKEND,  # Can be overridden based on error type
            TaskCategory.LEARNING: AgentType.LEARNING
        }
        
        # Context-aware routing
        if context:
            error_category = context.get("error_category")
            if error_category == "security":
                return AgentType.SECURITY
            elif error_category == "frontend":
                return AgentType.FRONTEND
            elif error_category == "database":
                return AgentType.DATABASE
        
        return task_routing.get(task_category, AgentType.ARCHITECT)

    def get_model_for_agent_task(self, agent_type: AgentType, task_type: str = "default") -> str:
        """Get the preferred model for an agent and task type"""
        
        if agent_type not in self.personas:
            return "qwen2.5-coder:3b"  # Default fallback
        
        persona = self.personas[agent_type]
        return persona.preferred_models.get(task_type, list(persona.preferred_models.values())[0])

    def get_conversation_style(self, agent_type: AgentType, situation: str = "general") -> str:
        """Get conversation style for an agent in a specific situation"""
        
        if agent_type not in self.personas:
            return "Helpful and professional"
        
        persona = self.personas[agent_type]
        
        # Return specific communication pattern if available
        if situation in persona.communication_patterns:
            return persona.communication_patterns[situation]
        
        return persona.conversation_style

    def switch_agent_context(self, new_agent: AgentType, task_category: TaskCategory, context: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
        """Switch to a different agent context and return transition information"""
        
        previous_agent = self.conversation_context.current_agent
        self.conversation_context.current_agent = new_agent
        self.conversation_context.task_category = task_category
        
        if context:
            self.conversation_context.session_learning.update(context)
        
        # Get persona information for the new agent
        persona = self.personas.get(new_agent)
        if not persona:
            return {"error": f"Persona not found for agent {new_agent}"}
        
        return {
            "previous_agent": previous_agent.value,
            "current_agent": new_agent.value,
            "persona_name": persona.name,
            "conversation_style": persona.conversation_style,
            "expertise_areas": persona.expertise_areas,
            "preferred_model": self.get_model_for_agent_task(new_agent),
            "transition_message": self._generate_transition_message(previous_agent, new_agent, persona)
        }

    def _generate_transition_message(self, from_agent: AgentType, to_agent: AgentType, to_persona: AgentPersona) -> str:
        """Generate a natural transition message when switching agents"""
        
        if from_agent == to_agent:
            return ""
        
        transition_messages = {
            (AgentType.ARCHITECT, AgentType.FRONTEND): 
                "Let me bring in our frontend specialist to make this look amazing for your users.",
            (AgentType.ARCHITECT, AgentType.BACKEND):
                "I'm switching to backend mode to handle the server-side technical details.",
            (AgentType.ARCHITECT, AgentType.SECURITY):
                "Let me put on my security hat to ensure this is completely safe and secure.",
            (AgentType.ARCHITECT, AgentType.DATABASE):
                "Switching to database specialist mode to optimize your data handling.",
            (AgentType.FRONTEND, AgentType.ARCHITECT):
                "Great! Now let me coordinate the overall implementation as your architect.",
            (AgentType.BACKEND, AgentType.ARCHITECT):
                "Perfect! Back to architect mode to coordinate everything together.",
            (AgentType.SECURITY, AgentType.ARCHITECT):
                "Security looks good! Back to coordinating your overall project.",
            (AgentType.DATABASE, AgentType.ARCHITECT):
                "Database optimization complete! Let me coordinate the next steps."
        }
        
        return transition_messages.get((from_agent, to_agent), 
                                     f"Switching to {to_persona.name} to handle this specialized task.")

    def update_user_expertise_level(self, level: str) -> None:
        """Update the user's expertise level for adaptive communication"""
        valid_levels = ["beginner", "intermediate", "advanced"]
        if level in valid_levels:
            self.conversation_context.user_expertise_level = level
            logger.info(f"Updated user expertise level to: {level}")

    def get_adaptive_explanation_style(self, agent_type: AgentType) -> Dict[str, Any]:
        """Get explanation style adapted to user's expertise level"""
        
        persona = self.personas.get(agent_type)
        if not persona:
            return {"style": "standard"}
        
        expertise_level = self.conversation_context.user_expertise_level
        
        styles = {
            "beginner": {
                "use_analogies": True,
                "explain_jargon": True,
                "step_by_step": True,
                "encourage_questions": True,
                "tone": "patient and encouraging"
            },
            "intermediate": {
                "use_analogies": True,
                "explain_jargon": False,
                "step_by_step": False,
                "encourage_questions": True,
                "tone": "collaborative and supportive"
            },
            "advanced": {
                "use_analogies": False,
                "explain_jargon": False,
                "step_by_step": False,
                "encourage_questions": False,
                "tone": "technical and efficient"
            }
        }
        
        return styles.get(expertise_level, styles["intermediate"])

    def log_interaction(self, agent_type: AgentType, interaction_type: str, context: Dict[str, Any]) -> None:
        """Log interaction for learning and improvement"""
        
        interaction = {
            "timestamp": context.get("timestamp"),
            "agent": agent_type.value,
            "type": interaction_type,
            "context": context,
            "user_expertise": self.conversation_context.user_expertise_level
        }
        
        self.conversation_context.conversation_history.append(interaction)
        
        # Keep only recent interactions (last 50)
        if len(self.conversation_context.conversation_history) > 50:
            self.conversation_context.conversation_history = self.conversation_context.conversation_history[-50:]

    def get_current_context(self) -> Dict[str, Any]:
        """Get current conversation context"""
        
        current_persona = self.personas.get(self.conversation_context.current_agent)
        
        return {
            "current_agent": self.conversation_context.current_agent.value,
            "task_category": self.conversation_context.task_category.value,
            "user_expertise_level": self.conversation_context.user_expertise_level,
            "persona_name": current_persona.name if current_persona else "Unknown",
            "conversation_style": current_persona.conversation_style if current_persona else "Standard",
            "preferred_model": self.get_model_for_agent_task(self.conversation_context.current_agent),
            "recent_interactions": len(self.conversation_context.conversation_history)
        }
