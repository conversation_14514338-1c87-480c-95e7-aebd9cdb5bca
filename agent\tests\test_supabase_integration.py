#!/usr/bin/env python3
"""
End-to-End Test Script for Supabase Integration

This script tests the complete Supabase integration workflow:
1. Database layer (models and managers)
2. Backend API layer (FastAPI endpoints)
3. CLI layer (command execution)
4. Frontend integration (API calls)

Usage:
    python scripts/test_supabase_integration.py
"""

import json
import os
import sys
import time
from pathlib import Path
from typing import Any, Dict

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agent.dashboard.models import (
    DatabaseMigrationCreate,
    DatabaseMigrationUpdate,
    SupabaseConfigCreate,
    SupabaseConfigUpdate,
)
from agent.database.migration_manager import MigrationManager
from agent.database.supabase_cli import SupabaseCLI
from db.database_manager import DatabaseManager
from db.models import DatabaseMigration, Project, SupabaseConfig, SupabaseTable, User


class SupabaseIntegrationTester:
    """Comprehensive tester for Supabase integration"""

    def __init__(self):
        # Initialize database managers with specific models
        from db.database_manager import (
            DatabaseMigrationManager,
            ProjectManager,
            SupabaseConfigManager,
            SupabaseTableManager,
            UserManager,
        )
        from db.models import (
            DatabaseMigration,
            Project,
            SupabaseConfig,
            SupabaseTable,
            User,
        )

        self.user_manager = UserManager()
        self.project_manager = ProjectManager()
        self.supabase_config_manager = SupabaseConfigManager()
        self.database_migration_manager = DatabaseMigrationManager()
        self.supabase_table_manager = SupabaseTableManager()

        self.supabase_cli = SupabaseCLI()
        self.migration_manager = MigrationManager()
        self.test_results = []

    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test results"""
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": time.time(),
        }
        self.test_results.append(result)

        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
        print()

    def test_database_layer(self):
        """Test database models and managers"""
        print("🧪 Testing Database Layer...")

        try:
            # Import models
            from sqlalchemy.orm import Session

            from agent.dashboard.database import get_db
            from db.models import (
                DatabaseMigration,
                Project,
                SupabaseConfig,
                SupabaseTable,
                User,
            )

            # Get database session
            db = next(get_db())
            self.log_test(
                "Database Connection", True, "Successfully connected to database"
            )

            # Test user creation
            import time

            timestamp = int(time.time())
            test_user_data = {
                "username": f"test_supabase_user_{timestamp}",
                "email": f"test_{timestamp}@supabase.com",
                "hashed_password": "test_hash",
            }
            test_user = self.user_manager.create(db, obj_in=test_user_data)
            self.log_test(
                "User Creation", True, f"Created user with ID: {test_user.id}"
            )

            # Test project creation
            test_project_data = {
                "name": "Test Supabase Project",
                "description": "Test project for Supabase integration",
                "owner_id": test_user.id,
                "project_type": "web",
                "backend_type": "supabase",
            }
            test_project = self.project_manager.create(db, obj_in=test_project_data)
            self.log_test(
                "Project Creation", True, f"Created project with ID: {test_project.id}"
            )

            # Test SupabaseConfig creation
            test_config_data = {
                "project_id": test_project.id,
                "supabase_url": "https://test-project.supabase.co",
                "supabase_anon_key": "test_anon_key",
                "supabase_service_role_key": "test_service_key",
                "project_ref": "test-project-ref",
                "organization_id": "test-org-id",
                "is_active": True,
            }
            test_config = self.supabase_config_manager.create(
                db, obj_in=test_config_data
            )
            self.log_test(
                "SupabaseConfig Creation",
                True,
                f"Created config with ID: {test_config.id}",
            )

            # Test DatabaseMigration creation
            test_migration_data = {
                "project_id": test_project.id,
                "migration_name": "test_migration",
                "migration_file": "test_migration.sql",
                "sql_content": "CREATE TABLE test_table (id SERIAL PRIMARY KEY);",
                "status": "pending",
            }
            test_migration = self.database_migration_manager.create(
                db, obj_in=test_migration_data
            )
            self.log_test(
                "DatabaseMigration Creation",
                True,
                f"Created migration with ID: {test_migration.id}",
            )

            # Test SupabaseTable creation
            test_table_data = {
                "project_id": test_project.id,
                "table_name": "test_table",
                "schema_name": "public",
                "table_definition": {"name": "test_table", "schema": "public"},
                "columns": [{"name": "id", "type": "serial", "primary_key": True}],
                "indexes": [],
                "foreign_keys": [],
            }
            test_table = self.supabase_table_manager.create(db, obj_in=test_table_data)
            self.log_test(
                "SupabaseTable Creation",
                True,
                f"Created table with ID: {test_table.id}",
            )

            # Test data retrieval
            retrieved_config = self.supabase_config_manager.get_by_project(
                db, test_project.id
            )
            self.log_test(
                "Config Retrieval",
                retrieved_config is not None,
                f"Retrieved config: {retrieved_config.project_ref if retrieved_config else 'None'}",
            )

            retrieved_migrations = self.database_migration_manager.get_by_project(
                db, test_project.id
            )
            self.log_test(
                "Migrations Retrieval",
                len(retrieved_migrations) > 0,
                f"Retrieved {len(retrieved_migrations)} migrations",
            )

            # Cleanup test data
            self.supabase_table_manager.remove(db, id=test_table.id)
            self.database_migration_manager.remove(db, id=test_migration.id)
            self.supabase_config_manager.remove(db, id=test_config.id)
            self.project_manager.remove(db, id=test_project.id)
            self.user_manager.remove(db, id=test_user.id)

            return True

        except Exception as e:
            self.log_test("Database Layer", False, f"Error: {str(e)}")
            return False

    def test_supabase_cli(self):
        """Test Supabase CLI integration"""
        print("🧪 Testing Supabase CLI Integration...")

        try:
            # Test CLI path detection
            cli_path = self.supabase_cli.cli_path
            self.log_test(
                "CLI Path Detection", cli_path is not None, f"CLI path: {cli_path}"
            )

            # Test CLI version check (using --version command)
            if cli_path:
                try:
                    import subprocess

                    # Handle both string and list formats for CLI path
                    if isinstance(cli_path, str) and cli_path.startswith("python "):
                        # Split the command into parts
                        cmd_parts = cli_path.split()
                        cmd_parts.append("--version")
                        result = subprocess.run(
                            cmd_parts, capture_output=True, text=True, timeout=10
                        )
                    else:
                        # Handle list format
                        cmd = (
                            [cli_path, "--version"]
                            if isinstance(cli_path, str)
                            else cli_path + ["--version"]
                        )
                        result = subprocess.run(
                            cmd, capture_output=True, text=True, timeout=10
                        )

                    version = result.stdout.strip() if result.returncode == 0 else None
                    self.log_test(
                        "CLI Version Check", version is not None, f"Version: {version}"
                    )
                except Exception as e:
                    self.log_test(
                        "CLI Version Check", False, f"Could not get version: {str(e)}"
                    )
            else:
                self.log_test("CLI Version Check", False, "CLI not available")

            # Test project linking (mock)
            test_config = {
                "supabase_url": "https://test-project.supabase.co",
                "supabase_anon_key": "test_key",
                "project_ref": "test-project-ref",
            }

            # Test migration file creation
            migration_content = "CREATE TABLE test_table (id SERIAL PRIMARY KEY);"
            migration_file = self.migration_manager.create_migration_file(
                "test_migration", migration_content, "Test migration"
            )
            self.log_test(
                "Migration File Creation",
                migration_file is not None,
                f"Created file: {migration_file}",
            )

            # Cleanup
            if migration_file and os.path.exists(migration_file):
                os.remove(migration_file)

            return True

        except Exception as e:
            self.log_test("Supabase CLI", False, f"Error: {str(e)}")
            return False

    def test_migration_manager(self):
        """Test migration management functionality"""
        print("🧪 Testing Migration Manager...")

        try:
            # Test template loading
            templates = self.migration_manager.get_templates()
            self.log_test(
                "Template Loading",
                len(templates) > 0,
                f"Loaded {len(templates)} templates",
            )

            # Test migration validation
            valid_sql = "CREATE TABLE users (id SERIAL PRIMARY KEY, name TEXT);"
            is_valid = self.migration_manager.validate_sql(valid_sql)
            self.log_test(
                "SQL Validation (Valid)", is_valid, "Valid SQL passed validation"
            )

            invalid_sql = "INVALID SQL STATEMENT;"
            is_invalid = not self.migration_manager.validate_sql(invalid_sql)
            self.log_test(
                "SQL Validation (Invalid)", is_invalid, "Invalid SQL correctly rejected"
            )

            # Test migration file operations
            test_migration = {
                "name": "test_migration",
                "description": "Test migration",
                "sql_content": valid_sql,
                "version": "1.0.0",
            }

            # Test file creation
            file_path = self.migration_manager.create_migration_file(
                test_migration["name"],
                test_migration["sql_content"],
                test_migration["description"],
            )
            self.log_test(
                "Migration File Creation",
                file_path is not None,
                f"Created: {file_path}",
            )

            # Test file reading
            if file_path:
                content = self.migration_manager.read_migration_file(file_path)
                self.log_test(
                    "Migration File Reading",
                    content is not None,
                    f"Read {len(content)} characters",
                )

                # Cleanup
                os.remove(file_path)

            return True

        except Exception as e:
            self.log_test("Migration Manager", False, f"Error: {str(e)}")
            return False

    def test_api_models(self):
        """Test Pydantic models for API"""
        print("🧪 Testing API Models...")

        try:
            # Test SupabaseConfigCreate
            config_data = {
                "supabase_url": "https://test-project.supabase.co",
                "supabase_anon_key": "test_anon_key",
                "supabase_service_role_key": "test_service_key",
                "project_ref": "test-project-ref",
                "organization_id": "test-org-id",
            }
            config_model = SupabaseConfigCreate(**config_data)
            self.log_test(
                "SupabaseConfigCreate Model",
                True,
                f"Created model with project_ref: {config_model.project_ref}",
            )

            # Test DatabaseMigrationCreate
            migration_data = {
                "name": "test_migration",
                "description": "Test migration",
                "sql_content": "CREATE TABLE test_table (id SERIAL PRIMARY KEY);",
                "version": "1.0.0",
            }
            migration_model = DatabaseMigrationCreate(**migration_data)
            self.log_test(
                "DatabaseMigrationCreate Model",
                True,
                f"Created model with name: {migration_model.name}",
            )

            # Test validation with missing required fields
            try:
                # This should fail because we're missing required fields
                invalid_config = SupabaseConfigCreate()
                self.log_test(
                    "Model Validation", False, "Should have failed validation"
                )
            except Exception:
                self.log_test(
                    "Model Validation", True, "Correctly rejected invalid data"
                )

            return True

        except Exception as e:
            self.log_test("API Models", False, f"Error: {str(e)}")
            return False

    def run_all_tests(self):
        """Run all integration tests"""
        print("🚀 Starting Supabase Integration Tests")
        print("=" * 50)

        # Run tests
        db_success = self.test_database_layer()
        cli_success = self.test_supabase_cli()
        migration_success = self.test_migration_manager()
        api_success = self.test_api_models()

        # Generate report
        print("\n" + "=" * 50)
        print("📊 Test Results Summary")
        print("=" * 50)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests

        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")

        # Overall success
        overall_success = (
            db_success and cli_success and migration_success and api_success
        )

        if overall_success:
            print("\n🎉 All integration tests passed!")
        else:
            print("\n⚠️  Some integration tests failed. Check the details above.")

        # Save detailed results
        report_file = (
            project_root / "test_reports" / "supabase_integration_test_report.json"
        )
        report_file.parent.mkdir(exist_ok=True)

        with open(report_file, "w") as f:
            json.dump(
                {
                    "test_date": time.strftime("%Y-%m-%d %H:%M:%S"),
                    "overall_success": overall_success,
                    "summary": {
                        "total_tests": total_tests,
                        "passed_tests": passed_tests,
                        "failed_tests": failed_tests,
                        "success_rate": (passed_tests / total_tests) * 100,
                    },
                    "results": self.test_results,
                },
                f,
                indent=2,
            )

        print(f"\n📄 Detailed report saved to: {report_file}")

        return overall_success


def main():
    """Main test runner"""
    tester = SupabaseIntegrationTester()
    success = tester.run_all_tests()

    if success:
        print("\n✅ Supabase integration is ready for use!")
        sys.exit(0)
    else:
        print("\n❌ Supabase integration needs attention before use.")
        sys.exit(1)


if __name__ == "__main__":
    main()
