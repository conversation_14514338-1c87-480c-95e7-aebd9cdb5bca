{"openapi": "3.1.0", "info": {"title": "AI Coding Agent API", "description": "RESTful API for AI-powered coding assistance and project management", "version": "1.0.0"}, "paths": {"/api/v1/chat/": {"post": {"tags": ["chat"], "summary": "Chat Endpoint", "description": "Chat endpoint for AI-powered coding assistance\n\n- **prompt**: User's message or question\n- **context**: Additional context for the request\n- **intent**: User's intent or goal\n- **history**: Conversation history\n- **model**: AI model to use\n- **complexity_hint**: Task complexity hint", "operationId": "chat_endpoint_api_v1_chat__post", "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatRequest"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChatResponse"}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/chat/models": {"get": {"tags": ["chat"], "summary": "Get Available Models", "description": "Get list of available AI models\n\nReturns information about all available models including their status and capabilities.", "operationId": "get_available_models_api_v1_chat_models_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/ModelInfo"}, "type": "array", "title": "Response Get Available Models Api V1 Chat Models Get"}}}}}}}, "/api/v1/chat/test": {"post": {"tags": ["chat"], "summary": "Test Chat Endpoint", "description": "Test endpoint for chat functionality\n\nSimple test endpoint to verify chat service is working.", "operationId": "test_chat_endpoint_api_v1_chat_test_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/upload-site": {"post": {"tags": ["upload"], "summary": "Upload Site", "description": "Upload and process a web project for import with enhanced security", "operationId": "upload_site_api_v1_api_upload_site_post", "requestBody": {"content": {"multipart/form-data": {"schema": {"$ref": "#/components/schemas/Body_upload_site_api_v1_api_upload_site_post"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/upload-site/confirm": {"post": {"tags": ["upload"], "summary": "Confirm Upload", "description": "Confirm and complete the import of a reviewed upload", "operationId": "confirm_upload_api_v1_api_upload_site_confirm_post", "parameters": [{"name": "upload_path", "in": "query", "required": true, "schema": {"type": "string", "title": "Upload Path"}}, {"name": "target_name", "in": "query", "required": false, "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Target Name"}}, {"name": "cleanup_after", "in": "query", "required": false, "schema": {"type": "boolean", "default": false, "title": "Cleanup After"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/list": {"get": {"tags": ["upload"], "summary": "List Sites", "description": "Get list of all imported sites", "operationId": "list_sites_api_v1_api_sites_list_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/sites/validate/{site_name}": {"post": {"tags": ["upload"], "summary": "Validate Site", "description": "Validate an imported site", "operationId": "validate_site_api_v1_api_sites_validate__site_name__post", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/manifest": {"get": {"tags": ["upload"], "summary": "Get Site Manifest", "description": "Get manifest for a specific site", "operationId": "get_site_manifest_api_v1_api_sites__site_name__manifest_get", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}": {"delete": {"tags": ["upload"], "summary": "Delete Site", "description": "Delete an imported site", "operationId": "delete_site_api_v1_api_sites__site_name__delete", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/upload/statistics": {"get": {"tags": ["upload"], "summary": "Get Upload Statistics", "description": "Get upload statistics", "operationId": "get_upload_statistics_api_v1_api_upload_statistics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/upload/cleanup": {"post": {"tags": ["upload"], "summary": "Cleanup Uploads", "description": "Clean up pending uploads", "operationId": "cleanup_uploads_api_v1_api_upload_cleanup_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/sites/{site_name}/preview": {"get": {"tags": ["upload"], "summary": "Preview Site", "description": "Serve imported site for preview", "operationId": "preview_site_api_v1_api_sites__site_name__preview_get", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/preview/start": {"post": {"tags": ["upload"], "summary": "Start Preview Server", "description": "Start a preview server for a site", "operationId": "start_preview_server_api_v1_api_sites__site_name__preview_start_post", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/preview/stop": {"post": {"tags": ["upload"], "summary": "Stop Preview Server", "description": "Stop a preview server for a site", "operationId": "stop_preview_server_api_v1_api_sites__site_name__preview_stop_post", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/files": {"get": {"tags": ["upload"], "summary": "Browse Site Files", "description": "Browse files within an imported site", "operationId": "browse_site_files_api_v1_api_sites__site_name__files_get", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}, {"name": "path", "in": "query", "required": false, "schema": {"type": "string", "default": "", "title": "Path"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}, "put": {"tags": ["upload"], "summary": "Save File Content", "description": "Save file content for editing", "operationId": "save_file_content_api_v1_api_sites__site_name__files_put", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/commands": {"get": {"tags": ["upload"], "summary": "Get Available Commands", "description": "Get available commands for a site", "operationId": "get_available_commands_api_v1_api_sites__site_name__commands_get", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/commands/execute": {"post": {"tags": ["upload"], "summary": "Execute Command", "description": "Execute a command in the site directory", "operationId": "execute_command_api_v1_api_sites__site_name__commands_execute_post", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/commands/cancel": {"post": {"tags": ["upload"], "summary": "Cancel Command", "description": "Cancel a running command", "operationId": "cancel_command_api_v1_api_sites__site_name__commands_cancel_post", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/git/status": {"get": {"tags": ["upload"], "summary": "Get Git Status", "description": "Get Git status for a site", "operationId": "get_git_status_api_v1_api_sites__site_name__git_status_get", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/git/history": {"get": {"tags": ["upload"], "summary": "Get Git History", "description": "Get Git commit history for a site", "operationId": "get_git_history_api_v1_api_sites__site_name__git_history_get", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/git/stage": {"post": {"tags": ["upload"], "summary": "Stage Files", "description": "Stage files in Git", "operationId": "stage_files_api_v1_api_sites__site_name__git_stage_post", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/git/unstage": {"post": {"tags": ["upload"], "summary": "Unstage Files", "description": "Unstage files in Git", "operationId": "unstage_files_api_v1_api_sites__site_name__git_unstage_post", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/git/commit": {"post": {"tags": ["upload"], "summary": "Create Commit", "description": "Create a Git commit", "operationId": "create_commit_api_v1_api_sites__site_name__git_commit_post", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/git/commit/{commit_hash}/diff": {"get": {"tags": ["upload"], "summary": "Get Commit <PERSON>ff", "description": "Get diff for a specific commit", "operationId": "get_commit_diff_api_v1_api_sites__site_name__git_commit__commit_hash__diff_get", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}, {"name": "commit_hash", "in": "path", "required": true, "schema": {"type": "string", "title": "<PERSON><PERSON><PERSON>"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/sites/{site_name}/upload-manifest": {"get": {"tags": ["upload"], "summary": "Get Upload Manifest", "description": "Get the original upload manifest for a site", "operationId": "get_upload_manifest_api_v1_api_sites__site_name__upload_manifest_get", "parameters": [{"name": "site_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Site Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/errors/report": {"post": {"summary": "Report Error", "description": "Endpoint for clients to report errors", "operationId": "report_error_api_v1_errors_report_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/errors/health": {"get": {"summary": "Error Service Health", "description": "Health check for error handling service", "operationId": "error_service_health_api_v1_errors_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/run-cycle": {"post": {"tags": ["Advanced Learning Enhancements"], "summary": "Run Enhancement Cycle", "description": "Run a complete enhancement cycle", "operationId": "run_enhancement_cycle_api_v1_api_advanced_learning_run_cycle_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/status": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Enhancement Status", "description": "Get status of all enhancement components", "operationId": "get_enhancement_status_api_v1_api_advanced_learning_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/enable/{enhancement_name}": {"post": {"tags": ["Advanced Learning Enhancements"], "summary": "Enable Enhancement", "description": "Enable a specific enhancement", "operationId": "enable_enhancement_api_v1_api_advanced_learning_enable__enhancement_name__post", "parameters": [{"name": "enhancement_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Enhancement Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/advanced-learning/disable/{enhancement_name}": {"post": {"tags": ["Advanced Learning Enhancements"], "summary": "Disable Enhancement", "description": "Disable a specific enhancement", "operationId": "disable_enhancement_api_v1_api_advanced_learning_disable__enhancement_name__post", "parameters": [{"name": "enhancement_name", "in": "path", "required": true, "schema": {"type": "string", "title": "Enhancement Name"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/api/advanced-learning/meta-learning/status": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Meta Learning Status", "description": "Get meta-learning optimization status", "operationId": "get_meta_learning_status_api_v1_api_advanced_learning_meta_learning_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/pareto-optimization/status": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Pareto Optimization Status", "description": "Get Pareto optimization status", "operationId": "get_pareto_optimization_status_api_v1_api_advanced_learning_pareto_optimization_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/workload-prediction/status": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Workload Prediction Status", "description": "Get workload prediction status", "operationId": "get_workload_prediction_status_api_v1_api_advanced_learning_workload_prediction_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/cascade-prediction/status": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Cascade Prediction Status", "description": "Get cascade prediction status", "operationId": "get_cascade_prediction_status_api_v1_api_advanced_learning_cascade_prediction_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/federated-learning/status": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Federated Learning Status", "description": "Get federated learning status", "operationId": "get_federated_learning_status_api_v1_api_advanced_learning_federated_learning_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/capability-discovery/status": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Capability Discovery Status", "description": "Get capability discovery status", "operationId": "get_capability_discovery_status_api_v1_api_advanced_learning_capability_discovery_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/adversarial-detection/status": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Adversarial Detection Status", "description": "Get adversarial detection status", "operationId": "get_adversarial_detection_status_api_v1_api_advanced_learning_adversarial_detection_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/degradation/status": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Degradation Status", "description": "Get graceful degradation status", "operationId": "get_degradation_status_api_v1_api_advanced_learning_degradation_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/causal-analysis/status": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Causal Analysis Status", "description": "Get causal analysis status", "operationId": "get_causal_analysis_status_api_v1_api_advanced_learning_causal_analysis_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/business-impact/status": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Business Impact Status", "description": "Get business impact analysis status", "operationId": "get_business_impact_status_api_v1_api_advanced_learning_business_impact_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/quantum-ready/status": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Quantum Ready Status", "description": "Get quantum-ready architecture status", "operationId": "get_quantum_ready_status_api_v1_api_advanced_learning_quantum_ready_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/nas-integration/status": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Nas Integration Status", "description": "Get NAS integration status", "operationId": "get_nas_integration_status_api_v1_api_advanced_learning_nas_integration_status_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/api/advanced-learning/dashboard": {"get": {"tags": ["Advanced Learning Enhancements"], "summary": "Get Enhancement Dashboard", "description": "Get comprehensive dashboard data for all enhancements", "operationId": "get_enhancement_dashboard_api_v1_api_advanced_learning_dashboard_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/monitoring/health": {"get": {"tags": ["monitoring"], "summary": "Get System Health", "description": "Get current system health metrics", "operationId": "get_system_health_api_v1_monitoring_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/monitoring/metrics": {"get": {"tags": ["monitoring"], "summary": "Get Metrics History", "description": "Get metrics history (mock implementation)", "operationId": "get_metrics_history_api_v1_monitoring_metrics_get", "parameters": [{"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "default": 50, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/monitoring/config": {"get": {"tags": ["monitoring"], "summary": "Get Monitoring Config", "description": "Get monitoring configuration", "operationId": "get_monitoring_config_api_v1_monitoring_config_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}, "post": {"tags": ["monitoring"], "summary": "Update Monitoring Config", "description": "Update monitoring configuration", "operationId": "update_monitoring_config_api_v1_monitoring_config_post", "requestBody": {"content": {"application/json": {"schema": {"type": "object", "title": "Config"}}}, "required": true}, "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/monitoring/start": {"post": {"tags": ["monitoring"], "summary": "Start Monitoring", "description": "Start monitoring agent", "operationId": "start_monitoring_api_v1_monitoring_start_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/monitoring/stop": {"post": {"tags": ["monitoring"], "summary": "Stop Monitoring", "description": "Stop monitoring agent", "operationId": "stop_monitoring_api_v1_monitoring_stop_post", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/monitoring/alerts": {"get": {"tags": ["monitoring"], "summary": "<PERSON>erts", "description": "Get current alerts", "operationId": "get_alerts_api_v1_monitoring_alerts_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/dashboard/overview": {"get": {"tags": ["dashboard"], "summary": "Get Dashboard Overview", "description": "Get dashboard overview data", "operationId": "get_dashboard_overview_api_v1_dashboard_overview_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/dashboard/sites": {"get": {"tags": ["dashboard"], "summary": "Get Dashboard Sites", "description": "Get sites for dashboard", "operationId": "get_dashboard_sites_api_v1_dashboard_sites_get", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/dashboard/deployments": {"get": {"tags": ["dashboard"], "summary": "Get Dashboard Deployments", "description": "Get recent deployments for dashboard", "operationId": "get_dashboard_deployments_api_v1_dashboard_deployments_get", "parameters": [{"name": "skip", "in": "query", "required": false, "schema": {"type": "integer", "minimum": 0, "default": 0, "title": "<PERSON><PERSON>"}}, {"name": "limit", "in": "query", "required": false, "schema": {"type": "integer", "maximum": 1000, "minimum": 1, "default": 100, "title": "Limit"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/dashboard/analytics": {"get": {"tags": ["dashboard"], "summary": "Get Dashboard Analytics", "description": "Get analytics data for dashboard", "operationId": "get_dashboard_analytics_api_v1_dashboard_analytics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/dashboard/system": {"get": {"tags": ["dashboard"], "summary": "Get Dashboard System", "description": "Get system information for dashboard", "operationId": "get_dashboard_system_api_v1_dashboard_system_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/dashboard/notifications": {"get": {"tags": ["dashboard"], "summary": "Get Dashboard Notifications", "description": "Get notifications for dashboard", "operationId": "get_dashboard_notifications_api_v1_dashboard_notifications_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/dashboard/notifications/{notification_id}/read": {"post": {"tags": ["dashboard"], "summary": "Mark Notification Read", "description": "Mark a notification as read", "operationId": "mark_notification_read_api_v1_dashboard_notifications__notification_id__read_post", "parameters": [{"name": "notification_id", "in": "path", "required": true, "schema": {"type": "integer", "title": "Notification Id"}}], "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}, "422": {"description": "Validation Error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HTTPValidationError"}}}}}}}, "/api/v1/dashboard/quick-actions": {"get": {"tags": ["dashboard"], "summary": "Get Quick Actions", "description": "Get available quick actions for dashboard", "operationId": "get_quick_actions_api_v1_dashboard_quick_actions_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/health": {"get": {"summary": "Health Check Endpoint", "description": "Health check endpoint", "operationId": "health_check_endpoint_health_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/": {"get": {"summary": "Root", "description": "Root endpoint with API information", "operationId": "root__get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/config": {"get": {"summary": "Get Api Config", "description": "Get API configuration (non-sensitive)", "operationId": "get_api_config_api_v1_config_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}, "/api/v1/metrics": {"get": {"summary": "Get Metrics", "description": "Get API metrics", "operationId": "get_metrics_api_v1_metrics_get", "responses": {"200": {"description": "Successful Response", "content": {"application/json": {"schema": {}}}}}}}}, "components": {"schemas": {"Body_upload_site_api_v1_api_upload_site_post": {"properties": {"files": {"items": {"type": "string", "format": "binary"}, "type": "array", "title": "Files"}, "target_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Target Name"}, "review_first": {"type": "boolean", "title": "Review First", "default": true}, "cleanup_after": {"type": "boolean", "title": "Cleanup After", "default": false}}, "type": "object", "required": ["files"], "title": "Body_upload_site_api_v1_api_upload_site_post"}, "ChatRequest": {"properties": {"prompt": {"type": "string", "maxLength": 10000, "minLength": 1, "title": "Prompt", "description": "User's message or question"}, "context": {"anyOf": [{"type": "object"}, {"type": "null"}], "title": "Context", "description": "Additional context for the request"}, "intent": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Intent", "description": "User's intent or goal"}, "history": {"anyOf": [{"items": {"type": "object"}, "type": "array"}, {"type": "null"}], "title": "History", "description": "Conversation history"}, "model": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Model", "description": "AI model to use", "default": "deepseek-coder:1.3b"}, "complexity_hint": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Complexity Hint", "description": "Task complexity hint"}}, "type": "object", "required": ["prompt"], "title": "ChatRequest", "description": "Request model for chat endpoint"}, "ChatResponse": {"properties": {"success": {"type": "boolean", "title": "Success", "description": "Whether the request was successful"}, "response": {"type": "object", "title": "Response", "description": "AI response content"}, "metadata": {"type": "object", "title": "<PERSON><PERSON><PERSON>", "description": "Request metadata and timing"}}, "type": "object", "required": ["success", "response", "metadata"], "title": "ChatResponse", "description": "Response model for chat endpoint"}, "HTTPValidationError": {"properties": {"detail": {"items": {"$ref": "#/components/schemas/ValidationError"}, "type": "array", "title": "Detail"}}, "type": "object", "title": "HTTPValidationError"}, "ModelInfo": {"properties": {"name": {"type": "string", "title": "Name", "description": "Model name"}, "type": {"type": "string", "title": "Type", "description": "Model type (local/cloud)"}, "status": {"type": "string", "title": "Status", "description": "Model status (available/unavailable)"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Description", "description": "Model description"}, "capabilities": {"items": {"type": "string"}, "type": "array", "title": "Capabilities", "description": "Model capabilities"}}, "type": "object", "required": ["name", "type", "status"], "title": "ModelInfo", "description": "Model information for available models endpoint"}, "ValidationError": {"properties": {"loc": {"items": {"anyOf": [{"type": "string"}, {"type": "integer"}]}, "type": "array", "title": "Location"}, "msg": {"type": "string", "title": "Message"}, "type": {"type": "string", "title": "Error Type"}}, "type": "object", "required": ["loc", "msg", "type"], "title": "ValidationError"}}}}