#!/usr/bin/env python3
"""
Test AI Optimization System Integration

This script tests the AI optimization system integration independently
to verify it works without other component dependencies.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
import json
from datetime import datetime


def test_ai_optimization_imports():
    """Test that AI optimization modules can be imported"""
    try:
        from ai_optimization import (
            AIOptimizationManager,
            ModelOptimizer,
            ModelPerformanceMetrics,
            OptimizationConfig,
            PerformanceAlert,
            PerformanceMonitor,
            ResourceUsage,
        )

        print("✅ AI Optimization imports successful")
        return True
    except Exception as e:
        print(f"❌ AI Optimization imports failed: {e}")
        return False


def test_optimization_manager_creation():
    """Test creating an AIOptimizationManager"""
    try:
        from ai_optimization import AIOptimizationManager

        config = {
            "optimization": {
                "target_response_time_ms": 2000.0,
                "max_memory_usage_mb": 4096.0,
                "max_cpu_usage_percent": 80.0,
                "auto_optimize": False,  # Disable for testing
                "metrics_retention_days": 1,
            },
            "monitoring": {
                "resource_monitor_interval": 60,
                "alert_check_interval": 60,
                "health_check_interval": 60,
                "auto_start_monitoring": False,
            },
            "data_dir": "data/ai_optimization_test",
        }

        manager = AIOptimizationManager(config)
        print("✅ AIOptimizationManager creation successful")
        return manager
    except Exception as e:
        print(f"❌ AIOptimizationManager creation failed: {e}")
        return None


def test_optimization_commands(manager):
    """Test optimization commands"""
    if not manager:
        return False

    try:
        # Test model registration
        manager.register_model("test-model", {"type": "test"})
        print("✅ Model registration successful")

        # Test performance recording
        manager.record_model_performance(
            model_name="test-model",
            response_time_ms=1500.0,
            success=True,
            memory_usage_mb=2048.0,
            cpu_usage_percent=60.0,
        )
        print("✅ Performance recording successful")

        # Test optimization recommendations
        recommendations = manager.get_optimization_recommendations("test-model")
        print(f"✅ Optimization recommendations: {len(recommendations)} found")

        # Test optimization stats
        stats = manager.get_optimization_stats()
        print(
            f"✅ Optimization stats: {stats['total_optimizations']} total optimizations"
        )

        return True
    except Exception as e:
        print(f"❌ Optimization commands failed: {e}")
        return False


def test_optimization_integration():
    """Test the complete optimization integration"""
    print("🤖 Testing AI Optimization System Integration")
    print("=" * 50)

    # Test imports
    if not test_ai_optimization_imports():
        return False

    # Test manager creation
    manager = test_optimization_manager_creation()
    if not manager:
        return False

    # Test commands
    if not test_optimization_commands(manager):
        return False

    print("\n✅ AI Optimization System Integration Test PASSED")
    return True


if __name__ == "__main__":
    success = test_optimization_integration()
    if success:
        print("\n🎉 All AI Optimization tests passed!")
        sys.exit(0)
    else:
        print("\n💥 AI Optimization tests failed!")
        sys.exit(1)
