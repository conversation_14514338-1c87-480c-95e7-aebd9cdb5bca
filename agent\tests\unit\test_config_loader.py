"""
Unit tests for ConfigLoader utility
"""

import json
import os
import tempfile
from pathlib import Path
from unittest.mock import mock_open, patch

import pytest

from agent.utils.config_loader import ConfigLoader


class TestConfigLoader:
    """Test cases for ConfigLoader"""

    def test_load_existing_config(self, temp_dir):
        """Test loading existing configuration file"""
        config_file = temp_dir / "test_config.json"
        test_config = {"key1": "value1", "key2": 42, "nested": {"key3": True}}

        with open(config_file, "w") as f:
            json.dump(test_config, f)

        result = ConfigLoader.load_config_with_defaults(str(config_file), {})
        assert result == test_config

    def test_load_nonexistent_config(self, temp_dir):
        """Test loading non-existent configuration file with defaults"""
        config_file = temp_dir / "nonexistent.json"
        defaults = {"default_key": "default_value"}

        result = ConfigLoader.load_config_with_defaults(str(config_file), defaults)
        assert result == defaults

    def test_merge_with_defaults(self, temp_dir):
        """Test merging config with defaults"""
        config_file = temp_dir / "test_config.json"
        partial_config = {"key1": "value1"}
        defaults = {
            "key1": "default1",
            "key2": "default2",
            "key3": {"nested": "default"},
        }

        with open(config_file, "w") as f:
            json.dump(partial_config, f)

        result = ConfigLoader.load_config_with_defaults(str(config_file), defaults)
        expected = {
            "key1": "value1",  # Overridden by file
            "key2": "default2",  # From defaults
            "key3": {"nested": "default"},  # From defaults
        }
        assert result == expected

    def test_nested_merge(self, temp_dir):
        """Test nested dictionary merging"""
        config_file = temp_dir / "test_config.json"
        partial_config = {"nested": {"key1": "new_value"}}
        defaults = {"nested": {"key1": "default1", "key2": "default2"}}

        with open(config_file, "w") as f:
            json.dump(partial_config, f)

        result = ConfigLoader.load_config_with_defaults(str(config_file), defaults)
        expected = {
            "nested": {
                "key1": "new_value",  # Overridden
                "key2": "default2",  # From defaults
            }
        }
        assert result == expected

    def test_invalid_json_handling(self, temp_dir):
        """Test handling of invalid JSON"""
        config_file = temp_dir / "invalid.json"
        config_file.write_text("{invalid json}")
        defaults = {"fallback": "value"}

        result = ConfigLoader.load_config_with_defaults(str(config_file), defaults)
        assert result == defaults

    def test_empty_config_file(self, temp_dir):
        """Test handling of empty config file"""
        config_file = temp_dir / "empty.json"
        config_file.write_text("{}")
        defaults = {"key": "value"}

        result = ConfigLoader.load_config_with_defaults(str(config_file), defaults)
        assert result == defaults

    def test_none_defaults(self, temp_dir):
        """Test with None defaults"""
        config_file = temp_dir / "test_config.json"
        test_config = {"key": "value"}

        with open(config_file, "w") as f:
            json.dump(test_config, f)

        result = ConfigLoader.load_config_with_defaults(str(config_file), None)
        assert result == test_config

    def test_complex_nested_merge(self, temp_dir):
        """Test complex nested dictionary merging"""
        config_file = temp_dir / "test_config.json"
        partial_config = {
            "database": {"host": "localhost", "port": 5432},
            "features": ["feature1"],
        }
        defaults = {
            "database": {"host": "default_host", "port": 3306, "ssl": True},
            "features": ["default1", "default2"],
            "logging": {"level": "INFO"},
        }

        with open(config_file, "w") as f:
            json.dump(partial_config, f)

        result = ConfigLoader.load_config_with_defaults(str(config_file), defaults)
        expected = {
            "database": {
                "host": "localhost",  # Overridden
                "port": 5432,  # Overridden
                "ssl": True,  # From defaults
            },
            "features": ["feature1"],  # Completely overridden
            "logging": {"level": "INFO"},  # From defaults
        }
        assert result == expected

    def test_list_merge_behavior(self, temp_dir):
        """Test that lists are replaced, not merged"""
        config_file = temp_dir / "test_config.json"
        partial_config = {"list_key": ["item1"]}
        defaults = {"list_key": ["default1", "default2"]}

        with open(config_file, "w") as f:
            json.dump(partial_config, f)

        result = ConfigLoader.load_config_with_defaults(str(config_file), defaults)
        assert result == {"list_key": ["item1"]}  # Replaced, not merged

    def test_file_permissions_error(self, temp_dir):
        """Test handling of file permission errors"""
        config_file = temp_dir / "readonly.json"
        config_file.write_text('{"test": "data"}')

        # Make file unreadable (skip on Windows)
        import platform

        if platform.system() == "Windows":
            pytest.skip("File permission test not applicable on Windows")

        try:
            config_file.chmod(0o000)
            defaults = {"fallback": "value"}
            result = ConfigLoader.load_config_with_defaults(str(config_file), defaults)
            assert result == defaults
        except (OSError, PermissionError):
            pytest.skip("File permission test not applicable on this system")
        finally:
            try:
                config_file.chmod(0o644)
            except:
                pass

    def test_relative_path_handling(self, temp_dir):
        """Test handling of relative paths"""
        config_file = temp_dir / "config.json"
        test_config = {"path": "relative/path"}

        with open(config_file, "w") as f:
            json.dump(test_config, f)

        # Change to temp directory
        original_cwd = os.getcwd()
        try:
            os.chdir(temp_dir)
            result = ConfigLoader.load_config_with_defaults("config.json", {})
            assert result == test_config
        finally:
            os.chdir(original_cwd)
