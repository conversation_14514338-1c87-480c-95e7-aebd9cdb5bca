# tests/test_site_container_api.py
"""
Tests for SiteContainer API routes
"""

import asyncio
from unittest.mock import As<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from agent.api.site_container_routes import router
from agent.core.site_container_manager import SiteContainerManager


class TestSiteContainerAPI:
    """Test cases for SiteContainer API routes"""

    @pytest.fixture
    def app(self):
        """Create FastAPI app with site container routes"""
        app = FastAPI()
        app.include_router(router)
        return app

    @pytest.fixture
    def client(self, app):
        """Create test client"""
        return TestClient(app)

    @pytest.fixture
    def mock_agent(self):
        """Create mock agent for dependency injection"""
        return Mock()

    def test_create_site_container_success(self, client):
        """Test successful site container creation via API"""
        with patch("api.site_container_routes.get_agent") as mock_get_agent, patch(
            "subprocess.run"
        ) as mock_subprocess, patch("core.site_container_manager.docker.from_env"):

            # Mock the agent dependency
            mock_agent = Mock()
            mock_get_agent.return_value = mock_agent

            # Mock subprocess.run to simulate successful Docker builds
            mock_subprocess.return_value.returncode = 0
            mock_subprocess.return_value.stdout = "Build successful"

            # Create a temporary site directory for the test
            import os
            import tempfile

            with tempfile.TemporaryDirectory() as temp_dir:
                site_dir = os.path.join(temp_dir, "sites", "test-site")
                os.makedirs(site_dir, exist_ok=True)
                with open(os.path.join(site_dir, "index.html"), "w") as f:
                    f.write("<html><body>Test</body></html>")

                # Patch the sites directory
                with patch(
                    "core.site_container_manager.SiteContainerManager"
                ) as mock_manager_class:
                    mock_manager = Mock()
                    mock_manager_class.return_value = mock_manager
                    mock_manager.sites_dir = temp_dir + "/sites"

                    # Mock successful creation
                    mock_manager.create_site_container = AsyncMock(
                        return_value={
                            "success": True,
                            "port": 8080,
                            "container": {
                                "site_name": "test-site",
                                "container_name": "site-test-site",
                                "port": 8080,
                                "status": "stopped",
                            },
                            "dockerfile": "/path/to/Dockerfile",
                            "compose": "/path/to/docker-compose.yml",
                        }
                    )

                    request_data = {
                        "site_name": "test-site",
                        "environment": "production",
                    }

                    response = client.post(
                        "/api/site-containers/create", json=request_data
                    )

                    assert response.status_code == 200
                    data = response.json()
                    assert data["success"] is True
                    assert data["port"] == 8080
                    assert "container" in data

    def test_create_site_container_failure(self, client):
        """Test site container creation failure via API"""
        with patch("api.site_container_routes.get_agent") as mock_get_agent, patch(
            "api.site_container_routes.SiteContainerManager"
        ) as mock_manager_class, patch("core.site_container_manager.docker.from_env"):

            mock_agent = Mock()
            mock_get_agent.return_value = mock_agent

            mock_manager = Mock()
            mock_manager_class.return_value = mock_manager
            mock_manager.create_site_container = AsyncMock(
                return_value={"success": False, "error": "Site does not exist"}
            )

            request_data = {
                "site_name": "non-existent-site",
                "environment": "production",
            }

            response = client.post("/api/site-containers/create", json=request_data)

            # The API returns 500 with the error message in the detail
            assert response.status_code == 500
            data = response.json()
            assert "detail" in data
            assert "Site does not exist" in data["detail"]

    def test_start_site_container_success(self, client):
        """Test successful site container start via API"""
        with patch("api.site_container_routes.get_agent") as mock_get_agent, patch(
            "api.site_container_routes.SiteContainerManager"
        ) as mock_manager_class:

            mock_agent = Mock()
            mock_get_agent.return_value = mock_agent

            mock_manager = Mock()
            mock_manager_class.return_value = mock_manager
            mock_manager.start_site_container = AsyncMock(
                return_value={
                    "success": True,
                    "url": "http://localhost:8080",
                    "status": "running",
                }
            )

            response = client.post("/api/site-containers/test-site/start")

            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["url"] == "http://localhost:8080"

    def test_stop_site_container_success(self, client):
        """Test successful site container stop via API"""
        with patch("api.site_container_routes.get_agent") as mock_get_agent, patch(
            "api.site_container_routes.SiteContainerManager"
        ) as mock_manager_class:

            mock_agent = Mock()
            mock_get_agent.return_value = mock_agent

            mock_manager = Mock()
            mock_manager_class.return_value = mock_manager
            mock_manager.stop_site_container = AsyncMock(
                return_value={"success": True, "status": "stopped"}
            )

            response = client.post("/api/site-containers/test-site/stop")

            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True

    def test_delete_site_container_success(self, client):
        """Test successful site container deletion via API"""
        with patch("api.site_container_routes.get_agent") as mock_get_agent, patch(
            "api.site_container_routes.SiteContainerManager"
        ) as mock_manager_class:

            mock_agent = Mock()
            mock_get_agent.return_value = mock_agent

            mock_manager = Mock()
            mock_manager_class.return_value = mock_manager
            mock_manager.delete_site_container = AsyncMock(
                return_value={
                    "success": True,
                    "message": "Container deleted successfully",
                }
            )

            response = client.delete("/api/site-containers/test-site")

            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True

    def test_list_site_containers_success(self, client):
        """Test successful site containers listing via API"""
        with patch("api.site_container_routes.get_agent") as mock_get_agent, patch(
            "api.site_container_routes.SiteContainerManager"
        ) as mock_manager_class, patch("core.site_container_manager.docker.from_env"):

            mock_agent = Mock()
            mock_get_agent.return_value = mock_agent

            mock_manager = Mock()
            mock_manager_class.return_value = mock_manager
            mock_manager.list_containers = AsyncMock(
                return_value={
                    "success": True,
                    "containers": [
                        {
                            "site_name": "site1",
                            "port": 8080,
                            "status": "running",
                            "health_status": "healthy",
                        },
                        {
                            "site_name": "site2",
                            "port": 8081,
                            "status": "stopped",
                            "health_status": "unhealthy",
                        },
                    ],
                    "total": 2,
                }
            )

            response = client.get("/api/site-containers/")

            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["total"] == 2
            assert len(data["containers"]) == 2

    def test_get_container_status_success(self, client):
        """Test successful container status retrieval via API"""
        with patch("api.site_container_routes.get_agent") as mock_get_agent, patch(
            "core.site_container_manager.SiteContainerManager"
        ) as mock_manager_class, patch("core.site_container_manager.docker.from_env"):

            mock_agent = Mock()
            mock_get_agent.return_value = mock_agent

            mock_manager = Mock()
            mock_manager_class.return_value = mock_manager
            mock_manager.get_container_status = AsyncMock(
                return_value={
                    "success": True,
                    "container": {
                        "site_name": "test-site",
                        "port": 8080,
                        "status": "running",
                        "health_status": "healthy",
                    },
                }
            )

            response = client.get("/api/site-containers/test-site/status")

            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert data["container"]["site_name"] == "test-site"

    def test_rebuild_site_container_success(self, client):
        """Test successful site container rebuild via API"""
        with patch("api.site_container_routes.get_agent") as mock_get_agent, patch(
            "api.site_container_routes.SiteContainerManager"
        ) as mock_manager_class:

            mock_agent = Mock()
            mock_get_agent.return_value = mock_agent

            mock_manager = Mock()
            mock_manager_class.return_value = mock_manager
            mock_manager.rebuild_site_container = AsyncMock(
                return_value={
                    "success": True,
                    "message": "Container rebuilt successfully",
                    "port": 8080,
                }
            )

            response = client.post("/api/site-containers/test-site/rebuild")

            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True

    def test_get_container_logs_success(self, client):
        """Test successful container logs retrieval via API"""
        with patch("api.site_container_routes.get_agent") as mock_get_agent, patch(
            "api.site_container_routes.SiteContainerManager"
        ) as mock_manager_class, patch("core.site_container_manager.docker.from_env"):

            mock_agent = Mock()
            mock_get_agent.return_value = mock_agent

            mock_manager = Mock()
            mock_manager_class.return_value = mock_manager
            mock_manager.get_container_logs = AsyncMock(
                return_value={
                    "success": True,
                    "logs": [
                        "2024-01-01 12:00:00 - Container started",
                        "2024-01-01 12:01:00 - HTTP server listening on port 8080",
                    ],
                    "container_name": "site-test-site",
                }
            )

            response = client.get("/api/site-containers/test-site/logs?lines=100")

            assert response.status_code == 200
            data = response.json()
            assert data["success"] is True
            assert "Container started" in str(data["logs"])

    def test_api_error_handling(self, client):
        """Test API error handling"""
        with patch("api.site_container_routes.get_agent") as mock_get_agent, patch(
            "api.site_container_routes.SiteContainerManager"
        ) as mock_manager_class:

            mock_agent = Mock()
            mock_get_agent.return_value = mock_agent

            mock_manager = Mock()
            mock_manager_class.return_value = mock_manager
            mock_manager.create_site_container = AsyncMock(
                side_effect=Exception("Test error")
            )

            request_data = {"site_name": "test-site", "environment": "production"}

            response = client.post("/api/site-containers/create", json=request_data)

            assert response.status_code == 500

    def test_invalid_request_data(self, client):
        """Test API with invalid request data"""
        # Missing required site_name field
        request_data = {"environment": "production"}

        response = client.post("/api/site-containers/create", json=request_data)

        assert response.status_code == 422  # Validation error
