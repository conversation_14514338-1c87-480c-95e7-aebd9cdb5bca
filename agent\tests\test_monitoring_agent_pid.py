"""
Unit tests for monitoring agent P<PERSON> file functionality and status checking
"""

import os
import tempfile
import time
from pathlib import Path
from unittest.mock import MagicMock, mock_open, patch

import psutil
import pytest

from agent.monitoring.monitoring_agent import MonitoringAgent


class TestMonitoringAgentPIDFile:
    """Test PID file functionality"""

    @pytest.fixture
    def temp_logs_dir(self):
        """Create temporary logs directory"""
        with tempfile.TemporaryDirectory() as temp_dir:
            logs_dir = Path(temp_dir) / "logs"
            logs_dir.mkdir()
            yield logs_dir

    @pytest.fixture
    def agent(self, temp_logs_dir):
        """Create monitoring agent with temporary logs directory"""
        with patch("monitoring.monitoring_agent.Path") as mock_path:
            mock_path.return_value = temp_logs_dir / "monitor.pid"
            agent = MonitoringAgent()
            agent.pid_file_path = temp_logs_dir / "monitor.pid"
            yield agent

    def test_check_pid_file_not_exists(self, agent):
        """Test checking PID file when it doesn't exist"""
        result = agent._check_pid_file()
        assert result is None

    def test_check_pid_file_empty(self, agent):
        """Test checking empty PID file"""
        # Create empty PID file
        agent.pid_file_path.write_text("")

        result = agent._check_pid_file()
        assert result is None
        assert not agent.pid_file_path.exists()  # Should be removed

    def test_check_pid_file_invalid_content(self, agent):
        """Test checking PID file with invalid content"""
        # Create PID file with invalid content
        agent.pid_file_path.write_text("invalid")

        result = agent._check_pid_file()
        assert result is None
        assert not agent.pid_file_path.exists()  # Should be removed

    @patch("psutil.pid_exists")
    def test_check_pid_file_process_alive(self, mock_pid_exists, agent):
        """Test checking PID file when process is alive"""
        mock_pid_exists.return_value = True

        # Create PID file with valid PID
        test_pid = 12345
        agent.pid_file_path.write_text(str(test_pid))

        result = agent._check_pid_file()
        assert result == test_pid
        assert agent.pid_file_path.exists()  # Should not be removed

    @patch("psutil.pid_exists")
    def test_check_pid_file_process_dead(self, mock_pid_exists, agent):
        """Test checking PID file when process is dead"""
        mock_pid_exists.return_value = False

        # Create PID file with valid PID
        test_pid = 12345
        agent.pid_file_path.write_text(str(test_pid))

        result = agent._check_pid_file()
        assert result is None
        assert not agent.pid_file_path.exists()  # Should be removed

    def test_write_pid_file_success(self, agent):
        """Test writing PID file successfully"""
        result = agent._write_pid_file()
        assert result is True
        assert agent.pid_file_path.exists()
        assert agent.pid_file_path.read_text().strip() == str(agent.pid)

    def test_write_pid_file_failure(self, agent):
        """Test writing PID file when it fails"""
        # Mock the open to raise an exception
        with patch("builtins.open", side_effect=OSError("Permission denied")):
            result = agent._write_pid_file()
            assert result is False

    def test_remove_pid_file_exists(self, agent):
        """Test removing PID file when it exists"""
        # Create PID file
        agent.pid_file_path.write_text("12345")
        assert agent.pid_file_path.exists()

        agent._remove_pid_file()
        assert not agent.pid_file_path.exists()

    def test_remove_pid_file_not_exists(self, agent):
        """Test removing PID file when it doesn't exist"""
        assert not agent.pid_file_path.exists()

        # Should not raise an exception
        agent._remove_pid_file()
        assert not agent.pid_file_path.exists()


class TestMonitoringAgentStatus:
    """Test status checking functionality"""

    @pytest.fixture
    def temp_logs_dir(self):
        """Create temporary logs directory"""
        with tempfile.TemporaryDirectory() as temp_dir:
            logs_dir = Path(temp_dir) / "logs"
            logs_dir.mkdir()
            yield logs_dir

    @pytest.fixture
    def agent(self, temp_logs_dir):
        """Create monitoring agent with temporary logs directory"""
        with patch("monitoring.monitoring_agent.Path") as mock_path:
            mock_path.return_value = temp_logs_dir / "monitor.pid"
            agent = MonitoringAgent()
            agent.pid_file_path = temp_logs_dir / "monitor.pid"
            yield agent

    @patch("psutil.pid_exists")
    def test_get_status_running_pid_file(self, mock_pid_exists, agent):
        """Test getting status when process is running and PID file exists"""
        mock_pid_exists.return_value = True

        # Create PID file
        test_pid = 12345
        agent.pid_file_path.write_text(str(test_pid))

        status = agent.get_status()

        assert status["status"] == "running"
        assert status["pid"] == test_pid
        assert status["message"] == f"Monitoring running (PID {test_pid})"
        assert status["source"] == "pid_file"

    @patch("psutil.pid_exists")
    def test_get_status_not_running_pid_file(self, mock_pid_exists, agent):
        """Test getting status when PID file exists but process is dead"""
        mock_pid_exists.return_value = False

        # Create PID file
        test_pid = 12345
        agent.pid_file_path.write_text(str(test_pid))

        status = agent.get_status()

        assert status["status"] == "not_running"
        assert status["pid"] is None
        assert status["message"] == "Monitoring not running"
        assert status["source"] == "process_scan"

    @patch("psutil.process_iter")
    def test_get_status_running_process_scan_single(self, mock_process_iter, agent):
        """Test getting status when no PID file but single process found"""
        # Mock process_iter to return one monitoring process
        mock_process = MagicMock()
        mock_process.pid = 12345
        mock_process.info = {
            "pid": 12345,
            "name": "python",
            "cmdline": ["python", "-m", "monitoring.monitoring_agent"],
        }
        mock_process_iter.return_value = [mock_process]

        status = agent.get_status()

        assert status["status"] == "running"
        assert status["pid"] == 12345
        assert status["message"] == "Monitoring running (PID 12345) (recovered)"
        assert status["source"] == "process_scan_recovered"
        assert agent.pid_file_path.exists()  # PID file should be recreated

    @patch("psutil.process_iter")
    def test_get_status_running_process_scan_multiple(self, mock_process_iter, agent):
        """Test getting status when no PID file but multiple processes found"""
        # Mock process_iter to return multiple monitoring processes
        mock_process1 = MagicMock()
        mock_process1.pid = 12345
        mock_process1.info = {
            "pid": 12345,
            "name": "python",
            "cmdline": ["python", "-m", "monitoring.monitoring_agent"],
        }
        mock_process2 = MagicMock()
        mock_process2.pid = 67890
        mock_process2.info = {
            "pid": 67890,
            "name": "python",
            "cmdline": ["python", "-m", "monitoring.monitoring_agent"],
        }
        mock_process_iter.return_value = [mock_process1, mock_process2]

        status = agent.get_status()

        assert status["status"] == "multiple_processes"
        assert status["pid"] is None
        assert (
            status["message"]
            == "Multiple monitoring processes detected: PIDs [12345, 67890]"
        )
        assert status["source"] == "process_scan"
        assert status["pids"] == [12345, 67890]

    @patch("psutil.process_iter")
    def test_get_status_not_running_process_scan(self, mock_process_iter, agent):
        """Test getting status when no PID file and no processes found"""
        # Mock process_iter to return no processes
        mock_process_iter.return_value = []

        status = agent.get_status()

        assert status["status"] == "not_running"
        assert status["pid"] is None
        assert status["message"] == "Monitoring not running"
        assert status["source"] == "process_scan"

    def test_get_status_error(self, agent):
        """Test getting status when an error occurs"""
        # Mock _check_pid_file to raise an exception
        with patch.object(
            agent, "_check_pid_file", side_effect=Exception("Test error")
        ):
            status = agent.get_status()

            assert status["status"] == "error"
            assert status["pid"] is None
            assert "Test error" in status["message"]
            assert status["source"] == "error"


class TestMonitoringAgentStartup:
    """Test startup functionality"""

    @pytest.fixture
    def temp_logs_dir(self):
        """Create temporary logs directory"""
        with tempfile.TemporaryDirectory() as temp_dir:
            logs_dir = Path(temp_dir) / "logs"
            logs_dir.mkdir()
            yield logs_dir

    @pytest.fixture
    def agent(self, temp_logs_dir):
        """Create monitoring agent with temporary logs directory"""
        with patch("monitoring.monitoring_agent.Path") as mock_path:
            mock_path.return_value = temp_logs_dir / "monitor.pid"
            agent = MonitoringAgent()
            agent.pid_file_path = temp_logs_dir / "monitor.pid"
            yield agent

    @patch("psutil.pid_exists")
    async def test_start_already_running(self, mock_pid_exists, agent):
        """Test starting when another instance is already running"""
        mock_pid_exists.return_value = True

        # Create PID file for existing process
        test_pid = 12345
        agent.pid_file_path.write_text(str(test_pid))

        await agent.start()

        assert not agent.is_running  # Should not start
        assert not agent.monitoring_task  # Should not create monitoring task

    @patch("psutil.pid_exists")
    async def test_start_success(self, mock_pid_exists, agent):
        """Test starting successfully when no other instance is running"""
        mock_pid_exists.return_value = False

        await agent.start()

        assert agent.is_running
        assert agent.monitoring_task is not None
        assert agent.pid_file_path.exists()
        assert agent.pid_file_path.read_text().strip() == str(agent.pid)

    async def test_start_pid_file_write_failure(self, agent):
        """Test starting when PID file write fails"""
        # Mock _write_pid_file to return False
        with patch.object(agent, "_write_pid_file", return_value=False):
            await agent.start()

            assert not agent.is_running  # Should not start
            assert not agent.monitoring_task  # Should not create monitoring task

    async def test_stop_success(self, agent):
        """Test stopping successfully"""
        # Start the agent first
        agent.is_running = True
        agent.monitoring_task = MagicMock()

        await agent.stop()

        assert not agent.is_running
        assert not agent.pid_file_path.exists()  # PID file should be removed

    async def test_stop_not_running(self, agent):
        """Test stopping when not running"""
        agent.is_running = False

        # Should not raise an exception
        await agent.stop()

        assert not agent.is_running
