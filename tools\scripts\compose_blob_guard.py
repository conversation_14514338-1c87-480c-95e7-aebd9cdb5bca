#!/usr/bin/env python3
"""Fail commit if compose files contain large inline blobs or secrets.
Scans infrastructure/docker-compose*.yml and containers/docker-compose*.yml for:
- PEM blocks (-----BEGIN ...)
- base64-like long strings
- embedded JSON blocks over threshold
"""
from __future__ import annotations

import re
from pathlib import Path
import sys

COMPOSE_GLOBS = [
    "infrastructure/docker-compose*.yml",
    "containers/docker-compose*.yml",
]

MAX_INLINE_LEN = 2048  # characters

PEM_RE = re.compile(r"-----BEGIN [A-Z ]+-----")
LONG_RE = re.compile(r"[A-Za-z0-9+/=\n\r]{4096,}")


def has_large_inline_blob(text: str) -> bool:
    if PEM_RE.search(text):
        return True
    if LONG_RE.search(text):
        return True
    if len(text) > MAX_INLINE_LEN and ("{" in text or "}" in text):
        # crude JSON block heuristic
        return True
    return False


def scan_file(p: Path) -> list[str]:
    problems: list[str] = []
    try:
        text = p.read_text(encoding="utf-8", errors="replace")
    except Exception as e:
        return [f"{p}: unreadable ({e})"]

    lines = text.splitlines()
    for i, line in enumerate(lines, 1):
        if PEM_RE.search(line):
            problems.append(f"{p}:{i}: embedded PEM block detected")
        if len(line) > MAX_INLINE_LEN:
            problems.append(f"{p}:{i}: very long line ({len(line)} chars)")

    if has_large_inline_blob(text):
        problems.append(f"{p}: probable large inline blob detected")

    # Simple secret keyword sniff (non-fatal):
    for kw in ("JWT_SECRET", "SECRET_KEY", "DB_PASSWORD"):
        if re.search(rf"\b{kw}\b\s*:\s*[A-Za-z0-9]{{16,}}", text):
            problems.append(f"{p}: inline secret-like value for {kw}")

    return problems


def main() -> int:
    root = Path(".")
    matches = []
    for pattern in COMPOSE_GLOBS:
        matches.extend(root.glob(pattern))

    failures: list[str] = []
    for p in matches:
        failures.extend(scan_file(p))

    if failures:
        print("❌ Compose Blob Guard detected issues:\n" + "\n".join(failures))
        print("\nRemediation: externalize blobs/secrets to files or Docker secrets and mount via volumes or env_file.")
        return 1

    print("✅ Compose files clean (no large inline blobs detected)")
    return 0


if __name__ == "__main__":
    sys.exit(main())

