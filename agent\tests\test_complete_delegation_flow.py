#!/usr/bin/env python3
"""
Integration test for complete delegation flow:
ArchitectAgent -> SmartTaskRouter -> AgentMessageBus -> TaskQueue
"""
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from agent.core.agents.architect_agent import ArchitectAgent
from agent.core.project_models import Roadmap, PhaseSpec, StepSpec, TaskSpec


@pytest.fixture
def temp_project_dir():
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir


@pytest.fixture
def architect_agent(temp_project_dir):
    config_path = Path(temp_project_dir) / "test_config.json"
    config_path.write_text('{"model_settings": {"model_name": "test-model"}}')
    
    agent = ArchitectAgent(str(config_path))
    return agent


@pytest.fixture
def complex_roadmap():
    """Create a complex roadmap with dependencies for testing"""
    return Roadmap(
        id="complex_roadmap",
        title="Complex Project",
        phases=[
            PhaseSpec(
                id="phase1",
                title="Foundation Phase",
                order=0,
                steps=[
                    StepSpec(
                        id="step1",
                        title="Environment Setup",
                        order=0,
                        tasks=[
                            TaskSpec(
                                id="task1",
                                title="Initialize repository",
                                description="Set up git repository",
                                agent_type="shell",
                                requirements=["git"],
                                dependencies=[]
                            ),
                            TaskSpec(
                                id="task2",
                                title="Setup database",
                                description="Configure PostgreSQL database",
                                agent_type="backend",
                                requirements=["PostgreSQL", "migrations"],
                                dependencies=["task1"]
                            ),
                            TaskSpec(
                                id="task3",
                                title="Create frontend scaffold",
                                description="Initialize React application",
                                agent_type="frontend",
                                requirements=["React", "TypeScript"],
                                dependencies=["task1"]
                            ),
                            TaskSpec(
                                id="task4",
                                title="Setup containerization",
                                description="Create Docker configuration",
                                agent_type="container",
                                requirements=["Docker", "docker-compose"],
                                dependencies=["task2", "task3"]
                            )
                        ]
                    )
                ]
            )
        ]
    )


@pytest.mark.asyncio
@patch('core.agents.architect_agent.SmartTaskRouter')
@patch('core.agents.architect_agent.AgentMessageBus')
@patch('core.agents.architect_agent.TaskQueueManager')
async def test_complete_delegation_flow(
    mock_task_queue_class, mock_message_bus_class, mock_router_class, 
    architect_agent, complex_roadmap
):
    """Test the complete delegation flow from routing to scheduling"""
    
    # Setup mocks
    mock_router = AsyncMock()
    mock_message_bus = AsyncMock()
    mock_task_queue = AsyncMock()
    
    mock_router_class.return_value = mock_router
    mock_message_bus_class.return_value = mock_message_bus
    mock_task_queue_class.return_value = mock_task_queue
    
    # Mock routing decisions for each task
    from agent.core.agents.smart_task_router import RoutingDecision, RoutingStrategy
    
    routing_decisions = [
        RoutingDecision(
            task_id="task1",
            selected_agent_id="shell_agent",
            strategy_used=RoutingStrategy.AI_POWERED,
            confidence_score=0.95,
            reasoning="Shell operations specialist"
        ),
        RoutingDecision(
            task_id="task2",
            selected_agent_id="backend_agent",
            strategy_used=RoutingStrategy.AI_POWERED,
            confidence_score=0.92,
            reasoning="Database setup specialist"
        ),
        RoutingDecision(
            task_id="task3",
            selected_agent_id="frontend_agent",
            strategy_used=RoutingStrategy.AI_POWERED,
            confidence_score=0.88,
            reasoning="React specialist"
        ),
        RoutingDecision(
            task_id="task4",
            selected_agent_id="container_agent",
            strategy_used=RoutingStrategy.AI_POWERED,
            confidence_score=0.90,
            reasoning="Docker specialist"
        )
    ]
    
    # Configure mock to return different decisions for each call
    mock_router.route_task.side_effect = routing_decisions
    
    # Mock message bus
    mock_message_bus.publish.return_value = True
    mock_message_bus.subscribe.return_value = True
    
    # Mock task queue
    mock_task_queue.add_task.side_effect = [
        "queue_task_001", "queue_task_002", "queue_task_003", "queue_task_004"
    ]
    mock_task_queue.start.return_value = None
    
    # Setup project context
    architect_agent._current_project_id = "test_project"
    
    # Mock project store
    mock_store = MagicMock()
    mock_store.load_state.return_value = MagicMock()
    mock_store.save_state.return_value = None
    architect_agent._project_store = mock_store
    
    # Execute delegation
    result = await architect_agent._delegate_tasks_to_agents(complex_roadmap)
    
    # Verify delegation results
    assert result["success_rate"] > 0
    assert len(result["delegated_tasks"]) == 4
    assert len(result["routing_failures"]) == 0
    
    # Verify routing was called for each task
    assert mock_router.route_task.call_count == 4
    
    # Verify messages were sent for each task
    assert mock_message_bus.publish.call_count == 4
    
    # Verify task queue integration
    assert mock_task_queue.add_task.call_count == 4
    
    # Verify dependency handling
    delegated_tasks = result["delegated_tasks"]
    
    # Task 1 should have no dependencies
    task1 = next(t for t in delegated_tasks if t["task_id"] == "task1")
    assert task1["dependencies"] == []
    
    # Task 2 should depend on task 1
    task2 = next(t for t in delegated_tasks if t["task_id"] == "task2")
    assert "task1" in task2["dependencies"]
    
    # Task 4 should depend on tasks 2 and 3
    task4 = next(t for t in delegated_tasks if t["task_id"] == "task4")
    assert "task2" in task4["dependencies"]
    assert "task3" in task4["dependencies"]


@pytest.mark.asyncio
async def test_task_execution_simulation(architect_agent):
    """Test the task execution simulation"""
    architect_agent._current_project_id = "test_project"
    
    # Mock project store
    mock_store = MagicMock()
    architect_agent._project_store = mock_store
    
    task_data = {
        "task_id": "test_task",
        "agent": "shell_agent",
        "title": "Test Task",
        "description": "Test description"
    }
    
    # Execute task
    result = await architect_agent._execute_delegated_task(task_data)
    
    # Verify execution result
    assert result["success"] is True
    assert result["task_id"] == "test_task"
    assert result["agent"] == "shell_agent"


@pytest.mark.asyncio
async def test_task_scheduling_with_dependencies(architect_agent):
    """Test task scheduling with dependency management"""
    
    # Mock task queue
    mock_task_queue = AsyncMock()
    mock_task_queue.add_task.side_effect = ["queue_1", "queue_2", "queue_3"]
    mock_task_queue.start.return_value = None
    architect_agent._task_queue = mock_task_queue
    
    # Create delegated tasks with dependencies
    delegated_tasks = [
        {
            "task_id": "task1",
            "title": "First Task",
            "description": "No dependencies",
            "agent": "shell_agent",
            "dependencies": [],
            "correlation_id": "corr_1"
        },
        {
            "task_id": "task2", 
            "title": "Second Task",
            "description": "Depends on task1",
            "agent": "backend_agent",
            "dependencies": ["task1"],
            "correlation_id": "corr_2"
        },
        {
            "task_id": "task3",
            "title": "Third Task", 
            "description": "Depends on task1 and task2",
            "agent": "frontend_agent",
            "dependencies": ["task1", "task2"],
            "correlation_id": "corr_3"
        }
    ]
    
    # Schedule tasks
    result = await architect_agent._schedule_tasks_with_dependencies(delegated_tasks)
    
    # Verify scheduling results
    assert result["success_rate"] == 1.0
    assert len(result["scheduled_tasks"]) == 3
    assert len(result["scheduling_failures"]) == 0
    
    # Verify task queue was called for each task
    assert mock_task_queue.add_task.call_count == 3
    
    # Verify dependencies were preserved
    scheduled_tasks = result["scheduled_tasks"]
    
    task1_scheduled = next(t for t in scheduled_tasks if t["original_task_id"] == "task1")
    assert task1_scheduled["dependencies"] == []
    
    task2_scheduled = next(t for t in scheduled_tasks if t["original_task_id"] == "task2")
    assert task2_scheduled["dependencies"] == ["task1"]
    
    task3_scheduled = next(t for t in scheduled_tasks if t["original_task_id"] == "task3")
    assert task3_scheduled["dependencies"] == ["task1", "task2"]


@pytest.mark.asyncio
async def test_agent_response_handling(architect_agent):
    """Test handling of agent responses"""
    from agent.core.agents.agent_message_bus import AgentMessage, MessageType
    
    # Setup project context
    architect_agent._current_project_id = "test_project"
    
    # Mock project store with roadmap
    mock_store = MagicMock()
    mock_roadmap = MagicMock()
    mock_store.load_roadmap.return_value = mock_roadmap
    architect_agent._project_store = mock_store
    
    # Create response message
    response_message = AgentMessage(
        id="response_123",
        message_type=MessageType.TASK_RESPONSE,
        sender_id="shell_agent",
        recipient_id="architect_agent",
        subject="Task Completed",
        content={
            "task_id": "task1",
            "status": "completed",
            "artifacts": ["setup_complete.log"],
            "execution_time": 120
        }
    )
    
    # Handle response
    result = await architect_agent._handle_agent_response(response_message)
    
    # Verify response was handled successfully
    assert result is True
    
    # Verify project store was accessed
    mock_store.load_roadmap.assert_called_once_with("test_project")
