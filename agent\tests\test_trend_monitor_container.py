#!/usr/bin/env python3
"""
Tests for Trend Monitor Containerization

Tests the containerization implementation including:
- Container creation and management
- Service health checks
- API endpoints
- CLI commands
"""

import asyncio
import json
import shutil
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch

import pytest
from fastapi.testclient import TestClient

from trend_monitoring.trend_monitor_container_manager import (
    TrendMonitorContainer,
    TrendMonitorContainerManager,
    TrendMonitorStatus,
)
from trend_monitoring.trend_monitor_service import app


class TestTrendMonitorContainer:
    """Test Trend Monitor Container dataclass"""

    def test_container_initialization(self):
        """Test container initialization with default values"""
        container = TrendMonitorContainer()

        assert container.container_name == "ai-coding-trend-monitor"
        assert container.image_name == "ai-coding-trend-monitor"
        assert container.port == 8080
        assert container.status == TrendMonitorStatus.STOPPED
        assert container.monitoring_enabled is True
        assert container.data_dir == "data/trends"
        assert container.update_interval == 3600
        assert container.analysis_window == 30
        assert container.created_at is not None
        assert container.resource_usage == {}
        assert container.logs == []

    def test_container_custom_initialization(self):
        """Test container initialization with custom values"""
        container = TrendMonitorContainer(
            container_name="test-container", port=9000, monitoring_enabled=False
        )

        assert container.container_name == "test-container"
        assert container.port == 9000
        assert container.monitoring_enabled is False


class TestTrendMonitorContainerManager:
    """Test Trend Monitor Container Manager"""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for testing"""
        temp_dir = tempfile.mkdtemp()
        yield Path(temp_dir)
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def manager(self, temp_dir):
        """Create Trend Monitor Container Manager instance"""
        return TrendMonitorContainerManager(str(temp_dir), str(temp_dir / "containers"))

    def test_manager_initialization(self, manager, temp_dir):
        """Test manager initialization"""
        assert manager.base_dir == temp_dir
        assert manager.containers_dir == temp_dir / "containers"
        assert manager.trend_monitor_dir == temp_dir / "trend_monitoring"
        assert manager.container is not None
        assert isinstance(manager.container, TrendMonitorContainer)

        # Check if directories were created
        assert manager.containers_dir.exists()
        assert manager.trend_monitor_dir.exists()

    @pytest.mark.asyncio
    async def test_create_trend_monitor_container_success(self, manager, temp_dir):
        """Test successful container creation"""
        # Create mock Dockerfile and docker-compose.yml
        dockerfile_path = manager.trend_monitor_dir / "Dockerfile"
        dockerfile_path.parent.mkdir(parents=True, exist_ok=True)
        dockerfile_path.write_text("# Test Dockerfile")

        compose_path = manager.trend_monitor_dir / "docker-compose.yml"
        compose_path.write_text("version: '3.8'")

        with patch.object(manager, "_build_trend_monitor_image") as mock_build:
            mock_build.return_value = {"success": True, "image_id": "test-image"}

            result = await manager.create_trend_monitor_container()

            assert result["success"] is True
            assert "container" in result
            assert result["container"]["container_name"] == "ai-coding-trend-monitor"
            assert result["container"]["status"] == TrendMonitorStatus.STOPPED.value

    @pytest.mark.asyncio
    async def test_create_trend_monitor_container_missing_dockerfile(self, manager):
        """Test container creation with missing Dockerfile"""
        result = await manager.create_trend_monitor_container()

        assert result["success"] is False
        assert "Dockerfile not found" in result["error"]

    @pytest.mark.asyncio
    async def test_start_trend_monitor_container_success(self, manager, temp_dir):
        """Test successful container start"""
        # Create mock docker-compose.yml
        compose_path = manager.trend_monitor_dir / "docker-compose.yml"
        compose_path.parent.mkdir(parents=True, exist_ok=True)
        compose_path.write_text("version: '3.8'")

        with patch("subprocess.run") as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Container started"

            result = await manager.start_trend_monitor_container()

            assert result["success"] is True
            assert "url" in result
            assert result["url"] == "http://localhost:8080"

    @pytest.mark.asyncio
    async def test_stop_trend_monitor_container_success(self, manager, temp_dir):
        """Test successful container stop"""
        # Create mock docker-compose.yml
        compose_path = manager.trend_monitor_dir / "docker-compose.yml"
        compose_path.parent.mkdir(parents=True, exist_ok=True)
        compose_path.write_text("version: '3.8'")

        with patch("subprocess.run") as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Container stopped"

            result = await manager.stop_trend_monitor_container()

            assert result["success"] is True
            assert result["container"]["status"] == TrendMonitorStatus.STOPPED.value

    @pytest.mark.asyncio
    async def test_get_trend_monitor_status(self, manager):
        """Test getting container status"""
        result = await manager.get_trend_monitor_status()

        assert result["success"] is True
        assert "container" in result
        assert result["container"]["container_name"] == "ai-coding-trend-monitor"

    @pytest.mark.asyncio
    async def test_health_check_success(self, manager):
        """Test successful health check"""
        with patch("aiohttp.ClientSession") as mock_session:
            mock_response = Mock()
            mock_response.status = 200
            mock_response.json = AsyncMock(
                return_value={
                    "status": "healthy",
                    "version": "1.0.0",
                    "uptime": 3600.5,
                    "monitoring_active": True,
                    "trends_count": 150,
                    "insights_count": 25,
                }
            )

            mock_session.return_value.__aenter__.return_value.get.return_value.__aenter__.return_value = (
                mock_response
            )

            result = await manager.health_check()

            assert result["success"] is True
            assert result["health"]["status"] == "healthy"

    @pytest.mark.asyncio
    async def test_health_check_failure(self, manager):
        """Test health check failure"""
        with patch("aiohttp.ClientSession") as mock_session:
            mock_session.return_value.__aenter__.return_value.get.side_effect = (
                Exception("Connection failed")
            )

            result = await manager.health_check()

            assert result["success"] is False
            assert "Connection failed" in result["error"]


class TestTrendMonitorService:
    """Test Trend Monitor Service API"""

    @pytest.fixture
    def client(self):
        """Create test client"""
        return TestClient(app)

    def test_root_endpoint(self, client):
        """Test root endpoint"""
        response = client.get("/")
        assert response.status_code == 200

        data = response.json()
        assert data["service"] == "Trend Monitor Service"
        assert data["version"] == "1.0.0"
        assert "endpoints" in data

    def test_health_endpoint(self, client):
        """Test health endpoint"""
        response = client.get("/health")
        assert response.status_code == 200

        data = response.json()
        assert "status" in data
        assert "timestamp" in data
        assert "version" in data

    def test_trends_endpoint(self, client):
        """Test trends endpoint"""
        response = client.get("/api/v1/trends")
        assert response.status_code == 200

        data = response.json()
        assert data["success"] is True
        assert "data" in data
        assert "trends" in data["data"] or "coding_trends" in data["data"]

    def test_insights_endpoint(self, client):
        """Test insights endpoint"""
        response = client.get("/api/v1/insights")
        assert response.status_code == 200

        data = response.json()
        assert data["success"] is True
        assert "data" in data

    def test_predictions_endpoint(self, client):
        """Test predictions endpoint"""
        response = client.get("/api/v1/predictions")
        assert response.status_code == 200

        data = response.json()
        assert data["success"] is True
        assert "data" in data

    def test_summary_endpoint(self, client):
        """Test summary endpoint"""
        response = client.get("/api/v1/summary")
        assert response.status_code == 200

        data = response.json()
        assert data["success"] is True
        assert "data" in data


class TestTrendMonitorCLI:
    """Test Trend Monitor CLI Commands"""

    @pytest.mark.asyncio
    async def test_create_command(self):
        """Test create command"""
        from agent.cli.trend_monitor_commands import trend_monitor

        with patch(
            "cli.trend_monitor_commands.TrendMonitorContainerManager"
        ) as mock_manager_class:
            mock_manager = Mock()
            mock_manager.create_trend_monitor_container.return_value = {
                "success": True,
                "container": {
                    "container_name": "ai-coding-trend-monitor",
                    "port": 8080,
                    "status": "stopped",
                },
            }
            mock_manager_class.return_value = mock_manager

            # Test the create command
            result = await trend_monitor.commands["create"].callback()
            assert result is None  # CLI commands don't return values

    @pytest.mark.asyncio
    async def test_status_command(self):
        """Test status command"""
        from agent.cli.trend_monitor_commands import trend_monitor

        with patch(
            "cli.trend_monitor_commands.TrendMonitorContainerManager"
        ) as mock_manager_class:
            mock_manager = Mock()
            mock_manager.get_trend_monitor_status.return_value = {
                "success": True,
                "container": {
                    "container_name": "ai-coding-trend-monitor",
                    "status": "running",
                    "port": 8080,
                    "health_status": "healthy",
                },
            }
            mock_manager_class.return_value = mock_manager

            # Test the status command
            result = await trend_monitor.commands["status"].callback()
            assert result is None


class TestTrendMonitorIntegration:
    """Integration tests for Trend Monitor"""

    @pytest.mark.asyncio
    async def test_full_container_lifecycle(self, temp_dir):
        """Test full container lifecycle"""
        manager = TrendMonitorContainerManager(
            str(temp_dir), str(temp_dir / "containers")
        )

        # Create mock files
        dockerfile_path = manager.trend_monitor_dir / "Dockerfile"
        dockerfile_path.parent.mkdir(parents=True, exist_ok=True)
        dockerfile_path.write_text("# Test Dockerfile")

        compose_path = manager.trend_monitor_dir / "docker-compose.yml"
        compose_path.write_text("version: '3.8'")

        # Test container creation
        with patch.object(manager, "_build_trend_monitor_image") as mock_build:
            mock_build.return_value = {"success": True, "image_id": "test-image"}

            create_result = await manager.create_trend_monitor_container()
            assert create_result["success"] is True

        # Test container start
        with patch("subprocess.run") as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Container started"

            start_result = await manager.start_trend_monitor_container()
            assert start_result["success"] is True

        # Test container status
        status_result = await manager.get_trend_monitor_status()
        assert status_result["success"] is True

        # Test container stop
        with patch("subprocess.run") as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Container stopped"

            stop_result = await manager.stop_trend_monitor_container()
            assert stop_result["success"] is True


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
