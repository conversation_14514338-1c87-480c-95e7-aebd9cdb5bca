import json
import os
import unittest
from pathlib import Path
from unittest.mock import mock_open, patch

from agent.core.managers.deployment_manager import DeploymentManager


class TestDeploymentManager(unittest.TestCase):
    def setUp(self):
        self.manager = DeploymentManager()
        # Create test site directory
        test_site_dir = Path("sites/test-site")
        test_site_dir.mkdir(parents=True, exist_ok=True)
        (test_site_dir / "index.html").write_text("<html><body>Test</body></html>")

    @patch("shutil.copytree")
    @patch("subprocess.run")
    def test_deploy_site(self, mock_run, mock_copytree):
        """Test deploying a site"""
        result = self.manager.deploy_site("test-site")
        self.assertEqual(result["status"], "success")
        self.assertIn("deploy_path", result)

    def test_deploy_nonexistent_site(self):
        """Test deploying a non-existent site"""
        result = self.manager.deploy_site("non-existent")
        self.assertEqual(result["status"], "error")
        self.assertIn("message", result)

    @patch("shutil.copytree")
    def test_rollback_deployment(self, mock_copytree):
        """Test rolling back a deployment"""
        # Create a backup directory
        backup_path = Path("deployments/test-site_20250715_123456")
        backup_path.mkdir(parents=True, exist_ok=True)
        (backup_path / "index.html").write_text("<html><body>Backup</body></html>")

        timestamp = "20250715_123456"
        result = self.manager.rollback_deployment("test-site", timestamp)
        self.assertEqual(result["status"], "success")

    def test_rollback_nonexistent_backup(self):
        """Test rolling back to non-existent backup"""
        result = self.manager.rollback_deployment("test-site", "invalid_timestamp")
        self.assertEqual(result["status"], "error")

    @patch("os.symlink")
    @patch("subprocess.run")
    def test_update_nginx_config(self, mock_run, mock_symlink):
        """Test updating nginx configuration"""
        site_name = "test-site"
        deploy_path = Path("/path/to/deploy")

        # Mock the nginx config writing
        with patch("builtins.open", mock_open()) as mock_file:
            self.manager._update_nginx_config(site_name, deploy_path)
            mock_file.assert_called()


if __name__ == "__main__":
    unittest.main()
