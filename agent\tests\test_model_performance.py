#!/usr/bin/env python3
"""
Ollama Model Performance Testing Script
Benchmark and test the performance of all Ollama models.
"""

import asyncio
import json
import statistics
import time
from pathlib import Path
from typing import Any, Dict, List

import requests

# Test prompts for different use cases
TEST_PROMPTS = {
    "code_generation": "Write a Python function to sort a list of dictionaries by a specific key",
    "code_review": "Review this code for potential issues: def divide(a, b): return a / b",
    "content_creation": "Write a brief explanation of what machine learning is",
    "general_assistance": "Explain the difference between a list and a tuple in Python",
    "complex_problem_solving": "Design a system to handle user authentication with JWT tokens",
}


class ModelPerformanceTester:
    """Test and benchmark Ollama model performance."""

    def __init__(self, config_path: str = "config/ai_models_config.json"):
        self.config_path = Path(config_path)
        self.config = self._load_config()
        self.ollama_url = "http://localhost:11434"
        self.results = {}

    def _load_config(self) -> Dict[str, Any]:
        """Load AI models configuration."""
        try:
            with open(self.config_path, "r") as f:
                return json.load(f)
        except Exception as e:
            print(f"Failed to load config: {e}")
            return {}

    async def test_all_models(self) -> Dict[str, Any]:
        """Test performance of all models."""
        print("🚀 Starting Ollama Model Performance Testing")
        print("=" * 60)

        models = self.config.get("models", {})

        for model_name, config in models.items():
            print(f"\n🤖 Testing {model_name}...")
            model_results = await self._test_model(model_name, config)
            self.results[model_name] = model_results

        return self.results

    async def _test_model(
        self, model_name: str, config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Test performance of a specific model."""
        results = {"model_name": model_name, "tests": {}, "summary": {}}

        # Test each use case
        use_cases = config.get("use_cases", [])
        for use_case in use_cases:
            if use_case in TEST_PROMPTS:
                print(f"  📝 Testing {use_case}...")
                test_result = await self._test_use_case(model_name, use_case, config)
                results["tests"][use_case] = test_result

        # Calculate summary statistics
        results["summary"] = self._calculate_summary(results["tests"])

        return results

    async def _test_use_case(
        self, model_name: str, use_case: str, config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Test a specific use case with a model."""
        prompt = TEST_PROMPTS[use_case]
        optimization = config.get("optimization", {})

        # Run multiple tests for averaging
        response_times = []
        success_count = 0
        total_tests = 3

        for i in range(total_tests):
            try:
                start_time = time.time()

                response = requests.post(
                    f"{self.ollama_url}/api/generate",
                    json={
                        "model": model_name,
                        "prompt": prompt,
                        "stream": False,
                        **optimization,
                    },
                    timeout=60,
                )

                response_time = time.time() - start_time

                if response.status_code == 200:
                    response_data = response.json()
                    response_times.append(response_time)
                    success_count += 1

                    # Estimate token count (rough approximation)
                    token_count = len(response_data.get("response", "").split()) * 1.3

                    if i == 0:  # Store first response for analysis
                        result = {
                            "response_time": response_time,
                            "token_count": int(token_count),
                            "response_length": len(response_data.get("response", "")),
                            "success": True,
                            "response_preview": response_data.get("response", "")[:200]
                            + "...",
                        }
                else:
                    if i == 0:
                        result = {
                            "response_time": response_time,
                            "success": False,
                            "error": f"HTTP {response.status_code}",
                        }

            except Exception as e:
                if i == 0:
                    result = {"response_time": None, "success": False, "error": str(e)}

        # Calculate averages
        if response_times:
            result["avg_response_time"] = statistics.mean(response_times)
            result["min_response_time"] = min(response_times)
            result["max_response_time"] = max(response_times)
            result["success_rate"] = success_count / total_tests

        return result

    def _calculate_summary(self, tests: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate summary statistics for a model."""
        successful_tests = [
            test for test in tests.values() if test.get("success", False)
        ]

        if not successful_tests:
            return {
                "overall_success_rate": 0.0,
                "avg_response_time": None,
                "total_tests": len(tests),
            }

        response_times = [test.get("avg_response_time", 0) for test in successful_tests]
        token_counts = [test.get("token_count", 0) for test in successful_tests]

        return {
            "overall_success_rate": len(successful_tests) / len(tests),
            "avg_response_time": (
                statistics.mean(response_times) if response_times else None
            ),
            "min_response_time": min(response_times) if response_times else None,
            "max_response_time": max(response_times) if response_times else None,
            "avg_tokens_per_response": (
                statistics.mean(token_counts) if token_counts else None
            ),
            "total_tests": len(tests),
            "successful_tests": len(successful_tests),
        }

    def generate_performance_report(self) -> str:
        """Generate a comprehensive performance report."""
        report = []
        report.append("# Ollama Model Performance Test Report")
        report.append(f"Generated: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # Overall summary
        report.append("## Overall Summary")
        for model_name, results in self.results.items():
            summary = results["summary"]
            report.append(f"### {model_name}")
            report.append(f"- **Success Rate**: {summary['overall_success_rate']:.1%}")
            if summary.get("avg_response_time"):
                report.append(
                    f"- **Avg Response Time**: {summary['avg_response_time']:.2f}s"
                )
                report.append(
                    f"- **Response Time Range**: {summary['min_response_time']:.2f}s - {summary['max_response_time']:.2f}s"
                )
            if summary.get("avg_tokens_per_response"):
                report.append(
                    f"- **Avg Tokens per Response**: {summary['avg_tokens_per_response']:.0f}"
                )
            report.append(
                f"- **Tests**: {summary['successful_tests']}/{summary['total_tests']} successful"
            )
            report.append("")

        # Detailed results
        report.append("## Detailed Results")
        for model_name, results in self.results.items():
            report.append(f"### {model_name}")

            for use_case, test_result in results["tests"].items():
                report.append(f"#### {use_case}")
                if test_result.get("success"):
                    report.append(
                        f"- **Response Time**: {test_result.get('avg_response_time', 'N/A'):.2f}s"
                    )
                    report.append(
                        f"- **Tokens**: {test_result.get('token_count', 'N/A')}"
                    )
                    report.append(
                        f"- **Response Length**: {test_result.get('response_length', 'N/A')} chars"
                    )
                    report.append(
                        f"- **Success Rate**: {test_result.get('success_rate', 'N/A'):.1%}"
                    )
                    report.append(
                        f"- **Preview**: {test_result.get('response_preview', 'N/A')}"
                    )
                else:
                    report.append(f"- **Status**: Failed")
                    report.append(
                        f"- **Error**: {test_result.get('error', 'Unknown error')}"
                    )
                report.append("")

        return "\n".join(report)

    def print_summary(self):
        """Print a summary of test results."""
        print("\n📊 Performance Test Summary")
        print("=" * 60)

        for model_name, results in self.results.items():
            summary = results["summary"]
            print(f"\n🤖 {model_name}")
            print(f"   Success Rate: {summary['overall_success_rate']:.1%}")
            if summary.get("avg_response_time"):
                print(f"   Avg Response Time: {summary['avg_response_time']:.2f}s")
            print(
                f"   Tests: {summary['successful_tests']}/{summary['total_tests']} successful"
            )


async def main():
    """Main testing function."""
    tester = ModelPerformanceTester()

    # Run performance tests
    results = await tester.test_all_models()

    # Print summary
    tester.print_summary()

    # Generate and save report
    report = tester.generate_performance_report()
    report_path = Path("docs/ollama_performance_report.md")
    report_path.parent.mkdir(exist_ok=True)

    with open(report_path, "w") as f:
        f.write(report)

    print(f"\n📊 Performance report saved to: {report_path}")


if __name__ == "__main__":
    asyncio.run(main())
