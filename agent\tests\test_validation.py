#!/usr/bin/env python3
"""
Tests for core validation module
"""

import json
import os
import tempfile
from datetime import datetime
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, Mock, patch

import pytest

from agent.core.validation import (
    ValidationError,
    ValidationLevel,
    ValidationResult,
    validate_api_endpoint,
    validate_config,
    validate_database_connection,
    validate_directory,
    validate_environment_variables,
    validate_file_path,
    validate_json_schema,
    validate_performance_config,
    validate_security_config,
)


class TestValidationError:
    """Test ValidationError class"""

    def test_validation_error_creation(self):
        """Test creating a ValidationError"""
        error = ValidationError("Test error message", ValidationLevel.ERROR)
        assert error.message == "Test error message"
        assert error.level == ValidationLevel.ERROR

    def test_validation_error_str_representation(self):
        """Test string representation of ValidationError"""
        error = ValidationError("Test error", ValidationLevel.ERROR)
        assert str(error) == "Test error"


class TestValidationResult:
    """Test ValidationResult class"""

    def test_validation_result_creation(self):
        """Test creating a ValidationResult"""
        result = ValidationResult(
            success=True, level=ValidationLevel.INFO, message="Test message"
        )
        assert result.success is True
        assert result.level == ValidationLevel.INFO
        assert result.message == "Test message"

    def test_validation_result_with_details(self):
        """Test creating a ValidationResult with details"""
        details = {"field": "value"}
        result = ValidationResult(
            success=False,
            level=ValidationLevel.ERROR,
            message="Error message",
            details=details,
        )
        assert result.details == details


class TestValidationLevel:
    """Test ValidationLevel enum"""

    def test_validation_levels(self):
        """Test that all validation levels are defined"""
        assert ValidationLevel.DEBUG.value == "DEBUG"
        assert ValidationLevel.INFO.value == "INFO"
        assert ValidationLevel.WARNING.value == "WARNING"
        assert ValidationLevel.ERROR.value == "ERROR"
        assert ValidationLevel.CRITICAL.value == "CRITICAL"


class TestConfigValidation:
    """Test config validation functions"""

    def test_validate_config_success(self):
        """Test successful config validation"""
        config = {
            "database": {"host": "localhost", "port": 5432, "name": "testdb"},
            "api": {"host": "0.0.0.0", "port": 8000},
        }
        result = validate_config(config)
        assert result.success is True

    def test_validate_config_missing_required_fields(self):
        """Test config validation with missing required fields"""
        config = {
            "database": {
                "host": "localhost"
                # Missing port and name
            }
        }
        result = validate_config(config)
        assert result.success is False
        assert "missing required fields" in result.message.lower()

    def test_validate_config_invalid_values(self):
        """Test config validation with invalid values"""
        config = {
            "database": {
                "host": "localhost",
                "port": "invalid_port",  # Should be int
                "name": "testdb",
            }
        }
        result = validate_config(config)
        assert result.success is False


class TestFilePathValidation:
    """Test file path validation functions"""

    def test_validate_file_path_success(self):
        """Test successful file path validation"""
        with tempfile.NamedTemporaryFile() as temp_file:
            result = validate_file_path(temp_file.name)
            assert result.success is True

    def test_validate_file_path_nonexistent(self):
        """Test file path validation with non-existent file"""
        result = validate_file_path("/nonexistent/file.txt")
        assert result.success is False
        assert "does not exist" in result.message.lower()

    def test_validate_file_path_directory(self):
        """Test file path validation with directory"""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = validate_file_path(temp_dir)
            assert result.success is False
            assert "is a directory" in result.message.lower()


class TestDirectoryValidation:
    """Test directory validation functions"""

    def test_validate_directory_success(self):
        """Test successful directory validation"""
        with tempfile.TemporaryDirectory() as temp_dir:
            result = validate_directory(temp_dir)
            assert result.success is True

    def test_validate_directory_nonexistent(self):
        """Test directory validation with non-existent directory"""
        result = validate_directory("/nonexistent/directory")
        assert result.success is False
        assert "does not exist" in result.message.lower()

    def test_validate_directory_not_directory(self):
        """Test directory validation with file"""
        with tempfile.NamedTemporaryFile() as temp_file:
            result = validate_directory(temp_file.name)
            assert result.success is False
            assert "is not a directory" in result.message.lower()


class TestJsonSchemaValidation:
    """Test JSON schema validation functions"""

    def test_validate_json_schema_success(self):
        """Test successful JSON schema validation"""
        schema = {
            "type": "object",
            "properties": {"name": {"type": "string"}, "age": {"type": "integer"}},
            "required": ["name", "age"],
        }
        data = {"name": "John Doe", "age": 30}
        result = validate_json_schema(data, schema)
        assert result.success is True

    def test_validate_json_schema_invalid_data(self):
        """Test JSON schema validation with invalid data"""
        schema = {
            "type": "object",
            "properties": {"name": {"type": "string"}, "age": {"type": "integer"}},
            "required": ["name", "age"],
        }
        data = {"name": "John Doe", "age": "invalid_age"}  # Should be integer
        result = validate_json_schema(data, schema)
        assert result.success is False

    def test_validate_json_schema_missing_required(self):
        """Test JSON schema validation with missing required fields"""
        schema = {
            "type": "object",
            "properties": {"name": {"type": "string"}, "age": {"type": "integer"}},
            "required": ["name", "age"],
        }
        data = {
            "name": "John Doe"
            # Missing age
        }
        result = validate_json_schema(data, schema)
        assert result.success is False


class TestEnvironmentVariablesValidation:
    """Test environment variables validation functions"""

    @patch.dict(os.environ, {"TEST_VAR": "test_value"})
    def test_validate_environment_variables_success(self):
        """Test successful environment variables validation"""
        required_vars = ["TEST_VAR"]
        result = validate_environment_variables(required_vars)
        assert result.success is True

    @patch.dict(os.environ, {})
    def test_validate_environment_variables_missing(self):
        """Test environment variables validation with missing variables"""
        required_vars = ["MISSING_VAR"]
        result = validate_environment_variables(required_vars)
        assert result.success is False
        assert "missing required environment variables" in result.message.lower()

    @patch.dict(os.environ, {"TEST_VAR": ""})
    def test_validate_environment_variables_empty(self):
        """Test environment variables validation with empty variables"""
        required_vars = ["TEST_VAR"]
        result = validate_environment_variables(required_vars)
        assert result.success is False
        assert "empty environment variables" in result.message.lower()


class TestDatabaseConnectionValidation:
    """Test database connection validation functions"""

    @patch("core.validation.create_engine")
    def test_validate_database_connection_success(self, mock_create_engine):
        """Test successful database connection validation"""
        mock_engine = Mock()
        mock_engine.connect.return_value.__enter__.return_value = Mock()
        mock_create_engine.return_value = mock_engine

        config = {
            "host": "localhost",
            "port": 5432,
            "database": "testdb",
            "username": "testuser",
            "password": "testpass",
        }
        result = validate_database_connection(config)
        assert result.success is True

    @patch("core.validation.create_engine")
    def test_validate_database_connection_failure(self, mock_create_engine):
        """Test database connection validation failure"""
        mock_create_engine.side_effect = Exception("Connection failed")

        config = {
            "host": "localhost",
            "port": 5432,
            "database": "testdb",
            "username": "testuser",
            "password": "testpass",
        }
        result = validate_database_connection(config)
        assert result.success is False
        assert "connection failed" in result.message.lower()


class TestApiEndpointValidation:
    """Test API endpoint validation functions"""

    @patch("requests.get")
    def test_validate_api_endpoint_success(self, mock_get):
        """Test successful API endpoint validation"""
        mock_response = Mock()
        mock_response.status_code = 200
        mock_get.return_value = mock_response

        result = validate_api_endpoint("http://localhost:8000/health")
        assert result.success is True

    @patch("requests.get")
    def test_validate_api_endpoint_failure(self, mock_get):
        """Test API endpoint validation failure"""
        mock_get.side_effect = Exception("Connection failed")

        result = validate_api_endpoint("http://localhost:8000/health")
        assert result.success is False
        assert "connection failed" in result.message.lower()

    @patch("requests.get")
    def test_validate_api_endpoint_invalid_status(self, mock_get):
        """Test API endpoint validation with invalid status code"""
        mock_response = Mock()
        mock_response.status_code = 500
        mock_get.return_value = mock_response

        result = validate_api_endpoint("http://localhost:8000/health")
        assert result.success is False
        assert "invalid status code" in result.message.lower()


class TestSecurityConfigValidation:
    """Test security config validation functions"""

    def test_validate_security_config_success(self):
        """Test successful security config validation"""
        config = {
            "ssl_enabled": True,
            "ssl_cert_path": "/path/to/cert.pem",
            "ssl_key_path": "/path/to/key.pem",
            "allowed_hosts": ["localhost", "127.0.0.1"],
            "csrf_enabled": True,
            "session_timeout": 3600,
        }
        result = validate_security_config(config)
        assert result.success is True

    def test_validate_security_config_missing_ssl_files(self):
        """Test security config validation with missing SSL files"""
        config = {
            "ssl_enabled": True,
            "ssl_cert_path": "/nonexistent/cert.pem",
            "ssl_key_path": "/nonexistent/key.pem",
        }
        result = validate_security_config(config)
        assert result.success is False
        assert "ssl certificate" in result.message.lower()

    def test_validate_security_config_invalid_timeout(self):
        """Test security config validation with invalid timeout"""
        config = {"session_timeout": -1}  # Invalid negative timeout
        result = validate_security_config(config)
        assert result.success is False
        assert "invalid session timeout" in result.message.lower()


class TestPerformanceConfigValidation:
    """Test performance config validation functions"""

    def test_validate_performance_config_success(self):
        """Test successful performance config validation"""
        config = {
            "max_connections": 100,
            "connection_timeout": 30,
            "request_timeout": 60,
            "max_workers": 4,
            "memory_limit": "1GB",
        }
        result = validate_performance_config(config)
        assert result.success is True

    def test_validate_performance_config_invalid_connections(self):
        """Test performance config validation with invalid connections"""
        config = {"max_connections": -1}  # Invalid negative value
        result = validate_performance_config(config)
        assert result.success is False
        assert "invalid max_connections" in result.message.lower()

    def test_validate_performance_config_invalid_timeout(self):
        """Test performance config validation with invalid timeout"""
        config = {"connection_timeout": 0}  # Invalid zero timeout
        result = validate_performance_config(config)
        assert result.success is False
        assert "invalid connection_timeout" in result.message.lower()


if __name__ == "__main__":
    pytest.main([__file__])
