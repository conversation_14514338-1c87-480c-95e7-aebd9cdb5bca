import React, { useState, useEffect, useCallback, memo } from 'react';
import { Editor } from '@monaco-editor/react';
import { api } from '@/lib/api';

interface FileEditorProps {
  siteName: string;
  filePath: string;
  onSave?: (content: string) => void;
  onClose?: () => void;
}

interface FileContent {
  content: string;
  language: string;
  size: number;
  lastModified: string;
}

const FileEditor: React.FC<FileEditorProps> = memo(({
  siteName,
  filePath,
  onSave,
  onClose
}) => {
  const [fileContent, setFileContent] = useState<FileContent | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasChanges, setHasChanges] = useState(false);

  // Determine language based on file extension
  const getLanguage = (filename: string): string => {
    const ext = filename.split('.').pop()?.toLowerCase();
    const languageMap: { [key: string]: string } = {
      'html': 'html',
      'htm': 'html',
      'css': 'css',
      'js': 'javascript',
      'jsx': 'javascript',
      'ts': 'typescript',
      'tsx': 'typescript',
      'json': 'json',
      'py': 'python',
      'md': 'markdown',
      'txt': 'plaintext',
      'xml': 'xml',
      'yaml': 'yaml',
      'yml': 'yaml',
      'toml': 'toml',
      'ini': 'ini',
      'sh': 'shell',
      'bat': 'batch',
      'ps1': 'powershell'
    };
    return languageMap[ext || ''] || 'plaintext';
  };

  // Load file content
  useEffect(() => {
    const loadFile = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const data = await api.getSiteFile(siteName, filePath);

        if (data.status === 'success') {
          setFileContent({
            content: data.content,
            language: getLanguage(filePath),
            size: data.size || 0,
            lastModified: data.lastModified || new Date().toISOString()
          });
        } else {
          throw new Error(data.message || 'Failed to load file');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    };

    if (siteName && filePath) {
      loadFile();
    }
  }, [siteName, filePath]);

  // Save file content
  const handleSave = useCallback(async () => {
    if (!fileContent || !hasChanges) return;

    try {
      setIsSaving(true);
      setError(null);

      const data = await api.updateSiteFile(siteName, filePath, fileContent.content);

      if (data.status === 'success') {
        setHasChanges(false);
        onSave?.(fileContent.content);
      } else {
        throw new Error(data.message || 'Failed to save file');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsSaving(false);
    }
  }, [fileContent, hasChanges, siteName, filePath, onSave, setHasChanges, setError, setIsSaving]);

  // Handle editor content change
  const handleEditorChange = useCallback((value: string | undefined) => {
    if (fileContent && value !== undefined) {
      setFileContent(prev => prev ? { ...prev, content: value } : null);
      setHasChanges(true);
    }
  }, [fileContent, setFileContent, setHasChanges]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2">Loading file...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center">
          <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          <span className="text-red-800 font-medium">Error loading file</span>
        </div>
        <p className="text-red-600 mt-1">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
        >
          Retry
        </button>
      </div>
    );
  }

  if (!fileContent) {
    return (
      <div className="p-4 text-gray-500">
        No file content available
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full">
      {/* File Header */}
      <div className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="flex items-center">
            <svg className="w-4 h-4 text-gray-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
            </svg>
            <span className="font-mono text-sm text-gray-700">{filePath}</span>
          </div>
          <span className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded">
            {fileContent.language}
          </span>
          <span className="text-xs text-gray-500">
            {Math.round(fileContent.size / 1024)}KB
          </span>
        </div>

        <div className="flex items-center space-x-2">
          {hasChanges && (
            <span className="text-xs text-orange-600 bg-orange-100 px-2 py-1 rounded">
              Modified
            </span>
          )}

          <button
            onClick={handleSave}
            disabled={!hasChanges || isSaving}
            className={`px-3 py-1 text-sm rounded ${
              hasChanges && !isSaving
                ? 'bg-blue-500 text-white hover:bg-blue-600'
                : 'bg-gray-200 text-gray-500 cursor-not-allowed'
            }`}
          >
            {isSaving ? 'Saving...' : 'Save'}
          </button>

          {onClose && (
            <button
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-gray-600"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Editor */}
      <div className="flex-1 min-h-0">
        <Editor
          height="100%"
          language={fileContent.language}
          value={fileContent.content}
          onChange={handleEditorChange}
          theme="vs-dark"
          options={{
            minimap: { enabled: false },
            fontSize: 14,
            wordWrap: 'on',
            lineNumbers: 'on',
            scrollBeyondLastLine: false,
            automaticLayout: true,
            tabSize: 2,
            insertSpaces: true,
            detectIndentation: true,
            trimAutoWhitespace: true,
            largeFileOptimizations: true,
          }}
        />
      </div>

      {/* Status Bar */}
      <div className="flex items-center justify-between px-3 py-1 bg-gray-100 border-t border-gray-200 text-xs text-gray-600">
        <div className="flex items-center space-x-4">
          <span>Line: 1</span>
          <span>Column: 1</span>
          <span>Encoding: UTF-8</span>
        </div>
        <div className="flex items-center space-x-2">
          <span>Last modified: {new Date(fileContent.lastModified).toLocaleString()}</span>
        </div>
      </div>
    </div>
  );
});

export default FileEditor;
