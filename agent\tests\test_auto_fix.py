#!/usr/bin/env python3
"""
Test script for the automatic action system
"""

import sys
from pathlib import Path

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent))

from agent.scripts.cursor_rules_monitor import CursorRulesMonitor


def test_auto_fix_system():
    """Test the automatic action system"""
    print("🧪 Testing automatic action system...")

    # Create monitor instance
    monitor = CursorRulesMonitor(check_interval=5, strict_mode=True)

    # Test configuration loading
    print("✅ Configuration loaded successfully")
    print(
        f"   Auto-fix enabled: {monitor.auto_fix_config.get('auto_fix', {}).get('enabled', False)}"
    )

    # Test violation resolution
    test_violations = [
        "Test failures detected - fix before proceeding",
        "Found 5 incomplete TODOs",
        "Import violations detected",
        "Syntax errors found",
        "File organization violations",
        "Dependency violations detected",
        "Virtual environment not activated",
        "File cleanup needed",
        "Mock data found in production",
        "Security violations detected",
        "Git workflow violations",
        "CLI/API violations detected",
    ]

    print(f"\n🔧 Testing violation resolution for {len(test_violations)} violations...")

    for violation in test_violations:
        print(f"\n   Testing: {violation}")
        try:
            result = monitor._resolve_violation_with_config(violation)
            if result:
                print(f"   ✅ Resolved: {result}")
            else:
                print(f"   ⚠️ No automatic resolution available")
        except Exception as e:
            print(f"   ❌ Error: {e}")

    print("\n🎉 Automatic action system test completed!")


if __name__ == "__main__":
    test_auto_fix_system()
