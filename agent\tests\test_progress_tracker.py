#!/usr/bin/env python3
"""
Tests for progress_tracker module
"""

import json
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

import pytest

from agent.utils.progress_tracker import ProgressTracker, TaskStatus


class TestTaskStatus:
    """Test TaskStatus enum"""

    def test_task_status_values(self):
        """Test that all task status values are defined"""
        assert TaskStatus.PENDING.value == "pending"
        assert TaskStatus.IN_PROGRESS.value == "in_progress"
        assert TaskStatus.COMPLETED.value == "completed"
        assert TaskStatus.FAILED.value == "failed"
        assert TaskStatus.CANCELLED.value == "cancelled"
        assert TaskStatus.BLOCKED.value == "blocked"


class TestProgressTracker:
    """Test ProgressTracker class"""

    def test_progress_tracker_creation(self):
        """Test creating a ProgressTracker"""
        tracker = ProgressTracker()
        assert tracker is not None
        assert len(tracker.tasks) == 0
        assert len(tracker.dependencies) == 0
        assert len(tracker.status_history) == 0

    def test_progress_tracker_add_task(self):
        """Test adding a task to ProgressTracker"""
        tracker = ProgressTracker()
        task_info = {
            "name": "Test Task",
            "description": "Test description",
            "estimated_duration": 120,
        }
        success = tracker.add_task("task1", task_info)
        assert success is True
        assert "task1" in tracker.tasks
        assert tracker.tasks["task1"]["name"] == "Test Task"

    def test_progress_tracker_get_task_status(self):
        """Test getting task status"""
        tracker = ProgressTracker()
        task_info = {"name": "Test Task"}
        tracker.add_task("task1", task_info)
        status = tracker.get_task_status("task1")
        assert status is not None
        assert status["id"] == "task1"
        assert status["name"] == "Test Task"

    def test_progress_tracker_get_nonexistent_task_status(self):
        """Test getting status of non-existent task"""
        tracker = ProgressTracker()
        status = tracker.get_task_status("nonexistent")
        assert status is None

    def test_progress_tracker_update_task_status(self):
        """Test updating task status"""
        tracker = ProgressTracker()
        task_info = {"name": "Test Task"}
        tracker.add_task("task1", task_info)
        success = tracker.update_task_status("task1", TaskStatus.IN_PROGRESS, 50.0)
        assert success is True
        status = tracker.get_task_status("task1")
        assert status["status"] == "in_progress"  # Compare with string value
        assert status["progress"] == 50.0

    def test_progress_tracker_update_nonexistent_task_status(self):
        """Test updating status of non-existent task"""
        tracker = ProgressTracker()
        success = tracker.update_task_status("nonexistent", TaskStatus.IN_PROGRESS)
        assert success is False

    def test_progress_tracker_get_all_tasks(self):
        """Test getting all tasks"""
        tracker = ProgressTracker()
        task_info1 = {"name": "Task 1"}
        task_info2 = {"name": "Task 2"}
        tracker.add_task("task1", task_info1)
        tracker.add_task("task2", task_info2)
        tasks = tracker.get_all_tasks()
        assert isinstance(tasks, dict)
        assert "active_tasks" in tasks
        assert "completed_tasks" in tasks
        assert "total_active" in tasks
        assert "total_completed" in tasks

    def test_progress_tracker_update_sub_task_progress(self):
        """Test updating sub-task progress"""
        tracker = ProgressTracker()
        task_info = {
            "name": "Test Task",
            "sub_tasks": [
                {"id": "sub1", "name": "Sub Task 1"},
                {"id": "sub2", "name": "Sub Task 2"},
            ],
        }
        tracker.add_task("task1", task_info)
        success = tracker.update_sub_task_progress(
            "task1", "sub1", TaskStatus.IN_PROGRESS, 75.0
        )
        assert success is True

    def test_progress_tracker_check_dependencies(self):
        """Test checking task dependencies"""
        tracker = ProgressTracker()
        task_info = {"name": "Test Task", "dependencies": ["dep1", "dep2"]}
        tracker.add_task("task1", task_info)
        dependencies = tracker.check_dependencies("task1")
        assert len(dependencies) == 2
        assert "dep1" in dependencies
        assert "dep2" in dependencies

    def test_progress_tracker_can_start_task(self):
        """Test checking if task can start"""
        tracker = ProgressTracker()
        task_info = {"name": "Test Task"}
        tracker.add_task("task1", task_info)
        can_start = tracker.can_start_task("task1")
        assert can_start is True

    def test_progress_tracker_can_start_task_with_dependencies(self):
        """Test checking if task can start with dependencies"""
        tracker = ProgressTracker()
        task_info = {"name": "Test Task", "dependencies": ["dep1"]}
        tracker.add_task("task1", task_info)
        can_start = tracker.can_start_task("task1")
        assert can_start is False

    def test_progress_tracker_recalculate_progress(self):
        """Test recalculating task progress"""
        tracker = ProgressTracker()
        task_info = {"name": "Test Task"}
        tracker.add_task("task1", task_info)
        tracker._recalculate_task_progress("task1")
        # This should not raise an exception

    def test_progress_tracker_estimate_completion_time(self):
        """Test estimating completion time"""
        tracker = ProgressTracker()
        task_info = {"name": "Test Task", "estimated_duration": 60}
        tracker.add_task("task1", task_info)
        task = tracker.tasks["task1"]
        # Mock the task to have started_at and progress
        task["started_at"] = datetime.now() - timedelta(
            minutes=30
        )  # Started 30 minutes ago
        task["progress"] = 50.0  # 50% complete
        estimated_time = tracker._estimate_completion_time(task)
        assert estimated_time is not None


if __name__ == "__main__":
    pytest.main([__file__])
