import asyncio
import json
import shutil
import tempfile
from pathlib import Path
from unittest.mock import Async<PERSON><PERSON>, Mock, patch

import pytest

from agent.core.agents.collaboration_manager import (
    Collaboration,
    CollaborationManager,
    CollaborationPattern,
    CollaborationTask,
    TaskStatus,
)
from agent.core.agents.collaboration_patterns import (
    CollaborationPatternLibrary,
    PatternTemplate,
)
from agent.core.agents.shared_context import (
    ContextEvent,
    ContextEventType,
    SharedContext,
    SharedContextManager,
)


class TestCollaborationManager:
    """Test suite for CollaborationManager."""

    @pytest.fixture
    def collaboration_manager(self):
        """Create a CollaborationManager for testing."""
        return CollaborationManager(max_workers=2)

    @pytest.fixture
    def mock_agent(self):
        """Create a mock agent for testing."""
        agent = Mock()
        agent.execute_task = AsyncMock(
            return_value={
                "success": True,
                "output": {"result": "test_output"},
                "context_updates": {"test_key": "test_value"},
            }
        )
        return agent

    def test_agent_registration(self, collaboration_manager, mock_agent):
        """Test agent registration."""
        collaboration_manager.register_agent("test_agent", mock_agent)
        assert "test_agent" in collaboration_manager.agent_registry
        assert collaboration_manager.agent_registry["test_agent"] == mock_agent

    def test_collaboration_creation(self, collaboration_manager):
        """Test collaboration creation."""
        collaboration = collaboration_manager.create_collaboration(
            name="Test Collaboration",
            pattern=CollaborationPattern.SEQUENTIAL,
            max_parallel_tasks=2,
            timeout_seconds=120,
        )

        assert collaboration.name == "Test Collaboration"
        assert collaboration.pattern == CollaborationPattern.SEQUENTIAL
        assert collaboration.max_parallel_tasks == 2
        assert collaboration.timeout_seconds == 120
        assert collaboration.id in collaboration_manager.active_collaborations

    def test_task_addition(self, collaboration_manager, mock_agent):
        """Test adding tasks to collaboration."""
        collaboration_manager.register_agent("test_agent", mock_agent)
        collaboration = collaboration_manager.create_collaboration(
            name="Test Collaboration", pattern=CollaborationPattern.SEQUENTIAL
        )

        task_id = collaboration_manager.add_task_to_collaboration(
            collaboration.id,
            "test_agent",
            "test_task",
            {"input": "test_data"},
            dependencies=[],
            priority=1,
        )

        assert len(collaboration.tasks) == 1
        task = collaboration.tasks[0]
        assert task.id == task_id
        assert task.agent_id == "test_agent"
        assert task.task_type == "test_task"
        assert task.priority == 1

    @pytest.mark.asyncio
    async def test_sequential_execution(self, collaboration_manager, mock_agent):
        """Test sequential task execution."""
        collaboration_manager.register_agent("test_agent", mock_agent)
        collaboration = collaboration_manager.create_collaboration(
            name="Sequential Test", pattern=CollaborationPattern.SEQUENTIAL
        )

        # Add two sequential tasks
        task1_id = collaboration_manager.add_task_to_collaboration(
            collaboration.id, "test_agent", "task1", {"step": 1}
        )
        task2_id = collaboration_manager.add_task_to_collaboration(
            collaboration.id,
            "test_agent",
            "task2",
            {"step": 2},
            dependencies=[task1_id],
        )

        result = await collaboration_manager.execute_collaboration(collaboration.id)

        assert result["pattern"] == "sequential"
        assert len(result["task_results"]) == 2
        assert mock_agent.execute_task.call_count == 2

        # Verify collaboration is completed
        assert collaboration.id in collaboration_manager.completed_collaborations
        assert collaboration.id not in collaboration_manager.active_collaborations

    @pytest.mark.asyncio
    async def test_parallel_execution(self, collaboration_manager, mock_agent):
        """Test parallel task execution."""
        collaboration_manager.register_agent("test_agent", mock_agent)
        collaboration = collaboration_manager.create_collaboration(
            name="Parallel Test",
            pattern=CollaborationPattern.PARALLEL,
            max_parallel_tasks=2,
        )

        # Add two parallel tasks (no dependencies)
        collaboration_manager.add_task_to_collaboration(
            collaboration.id, "test_agent", "task1", {"step": 1}
        )
        collaboration_manager.add_task_to_collaboration(
            collaboration.id, "test_agent", "task2", {"step": 2}
        )

        result = await collaboration_manager.execute_collaboration(collaboration.id)

        assert result["pattern"] == "parallel"
        assert len(result["task_results"]) == 2
        assert mock_agent.execute_task.call_count == 2

    def test_performance_metrics(self, collaboration_manager):
        """Test performance metrics collection."""
        # Add some mock history
        collaboration_manager.collaboration_history = [
            {"success": True, "duration": 10.0, "pattern": "sequential"},
            {"success": True, "duration": 15.0, "pattern": "parallel"},
            {"success": False, "duration": 5.0, "pattern": "sequential"},
        ]

        metrics = collaboration_manager.get_performance_metrics()

        assert metrics["total_collaborations"] == 3
        assert metrics["successful_collaborations"] == 2
        assert metrics["success_rate"] == 2 / 3
        assert metrics["average_duration"] == 10.0


class TestSharedContext:
    """Test suite for SharedContext."""

    @pytest.fixture
    def shared_context(self):
        """Create a SharedContext for testing."""
        return SharedContext("test_context")

    def test_basic_operations(self, shared_context):
        """Test basic set/get operations."""
        shared_context.set("key1", "value1", "agent1")
        assert shared_context.get("key1") == "value1"
        assert shared_context.has_key("key1")
        assert "key1" in shared_context.keys()

    def test_metadata_operations(self, shared_context):
        """Test operations with metadata."""
        metadata = {"source": "test", "priority": 1}
        shared_context.set("key1", "value1", "agent1", metadata)

        result = shared_context.get_with_metadata("key1")
        assert result["value"] == "value1"
        assert result["metadata"] == metadata
        assert result["exists"] is True

    def test_locking_mechanism(self, shared_context):
        """Test context locking mechanism."""
        # Set initial value
        shared_context.set("key1", "value1", "agent1")

        # Lock the key
        assert shared_context.lock("key1", "agent1", 1.0)
        assert shared_context.is_locked("key1", "agent2")
        assert not shared_context.is_locked("key1", "agent1")  # Owner can access

        # Try to modify from different agent (should fail)
        with pytest.raises(ValueError):
            shared_context.set("key1", "value2", "agent2")

        # Unlock and verify
        assert shared_context.unlock("key1", "agent1")
        assert not shared_context.is_locked("key1")

        # Now modification should work
        shared_context.set("key1", "value2", "agent2")
        assert shared_context.get("key1") == "value2"

    def test_event_system(self, shared_context):
        """Test event subscription and notifications."""
        events = []

        def event_handler(event: ContextEvent):
            events.append(event)

        shared_context.subscribe("key1", event_handler)
        shared_context.set("key1", "value1", "agent1")
        shared_context.set("key1", "value2", "agent1")
        shared_context.delete("key1", "agent1")

        assert len(events) == 3
        assert events[0].event_type == ContextEventType.CREATED
        assert events[1].event_type == ContextEventType.UPDATED
        assert events[2].event_type == ContextEventType.DELETED

    def test_atomic_updates(self, shared_context):
        """Test atomic multi-key updates."""
        updates = {"key1": "value1", "key2": "value2", "key3": "value3"}
        shared_context.update(updates, "agent1")

        assert shared_context.get("key1") == "value1"
        assert shared_context.get("key2") == "value2"
        assert shared_context.get("key3") == "value3"


class TestCollaborationPatternLibrary:
    """Test suite for CollaborationPatternLibrary."""

    @pytest.fixture
    def pattern_library(self):
        """Create a CollaborationPatternLibrary for testing."""
        return CollaborationPatternLibrary()

    def test_default_patterns_loaded(self, pattern_library):
        """Test that default patterns are loaded."""
        patterns = pattern_library.list_patterns()
        assert len(patterns) > 0
        assert "website_creation" in patterns
        assert "code_review" in patterns
        assert "bug_fix" in patterns

    def test_pattern_retrieval(self, pattern_library):
        """Test pattern retrieval."""
        pattern = pattern_library.get_pattern("website_creation")
        assert pattern is not None
        assert pattern.name == "Website Creation"
        assert pattern.pattern_type == CollaborationPattern.SEQUENTIAL
        assert "frontend" in pattern.required_agents
        assert "backend" in pattern.required_agents

    def test_pattern_recommendations(self, pattern_library):
        """Test pattern recommendations."""
        available_agents = ["frontend", "backend", "container", "security"]

        recommendations = pattern_library.get_pattern_recommendations(
            "Create a new website with user authentication", available_agents
        )

        assert len(recommendations) > 0
        # Should recommend patterns that include frontend, backend, security
        top_recommendation = recommendations[0]
        assert top_recommendation["score"] > 10

    def test_agent_compatibility(self, pattern_library):
        """Test agent compatibility checking."""
        available_agents = ["frontend", "backend"]
        compatible_patterns = pattern_library.get_patterns_for_agents(available_agents)

        # Should only return patterns that can run with available agents
        for pattern_name in compatible_patterns:
            pattern = pattern_library.get_pattern(pattern_name)
            assert all(agent in available_agents for agent in pattern.required_agents)

    def test_collaboration_creation_from_pattern(self, pattern_library):
        """Test creating collaboration from pattern."""
        task_data = {"description": "Create a blog website"}
        collaboration = pattern_library.create_collaboration_from_pattern(
            "website_creation", "Test Blog", task_data
        )

        assert collaboration is not None
        assert collaboration.name == "Test Blog"
        assert collaboration.pattern == CollaborationPattern.SEQUENTIAL
        assert len(collaboration.tasks) > 0

        # Verify task dependencies are correctly set
        task_ids = {task.id for task in collaboration.tasks}
        for task in collaboration.tasks:
            for dep in task.dependencies:
                assert dep in task_ids

    def test_custom_pattern_addition(self, pattern_library):
        """Test adding custom patterns."""
        custom_pattern = PatternTemplate(
            name="Custom Test Pattern",
            pattern_type=CollaborationPattern.PARALLEL,
            description="A custom test pattern",
            required_agents=["frontend"],
            task_templates=[
                {
                    "task_type": "test_task",
                    "agent_id": "frontend",
                    "priority": 1,
                    "dependencies": [],
                }
            ],
        )

        pattern_library.add_custom_pattern(custom_pattern)

        patterns = pattern_library.list_patterns()
        assert "custom_test_pattern" in patterns

        retrieved_pattern = pattern_library.get_pattern("custom_test_pattern")
        assert retrieved_pattern.name == "Custom Test Pattern"

    def test_pattern_validation(self, pattern_library):
        """Test pattern validation."""
        # Valid pattern
        valid_pattern = PatternTemplate(
            name="Valid Pattern",
            pattern_type=CollaborationPattern.SEQUENTIAL,
            description="A valid pattern",
            required_agents=["frontend"],
            task_templates=[
                {"task_type": "task1", "agent_id": "frontend", "dependencies": []}
            ],
        )

        issues = pattern_library.validate_pattern(valid_pattern)
        assert len(issues) == 0

        # Invalid pattern with circular dependencies
        invalid_pattern = PatternTemplate(
            name="Invalid Pattern",
            pattern_type=CollaborationPattern.SEQUENTIAL,
            description="An invalid pattern",
            required_agents=["frontend"],
            task_templates=[
                {
                    "task_type": "task1",
                    "agent_id": "frontend",
                    "dependencies": ["task2"],
                },
                {
                    "task_type": "task2",
                    "agent_id": "frontend",
                    "dependencies": ["task1"],
                },
            ],
        )

        issues = pattern_library.validate_pattern(invalid_pattern)
        assert len(issues) > 0
        assert any("circular" in issue.lower() for issue in issues)


class TestIntegration:
    """Integration tests for the complete collaboration system."""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing."""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.mark.asyncio
    async def test_end_to_end_collaboration(self, temp_dir):
        """Test end-to-end collaboration workflow."""
        # Setup
        collaboration_manager = CollaborationManager(max_workers=2)
        pattern_library = CollaborationPatternLibrary()

        # Create mock agents
        frontend_agent = Mock()
        frontend_agent.execute_task = AsyncMock(
            return_value={
                "success": True,
                "output": {"frontend_result": "completed"},
                "context_updates": {"frontend_done": True},
            }
        )

        backend_agent = Mock()
        backend_agent.execute_task = AsyncMock(
            return_value={
                "success": True,
                "output": {"backend_result": "completed"},
                "context_updates": {"backend_done": True},
            }
        )

        # Register agents
        collaboration_manager.register_agent("frontend", frontend_agent)
        collaboration_manager.register_agent("backend", backend_agent)

        # Create collaboration from pattern
        task_data = {"description": "Build a simple website"}
        collaboration = pattern_library.create_collaboration_from_pattern(
            "quick_fix",  # Use simpler pattern for testing
            "Test Website",
            task_data,
            agent_mapping={
                "learning": "frontend"
            },  # Map learning to frontend for testing
        )

        assert collaboration is not None

        # Execute collaboration
        result = await collaboration_manager.execute_collaboration(collaboration.id)

        assert result is not None
        assert "task_results" in result

        # Verify agents were called
        assert frontend_agent.execute_task.called

        # Check metrics
        metrics = collaboration_manager.get_performance_metrics()
        assert metrics["total_collaborations"] == 1
        assert metrics["successful_collaborations"] == 1


def run_collaboration_tests():
    """Run all collaboration framework tests."""
    print("Running collaboration framework tests...")

    # Run tests using pytest
    test_files = ["tests/test_collaboration_framework.py"]

    import subprocess
    import sys

    try:
        result = subprocess.run(
            [sys.executable, "-m", "pytest", "-v", "--tb=short"] + test_files,
            capture_output=True,
            text=True,
        )

        print("Test Output:")
        print(result.stdout)
        if result.stderr:
            print("Test Errors:")
            print(result.stderr)

        return result.returncode == 0
    except Exception as e:
        print(f"Error running tests: {e}")
        return False


if __name__ == "__main__":
    # Run the tests
    success = run_collaboration_tests()
    exit(0 if success else 1)
