import React, { useEffect, useState } from 'react';
import { api } from '@/lib/api';

interface Site {
  name: string;
  manifest: {
    framework_info: { framework: string; confidence: number };
    security_report: { status: string };
    status: string;
    uploaded_at: string;
    file_count: number;
    total_size_mb: number;
  };
}

interface SiteListProps {
  onSiteSelect?: (siteName: string) => void;
  onSiteValidate?: (siteName: string) => void;
  onSitePreview?: (siteName: string) => void;
}

export const SiteList: React.FC<SiteListProps> = ({
  onSiteSelect,
  onSiteValidate,
  onSitePreview
}) => {
  const [sites, setSites] = useState<Site[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState('all'); // all, safe, warning, needs_review

  useEffect(() => {
    fetchSites();
  }, []);

  const fetchSites = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await api.getLegacySites();

      if (data.status === 'success') {
        setSites(data.uploaded_sites || []);
      } else {
        setError(data.message || 'Failed to fetch sites');
      }
    } catch (error) {
      setError('Network error while fetching sites');
      console.error('Failed to fetch sites:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleValidateSite = async (siteName: string) => {
    try {
      const result = await api.validateSite(siteName);

      if (result.status === 'success') {
        // Refresh the site list
        fetchSites();
        onSiteValidate?.(siteName);
      } else {
        console.error('Validation failed:', result.message);
      }
    } catch (error) {
      console.error('Validation error:', error);
    }
  };

  const getSecurityBadge = (status: string) => {
    switch (status) {
      case 'safe': return <span className="badge safe">✅ Safe</span>;
      case 'warning': return <span className="badge warning">⚠️ Review</span>;
      case 'needs_review': return <span className="badge danger">❌ Review Required</span>;
      default: return <span className="badge unknown">❓ Unknown</span>;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'imported': return <span className="status-badge imported">📦 Imported</span>;
      case 'validated': return <span className="status-badge validated">✅ Validated</span>;
      case 'pending_validation': return <span className="status-badge pending">⏳ Pending</span>;
      default: return <span className="status-badge unknown">❓ {status}</span>;
    }
  };

  const filteredSites = sites.filter(site => {
    if (filter === 'all') return true;
    return site.manifest.security_report.status === filter;
  });

  if (loading) {
    return (
      <div className="site-list-container">
        <div className="loading">Loading sites...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="site-list-container">
        <div className="error">
          <p>❌ {error}</p>
          <button onClick={fetchSites} className="retry-button">
            🔄 Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="site-list-container">
      <div className="site-list-header">
        <h3>Imported Sites ({filteredSites.length})</h3>
        <div className="site-list-controls">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="filter-select"
          >
            <option value="all">All Sites</option>
            <option value="safe">Safe Only</option>
            <option value="warning">Warnings</option>
            <option value="needs_review">Review Required</option>
          </select>
          <button onClick={fetchSites} className="refresh-button">
            🔄 Refresh
          </button>
        </div>
      </div>

      {filteredSites.length === 0 ? (
        <div className="empty-state">
          <p>No sites found. Import your first web project to get started!</p>
        </div>
      ) : (
        <div className="site-grid">
          {filteredSites.map(site => (
            <div key={site.name} className="site-card">
              <div className="site-header">
                <h4 className="site-name">{site.name}</h4>
                <div className="site-badges">
                  {getSecurityBadge(site.manifest.security_report.status)}
                  {getStatusBadge(site.manifest.status)}
                </div>
              </div>

              <div className="site-details">
                <div className="detail-row">
                  <span className="detail-label">Framework:</span>
                  <span className="detail-value">
                    {site.manifest.framework_info.framework}
                    <span className="confidence">
                      ({Math.round(site.manifest.framework_info.confidence * 100)}%)
                    </span>
                  </span>
                </div>

                <div className="detail-row">
                  <span className="detail-label">Files:</span>
                  <span className="detail-value">{site.manifest.file_count}</span>
                </div>

                <div className="detail-row">
                  <span className="detail-label">Size:</span>
                  <span className="detail-value">
                    {site.manifest.total_size_mb.toFixed(2)} MB
                  </span>
                </div>

                <div className="detail-row">
                  <span className="detail-label">Imported:</span>
                  <span className="detail-value">
                    {new Date(site.manifest.uploaded_at).toLocaleDateString()}
                  </span>
                </div>
              </div>

              <div className="site-actions">
                {site.manifest.status === 'imported' && (
                  <button
                    onClick={() => handleValidateSite(site.name)}
                    className="action-button validate"
                  >
                    ✅ Validate
                  </button>
                )}

                <button
                  onClick={() => onSitePreview?.(site.name)}
                  className="action-button preview"
                >
                  🔍 Preview
                </button>

                <button
                  onClick={() => onSiteSelect?.(site.name)}
                  className="action-button open"
                >
                  📝 Open in Editor
                </button>
              </div>
            </div>
          ))}
        </div>
      )}

      <style jsx>{`
        .site-list-container {
          width: 100%;
          max-width: 1200px;
          margin: 0 auto;
        }

        .site-list-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 24px;
        }

        .site-list-header h3 {
          margin: 0;
          color: #333;
          font-size: 24px;
        }

        .site-list-controls {
          display: flex;
          gap: 12px;
          align-items: center;
        }

        .filter-select {
          padding: 8px 12px;
          border: 1px solid #ddd;
          border-radius: 6px;
          background: white;
        }

        .refresh-button {
          padding: 8px 12px;
          background: #007bff;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
        }

        .refresh-button:hover {
          background: #0056b3;
        }

        .loading, .error, .empty-state {
          text-align: center;
          padding: 40px;
          color: #666;
        }

        .error {
          color: #dc3545;
        }

        .retry-button {
          margin-top: 12px;
          padding: 8px 16px;
          background: #007bff;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
        }

        .site-grid {
          display: grid;
          grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
          gap: 20px;
        }

        .site-card {
          background: white;
          border-radius: 8px;
          padding: 20px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        .site-card:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .site-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: 16px;
        }

        .site-name {
          margin: 0;
          font-size: 18px;
          color: #333;
          word-break: break-word;
        }

        .site-badges {
          display: flex;
          flex-direction: column;
          gap: 4px;
          align-items: flex-end;
        }

        .badge, .status-badge {
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }

        .badge.safe {
          background: #d4edda;
          color: #155724;
        }

        .badge.warning {
          background: #fff3cd;
          color: #856404;
        }

        .badge.danger {
          background: #f8d7da;
          color: #721c24;
        }

        .badge.unknown {
          background: #e2e3e5;
          color: #383d41;
        }

        .status-badge.imported {
          background: #cce5ff;
          color: #004085;
        }

        .status-badge.validated {
          background: #d4edda;
          color: #155724;
        }

        .status-badge.pending {
          background: #fff3cd;
          color: #856404;
        }

        .site-details {
          margin-bottom: 16px;
        }

        .detail-row {
          display: flex;
          justify-content: space-between;
          margin-bottom: 8px;
        }

        .detail-label {
          font-weight: 500;
          color: #666;
        }

        .detail-value {
          color: #333;
        }

        .confidence {
          font-size: 12px;
          color: #666;
          margin-left: 4px;
        }

        .site-actions {
          display: flex;
          gap: 8px;
          flex-wrap: wrap;
        }

        .action-button {
          flex: 1;
          min-width: 80px;
          padding: 8px 12px;
          border: none;
          border-radius: 6px;
          font-size: 12px;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.2s ease;
        }

        .action-button.validate {
          background: #28a745;
          color: white;
        }

        .action-button.validate:hover {
          background: #218838;
        }

        .action-button.preview {
          background: #17a2b8;
          color: white;
        }

        .action-button.preview:hover {
          background: #138496;
        }

        .action-button.open {
          background: #007bff;
          color: white;
        }

        .action-button.open:hover {
          background: #0056b3;
        }
      `}</style>
    </div>
  );
};
