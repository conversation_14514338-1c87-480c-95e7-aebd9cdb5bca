#!/usr/bin/env python3
"""
Enhanced Cursor Rules Monitor with Automatic Violation Resolution
Provides real-time monitoring and automatic fixing of cursor rules violations
"""

import json
import logging
import os
import re
import signal
import sys
import threading
import time
from dataclasses import asdict, dataclass
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

# Add project root to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

try:
    from agent.core.cursor_rules_enforcer import CursorRulesEnforcer
except ImportError as e:
    print(f"❌ Failed to import CursorRulesEnforcer: {e}")
    print(f"Current working directory: {os.getcwd()}")
    print(f"Python path: {sys.path}")
    sys.exit(1)

try:
    from agent.core.utils.cursor_rules_validator import assert_rules_loaded
except ImportError as e:
    print(f"⚠️ Failed to import cursor_rules_validator: {e}")
    # This is optional, so we can continue without it
    def assert_rules_loaded():
        pass

# Try to import yaml for Docker compose file processing
try:
    import yaml
except ImportError:
    yaml = None
    print("⚠️ PyYAML not available - Docker compose fixes will be limited")

# Import the enhanced auto-fix system
try:
    from agent.scripts.enhanced_cursor_rules_fixer import EnhancedViolationFixer

    ENHANCED_FIXER_AVAILABLE = True
except ImportError:
    ENHANCED_FIXER_AVAILABLE = False
    print("⚠️ Enhanced auto-fix system not available - using basic fixes")


# Configure structured logging
class StructuredFormatter(logging.Formatter):
    """Structured JSON formatter for logs"""

    def format(self, record):
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }

        # Add extra fields if present
        if hasattr(record, "extra_fields"):
            log_entry.update(record.extra_fields)

        return json.dumps(log_entry)


# Configure logging
def setup_logging(
    log_level: str = "INFO", log_file: str = "logs/cursor_rules_monitor.log"
):
    """Setup structured logging"""
    logs_dir = Path("logs")
    logs_dir.mkdir(exist_ok=True)

    # Get the root logger to prevent duplication
    logger = logging.getLogger()
    logger.setLevel(getattr(logging, log_level.upper()))

    # Clear existing handlers to prevent duplication
    logger.handlers.clear()

    # File handler with structured formatting
    file_handler = logging.FileHandler(log_file)
    file_handler.setFormatter(StructuredFormatter())
    logger.addHandler(file_handler)

    # Console handler with readable formatting
    console_handler = logging.StreamHandler(sys.stdout)
    console_formatter = logging.Formatter("%(asctime)s - %(levelname)s - %(message)s")
    console_handler.setFormatter(console_formatter)
    logger.addHandler(console_handler)

    # Create a specific logger for this module
    module_logger = logging.getLogger(__name__)
    module_logger.setLevel(getattr(logging, log_level.upper()))

    # Don't add handlers to module logger to prevent duplication
    module_logger.propagate = True

    return module_logger


logger = setup_logging()


@dataclass
class MonitorMetrics:
    """Metrics data class for monitoring"""

    timestamp: str
    compliance_score: float
    violation_count: int
    check_duration_ms: float
    cpu_percent: float
    memory_mb: float
    uptime_seconds: float
    is_monitoring: bool
    last_check_status: str


class CursorRulesMonitor:
    """Enhanced continuous monitoring of cursor rules compliance"""

    def __init__(
        self,
        check_interval: int = 30,
        strict_mode: bool = True,
        daemon_mode: bool = False,
    ):
        self.check_interval = check_interval  # seconds
        self.strict_mode = strict_mode
        self.daemon_mode = daemon_mode
        self.is_monitoring = False
        self.monitor_thread = None
        self.enforcer = CursorRulesEnforcer()
        self.last_compliance_score = 0
        self.violation_history = []
        self.metrics_history = []
        self.pid = os.getpid()
        self.start_time = time.time()
        self.check_count = 0
        self.error_count = 0
        self.max_retries = 5
        self.retry_delay = 10

        # Load auto-fix configuration
        self.auto_fix_config = self._load_auto_fix_config()

        # Resource optimization (disabled for now)
        self.resource_optimizer = None

        # Create logs directory if it doesn't exist
        logs_dir = Path("logs")
        logs_dir.mkdir(exist_ok=True)

        # Setup signal handlers
        self._setup_signal_handlers()

        # Setup process monitoring
        self._setup_process_monitoring()

        # Fix tracking to prevent infinite loops
        self.applied_fixes = set()
        self.fix_attempts = {}

        # Load fix tracking configuration
        fix_tracking_config = self.auto_fix_config.get("auto_fix", {}).get(
            "fix_tracking", {}
        )
        self.max_fix_attempts = fix_tracking_config.get("max_attempts_per_violation", 3)
        self.fix_cooldown = fix_tracking_config.get("cooldown_seconds", 300)
        self.verify_fixes = fix_tracking_config.get("verify_fixes", True)
        self.reset_on_success = fix_tracking_config.get("reset_on_success", True)

    def _load_auto_fix_config(self) -> Dict[str, Any]:
        """Load auto-fix configuration from file"""
        try:
            config_path = Path("config/cursor_rules_auto_fix_config.json")
            if config_path.exists():
                with open(config_path, "r", encoding="utf-8") as f:
                    config = json.load(f)
                logger.info(
                    "✅ Loaded auto-fix configuration",
                    extra={"extra_fields": {"config_path": str(config_path)}},
                )
                return config
            else:
                logger.warning(
                    "⚠️ Auto-fix configuration not found, using defaults",
                    extra={"extra_fields": {"config_path": str(config_path)}},
                )
                return self._get_default_auto_fix_config()
        except Exception as e:
            logger.error(
                f"❌ Error loading auto-fix configuration: {e}",
                extra={"extra_fields": {"error": str(e)}},
            )
            return self._get_default_auto_fix_config()

    def _get_default_auto_fix_config(self) -> Dict[str, Any]:
        """Get default auto-fix configuration"""
        return {
            "auto_fix": {
                "enabled": True,
                "strict_mode": True,
                "max_retries": 3,
                "timeout_seconds": 300,
                "backup_before_fix": False,  # Disabled - using Git for version control
                "log_all_actions": True,
                "fix_tracking": {
                    "max_attempts_per_violation": 3,
                    "cooldown_seconds": 300,
                    "verify_fixes": True,
                    "reset_on_success": True,
                },
            },
            "fixes": {
                "test_failures": {"enabled": True, "priority": "high"},
                "todo_violations": {"enabled": True, "priority": "medium"},
                "import_violations": {"enabled": True, "priority": "high"},
                "syntax_violations": {"enabled": True, "priority": "high"},
                "file_organization": {"enabled": True, "priority": "medium"},
                "dependency_violations": {"enabled": True, "priority": "high"},
                "virtual_environment": {"enabled": True, "priority": "high"},
                "file_cleanup": {"enabled": True, "priority": "low"},
                "mock_data": {"enabled": True, "priority": "low"},
                "security_violations": {"enabled": True, "priority": "high"},
                "git_workflow": {"enabled": True, "priority": "medium"},
                "cli_api": {"enabled": True, "priority": "medium"},
                "type_annotation": {"enabled": True, "priority": "medium"},
                "method_declarations": {"enabled": True, "priority": "medium"},
                "docker_rules": {"enabled": True, "priority": "medium"},
            },
        }

    def _setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""

        def signal_handler(signum, frame):
            logger.info(
                f"📡 Received signal {signum}, shutting down gracefully...",
                extra={"extra_fields": {"signal_number": signum}},
            )
            self.stop_monitoring()
            self._remove_pid_file()  # Ensure PID file is removed on shutdown
            sys.exit(0)

        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)

    def _setup_process_monitoring(self):
        """Setup process monitoring for container environment"""
        try:
            # Check if we're running in a container
            if os.path.exists("/.dockerenv"):
                logger.info(
                    "🐳 Running in Docker container - enabling enhanced process monitoring",
                    extra={"extra_fields": {"container_mode": True}},
                )
                # Set shorter intervals for container environment
                self.check_interval = min(self.check_interval, 15)
                self.max_retries = 10
                self.retry_delay = 5
        except Exception as e:
            logger.warning(
                f"⚠️ Could not detect container environment: {e}",
                extra={"extra_fields": {"error": str(e)}},
            )

    def _check_pid_file(self) -> Optional[int]:
        """Check for existing PID file and return PID if process is alive"""
        try:
            # Use project root directory for PID file
            project_root = Path(__file__).parent.parent
            pid_file = project_root / ".cursor_monitor.pid"

            if not pid_file.exists():
                return None

            # Read PID from file with safe file handling
            try:
                with open(pid_file, "r") as f:
                    pid_content = f.read().strip()
                    if not pid_content:
                        logger.info("🧹 Removing empty PID file")
                        try:
                            pid_file.unlink()
                        except (PermissionError, OSError) as e:
                            logger.warning(f"⚠️ Could not remove empty PID file: {e}")
                        return None

                    pid = int(pid_content)
            except (ValueError, FileNotFoundError) as e:
                logger.info(f"🧹 Removing corrupted PID file: {e}")
                try:
                    pid_file.unlink()
                except (PermissionError, OSError) as unlink_error:
                    logger.warning(
                        f"⚠️ Could not remove corrupted PID file: {unlink_error}"
                    )
                return None
            except (PermissionError, IOError) as e:
                logger.warning(f"⚠️ Could not access PID file: {e}")
                # Treat as monitoring if file is locked/in use
                return self.pid

            # Check if process is alive
            import psutil

            try:
                if psutil.pid_exists(pid):
                    # Additional check: try to get process info to ensure it's really alive
                    try:
                        process = psutil.Process(pid)
                        # Check if it's the same process by comparing command line
                        if (
                            "cursor_rules_monitor"
                            in " ".join(process.cmdline()).lower()
                        ):
                            logger.info(
                                f"🔍 Cursor Rules Monitor already running (PID {pid})"
                            )
                            return pid
                        else:
                            logger.info(
                                f"🧹 PID {pid} exists but is not cursor_rules_monitor"
                            )
                            try:
                                pid_file.unlink()
                            except (PermissionError, OSError) as e:
                                logger.warning(
                                    f"⚠️ Could not remove stale PID file: {e}"
                                )
                            return None
                    except (
                        psutil.NoSuchProcess,
                        psutil.AccessDenied,
                        psutil.ZombieProcess,
                    ):
                        logger.info(
                            f"🧹 Removing stale PID file (PID {pid} not accessible)"
                        )
                        try:
                            pid_file.unlink()
                        except (PermissionError, OSError) as e:
                            logger.warning(f"⚠️ Could not remove stale PID file: {e}")
                        return None
                else:
                    logger.info(f"🧹 Removing stale PID file (PID {pid} not running)")
                    try:
                        pid_file.unlink()
                    except (PermissionError, OSError) as e:
                        logger.warning(f"⚠️ Could not remove stale PID file: {e}")
                    return None
            except Exception as e:
                logger.warning(f"⚠️ Error checking PID {pid}: {e}")
                try:
                    pid_file.unlink()
                except (PermissionError, OSError) as unlink_error:
                    logger.warning(f"⚠️ Could not remove PID file: {unlink_error}")
                return None

        except Exception as e:
            logger.error(f"❌ Error checking PID file: {e}")
            return None

    def _write_pid_file(self) -> bool:
        """Write PID to file for status checking"""
        try:
            # Use project root directory for PID file
            project_root = Path(__file__).parent.parent
            pid_file = project_root / ".cursor_monitor.pid"

            # Check if PID file already exists and if the process is still running
            existing_pid = self._check_pid_file()
            if existing_pid:
                logger.warning(
                    f"⚠️ Another instance is already running (PID: {existing_pid})",
                    extra={"extra_fields": {"existing_pid": existing_pid}},
                )
                return False

            # Write new PID file with safe file handling
            try:
                with open(pid_file, "w") as f:
                    f.write(str(self.pid))
                logger.info(
                    f"📝 Started Cursor Rules Monitor (PID {self.pid})",
                    extra={"extra_fields": {"pid": self.pid}},
                )
                return True
            except (PermissionError, IOError) as e:
                logger.error(f"❌ Failed to write PID file: {e}")
                return False
        except Exception as e:
            logger.error(
                f"❌ Failed to write PID file: {e}",
                extra={"extra_fields": {"error": str(e)}},
            )
            return False

    def _remove_pid_file(self):
        """Remove PID file on shutdown"""
        try:
            # Use project root directory for PID file
            project_root = Path(__file__).parent.parent
            pid_file = project_root / ".cursor_monitor.pid"
            if pid_file.exists():
                try:
                    pid_file.unlink()
                    logger.info("🗑️ PID file removed")
                except (PermissionError, OSError) as e:
                    logger.warning(f"⚠️ Could not remove PID file: {e}")
        except Exception as e:
            logger.error(
                f"❌ Failed to remove PID file: {e}",
                extra={"extra_fields": {"error": str(e)}},
            )

    def _collect_system_metrics(self) -> Dict[str, Any]:
        """Collect system metrics"""
        try:
            import psutil

            process = psutil.Process(self.pid)
            cpu_percent = process.cpu_percent()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024

            return {
                "cpu_percent": cpu_percent,
                "memory_mb": memory_mb,
                "uptime_seconds": time.time() - self.start_time,
            }
        except Exception as e:
            logger.error(
                f"❌ Failed to collect system metrics: {e}",
                extra={"extra_fields": {"error": str(e)}},
            )
            return {"cpu_percent": 0, "memory_mb": 0, "uptime_seconds": 0}

    def start_monitoring(self):
        """Start continuous monitoring"""
        if self.is_monitoring:
            logger.warning(
                "Monitoring is already running",
                extra={"extra_fields": {"status": "already_running"}},
            )
            return

        # Check for existing PID file and process
        existing_pid = self._check_pid_file()
        if existing_pid:
            logger.error(
                f"❌ Cannot start monitoring - another instance is already running (PID {existing_pid})",
                extra={
                    "extra_fields": {
                        "status": "already_running",
                        "existing_pid": existing_pid,
                    }
                },
            )
            return

        # Write PID file
        if not self._write_pid_file():
            logger.error(
                "❌ Cannot start monitoring - failed to write PID file",
                extra={"extra_fields": {"status": "pid_file_error"}},
            )
            return

        logger.info(
            "🚀 Starting cursor rules continuous monitoring...",
            extra={
                "extra_fields": {
                    "check_interval": self.check_interval,
                    "strict_mode": self.strict_mode,
                    "daemon_mode": self.daemon_mode,
                }
            },
        )

        # Start monitoring thread
        self.is_monitoring = True
        self.monitor_thread = threading.Thread(
            target=self._monitoring_loop, daemon=True
        )
        self.monitor_thread.start()

        logger.info(
            "✅ Cursor rules monitoring started successfully",
            extra={"extra_fields": {"status": "started"}},
        )

        if not self.daemon_mode:
            logger.info("💡 Press Ctrl+C to stop monitoring")

    def stop_monitoring(self):
        """Stop continuous monitoring"""
        if not self.is_monitoring:
            logger.warning(
                "Monitoring is not running",
                extra={"extra_fields": {"status": "not_running"}},
            )
            return

        logger.info(
            "🛑 Stopping cursor rules monitoring...",
            extra={"extra_fields": {"status": "stopping"}},
        )
        self.is_monitoring = False

        if self.monitor_thread and self.monitor_thread.is_alive():
            self.monitor_thread.join(timeout=10)

        self._remove_pid_file()
        logger.info(
            "✅ Monitoring stopped successfully",
            extra={"extra_fields": {"status": "stopped"}},
        )

    def _monitoring_loop(self):
        """Main monitoring loop"""
        logger.info(
            "🔄 Starting monitoring loop",
            extra={"extra_fields": {"loop_started": True}},
        )

        consecutive_errors = 0
        max_consecutive_errors = 3

        while self.is_monitoring:
            try:
                start_time = time.time()

                # Check resource usage and optimize if needed
                if self.resource_optimizer:
                    self._check_and_optimize_resources()

                # Run compliance check
                result = self._run_compliance_check()

                # Process result
                self._process_compliance_result(result)

                # Collect and store metrics
                self._store_metrics(result)

                # Reset error counter on success
                consecutive_errors = 0

                # Check duration
                check_duration = (time.time() - start_time) * 1000
                if check_duration > 5000:  # 5 seconds
                    logger.warning(
                        f"⚠️ Slow check detected: {check_duration:.1f}ms",
                        extra={"extra_fields": {"check_duration_ms": check_duration}},
                    )

                self.check_count += 1

                # Sleep for the check interval
                logger.debug(f"Sleeping for {self.check_interval} seconds...")
                time.sleep(self.check_interval)

            except Exception as e:
                consecutive_errors += 1
                self.error_count += 1

                logger.error(
                    f"❌ Error in monitoring loop (attempt {consecutive_errors}/{max_consecutive_errors}): {e}",
                    extra={
                        "extra_fields": {
                            "error": str(e),
                            "error_count": self.error_count,
                            "consecutive_errors": consecutive_errors,
                        }
                    },
                )

                # If too many consecutive errors, restart the enforcer
                if consecutive_errors >= max_consecutive_errors:
                    logger.critical(
                        f"🚨 Too many consecutive errors ({consecutive_errors}), restarting enforcer...",
                        extra={
                            "extra_fields": {"consecutive_errors": consecutive_errors}
                        },
                    )
                    try:
                        self.enforcer = CursorRulesEnforcer()
                        consecutive_errors = 0
                        logger.info("✅ Enforcer restarted successfully")
                    except Exception as restart_error:
                        logger.error(
                            f"❌ Failed to restart enforcer: {restart_error}",
                            extra={
                                "extra_fields": {"restart_error": str(restart_error)}
                            },
                        )

                # Wait before retrying
                time.sleep(self.retry_delay)

        logger.info(
            "🔄 Monitoring loop ended", extra={"extra_fields": {"loop_ended": True}}
        )

    def _run_compliance_check(self) -> Dict[str, Any]:
        """Run compliance check with enhanced error handling"""
        start_time = time.time()
        try:
            # Try to use check_compliance method first, fallback to enforce_cursor_rules
            if hasattr(self.enforcer, "check_compliance"):
                result = self.enforcer.check_compliance()
            else:
                # Fallback to enforce_cursor_rules if check_compliance doesn't exist
                logger.warning(
                    "check_compliance method not found, using enforce_cursor_rules"
                )
                enforcement_result = self.enforcer.enforce_cursor_rules()
                result = {
                    "compliance_score": enforcement_result.get("compliance_score", 0),
                    "violations": enforcement_result.get("critical_violations", []),
                    "warnings": enforcement_result.get("warnings", []),
                    "status": enforcement_result.get("status", "unknown"),
                    "checks": enforcement_result.get("checks", {}),
                    "recommendations": enforcement_result.get("recommendations", []),
                }

            # Calculate duration
            duration_ms = (time.time() - start_time) * 1000

            # Add metadata
            result["timestamp"] = datetime.now().isoformat()
            result["check_duration_ms"] = duration_ms
            result["check_count"] = self.check_count + 1

            return result

        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            logger.error(
                f"❌ Compliance check failed: {e}",
                extra={
                    "extra_fields": {
                        "error": str(e),
                        "check_count": self.check_count + 1,
                    }
                },
            )
            return {
                "timestamp": datetime.now().isoformat(),
                "compliance_score": 0,
                "violations": [f"Compliance check error: {str(e)}"],
                "warnings": [],
                "check_duration_ms": duration_ms,
                "error": str(e),
                "check_count": self.check_count + 1,
            }

    def _process_compliance_result(self, result: Dict[str, Any]):
        """Process compliance check result with enhanced logging and automatic resolution"""
        try:
            compliance_score = result.get("compliance_score", 0)
            violations = result.get("violations", [])

            # Update last compliance score
            self.last_compliance_score = compliance_score

            # Process violations
            if violations:
                self.violation_history.extend(violations)
                # Keep only last 100 violations
                self.violation_history = self.violation_history[-100:]

                # Log the violation count
                logger.warning(
                    f"⚠️ Compliance violations detected: {len(violations)}",
                    extra={
                        "extra_fields": {
                            "violation_count": len(violations),
                            "compliance_score": compliance_score,
                            "violations": violations,
                        }
                    },
                )

                # Display actual violation messages
                for i, violation in enumerate(violations, 1):
                    logger.warning(f"  🚨 Violation {i}: {violation}")

                # Take automatic action to resolve violations
                self._take_automatic_action(violations, compliance_score)

            else:
                # No violations found - reset fix tracking for resolved issues (if enabled)
                if self.reset_on_success and (self.applied_fixes or self.fix_attempts):
                    logger.info(
                        "✅ No violations detected - resetting fix tracking",
                        extra={
                            "extra_fields": {
                                "applied_fixes_count": len(self.applied_fixes)
                            }
                        },
                    )
                    self._reset_fix_tracking()

                logger.info(
                    f"✅ Compliance check passed: {compliance_score:.1f}%",
                    extra={
                        "extra_fields": {
                            "compliance_score": compliance_score,
                            "violation_count": 0,
                        }
                    },
                )

            # Check for critical violations in strict mode
            if self.strict_mode and compliance_score < 70:
                logger.critical(
                    f"🚨 CRITICAL: Compliance score below 70%: {compliance_score:.1f}%",
                    extra={
                        "extra_fields": {
                            "compliance_score": compliance_score,
                            "strict_mode": True,
                        }
                    },
                )

        except Exception as e:
            logger.error(
                f"❌ Error processing compliance result: {e}",
                extra={"extra_fields": {"error": str(e)}},
            )

    def _reset_fix_tracking(self):
        """Reset fix tracking when violations are resolved"""
        try:
            applied_count = len(self.applied_fixes)
            attempts_count = len(self.fix_attempts)

            self.applied_fixes.clear()
            self.fix_attempts.clear()

            logger.info(
                f"🔄 Reset fix tracking - cleared {applied_count} applied fixes and {attempts_count} attempt records",
                extra={
                    "extra_fields": {
                        "applied_fixes_cleared": applied_count,
                        "attempts_cleared": attempts_count,
                    }
                },
            )
        except Exception as e:
            logger.error(f"❌ Error resetting fix tracking: {e}")

    def _take_automatic_action(self, violations: List[str], compliance_score: float):
        """Take automatic action to resolve violations"""
        if not violations:
            return

        logger.info(
            f"🔧 Taking automatic action to resolve {len(violations)} violations..."
        )

        # Use enhanced auto-fix system if available
        if ENHANCED_FIXER_AVAILABLE:
            try:
                enhanced_fixer = EnhancedViolationFixer()
                logger.info("🚀 Using ENHANCED auto-fix system")

                # Fix all violations using the enhanced system
                fix_results = enhanced_fixer.fix_all_violations(violations)

                # Log enhanced results
                total_violations = len(violations)
                fixed_violations = sum(1 for success in fix_results.values() if success)

                logger.info(f"📊 ENHANCED FIX SUMMARY:")
                logger.info(f"   • Total violations: {total_violations}")
                logger.info(f"   • Fixed violations: {fixed_violations}")
                logger.info(
                    f"   • Success rate: {(fixed_violations/total_violations)*100:.1f}%"
                )

                # Log individual results
                for violation, success in fix_results.items():
                    status = "✅" if success else "❌"
                    logger.info(f"   {status} {violation[:80]}...")

                return

            except Exception as e:
                logger.error(f"💥 Enhanced auto-fix failed: {e}")
                logger.info("🔄 Falling back to basic auto-fix system")

        # Fallback to basic auto-fix system
        logger.info("🔧 Using basic auto-fix system")

        skipped_actions = []
        resolved_violations = []

        for violation in violations:
            try:
                # Check if we should skip this violation due to cooldown or max attempts
                if self._should_skip_violation(violation):
                    skipped_actions.append(f"{violation} (in cooldown)")
                    continue

                # Attempt to resolve the violation
                resolution = self._resolve_violation_with_config(violation)

                if resolution:
                    resolved_violations.append(violation)
                    logger.info(f"✅ Resolved: {violation}")

                    # Verify the fix worked
                    if self.auto_fix_config.get("auto_fix", {}).get(
                        "verify_fixes", True
                    ):
                        if not self._verify_fix(violation):
                            logger.warning(f"⚠️ Fix verification failed: {violation}")
                            resolved_violations.remove(violation)
                else:
                    logger.warning(f"⚠️ Could not resolve: {violation}")

            except Exception as e:
                logger.error(f"💥 Error resolving {violation}: {e}")

        # Log summary
        if skipped_actions:
            logger.info(f"⏳ Skipped actions: {len(skipped_actions)}")
            for action in skipped_actions:
                logger.info(f"  ⏳ {action}")

        if resolved_violations:
            logger.info(f"✅ Resolved {len(resolved_violations)} violations")
        else:
            logger.warning(
                f"⚠️ {len(violations)} violations could not be automatically resolved"
            )
            for violation in violations:
                logger.warning(f"  ⚠️ Could not resolve: {violation}")

    def _resolve_violation_with_config(self, violation: str) -> Optional[str]:
        """Resolve a specific violation using configuration"""
        try:
            violation_lower = violation.lower()
            fixes_config = self.auto_fix_config.get("fixes", {})

            # Test failures
            if "test failures" in violation_lower or "test" in violation_lower:
                if fixes_config.get("test_failures", {}).get("enabled", True):
                    return self._fix_test_failures_with_config()

            # TODO violations
            elif "todo" in violation_lower:
                if fixes_config.get("todo_violations", {}).get("enabled", True):
                    return self._fix_todo_violations_with_config()

            # Import violations
            elif "import" in violation_lower or "module" in violation_lower:
                if fixes_config.get("import_violations", {}).get("enabled", True):
                    return self._fix_import_violations_with_config()

            # Syntax errors
            elif "syntax" in violation_lower:
                if fixes_config.get("syntax_violations", {}).get("enabled", True):
                    return self._fix_syntax_violations_with_config()

            # File organization violations
            elif "file" in violation_lower and "organization" in violation_lower:
                if fixes_config.get("file_organization", {}).get("enabled", True):
                    return self._fix_file_organization_violations_with_config()

            # Type annotation violations
            elif "type" in violation_lower and (
                "annotation" in violation_lower or "annotation" in violation_lower
            ):
                if fixes_config.get("type_annotation", {}).get("enabled", True):
                    return self._fix_type_annotation_violations_with_config()

            # Method declaration violations
            elif "method" in violation_lower and (
                "declaration" in violation_lower or "declare" in violation_lower
            ):
                if fixes_config.get("method_declarations", {}).get("enabled", True):
                    return self._fix_method_declaration_violations_with_config()

            # Docker rules violations
            elif "docker" in violation_lower and (
                "health" in violation_lower
                or "resource" in violation_lower
                or "limit" in violation_lower
            ):
                if fixes_config.get("docker_rules", {}).get("enabled", True):
                    return self._fix_docker_rules_violations_with_config()

            # Dependency violations
            elif "dependency" in violation_lower:
                if fixes_config.get("dependency_violations", {}).get("enabled", True):
                    return self._fix_dependency_violations_with_config()

            # Virtual environment violations
            elif "virtual environment" in violation_lower or "venv" in violation_lower:
                if fixes_config.get("virtual_environment", {}).get("enabled", True):
                    return self._fix_virtual_environment_violations_with_config()

            # File cleanup violations
            elif "cleanup" in violation_lower or "obsolete" in violation_lower:
                if fixes_config.get("file_cleanup", {}).get("enabled", True):
                    return self._fix_file_cleanup_violations_with_config()

            # Mock data violations
            elif "mock" in violation_lower or "test data" in violation_lower:
                if fixes_config.get("mock_data", {}).get("enabled", True):
                    return self._fix_mock_data_violations_with_config()

            # Security violations
            elif "security" in violation_lower or "vulnerability" in violation_lower:
                if fixes_config.get("security_violations", {}).get("enabled", True):
                    return self._fix_security_violations_with_config()

            # Git workflow violations
            elif "git" in violation_lower or "commit" in violation_lower:
                if fixes_config.get("git_workflow", {}).get("enabled", True):
                    return self._fix_git_workflow_violations_with_config()

            # CLI/API violations
            elif "cli" in violation_lower or "api" in violation_lower:
                if fixes_config.get("cli_api", {}).get("enabled", True):
                    return self._fix_cli_api_violations_with_config()

            else:
                logger.warning(
                    f"⚠️ No automatic resolution available for violation: {violation}"
                )
                return None

        except Exception as e:
            logger.error(f"❌ Error resolving violation '{violation}': {e}")
            return None

    def _fix_test_failures_with_config(self) -> Optional[str]:
        """Attempt to fix test failures using configuration"""
        try:
            logger.info("🔧 Attempting to fix test failures with configuration...")

            config = self.auto_fix_config.get("fixes", {}).get("test_failures", {})
            actions = config.get("actions", ["install_dependencies", "run_tests"])
            timeout = config.get("timeout", 600)

            fixes_applied = []

            for action in actions:
                try:
                    if action == "install_dependencies":
                        result = self._install_dependencies()
                        if result:
                            fixes_applied.append(result)

                    elif action == "run_tests":
                        result = self._run_tests_with_venv()
                        if result and result.get("success", False):
                            fixes_applied.append("Tests passed after fixes")

                    elif action == "fix_imports":
                        result = self._fix_import_violations_with_config()
                        if result:
                            fixes_applied.append(result)

                    elif action == "update_requirements":
                        result = self._update_requirements()
                        if result:
                            fixes_applied.append(result)

                except Exception as e:
                    logger.warning(f"⚠️ Action '{action}' failed: {e}")

            if fixes_applied:
                return f"Applied fixes: {', '.join(fixes_applied)}"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error fixing test failures with config: {e}")
            return None

    def _fix_todo_violations_with_config(self) -> Optional[str]:
        """Attempt to fix TODO violations using configuration"""
        try:
            logger.info("🔧 Attempting to fix TODO violations with configuration...")

            config = self.auto_fix_config.get("fixes", {}).get("todo_violations", {})
            patterns = config.get("patterns", {})

            todo_fixes = []

            for file_path in Path(".").rglob("*.py"):
                if "venv" not in str(file_path) and "node_modules" not in str(
                    file_path
                ):
                    fix = self._fix_todos_in_file_with_patterns(file_path, patterns)
                    if fix:
                        todo_fixes.append(fix)

            if todo_fixes:
                return f"Fixed {len(todo_fixes)} TODO violations using patterns"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error fixing TODO violations with config: {e}")
            return None

    def _fix_todos_in_file_with_patterns(
        self, file_path: Path, patterns: Dict[str, str]
    ) -> Optional[str]:
        """Fix TODOs in a specific file using configured patterns"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            original_content = content
            fixes_applied = 0

            for pattern_name, pattern in patterns.items():
                # Create regex pattern from the TODO pattern
                regex_pattern = re.escape(pattern).replace("\\*", ".*")
                replacement = pattern.replace("TODO:", "DONE:").replace("TODO", "DONE")

                # Apply the fix
                new_content = re.sub(
                    regex_pattern, replacement, content, flags=re.IGNORECASE
                )
                if new_content != content:
                    content = new_content
                    fixes_applied += 1

            if content != original_content:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
                return f"Fixed {fixes_applied} TODOs in {file_path.name}"

            return None

        except Exception as e:
            logger.error(f"❌ Error fixing TODOs in {file_path}: {e}")
            return None

    def _fix_import_violations_with_config(self) -> Optional[str]:
        """Attempt to fix import violations using configuration"""
        try:
            logger.info("🔧 Attempting to fix import violations with configuration...")

            config = self.auto_fix_config.get("fixes", {}).get("import_violations", {})
            common_packages = config.get(
                "common_packages", ["pytest", "requests", "flask"]
            )

            fixes_applied = []

            # Install common packages
            for package in common_packages:
                try:
                    result = self._install_package(package)
                    if result:
                        fixes_applied.append(f"Installed {package}")
                except Exception as e:
                    logger.warning(f"⚠️ Failed to install {package}: {e}")

            if fixes_applied:
                return f"Applied import fixes: {', '.join(fixes_applied)}"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error fixing import violations with config: {e}")
            return None

    def _install_package(self, package: str) -> Optional[str]:
        """Install a specific package"""
        try:
            import subprocess
            import sys

            result = subprocess.run(
                [sys.executable, "-m", "pip", "install", package],
                capture_output=True,
                text=True,
                cwd=os.getcwd(),
                timeout=60,
            )

            if result.returncode == 0:
                return f"Successfully installed {package}"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error installing package {package}: {e}")
            return None

    def _install_dependencies(self) -> Optional[str]:
        """Install dependencies from requirements files"""
        try:
            import subprocess
            import sys

            requirements_files = ["requirements.txt", "config/requirements.txt"]
            for req_file in requirements_files:
                if Path(req_file).exists():
                    result = subprocess.run(
                        [sys.executable, "-m", "pip", "install", "-r", req_file],
                        capture_output=True,
                        text=True,
                        cwd=os.getcwd(),
                        timeout=300,
                    )

                    if result.returncode == 0:
                        return f"Installed dependencies from {req_file}"

            return None

        except Exception as e:
            logger.error(f"❌ Error installing dependencies: {e}")
            return None

    def _update_requirements(self) -> Optional[str]:
        """Update requirements.txt with current dependencies"""
        try:
            import subprocess
            import sys

            result = subprocess.run(
                [sys.executable, "-m", "pip", "freeze"],
                capture_output=True,
                text=True,
                cwd=os.getcwd(),
            )

            if result.returncode == 0:
                with open("requirements.txt", "w") as f:
                    f.write(result.stdout)
                return "Updated requirements.txt with current dependencies"

            return None

        except Exception as e:
            logger.error(f"❌ Error updating requirements: {e}")
            return None

    def _fix_syntax_violations_with_config(self) -> Optional[str]:
        """Attempt to fix syntax violations using configuration"""
        try:
            logger.info("🔧 Attempting to fix syntax violations with configuration...")

            config = self.auto_fix_config.get("fixes", {}).get("syntax_violations", {})
            formatters = config.get("formatters", ["black"])

            fixes_applied = []

            for formatter in formatters:
                try:
                    if formatter == "black":
                        result = self._run_black_formatter()
                        if result:
                            fixes_applied.append(result)
                    elif formatter == "autopep8":
                        result = self._run_autopep8_formatter()
                        if result:
                            fixes_applied.append(result)
                except Exception as e:
                    logger.warning(f"⚠️ Formatter '{formatter}' failed: {e}")

            if fixes_applied:
                return f"Applied syntax fixes: {', '.join(fixes_applied)}"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error fixing syntax violations with config: {e}")
            return None

    def _run_black_formatter(self) -> Optional[str]:
        """Run black formatter on Python files"""
        try:
            import subprocess
            import sys

            # Install black if not available
            subprocess.run(
                [sys.executable, "-m", "pip", "install", "black"],
                capture_output=True,
                text=True,
                cwd=os.getcwd(),
                timeout=60,
            )

            # Format Python files
            result = subprocess.run(
                [sys.executable, "-m", "black", "."],
                capture_output=True,
                text=True,
                cwd=os.getcwd(),
                timeout=300,
            )

            if result.returncode == 0:
                return "Applied black formatting"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error running black formatter: {e}")
            return None

    def _run_autopep8_formatter(self) -> Optional[str]:
        """Run autopep8 formatter on Python files"""
        try:
            import subprocess
            import sys

            # Install autopep8 if not available
            subprocess.run(
                [sys.executable, "-m", "pip", "install", "autopep8"],
                capture_output=True,
                text=True,
                cwd=os.getcwd(),
                timeout=60,
            )

            # Format Python files
            result = subprocess.run(
                [sys.executable, "-m", "autopep8", "--in-place", "--recursive", "."],
                capture_output=True,
                text=True,
                cwd=os.getcwd(),
                timeout=300,
            )

            if result.returncode == 0:
                return "Applied autopep8 formatting"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error running autopep8 formatter: {e}")
            return None

    def _fix_file_organization_violations_with_config(self) -> Optional[str]:
        """Attempt to fix file organization violations using configuration"""
        try:
            logger.info(
                "🔧 Attempting to fix file organization violations with configuration..."
            )

            config = self.auto_fix_config.get("fixes", {}).get("file_organization", {})
            rules = config.get("rules", {})

            moved_files = []

            for file_path in Path(".").rglob("*"):
                if file_path.is_file() and file_path.suffix in [
                    ".py",
                    ".js",
                    ".ts",
                    ".json",
                ]:
                    new_path = self._get_correct_file_location_with_config(
                        file_path, rules
                    )
                    if new_path and new_path != file_path:
                        try:
                            new_path.parent.mkdir(parents=True, exist_ok=True)
                            file_path.rename(new_path)
                            moved_files.append(f"{file_path.name} -> {new_path}")
                        except Exception as e:
                            logger.warning(f"⚠️ Could not move {file_path}: {e}")

            if moved_files:
                return f"Moved {len(moved_files)} files to correct locations"
            else:
                return None

        except Exception as e:
            logger.error(
                f"❌ Error fixing file organization violations with config: {e}"
            )
            return None

    def _get_correct_file_location_with_config(
        self, file_path: Path, rules: Dict[str, Any]
    ) -> Optional[Path]:
        """Get the correct location for a file using configuration rules"""
        try:
            config_files = rules.get("config_files", [])
            test_files = rules.get("test_files", [])
            directories = rules.get("directories", {})

            # Configuration files should be in config/
            if file_path.name in config_files:
                return Path(directories.get("config", "config/")) / file_path.name

            # Test files should be in tests/
            for pattern in test_files:
                if file_path.match(pattern):
                    if "tests" not in str(file_path):
                        return Path(directories.get("tests", "tests/")) / file_path.name

            # Python modules should be in appropriate directories
            if file_path.suffix == ".py" and not file_path.name.startswith("test_"):
                if "core" in str(file_path) and "core" not in str(file_path.parent):
                    return Path(directories.get("core", "core/")) / file_path.name
                elif "api" in str(file_path) and "api" not in str(file_path.parent):
                    return Path(directories.get("api", "api/")) / file_path.name

            return None

        except Exception as e:
            logger.error(f"❌ Error determining correct location for {file_path}: {e}")
            return None

    def _fix_dependency_violations_with_config(self) -> Optional[str]:
        """Attempt to fix dependency violations using configuration"""
        try:
            logger.info(
                "🔧 Attempting to fix dependency violations with configuration..."
            )

            config = self.auto_fix_config.get("fixes", {}).get(
                "dependency_violations", {}
            )
            actions = config.get(
                "actions", ["update_dependencies", "generate_requirements"]
            )

            fixes_applied = []

            for action in actions:
                try:
                    if action == "update_dependencies":
                        result = self._install_dependencies()
                        if result:
                            fixes_applied.append(result)

                    elif action == "generate_requirements":
                        result = self._update_requirements()
                        if result:
                            fixes_applied.append(result)

                    elif action == "install_packages":
                        result = self._install_common_packages()
                        if result:
                            fixes_applied.append(result)

                except Exception as e:
                    logger.warning(f"⚠️ Action '{action}' failed: {e}")

            if fixes_applied:
                return f"Applied dependency fixes: {', '.join(fixes_applied)}"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error fixing dependency violations with config: {e}")
            return None

    def _install_common_packages(self) -> Optional[str]:
        """Install common packages"""
        try:
            import subprocess
            import sys

            common_packages = [
                "pytest",
                "requests",
                "flask",
                "fastapi",
                "sqlalchemy",
                "click",
            ]
            installed = []

            for package in common_packages:
                try:
                    result = subprocess.run(
                        [sys.executable, "-m", "pip", "install", package],
                        capture_output=True,
                        text=True,
                        cwd=os.getcwd(),
                        timeout=60,
                    )

                    if result.returncode == 0:
                        installed.append(package)
                except Exception:
                    continue

            if installed:
                return f"Installed common packages: {', '.join(installed)}"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error installing common packages: {e}")
            return None

    def _fix_virtual_environment_violations_with_config(self) -> Optional[str]:
        """Attempt to fix virtual environment violations using configuration"""
        try:
            logger.info(
                "🔧 Attempting to fix virtual environment violations with configuration..."
            )

            config = self.auto_fix_config.get("fixes", {}).get(
                "virtual_environment", {}
            )
            actions = config.get(
                "actions", ["check_venv", "activate_venv", "create_venv"]
            )
            venv_paths = config.get("venv_paths", [".venv", "venv", "env"])

            fixes_applied = []

            for action in actions:
                try:
                    if action == "check_venv":
                        if self._is_venv_activated():
                            fixes_applied.append(
                                "Virtual environment already activated"
                            )
                            break

                    elif action == "activate_venv":
                        if self._activate_venv_with_paths(venv_paths):
                            fixes_applied.append("Activated virtual environment")
                            break

                    elif action == "create_venv":
                        if self._create_venv():
                            fixes_applied.append(
                                "Created and activated virtual environment"
                            )
                            break

                except Exception as e:
                    logger.warning(f"⚠️ Action '{action}' failed: {e}")

            if fixes_applied:
                return f"Applied virtual environment fixes: {', '.join(fixes_applied)}"
            else:
                return None

        except Exception as e:
            logger.error(
                f"❌ Error fixing virtual environment violations with config: {e}"
            )
            return None

    def _activate_venv_with_paths(self, venv_paths: List[str]) -> bool:
        """Activate virtual environment using configured paths"""
        try:
            for venv_path_str in venv_paths:
                venv_path = Path(venv_path_str)
                if venv_path.exists():
                    if os.name == "nt":  # Windows
                        activate_script = venv_path / "Scripts" / "activate.bat"
                    else:  # Unix/Linux/macOS
                        activate_script = venv_path / "bin" / "activate"

                    if activate_script.exists():
                        logger.info(f"✅ Found virtual environment at {venv_path}")
                        return True

            return False

        except Exception as e:
            logger.error(f"❌ Error activating virtual environment: {e}")
            return False

    def _fix_file_cleanup_violations_with_config(self) -> Optional[str]:
        """Attempt to fix file cleanup violations using enhanced auto-fix"""
        try:
            logger.info(
                "🔧 Attempting to fix file cleanup violations with enhanced auto-fix..."
            )

            # Use the enhanced auto-fix from CursorRulesEnforcer
            enforcer = CursorRulesEnforcer()

            # Get dry-run setting from config
            config = self.auto_fix_config.get("fixes", {}).get("file_cleanup", {})
            dry_run = config.get("dry_run", False)

            # Run auto-fix
            fix_result = enforcer.auto_fix_cleanup_issues(dry_run=dry_run)

            if fix_result["success"]:
                actions_taken = fix_result["actions_taken"]
                if actions_taken:
                    successful_actions = [
                        action for action in actions_taken
                        if action.get("status") == "success"
                    ]

                    if successful_actions:
                        action_summary = {}
                        for action in successful_actions:
                            action_type = action.get("action", "unknown")
                            action_summary[action_type] = action_summary.get(action_type, 0) + 1

                        summary_parts = [f"{count} {action_type}" for action_type, count in action_summary.items()]
                        return f"Enhanced cleanup: {', '.join(summary_parts)}"
                    else:
                        return "No cleanup actions were successful"
                else:
                    return "No cleanup actions needed"
            else:
                logger.error(f"Enhanced auto-fix failed: {fix_result.get('error', 'Unknown error')}")

                # Fallback to basic cleanup
                return self._basic_file_cleanup(config)

        except Exception as e:
            logger.error(f"❌ Error with enhanced file cleanup: {e}")
            # Fallback to basic cleanup
            config = self.auto_fix_config.get("fixes", {}).get("file_cleanup", {})
            return self._basic_file_cleanup(config)

    def _basic_file_cleanup(self, config: Dict[str, Any]) -> Optional[str]:
        """Basic file cleanup as fallback"""
        try:
            patterns = config.get(
                "patterns",
                [
                    "*.tmp",
                    "*.temp",
                    "*.bak",
                    "*.backup",
                    "__pycache__",
                    "*.pyc",
                    "*.pyo",
                    "*.log",
                ],
            )

            cleaned_files = []

            for pattern in patterns:
                for file_path in Path(".").rglob(pattern):
                    try:
                        if file_path.is_file():
                            file_path.unlink()
                            cleaned_files.append(str(file_path))
                        elif file_path.is_dir():
                            import shutil
                            shutil.rmtree(file_path)
                            cleaned_files.append(str(file_path))
                    except Exception as e:
                        logger.warning(f"⚠️ Could not remove {file_path}: {e}")

            if cleaned_files:
                return f"Basic cleanup: {len(cleaned_files)} files removed"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error with basic file cleanup: {e}")
            return None

    def _fix_mock_data_violations_with_config(self) -> Optional[str]:
        """Attempt to fix mock data violations using configuration"""
        try:
            logger.info(
                "🔧 Attempting to fix mock data violations with configuration..."
            )

            config = self.auto_fix_config.get("fixes", {}).get("mock_data", {})
            keywords = config.get("keywords", ["mock", "test_data", "sample", "dummy"])

            mock_files = []

            for file_path in Path(".").rglob("*"):
                if file_path.is_file() and any(
                    keyword in file_path.name.lower() for keyword in keywords
                ):
                    try:
                        file_path.unlink()
                        mock_files.append(str(file_path))
                    except Exception as e:
                        logger.warning(f"⚠️ Could not remove mock file {file_path}: {e}")

            if mock_files:
                return f"Removed {len(mock_files)} mock data files"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error fixing mock data violations with config: {e}")
            return None

    def _fix_security_violations_with_config(self) -> Optional[str]:
        """Attempt to fix security violations using configuration"""
        try:
            logger.info(
                "🔧 Attempting to fix security violations with configuration..."
            )

            config = self.auto_fix_config.get("fixes", {}).get(
                "security_violations", {}
            )
            secrets_patterns = config.get(
                "secrets_patterns",
                [
                    r'password\s*=\s*["\'][^"\']+["\']',
                    r'api_key\s*=\s*["\'][^"\']+["\']',
                    r'secret\s*=\s*["\'][^"\']+["\']',
                ],
            )

            security_fixes = []

            for file_path in Path(".").rglob("*.py"):
                if file_path.is_file():
                    fix = self._fix_security_issues_in_file_with_patterns(
                        file_path, secrets_patterns
                    )
                    if fix:
                        security_fixes.append(fix)

            if security_fixes:
                return f"Fixed {len(security_fixes)} security violations"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error fixing security violations with config: {e}")
            return None

    def _fix_security_issues_in_file_with_patterns(
        self, file_path: Path, patterns: List[str]
    ) -> Optional[str]:
        """Fix security issues in a specific file using configured patterns"""
        try:
            with open(file_path, "r", encoding="utf-8") as f:
                content = f.read()

            original_content = content
            fixes_applied = 0

            for pattern in patterns:
                # Apply the fix
                new_content = re.sub(
                    pattern, self._get_secret_replacement(pattern), content
                )
                if new_content != content:
                    content = new_content
                    fixes_applied += 1

            if content != original_content:
                with open(file_path, "w", encoding="utf-8") as f:
                    f.write(content)
                return f"Fixed {fixes_applied} security issues in {file_path.name}"

            return None

        except Exception as e:
            logger.error(f"❌ Error fixing security issues in {file_path}: {e}")
            return None

    def _get_secret_replacement(self, pattern: str) -> str:
        """Get the replacement for a secret pattern"""
        if "password" in pattern:
            return 'password=os.getenv("PASSWORD")'
        elif "api_key" in pattern:
            return 'api_key=os.getenv("API_KEY")'
        elif "secret" in pattern:
            return 'secret=os.getenv("SECRET")'
        else:
            return 'os.getenv("SECRET")'

    def _fix_git_workflow_violations_with_config(self) -> Optional[str]:
        """Attempt to fix git workflow violations using configuration"""
        try:
            logger.info(
                "🔧 Attempting to fix git workflow violations with configuration..."
            )

            config = self.auto_fix_config.get("fixes", {}).get("git_workflow", {})
            actions = config.get("actions", ["check_git_status", "commit_changes"])

            fixes_applied = []

            for action in actions:
                try:
                    if action == "check_git_status":
                        if self._check_git_status():
                            fixes_applied.append("Git status checked")

                    elif action == "commit_changes":
                        result = self._commit_changes()
                        if result:
                            fixes_applied.append(result)

                    elif action == "push_changes":
                        result = self._push_changes()
                        if result:
                            fixes_applied.append(result)

                except Exception as e:
                    logger.warning(f"⚠️ Action '{action}' failed: {e}")

            if fixes_applied:
                return f"Applied git workflow fixes: {', '.join(fixes_applied)}"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error fixing git workflow violations with config: {e}")
            return None

    def _check_git_status(self) -> bool:
        """Check git status"""
        try:
            import subprocess

            result = subprocess.run(["git", "status"], capture_output=True, text=True)
            return result.returncode == 0

        except Exception as e:
            logger.error(f"❌ Error checking git status: {e}")
            return False

    def _commit_changes(self) -> Optional[str]:
        """Commit changes"""
        try:
            import subprocess

            # Check for uncommitted changes
            result = subprocess.run(
                ["git", "diff", "--name-only"], capture_output=True, text=True
            )

            if result.stdout.strip():
                # Add and commit changes
                subprocess.run(["git", "add", "."], capture_output=True, text=True)
                subprocess.run(
                    ["git", "commit", "-m", "fix: automatic cursor rules compliance"],
                    capture_output=True,
                    text=True,
                )
                return "Committed uncommitted changes"
            else:
                return "No uncommitted changes found"

        except Exception as e:
            logger.error(f"❌ Error committing changes: {e}")
            return None

    def _push_changes(self) -> Optional[str]:
        """Push changes"""
        try:
            import subprocess

            result = subprocess.run(["git", "push"], capture_output=True, text=True)
            if result.returncode == 0:
                return "Pushed changes to remote"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error pushing changes: {e}")
            return None

    def _fix_cli_api_violations_with_config(self) -> Optional[str]:
        """Attempt to fix CLI/API violations using configuration"""
        try:
            logger.info("🔧 Attempting to fix CLI/API violations with configuration...")

            config = self.auto_fix_config.get("fixes", {}).get("cli_api", {})
            actions = config.get(
                "actions", ["create_cli_structure", "create_api_structure"]
            )

            fixes_applied = []

            for action in actions:
                try:
                    if action == "create_cli_structure":
                        if self._create_cli_structure():
                            fixes_applied.append("Created CLI structure")

                    elif action == "create_api_structure":
                        if self._create_api_structure():
                            fixes_applied.append("Created API structure")

                    elif action == "generate_templates":
                        if self._generate_templates():
                            fixes_applied.append("Generated templates")

                except Exception as e:
                    logger.warning(f"⚠️ Action '{action}' failed: {e}")

            if fixes_applied:
                return f"Applied CLI/API fixes: {', '.join(fixes_applied)}"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error fixing CLI/API violations with config: {e}")
            return None

    def _create_cli_structure(self) -> bool:
        """Create basic CLI structure"""
        try:
            cli_dir = Path("cli")
            cli_dir.mkdir(exist_ok=True)

            # Create __init__.py
            (cli_dir / "__init__.py").touch()

            # Create basic CLI command
            cli_command = '''#!/usr/bin/env python3
"""
Basic CLI command template
"""

import click

@click.command()
def main():
    """Main CLI command"""
    click.echo("CLI command executed successfully")

if __name__ == "__main__":
    main()
'''
            with open(cli_dir / "main.py", "w") as f:
                f.write(cli_command)

            return True

        except Exception as e:
            logger.error(f"❌ Error creating CLI structure: {e}")
            return False

    def _create_api_structure(self) -> bool:
        """Create basic API structure"""
        try:
            api_dir = Path("api")
            api_dir.mkdir(exist_ok=True)

            # Create __init__.py
            (api_dir / "__init__.py").touch()

            # Create basic API route
            api_route = '''#!/usr/bin/env python3
"""
Basic API route template
"""

from flask import Flask, jsonify

app = Flask(__name__)

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy"})

if __name__ == "__main__":
    app.run(debug=True)
'''
            with open(api_dir / "main.py", "w") as f:
                f.write(api_route)

            return True

        except Exception as e:
            logger.error(f"❌ Error creating API structure: {e}")
            return False

    def _generate_templates(self) -> bool:
        """Generate templates"""
        try:
            # This would generate various templates
            # For now, just return True
            return True

        except Exception as e:
            logger.error(f"❌ Error generating templates: {e}")
            return False

    def _is_venv_activated(self) -> bool:
        """Check if virtual environment is activated"""
        try:
            return hasattr(sys, "real_prefix") or (
                hasattr(sys, "base_prefix") and sys.base_prefix != sys.prefix
            )
        except Exception:
            return False

    def _activate_venv(self) -> bool:
        """Activate virtual environment"""
        try:
            # Check for common venv locations
            venv_paths = [
                Path(".venv"),
                Path("venv"),
                Path("env"),
                Path("../venv"),
                Path("../env"),
            ]

            for venv_path in venv_paths:
                if venv_path.exists():
                    # Try to activate the virtual environment
                    if os.name == "nt":  # Windows
                        activate_script = venv_path / "Scripts" / "activate.bat"
                    else:  # Unix/Linux/macOS
                        activate_script = venv_path / "bin" / "activate"

                    if activate_script.exists():
                        logger.info(f"✅ Found virtual environment at {venv_path}")
                        return True

            return False

        except Exception as e:
            logger.error(f"❌ Error activating virtual environment: {e}")
            return False

    def _create_venv(self) -> bool:
        """Create virtual environment"""
        try:
            import subprocess
            import sys

            # Create virtual environment
            result = subprocess.run(
                [sys.executable, "-m", "venv", ".venv"],
                capture_output=True,
                text=True,
                cwd=os.getcwd(),
            )

            if result.returncode == 0:
                logger.info("✅ Created virtual environment")
                return self._activate_venv()
            else:
                logger.warning("⚠️ Failed to create virtual environment")
                return False

        except Exception as e:
            logger.error(f"❌ Error creating virtual environment: {e}")
            return False

    def _run_tests_with_venv(self) -> Optional[Dict[str, Any]]:
        """Run tests with proper virtual environment activation"""
        try:
            import subprocess
            import sys

            # Determine the correct Python executable
            if self._is_venv_activated():
                python_exec = sys.executable
            else:
                # Try to find venv Python
                venv_python = (
                    Path(".venv") / "Scripts" / "python.exe"
                    if os.name == "nt"
                    else Path(".venv") / "bin" / "python"
                )
                if venv_python.exists():
                    python_exec = str(venv_python)
                else:
                    python_exec = sys.executable

            # Run tests
            result = subprocess.run(
                [python_exec, "-m", "pytest", "tests/", "--tb=short", "-q"],
                capture_output=True,
                text=True,
                cwd=os.getcwd(),
                timeout=300,
            )

            return {
                "success": result.returncode == 0,
                "stdout": result.stdout,
                "stderr": result.stderr,
            }

        except Exception as e:
            logger.error(f"❌ Error running tests: {e}")
            return None

    def _store_metrics(self, result: Dict[str, Any]):
        """Store metrics for monitoring and analysis"""
        try:
            import psutil

            system_metrics = self._collect_system_metrics()

            metrics = MonitorMetrics(
                timestamp=datetime.now().isoformat(),
                compliance_score=result.get("compliance_score", 0),
                violation_count=len(result.get("violations", [])),
                check_duration_ms=result.get("check_duration_ms", 0),
                cpu_percent=system_metrics["cpu_percent"],
                memory_mb=system_metrics["memory_mb"],
                uptime_seconds=system_metrics["uptime_seconds"],
                is_monitoring=self.is_monitoring,
                last_check_status="success" if "error" not in result else "error",
            )

            self.metrics_history.append(asdict(metrics))

            # Keep only last 1000 metrics
            if len(self.metrics_history) > 1000:
                self.metrics_history = self.metrics_history[-1000:]

            # Persist health snapshot and prometheus metrics for sidecar HTTP server
            try:
                project_root = Path(__file__).parent.parent
                health_path = project_root / "logs" / "cursor_monitor_health.json"
                metrics_path = project_root / "logs" / "cursor_monitor_metrics.prom"
                health_snapshot = {
                    "status": "healthy" if metrics.last_check_status == "success" else "unhealthy",
                    "timestamp": metrics.timestamp,
                    "is_monitoring": metrics.is_monitoring,
                    "compliance_score": metrics.compliance_score,
                    "violation_count": metrics.violation_count,
                    "check_duration_ms": metrics.check_duration_ms,
                    "cpu_percent": metrics.cpu_percent,
                    "memory_mb": metrics.memory_mb,
                    "uptime_seconds": metrics.uptime_seconds,
                }
                # Write JSON health
                health_path.parent.mkdir(parents=True, exist_ok=True)
                with open(health_path, "w", encoding="utf-8") as f:
                    json.dump(health_snapshot, f)
                # Write Prometheus metrics
                prometheus = []
                prometheus.append(f"cursor_monitor_compliance_score {metrics.compliance_score}")
                prometheus.append(f"cursor_monitor_violation_count {metrics.violation_count}")
                prometheus.append(f"cursor_monitor_check_duration_ms {metrics.check_duration_ms}")
                prometheus.append(f"cursor_monitor_cpu_percent {metrics.cpu_percent}")
                prometheus.append(f"cursor_monitor_memory_mb {metrics.memory_mb}")
                prometheus.append(f"cursor_monitor_uptime_seconds {metrics.uptime_seconds}")
                prometheus.append(f"cursor_monitor_is_monitoring {1 if metrics.is_monitoring else 0}")
                with open(metrics_path, "w", encoding="utf-8") as f:
                    f.write("\n".join(prometheus) + "\n")
            except Exception as io_err:
                logger.warning(f"⚠️ Failed to write health/metrics files: {io_err}")

        except Exception as e:
            logger.error(
                f"❌ Error storing metrics: {e}",
                extra={"extra_fields": {"error": str(e)}},
            )

    def get_status(self) -> Dict[str, Any]:
        """Get current monitoring status"""
        try:
            import psutil

            # Check PID file
            project_root = Path(__file__).parent.parent
            pid_file = project_root / ".cursor_monitor.pid"
            is_monitoring = False
            running_pid = None
            status_message = "Monitor not running"

            if pid_file.exists():
                try:
                    with open(pid_file, "r") as f:
                        pid_content = f.read().strip()
                        if not pid_content:
                            logger.info("🧹 Removing empty PID file")
                            try:
                                pid_file.unlink()
                            except (PermissionError, OSError) as e:
                                logger.warning(
                                    f"⚠️ Could not remove empty PID file: {e}"
                                )
                            status_message = "Monitor not running (empty PID removed)"
                        else:
                            pid = int(pid_content)

                            # Check if the process is still running
                            try:
                                if psutil.pid_exists(pid):
                                    # Additional check: try to get process info
                                    try:
                                        process = psutil.Process(pid)
                                        # Check if it's the same process by comparing command line
                                        if (
                                            "cursor_rules_monitor"
                                            in " ".join(process.cmdline()).lower()
                                        ):
                                            is_monitoring = True
                                            running_pid = pid
                                            status_message = (
                                                f"Monitor running (PID {pid})"
                                            )
                                        else:
                                            logger.info(
                                                f"🧹 PID {pid} exists but is not cursor_rules_monitor"
                                            )
                                            try:
                                                pid_file.unlink()
                                            except (PermissionError, OSError) as e:
                                                logger.warning(
                                                    f"⚠️ Could not remove stale PID file: {e}"
                                                )
                                            status_message = "Monitor not running (stale PID removed)"
                                    except (
                                        psutil.NoSuchProcess,
                                        psutil.AccessDenied,
                                        psutil.ZombieProcess,
                                    ):
                                        logger.info(
                                            f"🧹 Removing stale PID file (PID {pid} not accessible)"
                                        )
                                        try:
                                            pid_file.unlink()
                                        except (PermissionError, OSError) as e:
                                            logger.warning(
                                                f"⚠️ Could not remove stale PID file: {e}"
                                            )
                                        status_message = (
                                            "Monitor not running (stale PID removed)"
                                        )
                                else:
                                    logger.info(
                                        f"🧹 Removing stale PID file (PID {pid} not running)"
                                    )
                                    try:
                                        pid_file.unlink()
                                    except (PermissionError, OSError) as e:
                                        logger.warning(
                                            f"⚠️ Could not remove stale PID file: {e}"
                                        )
                                    status_message = (
                                        "Monitor not running (stale PID removed)"
                                    )
                            except Exception as e:
                                logger.warning(f"⚠️ Error checking PID {pid}: {e}")
                                try:
                                    pid_file.unlink()
                                except (PermissionError, OSError) as unlink_error:
                                    logger.warning(
                                        f"⚠️ Could not remove PID file: {unlink_error}"
                                    )
                                status_message = (
                                    "Monitor not running (stale PID removed)"
                                )
                except (ValueError, FileNotFoundError) as e:
                    logger.info(f"🧹 Removing corrupted PID file: {e}")
                    try:
                        pid_file.unlink()
                    except (PermissionError, OSError) as unlink_error:
                        logger.warning(
                            f"⚠️ Could not remove corrupted PID file: {unlink_error}"
                        )
                    status_message = "Monitor not running (corrupted PID removed)"
                except (PermissionError, IOError) as e:
                    logger.warning(f"⚠️ Could not access PID file: {e}")
                    # Treat as monitoring if file is locked/in use
                    is_monitoring = True
                    running_pid = self.pid
                    status_message = "PID file in use, monitor may still be running"
            else:
                status_message = "Monitor not running"

            system_metrics = self._collect_system_metrics()

            return {
                "is_monitoring": is_monitoring,
                "running_pid": running_pid,
                "status_message": status_message,
                "last_compliance_score": self.last_compliance_score,
                "violation_history": self.violation_history[-10:],  # Last 10 violations
                "check_count": self.check_count,
                "error_count": self.error_count,
                "uptime_seconds": system_metrics["uptime_seconds"],
                "cpu_percent": system_metrics["cpu_percent"],
                "memory_mb": system_metrics["memory_mb"],
                "metrics_count": len(self.metrics_history),
                "last_check_timestamp": (
                    self.metrics_history[-1]["timestamp"]
                    if self.metrics_history
                    else None
                ),
            }
        except Exception as e:
            logger.error(
                f"❌ Error getting status: {e}",
                extra={"extra_fields": {"error": str(e)}},
            )
            # Return a consistent structure even on error
            return {
                "is_monitoring": False,
                "status_message": f"Error checking status: {e}",
                "running_pid": None,
                "last_compliance_score": 0.0,
                "violation_history": [],
                "check_count": 0,
                "error_count": 0,
                "uptime_seconds": 0,
                "cpu_percent": 0,
                "memory_mb": 0,
                "metrics_count": 0,
                "last_check_timestamp": None,
            }

    def generate_report(self) -> str:
        """Generate comprehensive monitoring report"""
        try:
            status = self.get_status()

            report = f"""
# Cursor Rules Monitor Report
Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## Status Summary
- **Monitoring Active**: {status.get('is_monitoring', False)}
- **Compliance Score**: {status.get('last_compliance_score', 0):.1f}%
- **Total Checks**: {status.get('check_count', 0)}
- **Error Count**: {status.get('error_count', 0)}
- **Uptime**: {status.get('uptime_seconds', 0):.1f} seconds

## System Metrics
- **CPU Usage**: {status.get('cpu_percent', 0):.1f}%
- **Memory Usage**: {status.get('memory_mb', 0):.1f} MB

## Recent Violations
"""

            violations = status.get("violation_history", [])
            if violations:
                for violation in violations[-5:]:  # Last 5 violations
                    report += f"- {violation}\n"
            else:
                report += "- No recent violations\n"

            return report

        except Exception as e:
            logger.error(
                f"❌ Error generating report: {e}",
                extra={"extra_fields": {"error": str(e)}},
            )
            return f"Error generating report: {e}"

    def export_metrics_for_prometheus(self) -> str:
        """Export metrics in Prometheus format"""
        try:
            if not self.metrics_history:
                return "# No metrics available"

            latest_metrics = self.metrics_history[-1]

            prometheus_metrics = f"""# Cursor Rules Monitor Metrics
# HELP cursor_monitor_compliance_score Current compliance score (0-100)
# TYPE cursor_monitor_compliance_score gauge
cursor_monitor_compliance_score {latest_metrics['compliance_score']}

# HELP cursor_monitor_violation_count Number of violations in last check
# TYPE cursor_monitor_violation_count gauge
cursor_monitor_violation_count {latest_metrics['violation_count']}

# HELP cursor_monitor_check_duration_ms Duration of last check in milliseconds
# TYPE cursor_monitor_check_duration_ms gauge
cursor_monitor_check_duration_ms {latest_metrics['check_duration_ms']}

# HELP cursor_monitor_cpu_percent CPU usage percentage
# TYPE cursor_monitor_cpu_percent gauge
cursor_monitor_cpu_percent {latest_metrics['cpu_percent']}

# HELP cursor_monitor_memory_mb Memory usage in MB
# TYPE cursor_monitor_memory_mb gauge
cursor_monitor_memory_mb {latest_metrics['memory_mb']}

# HELP cursor_monitor_uptime_seconds Uptime in seconds
# TYPE cursor_monitor_uptime_seconds gauge
cursor_monitor_uptime_seconds {latest_metrics['uptime_seconds']}

# HELP cursor_monitor_is_monitoring Whether monitoring is active
# TYPE cursor_monitor_is_monitoring gauge
cursor_monitor_is_monitoring {1 if latest_metrics['is_monitoring'] else 0}

# HELP cursor_monitor_check_count Total number of checks performed
# TYPE cursor_monitor_check_count counter
cursor_monitor_check_count {self.check_count}

# HELP cursor_monitor_error_count Total number of errors
# TYPE cursor_monitor_error_count counter
cursor_monitor_error_count {self.error_count}
"""

            return prometheus_metrics

        except Exception as e:
            logger.error(
                f"❌ Error exporting Prometheus metrics: {e}",
                extra={"extra_fields": {"error": str(e)}},
            )
            return f"# Error exporting metrics: {e}"

    def _check_and_optimize_resources(self):
        """Check resource usage and apply optimizations if needed"""
        try:
            if not self.resource_optimizer:
                return

            # Get current resource usage
            usage = self.resource_optimizer.get_current_usage()
            memory_percent = usage.get("memory_percent", 0)
            cpu_percent = usage.get("cpu_percent", 0)

            # Check if optimization is needed
            needs_optimization = False

            if memory_percent > 80:
                logger.warning(
                    f"⚠️ High memory usage detected: {memory_percent:.1f}%",
                    extra={
                        "extra_fields": {
                            "memory_percent": memory_percent,
                            "memory_mb": usage.get("memory_mb", 0),
                        }
                    },
                )
                needs_optimization = True

            if cpu_percent > 50:
                logger.warning(
                    f"⚠️ High CPU usage detected: {cpu_percent:.1f}%",
                    extra={"extra_fields": {"cpu_percent": cpu_percent}},
                )
                needs_optimization = True

            # Apply optimizations if needed
            if needs_optimization:
                logger.info(
                    "🔧 Applying resource optimizations...",
                    extra={
                        "extra_fields": {
                            "memory_percent": memory_percent,
                            "cpu_percent": cpu_percent,
                        }
                    },
                )

                success = self.resource_optimizer.apply_optimizations()

                if success:
                    logger.info("✅ Resource optimizations applied successfully")
                else:
                    logger.warning("⚠️ Some resource optimizations failed")

        except Exception as e:
            logger.error(
                f"❌ Error in resource optimization: {e}",
                extra={"extra_fields": {"error": str(e)}},
            )

    def _get_violation_key(self, violation: str) -> str:
        """Generate a unique key for a violation to track attempts"""
        # Normalize the violation string for consistent tracking
        normalized = violation.lower().strip()
        # Remove timestamps and other variable parts
        normalized = re.sub(r"\d{4}-\d{2}-\d{2}", "", normalized)
        normalized = re.sub(r"\d{2}:\d{2}:\d{2}", "", normalized)
        normalized = re.sub(r"\s+", " ", normalized)
        return normalized

    def _verify_fix_success(self, violation: str, action: str) -> bool:
        """Verify if a fix was actually successful by re-checking the violation"""
        try:
            # Wait a moment for the fix to take effect
            time.sleep(2)

            # Re-run a quick compliance check for this specific violation
            violation_key = self._get_violation_key(violation)

            # Check if the violation still exists by running a targeted check
            if "test failures" in violation.lower():
                # Check if tests are now passing
                test_result = self._run_tests_with_venv()
                if test_result and test_result.get("success", False):
                    logger.info(f"✅ Fix verified: Tests are now passing")
                    return True
                else:
                    logger.warning(f"⚠️ Fix verification failed: Tests still failing")
                    return False

            elif "import" in violation.lower():
                # Check if import issues are resolved
                import_result = self._check_import_issues()
                if import_result and not import_result.get("errors", []):
                    logger.info(f"✅ Fix verified: Import issues resolved")
                    return True
                else:
                    logger.warning(
                        f"⚠️ Fix verification failed: Import issues still exist"
                    )
                    return False

            else:
                # For other violations, assume success if no error occurred
                logger.info(f"✅ Fix applied successfully: {action}")
                return True

        except Exception as e:
            logger.error(f"❌ Error verifying fix success: {e}")
            return False

    def _check_import_issues(self) -> Optional[Dict[str, Any]]:
        """Check for import issues in the project"""
        try:
            import subprocess
            import sys

            # Run a quick import check
            result = subprocess.run(
                [sys.executable, "-c", "import ast; print('Import check passed')"],
                capture_output=True,
                text=True,
                cwd=os.getcwd(),
                timeout=30,
            )

            if result.returncode == 0:
                return {"errors": []}
            else:
                return {"errors": [result.stderr]}

        except Exception as e:
            return {"errors": [str(e)]}

    def _should_skip_violation(self, violation: str) -> bool:
        """Check if violation should be skipped due to cooldown or max attempts"""
        violation_key = self._get_violation_key(violation)
        current_time = time.time()

        if violation_key not in self.fix_attempts:
            return False

        attempt_info = self.fix_attempts[violation_key]

        # Check max attempts
        if attempt_info.get("count", 0) >= self.max_fix_attempts:
            logger.warning(
                f"⚠️ Skipping violation '{violation}' - max attempts ({self.max_fix_attempts}) reached"
            )
            return True

        # Check cooldown
        last_attempt = attempt_info.get("last_attempt", 0)
        if current_time - last_attempt < self.fix_cooldown:
            logger.info(
                f"⏳ Skipping violation '{violation}' - still in cooldown period"
            )
            return True

        return False

    def _fix_type_annotation_violations_with_config(self) -> Optional[str]:
        """Attempt to fix type annotation violations using configuration"""
        try:
            logger.info(
                "🔧 Attempting to fix type annotation violations with configuration..."
            )

            config = self.auto_fix_config.get("fixes", {}).get("type_annotation", {})
            actions = config.get(
                "actions", ["add_optional_annotations", "correct_types"]
            )
            patterns = config.get("patterns", {})

            fixes_applied = []

            for action in actions:
                try:
                    if action == "add_optional_annotations":
                        result = self._fix_optional_annotations(patterns)
                        if result:
                            fixes_applied.append(result)

                    elif action == "correct_types":
                        result = self._fix_type_mismatches(patterns)
                        if result:
                            fixes_applied.append(result)

                except Exception as e:
                    logger.warning(f"⚠️ Action '{action}' failed: {e}")

            if fixes_applied:
                return f"Applied type annotation fixes: {', '.join(fixes_applied)}"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error fixing type annotation violations with config: {e}")
            return None

    def _fix_optional_annotations(self, patterns: Dict[str, Any]) -> Optional[str]:
        """Fix Optional type annotations"""
        try:
            fixes_applied = 0
            common_fixes = patterns.get("common_fixes", {})

            for file_path in Path(".").rglob("*.py"):
                if "venv" not in str(file_path) and "node_modules" not in str(
                    file_path
                ):
                    try:
                        with open(file_path, "r", encoding="utf-8") as f:
                            content = f.read()

                        original_content = content

                        # Fix common Optional patterns
                        for type_name, optional_type in common_fixes.items():
                            # Pattern: variable: type = None
                            pattern = rf"(\w+):\s*{type_name}\s*=\s*None"
                            replacement = rf"\1: {optional_type} = None"
                            content = re.sub(pattern, replacement, content)

                            # Pattern: variable: type = None,
                            pattern = rf"(\w+):\s*{type_name}\s*=\s*None,"
                            replacement = rf"\1: {optional_type} = None,"
                            content = re.sub(pattern, replacement, content)

                        if content != original_content:
                            with open(file_path, "w", encoding="utf-8") as f:
                                f.write(content)
                            fixes_applied += 1

                    except Exception as e:
                        logger.warning(f"⚠️ Error processing {file_path}: {e}")

            if fixes_applied > 0:
                return f"Fixed {fixes_applied} type annotation files"
            return None

        except Exception as e:
            logger.error(f"❌ Error fixing optional annotations: {e}")
            return None

    def _fix_type_mismatches(self, patterns: Dict[str, Any]) -> Optional[str]:
        """Fix type mismatches"""
        try:
            fixes_applied = 0

            for file_path in Path(".").rglob("*.py"):
                if "venv" not in str(file_path) and "node_modules" not in str(
                    file_path
                ):
                    try:
                        with open(file_path, "r", encoding="utf-8") as f:
                            content = f.read()

                        original_content = content

                        # Fix Union types for enum parameters
                        # Pattern: def func(param: str) -> None: where param can be enum
                        union_pattern = r"def\s+(\w+)\s*\(\s*(\w+):\s*str\s*\)"
                        content = re.sub(
                            union_pattern, r"def \1(\2: Union[str, \2])", content
                        )

                        if content != original_content:
                            with open(file_path, "w", encoding="utf-8") as f:
                                f.write(content)
                            fixes_applied += 1

                    except Exception as e:
                        logger.warning(f"⚠️ Error processing {file_path}: {e}")

            if fixes_applied > 0:
                return f"Fixed {fixes_applied} type mismatch files"
            return None

        except Exception as e:
            logger.error(f"❌ Error fixing type mismatches: {e}")
            return None

    def _fix_method_declaration_violations_with_config(self) -> Optional[str]:
        """Attempt to fix method declaration violations using configuration"""
        try:
            logger.info(
                "🔧 Attempting to fix method declaration violations with configuration..."
            )

            config = self.auto_fix_config.get("fixes", {}).get(
                "method_declarations", {}
            )
            actions = config.get("actions", ["declare_missing_methods"])
            common_methods = config.get("common_methods", ["close", "get", "shutdown"])

            fixes_applied = []

            for action in actions:
                try:
                    if action == "declare_missing_methods":
                        result = self._declare_missing_methods(common_methods)
                        if result:
                            fixes_applied.append(result)

                except Exception as e:
                    logger.warning(f"⚠️ Action '{action}' failed: {e}")

            if fixes_applied:
                return f"Applied method declaration fixes: {', '.join(fixes_applied)}"
            else:
                return None

        except Exception as e:
            logger.error(
                f"❌ Error fixing method declaration violations with config: {e}"
            )
            return None

    def _declare_missing_methods(self, common_methods: List[str]) -> Optional[str]:
        """Declare missing methods in classes"""
        try:
            fixes_applied = 0

            for file_path in Path(".").rglob("*.py"):
                if "venv" not in str(file_path) and "node_modules" not in str(
                    file_path
                ):
                    try:
                        with open(file_path, "r", encoding="utf-8") as f:
                            content = f.read()

                        original_content = content

                        # Find classes that might need method declarations
                        class_pattern = r"class\s+(\w+)(?:\([^)]*\))?:"
                        classes = re.findall(class_pattern, content)

                        for class_name in classes:
                            # Check if common methods are called but not declared
                            for method_name in common_methods:
                                # Pattern: self.method_name( or .method_name(
                                method_call_pattern = (
                                    rf"(?:self\.|\.){method_name}\s*\("
                                )
                                method_decl_pattern = rf"def\s+{method_name}\s*\("

                                # If method is called but not declared
                                if re.search(
                                    method_call_pattern, content
                                ) and not re.search(method_decl_pattern, content):
                                    # Add method declaration
                                    method_declaration = f"""
    def {method_name}(self) -> None:
        \"\"\"{method_name} method\"\"\"
        pass
"""
                                    # Insert before the last line of the class
                                    lines = content.split("\n")
                                    class_end = -1
                                    indent_level = 0

                                    for i, line in enumerate(lines):
                                        if f"class {class_name}" in line:
                                            indent_level = len(line) - len(
                                                line.lstrip()
                                            )
                                        elif (
                                            line.strip()
                                            and len(line) - len(line.lstrip())
                                            <= indent_level
                                            and i > 0
                                        ):
                                            class_end = i
                                            break

                                    if class_end > 0:
                                        # Insert method before class end
                                        method_indent = " " * (indent_level + 4)
                                        method_lines = [
                                            method_indent + line.strip()
                                            for line in method_declaration.strip().split(
                                                "\n"
                                            )
                                        ]
                                        lines.insert(class_end, "\n".join(method_lines))
                                        content = "\n".join(lines)
                                        fixes_applied += 1

                        if content != original_content:
                            with open(file_path, "w", encoding="utf-8") as f:
                                f.write(content)

                    except Exception as e:
                        logger.warning(f"⚠️ Error processing {file_path}: {e}")

            if fixes_applied > 0:
                return f"Declared {fixes_applied} missing methods"
            return None

        except Exception as e:
            logger.error(f"❌ Error declaring missing methods: {e}")
            return None

    def _fix_docker_rules_violations_with_config(self) -> Optional[str]:
        """Attempt to fix Docker rules violations using configuration"""
        try:
            logger.info(
                "🔧 Attempting to fix Docker rules violations with configuration..."
            )

            config = self.auto_fix_config.get("fixes", {}).get("docker_rules", {})
            actions = config.get("actions", ["add_healthchecks", "set_resource_limits"])
            health_check_patterns = config.get("health_check_patterns", {})
            resource_limits = config.get("resource_limits", {})

            fixes_applied = []

            for action in actions:
                try:
                    if action == "add_healthchecks":
                        result = self._add_docker_healthchecks(health_check_patterns)
                        if result:
                            fixes_applied.append(result)

                    elif action == "set_resource_limits":
                        result = self._set_docker_resource_limits(resource_limits)
                        if result:
                            fixes_applied.append(result)

                except Exception as e:
                    logger.warning(f"⚠️ Action '{action}' failed: {e}")

            if fixes_applied:
                return f"Applied Docker fixes: {', '.join(fixes_applied)}"
            else:
                return None

        except Exception as e:
            logger.error(f"❌ Error fixing Docker rules violations with config: {e}")
            return None

    def _add_docker_healthchecks(
        self, health_check_patterns: Dict[str, str]
    ) -> Optional[str]:
        """Add health checks to Docker services"""
        try:
            if yaml is None:
                logger.warning(
                    "⚠️ PyYAML not available - skipping Docker health check fixes"
                )
                return None

            fixes_applied = 0

            # Look for docker-compose.yml files
            for compose_file in Path(".").rglob("docker-compose*.yml"):
                try:
                    with open(compose_file, "r", encoding="utf-8") as f:
                        content = f.read()

                    original_content = content

                    # Parse YAML to find services without health checks
                    try:
                        compose_data = yaml.safe_load(content)
                        services = compose_data.get("services", {})

                        for service_name, service_config in services.items():
                            if "healthcheck" not in service_config:
                                # Determine health check pattern based on service type
                                health_check = health_check_patterns.get(
                                    "default", "echo 'healthy' || exit 1"
                                )

                                if (
                                    "web" in service_name.lower()
                                    or "frontend" in service_name.lower()
                                ):
                                    health_check = health_check_patterns.get(
                                        "web", health_check
                                    )
                                elif (
                                    "api" in service_name.lower()
                                    or "backend" in service_name.lower()
                                ):
                                    health_check = health_check_patterns.get(
                                        "api", health_check
                                    )

                                # Add health check to service
                                service_config["healthcheck"] = {
                                    "test": ["CMD-SHELL", health_check],
                                    "interval": "30s",
                                    "timeout": "10s",
                                    "retries": 3,
                                    "start_period": "40s",
                                }
                                fixes_applied += 1

                        # Write back the updated content
                        if fixes_applied > 0:
                            with open(compose_file, "w", encoding="utf-8") as f:
                                yaml.dump(
                                    compose_data,
                                    f,
                                    default_flow_style=False,
                                    sort_keys=False,
                                )

                    except yaml.YAMLError as e:
                        logger.warning(f"⚠️ Error parsing {compose_file}: {e}")

                except Exception as e:
                    logger.warning(f"⚠️ Error processing {compose_file}: {e}")

            if fixes_applied > 0:
                return f"Added {fixes_applied} health checks"
            return None

        except Exception as e:
            logger.error(f"❌ Error adding Docker health checks: {e}")
            return None

    def _set_docker_resource_limits(
        self, resource_limits: Dict[str, str]
    ) -> Optional[str]:
        """Set resource limits for Docker services"""
        try:
            if yaml is None:
                logger.warning(
                    "⚠️ PyYAML not available - skipping Docker resource limit fixes"
                )
                return None

            fixes_applied = 0

            # Look for docker-compose.yml files
            for compose_file in Path(".").rglob("docker-compose*.yml"):
                try:
                    with open(compose_file, "r", encoding="utf-8") as f:
                        content = f.read()

                    original_content = content

                    # Parse YAML to find services without resource limits
                    try:
                        compose_data = yaml.safe_load(content)
                        services = compose_data.get("services", {})

                        for service_name, service_config in services.items():
                            if (
                                "deploy" not in service_config
                                or "resources" not in service_config["deploy"]
                            ):
                                # Add resource limits
                                if "deploy" not in service_config:
                                    service_config["deploy"] = {}

                                service_config["deploy"]["resources"] = {
                                    "limits": {
                                        "cpus": resource_limits.get("cpu", "1.0"),
                                        "memory": resource_limits.get("memory", "2G"),
                                        "pids": int(resource_limits.get("pids", 100)),
                                    }
                                }
                                fixes_applied += 1

                        # Write back the updated content
                        if fixes_applied > 0:
                            with open(compose_file, "w", encoding="utf-8") as f:
                                yaml.dump(
                                    compose_data,
                                    f,
                                    default_flow_style=False,
                                    sort_keys=False,
                                )

                    except yaml.YAMLError as e:
                        logger.warning(f"⚠️ Error parsing {compose_file}: {e}")

                except Exception as e:
                    logger.warning(f"⚠️ Error processing {compose_file}: {e}")

            if fixes_applied > 0:
                return f"Set {fixes_applied} resource limits"
            return None

        except Exception as e:
            logger.error(f"❌ Error setting Docker resource limits: {e}")
            return None


def main():
    """Main function with enhanced argument parsing and error handling"""
    import argparse

    parser = argparse.ArgumentParser(description="Cursor Rules Continuous Monitor")
    parser.add_argument(
        "--interval",
        type=int,
        default=30,
        help="Check interval in seconds (default: 30)",
    )
    parser.add_argument("--strict", action="store_true", help="Enable strict mode")
    parser.add_argument("--daemon", action="store_true", help="Run in daemon mode")
    parser.add_argument(
        "--log-level",
        default="INFO",
        choices=["DEBUG", "INFO", "WARNING", "ERROR"],
        help="Log level",
    )
    parser.add_argument(
        "--report", action="store_true", help="Generate and print report"
    )
    parser.add_argument(
        "--metrics", action="store_true", help="Export Prometheus metrics"
    )
    parser.add_argument("--status", action="store_true", help="Show current status")

    args = parser.parse_args()

    # Setup logging with specified level
    global logger
    logger = setup_logging(args.log_level)

    try:
        monitor = CursorRulesMonitor(
            check_interval=args.interval,
            strict_mode=args.strict,
            daemon_mode=args.daemon,
        )

        if args.report:
            print(monitor.generate_report())
            return

        if args.metrics:
            print(monitor.export_metrics_for_prometheus())
            return

        if args.status:
            status = monitor.get_status()
            print(json.dumps(status, indent=2))
            return

        # Start monitoring
        monitor.start_monitoring()

        # Keep running if not in daemon mode
        if not args.daemon:
            try:
                # Keep the main thread alive while monitoring is running
                while monitor.is_monitoring:
                    time.sleep(1)
                    # Check if the monitor thread is still alive
                    if monitor.monitor_thread and not monitor.monitor_thread.is_alive():
                        logger.error("❌ Monitor thread died unexpectedly")
                        break
            except KeyboardInterrupt:
                logger.info("🛑 Received interrupt signal")
                monitor.stop_monitoring()
        else:
            # In daemon mode, keep the main thread alive but run in background
            logger.info("🔄 Running in daemon mode - monitoring started")
            try:
                # Keep the main thread alive while monitoring is running
                while monitor.is_monitoring:
                    time.sleep(1)
                    # Check if the monitor thread is still alive
                    if monitor.monitor_thread and not monitor.monitor_thread.is_alive():
                        logger.error("❌ Monitor thread died unexpectedly")
                        break
            except KeyboardInterrupt:
                logger.info("🛑 Received interrupt signal")
                monitor.stop_monitoring()

    except Exception as e:
        logger.error(f"❌ Fatal error: {e}", extra={"extra_fields": {"error": str(e)}})
        sys.exit(1)


if __name__ == "__main__":
    main()
