# Testing Guidelines

## Overview
Comprehensive guidelines for maintaining 100% test success rate and ensuring code quality through rigorous testing practices.

## 🚨 CRITICAL: 100% Test Success Requirement

### Core Requirements
- **ALL test runs MUST achieve 100% success rate**
- **NEVER accept, proceed with, or report test results below 100%**
- **Test failures indicate code quality issues that MUST be addressed immediately**

### Test Failure Resolution Protocol
If test results are less than 100%, immediately:
1. **STOP all development work**
2. **Analyze test failures** - identify root causes
3. **Check source files** for errors and fix them
4. **Review test code** for issues and correct them
5. **Fix ALL issues** before re-running tests
6. **Only proceed** after achieving 100% test success
7. **Document fixes** made to resolve test failures

## 🧪 Testing Standards

### Test Coverage Requirements
- **Maintain test coverage above 80%**
- **Focus on critical paths** and business logic
- **Test edge cases** and error conditions
- **Test integration points** between components

### Test Types Required

#### Unit Tests
- **Test individual functions** and methods
- **Mock external dependencies** (APIs, databases)
- **Test edge cases** and error conditions
- **Use descriptive test names** that explain the scenario

#### Integration Tests
- **Test component interactions**
- **Test API endpoints** with real requests
- **Test database operations** with test data
- **Test end-to-end workflows**

#### Performance Tests
- **Test response times** for API endpoints
- **Test memory usage** for large operations
- **Test concurrent access** patterns
- **Set performance benchmarks**

### Testing Tools

#### Python Testing
```bash
# Run all tests
pytest tests/ -v --tb=short

# Run with coverage
pytest tests/ --cov=core --cov-report=html

# Run specific test file
pytest tests/test_agent_main.py -v

# Run specific test function
pytest tests/test_agent_main.py::test_ai_agent_initialization -v
```

#### TypeScript/React Testing
```bash
# Run all tests
npm test

# Run with coverage
npm test -- --coverage

# Run specific test file
npm test -- tests/components/AgentForm.test.tsx

# Run in watch mode
npm test -- --watch
```

## 🔧 Test Implementation Guidelines

### Test Structure
```python
# ✅ CORRECT: Well-structured test
import pytest
from unittest.mock import Mock, patch
from core.agents.agent_main import AIAgent

class TestAIAgent:
    """Test suite for AIAgent class."""

    def setup_method(self):
        """Set up test fixtures."""
        self.config_path = "config/smart_routing_config.json"
        self.agent = AIAgent(self.config_path)

    def test_initialization(self):
        """Test AIAgent initialization."""
        assert self.agent is not None
        assert self.agent.config is not None
        assert isinstance(self.agent.config, dict)

    def test_config_loading(self):
        """Test config file loading."""
        assert "agents" in self.agent.config
        assert "routing" in self.agent.config

    @patch('core.agents.agent_main.get_config')
    def test_config_error_handling(self, mock_get_config):
        """Test error handling when config fails to load."""
        mock_get_config.side_effect = FileNotFoundError("Config not found")

        with pytest.raises(FileNotFoundError):
            AIAgent("nonexistent_config.json")
```

### Mocking Best Practices
```python
# ✅ CORRECT: Proper mocking
@patch('core.agents.agent_main.DatabaseManager')
@patch('core.agents.agent_main.CMSCommands')
def test_component_initialization(self, mock_cms, mock_db):
    """Test component initialization with mocked dependencies."""
    # Configure mocks
    mock_db.return_value = Mock()
    mock_cms.return_value = Mock()

    # Test initialization
    agent = AIAgent("config/test_config.json")

    # Verify mocks were called correctly
    mock_db.assert_called_once_with("config/database_config.json")
    mock_cms.assert_called_once_with("config/cms_config.json")
```

### Test Data Management
```python
# ✅ CORRECT: Test data fixtures
import pytest
from pathlib import Path

@pytest.fixture
def test_config():
    """Provide test configuration."""
    return {
        "agents": {
            "architect": {"enabled": True},
            "frontend": {"enabled": True},
            "backend": {"enabled": True}
        },
        "routing": {
            "strategy": "ai_powered",
            "fallback": "rule_based"
        }
    }

@pytest.fixture
def test_agent(test_config):
    """Provide test agent instance."""
    with patch('core.agents.agent_main.get_config', return_value=test_config):
        return AIAgent("config/test_config.json")
```

## 🚨 Test Failure Resolution

### Common Test Failure Patterns

#### Import Errors
**Problem**: ModuleNotFoundError during test execution
**Solution**:
- Ensure all dependencies are in requirements.txt
- Check virtual environment activation
- Verify import paths are correct

#### Configuration Errors
**Problem**: Config files not found or invalid
**Solution**:
- Use test fixtures for configuration
- Mock config loading functions
- Provide test-specific config files

#### Database Errors
**Problem**: Database connection or migration issues
**Solution**:
- Use test database or in-memory database
- Mock database operations
- Clean up test data after tests

#### Async/Await Issues
**Problem**: Async functions not properly tested
**Solution**:
- Use pytest-asyncio for async tests
- Properly await async functions
- Mock async dependencies

### Debugging Test Failures
```bash
# Run tests with verbose output
pytest tests/ -v -s

# Run specific failing test
pytest tests/test_agent_main.py::test_specific_function -v -s

# Run with debugger
pytest tests/ --pdb

# Run with coverage to identify untested code
pytest tests/ --cov=core --cov-report=term-missing
```

## 📊 Test Metrics & Reporting

### Coverage Reports
```bash
# Generate HTML coverage report
pytest tests/ --cov=core --cov-report=html

# Generate XML coverage report for CI
pytest tests/ --cov=core --cov-report=xml

# Generate terminal coverage report
pytest tests/ --cov=core --cov-report=term-missing
```

### Test Performance
```bash
# Run tests with timing information
pytest tests/ --durations=10

# Run tests in parallel
pytest tests/ -n auto

# Run tests with memory profiling
pytest tests/ --memray
```

## 🔄 Continuous Integration

### CI Pipeline Requirements
```yaml
# .github/workflows/test.yml
name: Tests
on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.11'

      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r config/requirements.txt
          pip install -r config/requirements-dev.txt

      - name: Run tests
        run: |
          pytest tests/ -v --cov=core --cov-report=xml

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml
```

### Pre-commit Hooks
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files

  - repo: local
    hooks:
      - id: pytest
        name: pytest
        entry: pytest
        language: system
        pass_filenames: false
        always_run: true
```

## 🎯 Best Practices

### Test Naming
- **Use descriptive names** that explain what is being tested
- **Follow pattern**: `test_<function_name>_<scenario>`
- **Include expected outcome** in test name

### Test Organization
- **Group related tests** in test classes
- **Use fixtures** for common setup
- **Keep tests independent** - no shared state

### Test Data
- **Use realistic test data** that represents real scenarios
- **Clean up test data** after tests
- **Use factories** for complex test objects

### Error Testing
- **Test error conditions** and edge cases
- **Verify error messages** are helpful
- **Test exception handling** properly

## 📚 Additional Resources

- [pytest Documentation](https://docs.pytest.org/)
- [Python Testing Best Practices](https://realpython.com/python-testing/)
- [Test-Driven Development](https://en.wikipedia.org/wiki/Test-driven_development)
- [Coverage.py Documentation](https://coverage.readthedocs.io/)
