#!/usr/bin/env python3
"""
Tests for auto-fix cleanup functionality in CursorRulesEnforcer
"""
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest

from agent.core.cursor_rules_enforcer import CursorRulesEnforcer


@pytest.fixture
def temp_project_dir():
    with tempfile.TemporaryDirectory() as tmpdir:
        project_dir = Path(tmpdir)
        
        # Create basic project structure
        (project_dir / "core").mkdir()
        (project_dir / "tests").mkdir()
        
        # Create some Python files
        (project_dir / "core" / "__init__.py").write_text("")
        (project_dir / "core" / "main.py").write_text("def hello():\n    return 'world'")
        
        yield str(project_dir)


@pytest.fixture
def enforcer(temp_project_dir):
    return CursorRulesEnforcer(project_root=temp_project_dir)


def test_auto_fix_no_issues(enforcer):
    """Test auto-fix when no cleanup issues exist"""
    result = enforcer.auto_fix_cleanup_issues(dry_run=True)
    
    assert result["success"] is True
    assert result["message"] == "No cleanup issues found"
    assert len(result["actions_taken"]) == 0


def test_auto_fix_dry_run_mode(enforcer, temp_project_dir):
    """Test auto-fix in dry-run mode"""
    project_dir = Path(temp_project_dir)
    
    # Create some problematic files
    obsolete_file = project_dir / "old_file.bak"
    obsolete_file.write_text("obsolete content")
    
    temp_file = project_dir / "temp_data.tmp"
    temp_file.write_text("temp content")
    
    # Run auto-fix in dry-run mode
    result = enforcer.auto_fix_cleanup_issues(dry_run=True)
    
    # Should identify issues but not fix them
    assert result["dry_run"] is True
    assert len(result["actions_taken"]) > 0
    
    # Files should still exist
    assert obsolete_file.exists()
    assert temp_file.exists()
    
    # Check that actions are marked as dry-run
    for action in result["actions_taken"]:
        assert action["status"] == "dry_run"


def test_auto_fix_obsolete_files(enforcer, temp_project_dir):
    """Test auto-fix of obsolete files"""
    project_dir = Path(temp_project_dir)
    
    # Create obsolete files
    obsolete_files = [
        project_dir / "old_module.py.bak",
        project_dir / "backup_data.old",
        project_dir / "temp_file.backup"
    ]
    
    for file_path in obsolete_files:
        file_path.write_text("obsolete content")
    
    # Mock git rm to avoid actual git operations
    with patch('subprocess.run') as mock_run:
        mock_run.return_value.returncode = 0
        mock_run.return_value.stderr = ""
        
        result = enforcer.auto_fix_cleanup_issues(dry_run=False)
    
    assert result["success"] is True
    assert len(result["actions_taken"]) >= len(obsolete_files)
    
    # Check that git rm was called for each file
    assert mock_run.call_count >= len(obsolete_files)


def test_auto_fix_temp_files(enforcer, temp_project_dir):
    """Test auto-fix of temporary files"""
    project_dir = Path(temp_project_dir)
    
    # Create temp files
    temp_files = [
        project_dir / "cache.tmp",
        project_dir / "output.temp",
        project_dir / "__pycache__"
    ]
    
    for file_path in temp_files:
        if file_path.name == "__pycache__":
            file_path.mkdir()
            (file_path / "test.pyc").write_text("compiled")
        else:
            file_path.write_text("temp content")
    
    # Set file modification time to be old enough
    import time
    old_time = time.time() - 7200  # 2 hours ago
    for file_path in temp_files:
        if file_path.exists():
            import os
            os.utime(file_path, (old_time, old_time))
    
    result = enforcer.auto_fix_cleanup_issues(dry_run=False)
    
    assert result["success"] is True
    
    # Check that temp files were removed
    for file_path in temp_files:
        assert not file_path.exists()


def test_auto_fix_duplicate_files(enforcer, temp_project_dir):
    """Test auto-fix of duplicate files"""
    project_dir = Path(temp_project_dir)
    
    # Create duplicate files
    original_file = project_dir / "core" / "utils.py"
    duplicate_file = project_dir / "core" / "utils_copy.py"
    
    content = "def utility_function():\n    return 'utility'"
    original_file.write_text(content)
    duplicate_file.write_text(content)
    
    # Mock git rm to avoid actual git operations
    with patch('subprocess.run') as mock_run:
        mock_run.return_value.returncode = 0
        mock_run.return_value.stderr = ""
        
        result = enforcer.auto_fix_cleanup_issues(dry_run=False)
    
    # Should detect and handle duplicates
    duplicate_actions = [
        action for action in result["actions_taken"]
        if action.get("action") == "consolidate_duplicate"
    ]
    
    assert len(duplicate_actions) > 0


def test_auto_fix_legacy_files(enforcer, temp_project_dir):
    """Test auto-fix of legacy files"""
    project_dir = Path(temp_project_dir)
    
    # Create legacy files
    legacy_file = project_dir / "old_module_v1.py"
    legacy_file.write_text("# @deprecated\ndef old_function():\n    pass")
    
    result = enforcer.auto_fix_cleanup_issues(dry_run=False)
    
    # Should detect and handle legacy files
    legacy_actions = [
        action for action in result["actions_taken"]
        if action.get("action") == "archive_legacy"
    ]
    
    # Legacy files should be archived, not deleted
    if legacy_actions:
        assert legacy_actions[0]["status"] in ["success", "dry_run"]


def test_verify_files_identical(enforcer, temp_project_dir):
    """Test file identity verification"""
    project_dir = Path(temp_project_dir)
    
    # Create identical files
    file1 = project_dir / "file1.py"
    file2 = project_dir / "file2.py"
    content = "def test():\n    return True"
    
    file1.write_text(content)
    file2.write_text(content)
    
    assert enforcer._verify_files_identical(file1, file2) is True
    
    # Create different files
    file2.write_text("def test():\n    return False")
    assert enforcer._verify_files_identical(file1, file2) is False
    
    # Test with non-existent file
    non_existent = project_dir / "non_existent.py"
    assert enforcer._verify_files_identical(file1, non_existent) is False


def test_find_file_references(enforcer, temp_project_dir):
    """Test finding file references"""
    project_dir = Path(temp_project_dir)
    
    # Create target file
    target_file = project_dir / "core" / "target_module.py"
    target_file.write_text("def target_function():\n    return 'target'")
    
    # Create file that references the target
    referencing_file = project_dir / "core" / "main.py"
    referencing_file.write_text("from target_module import target_function\n\ndef main():\n    return target_function()")
    
    references = enforcer._find_file_references(target_file)
    
    assert len(references) > 0
    assert referencing_file in references


def test_update_imports(enforcer, temp_project_dir):
    """Test updating import statements"""
    project_dir = Path(temp_project_dir)
    
    # Create files
    old_file = project_dir / "core" / "old_module.py"
    new_file = project_dir / "core" / "new_module.py"
    referencing_file = project_dir / "core" / "main.py"
    
    old_file.write_text("def old_function():\n    return 'old'")
    new_file.write_text("def old_function():\n    return 'new'")
    referencing_file.write_text("from old_module import old_function\n\ndef main():\n    return old_function()")
    
    # Update imports
    enforcer._update_imports([referencing_file], old_file, new_file)
    
    # Check that import was updated
    updated_content = referencing_file.read_text()
    assert "from new_module import old_function" in updated_content
    assert "from old_module import old_function" not in updated_content


def test_auto_fix_safety_checks(enforcer, temp_project_dir):
    """Test safety checks in auto-fix"""
    project_dir = Path(temp_project_dir)
    
    # Create a file that's referenced by another file
    target_file = project_dir / "important_module.bak"
    target_file.write_text("def important_function():\n    return 'important'")
    
    referencing_file = project_dir / "core" / "main.py"
    referencing_file.write_text("# This file references important_module.bak\n# import important_module")
    
    result = enforcer.auto_fix_cleanup_issues(dry_run=False)
    
    # Should skip files that have references for safety
    obsolete_actions = [
        action for action in result["actions_taken"]
        if action.get("action") == "remove_obsolete" and action.get("status") == "skipped"
    ]
    
    # At least some actions should be skipped for safety
    assert len(obsolete_actions) >= 0  # May or may not find references depending on detection


def test_auto_fix_error_handling(enforcer, temp_project_dir):
    """Test error handling in auto-fix"""
    project_dir = Path(temp_project_dir)
    
    # Create a file in a read-only directory (simulate permission error)
    readonly_dir = project_dir / "readonly"
    readonly_dir.mkdir()
    readonly_file = readonly_dir / "readonly.tmp"
    readonly_file.write_text("readonly content")
    
    # Make directory read-only
    import os
    os.chmod(readonly_dir, 0o444)
    
    try:
        result = enforcer.auto_fix_cleanup_issues(dry_run=False)
        
        # Should handle errors gracefully
        assert "errors" in result
        
        # Some actions might fail due to permissions
        failed_actions = [
            action for action in result["actions_taken"]
            if action.get("status") == "error"
        ]
        
        # Should continue processing other files even if some fail
        assert result["success"] is not None
        
    finally:
        # Restore permissions for cleanup
        os.chmod(readonly_dir, 0o755)


def test_auto_fix_integration_with_monitor():
    """Test integration with cursor rules monitor"""
    from agent.scripts.cursor_rules_monitor import CursorRulesMonitor
    
    # Create monitor instance
    monitor = CursorRulesMonitor()
    
    # Test that the enhanced cleanup method exists
    assert hasattr(monitor, '_fix_file_cleanup_violations_with_config')
    
    # Test that it can be called (will use fallback if enforcer fails)
    result = monitor._fix_file_cleanup_violations_with_config()
    
    # Should return None or a string message
    assert result is None or isinstance(result, str)
