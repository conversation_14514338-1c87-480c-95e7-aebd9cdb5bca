#!/usr/bin/env python3
"""Guard agent↔project separation and host execution policy.
- Detect direct writes to projects/ from agent code (heuristic search for 'projects/' usage in write contexts).
- Detect host execution patterns (reuse docker_first_policy patterns).
"""
from __future__ import annotations

import re
from pathlib import Path
import sys

# Allowed paths (definitions, tests, docs, and scripts are permitted to mention patterns)
ALLOW_PATHS = [
    "agent/core/docker_first_policy.py",
    "agent/tests",
    "tools/tests",
    "agent/scripts",
    "docs",
    "agent/docs",
]

WRITE_PATTERNS = [
    r"open\(.*projects/.*,\s*['\"](?:w|a|x|wb|ab|xb)['\"]",
    r"shutil\.copy2?\(.*projects/",
    r"os\.makedirs?\(.*projects/",
    r"Path\(.*projects/.*\)\.write_",
]

HOST_EXEC_PATTERNS = [
    r"python\s+-m\s+http\.server",
    r"npm\s+start",
    r"flask\s+run",
    r"django\s+runserver",
    r"rails\s+server",
    r"serve\s+-s",
    r"http-server",
    r"live-server",
]


def scan_py(path: Path) -> list[str]:
    issues: list[str] = []
    text = path.read_text(encoding="utf-8", errors="replace")

    # Simple line-wise scan; skip comment-only lines
    for i, line in enumerate(text.splitlines(), start=1):
        stripped = line.lstrip()
        if stripped.startswith("#"):
            continue
        for pat in WRITE_PATTERNS:
            if re.search(pat, line):
                issues.append(f"{path}:{i}: write to projects/ pattern: {pat}")
        for pat in HOST_EXEC_PATTERNS:
            if re.search(pat, line, flags=re.IGNORECASE):
                issues.append(f"{path}:{i}: host execution pattern: {pat}")

    return issues


def main() -> int:
    failures: list[str] = []
    for py in Path("agent").rglob("*.py"):
        # Allowlist exceptions: tests/docs/scripts or specific definition file
        p = py.as_posix()
        if any(p.startswith(ap) for ap in ALLOW_PATHS):
            continue
        failures.extend(scan_py(py))

    if failures:
        print("❌ Separation Guard violations:\n" + "\n".join(failures))
        print("\nRemediation: route writes via SiteContainerManager volumes and avoid host-execution; use Docker-first flows.")
        return 1

    print("✅ Separation & host-execution checks passed")
    return 0


if __name__ == "__main__":
    sys.exit(main())
