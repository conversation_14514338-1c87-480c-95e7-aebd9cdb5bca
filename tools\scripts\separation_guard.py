#!/usr/bin/env python3
"""Guard agent↔project separation and host execution policy.
- Detect direct writes to projects/ from agent code (heuristic search for 'projects/' usage in write contexts).
- Detect host execution patterns (reuse docker_first_policy patterns).
"""
from __future__ import annotations

import re
from pathlib import Path
import sys

WRITE_PATTERNS = [
    r"open\(.*projects/.*,\s*['\"](?:w|a|x|wb|ab|xb)['\"]",
    r"shutil\.copy2?\(.*projects/",
    r"os\.makedirs?\(.*projects/",
    r"Path\(.*projects/.*\)\.write_",
]

HOST_EXEC_PATTERNS = [
    r"python\s+-m\s+http\.server",
    r"npm\s+start",
    r"flask\s+run",
    r"django\s+runserver",
    r"rails\s+server",
    r"serve\s+-s",
    r"http-server",
    r"live-server",
]


def scan_py(path: Path) -> list[str]:
    issues: list[str] = []
    text = path.read_text(encoding="utf-8", errors="replace")
    for pat in WRITE_PATTERNS:
        if re.search(pat, text):
            issues.append(f"{path}: write to projects/ pattern: {pat}")
    for pat in HOST_EXEC_PATTERNS:
        if re.search(pat, text, flags=re.IGNORECASE):
            issues.append(f"{path}: host execution pattern: {pat}")
    return issues


def main() -> int:
    failures: list[str] = []
    for py in Path("agent").rglob("*.py"):
        failures.extend(scan_py(py))

    if failures:
        print("❌ Separation Guard violations:\n" + "\n".join(failures))
        print("\nRemediation: route writes via SiteContainerManager volumes and avoid host-execution; use Docker-first flows.")
        return 1

    print("✅ Separation & host-execution checks passed")
    return 0


if __name__ == "__main__":
    sys.exit(main())

