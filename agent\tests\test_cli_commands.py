#!/usr/bin/env python3
"""
Test CLI Commands - Verify update agent commands work
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))


async def test_cli_commands():
    """Test that CLI commands work correctly"""
    print("🚀 TESTING CLI COMMANDS")
    print("=" * 50)

    try:
        from agent.core.agents.agent_main import AIAgent

        # Initialize agent
        print("📋 Initializing AI Agent...")
        agent = AIAgent("config/smart_routing_config.json")
        await agent.initialize()
        print("✅ Agent initialized successfully")

        # Test check-updates command
        print("\n🔍 Testing check-updates command...")
        result = await agent.execute_command("check-updates", {})
        if result.get("success"):
            print("✅ check-updates command executed successfully")
            print(f"   Result: {result.get('message', 'No message')}")
        else:
            print(
                f"❌ check-updates command failed: {result.get('error', 'Unknown error')}"
            )

        # Test security-audit command
        print("\n🔒 Testing security-audit command...")
        result = await agent.execute_command("security-audit", {})
        if result.get("success"):
            print("✅ security-audit command executed successfully")
            print(f"   Result: {result.get('message', 'No message')}")
        else:
            print(
                f"❌ security-audit command failed: {result.get('error', 'Unknown error')}"
            )

        # Shutdown agent
        await agent.shutdown()
        print("\n✅ Agent shutdown successfully")

        return True

    except Exception as e:
        print(f"❌ CLI command test failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(test_cli_commands())
    sys.exit(0 if success else 1)
