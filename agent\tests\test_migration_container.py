#!/usr/bin/env python3
"""
Migration Container Test Suite
Comprehensive testing for Database Migration Runner containerization
"""

import asyncio
import json
import subprocess
import sys
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agent.cli.migration_commands import MigrationCommands
from agent.core.validators.safety_validator import SafetyValidator


class MigrationContainerTester:
    """Comprehensive test suite for migration container"""

    def __init__(self):
        self.test_results = []
        self.migration_commands = MigrationCommands()
        self.safety_validator = SafetyValidator()

        # Test configuration
        self.container_name = "ai-coding-migration-runner"
        self.service_name = "migration-runner"
        self.port = 8086

        # Test directories
        self.test_dirs = ["migrations", "backups", "logs", "database"]

    def log_test(self, test_name: str, passed: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"  {status} {test_name}")
        if details:
            print(f"    {details}")

        self.test_results.append(
            {
                "test": test_name,
                "passed": passed,
                "details": details,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )

    def test_dockerfile_exists(self):
        """Test that Dockerfile.migration-runner exists"""
        print("\n🔍 Testing Dockerfile.migration-runner...")

        dockerfile_path = Path("containers/Dockerfile.migration-runner")
        exists = dockerfile_path.exists()

        if exists:
            # Check Dockerfile content
            with open(dockerfile_path, "r") as f:
                content = f.read()

            checks = [
                ("Multi-stage build", "FROM python:3.11-slim as builder" in content),
                ("Runtime stage", "FROM python:3.11-slim as runtime" in content),
                (
                    "Non-root user",
                    "useradd -r -g migrationrunner migrationrunner" in content,
                ),
                ("Health check", "HEALTHCHECK" in content),
                ("Port exposure", "EXPOSE 8086" in content),
                ("Security", "USER migrationrunner" in content),
                ("Environment variables", "ENV PYTHONPATH=/app" in content),
            ]

            all_checks_passed = True
            for check_name, check_result in checks:
                self.log_test(f"Dockerfile {check_name}", check_result)
                if not check_result:
                    all_checks_passed = False

            self.log_test(
                "Dockerfile.migration-runner Complete",
                all_checks_passed,
                "Multi-stage build with security, health checks, and proper configuration",
            )
        else:
            self.log_test("Dockerfile.migration-runner Exists", False, "File not found")

    def test_docker_compose_integration(self):
        """Test docker-compose.yml integration"""
        print("\n🔍 Testing Docker Compose Integration...")

        compose_path = Path("containers/docker-compose.yml")
        if not compose_path.exists():
            self.log_test(
                "Docker Compose File", False, "containers/docker-compose.yml not found"
            )
            return

        with open(compose_path, "r") as f:
            content = f.read()

        checks = [
            ("Migration service defined", "migration-runner:" in content),
            (
                "Dockerfile reference",
                "dockerfile: containers/Dockerfile.migration-runner" in content,
            ),
            ("Port mapping", "8086:8086" in content),
            ("Health check", "healthcheck:" in content),
            ("Resource limits", "cpus: '0.5'" in content and "memory: 1G" in content),
            ("Volume mounts", "./database:/app/database" in content),
            ("Network configuration", "ai-coding-network" in content),
            ("Dependencies", "depends_on:" in content),
            ("Environment variables", "MIGRATION_ENVIRONMENT=production" in content),
        ]

        all_checks_passed = True
        for check_name, check_result in checks:
            self.log_test(f"Docker Compose {check_name}", check_result)
            if not check_result:
                all_checks_passed = False

        self.log_test(
            "Docker Compose Integration Complete",
            all_checks_passed,
            "Service properly configured with dependencies, resources, and networking",
        )

    def test_configuration_file(self):
        """Test migration Docker configuration file"""
        print("\n🔍 Testing Migration Configuration File...")

        config_path = Path("config/migration_docker_config.json")
        exists = config_path.exists()

        if exists:
            try:
                with open(config_path, "r") as f:
                    config = json.load(f)

                checks = [
                    (
                        "Service configuration",
                        "service" in config.get("migration_runner", {}),
                    ),
                    (
                        "Resource limits",
                        "resources" in config.get("migration_runner", {}),
                    ),
                    (
                        "Volume configuration",
                        "volumes" in config.get("migration_runner", {}),
                    ),
                    (
                        "Environment variables",
                        "environment" in config.get("migration_runner", {}),
                    ),
                    (
                        "Security settings",
                        "security" in config.get("migration_runner", {}),
                    ),
                    (
                        "Monitoring configuration",
                        "monitoring" in config.get("migration_runner", {}),
                    ),
                    ("Backup settings", "backup" in config.get("migration_runner", {})),
                    (
                        "Rollback settings",
                        "rollback" in config.get("migration_runner", {}),
                    ),
                    ("API configuration", "api" in config.get("migration_runner", {})),
                    ("CLI configuration", "cli" in config.get("migration_runner", {})),
                ]

                all_checks_passed = True
                for check_name, check_result in checks:
                    self.log_test(f"Configuration {check_name}", check_result)
                    if not check_result:
                        all_checks_passed = False

                self.log_test(
                    "Migration Configuration Complete",
                    all_checks_passed,
                    "Comprehensive configuration with all required sections",
                )

            except json.JSONDecodeError as e:
                self.log_test("Configuration JSON Valid", False, f"Invalid JSON: {e}")
            except Exception as e:
                self.log_test("Configuration Loading", False, f"Error: {e}")
        else:
            self.log_test(
                "Configuration File Exists",
                False,
                "config/migration_docker_config.json not found",
            )

    def test_cli_commands(self):
        """Test CLI commands implementation"""
        print("\n🔍 Testing CLI Commands...")

        cli_path = Path("cli/migration_commands.py")
        exists = cli_path.exists()

        if exists:
            # Test CLI methods
            expected_methods = [
                "migration_status",
                "migration_summary",
                "list_migrations",
                "run_migration",
                "rollback_migration",
                "validate_migration",
                "create_backup",
                "restore_backup",
                "list_backups",
                "migration_metrics",
                "export_migration_report",
            ]

            all_methods_exist = True
            for method_name in expected_methods:
                method_exists = hasattr(self.migration_commands, method_name)
                self.log_test(f"CLI Method: {method_name}", method_exists)
                if not method_exists:
                    all_methods_exist = False

            self.log_test(
                "CLI Commands Complete",
                all_methods_exist,
                f"All {len(expected_methods)} CLI methods implemented",
            )
        else:
            self.log_test(
                "CLI Commands File", False, "cli/migration_commands.py not found"
            )

    def test_api_routes(self):
        """Test API routes implementation"""
        print("\n🔍 Testing API Routes...")

        api_path = Path("api/migration_routes.py")
        exists = api_path.exists()

        if exists:
            with open(api_path, "r") as f:
                content = f.read()

            expected_endpoints = [
                "/status",
                "/summary",
                "/list",
                "/run",
                "/rollback",
                "/validate",
                "/backup/create",
                "/backup/restore",
                "/backup/list",
                "/metrics",
                "/export",
                "/health",
            ]

            all_endpoints_exist = True
            for endpoint in expected_endpoints:
                endpoint_exists = f'"/api/migration{endpoint}"' in content
                self.log_test(f"API Endpoint: {endpoint}", endpoint_exists)
                if not endpoint_exists:
                    all_endpoints_exist = False

            # Check Pydantic models
            model_checks = [
                ("Response models", "class MigrationStatusResponse" in content),
                ("Request models", "class MigrationRunRequest" in content),
                ("Router setup", 'APIRouter(prefix="/api/migration"' in content),
                ("Error handling", "HTTPException" in content),
            ]

            for check_name, check_result in model_checks:
                self.log_test(f"API {check_name}", check_result)
                if not check_result:
                    all_endpoints_exist = False

            self.log_test(
                "API Routes Complete",
                all_endpoints_exist,
                f"All {len(expected_endpoints)} API endpoints with proper models and error handling",
            )
        else:
            self.log_test("API Routes File", False, "api/migration_routes.py not found")

    def test_migration_runner_script(self):
        """Test migration runner script"""
        print("\n🔍 Testing Migration Runner Script...")

        script_path = Path("scripts/migration_runner.py")
        exists = script_path.exists()

        if exists:
            with open(script_path, "r") as f:
                content = f.read()

            checks = [
                ("FastAPI app", "FastAPI(" in content),
                ("Health check endpoint", '@app.get("/health")' in content),
                ("Migration endpoints", '@app.get("/api/migration/status")' in content),
                ("Signal handling", "signal_handler" in content),
                ("Graceful shutdown", "is_shutting_down" in content),
                ("Logging configuration", "logging.basicConfig" in content),
                ("Uvicorn server", "uvicorn.Config" in content),
                ("Scheduled tasks", "run_scheduled_tasks" in content),
            ]

            all_checks_passed = True
            for check_name, check_result in checks:
                self.log_test(f"Runner Script {check_name}", check_result)
                if not check_result:
                    all_checks_passed = False

            self.log_test(
                "Migration Runner Script Complete",
                all_checks_passed,
                "Complete FastAPI application with health checks, endpoints, and scheduled tasks",
            )
        else:
            self.log_test(
                "Migration Runner Script",
                False,
                "scripts/migration_runner.py not found",
            )

    def test_resource_limits(self):
        """Test resource limits configuration"""
        print("\n🔍 Testing Resource Limits...")

        compose_path = Path("containers/docker-compose.yml")
        if compose_path.exists():
            with open(compose_path, "r") as f:
                content = f.read()

            # Find migration-runner service section
            if "migration-runner:" in content:
                service_start = content.find("migration-runner:")
                service_end = content.find("\n  ", service_start + 1)
                if service_end == -1:
                    service_end = len(content)

                service_content = content[service_start:service_end]

                checks = [
                    ("CPU limit", "cpus: '0.5'" in service_content),
                    ("Memory limit", "memory: 1G" in service_content),
                    ("CPU reservation", "cpus: '0.25'" in service_content),
                    ("Memory reservation", "memory: 512M" in service_content),
                ]

                all_checks_passed = True
                for check_name, check_result in checks:
                    self.log_test(f"Resource {check_name}", check_result)
                    if not check_result:
                        all_checks_passed = False

                self.log_test(
                    "Resource Limits Complete",
                    all_checks_passed,
                    "CPU: 0.5/0.25, Memory: 1G/512M limits and reservations configured",
                )
            else:
                self.log_test(
                    "Migration Service Found",
                    False,
                    "migration-runner service not found in containers/docker-compose.yml",
                )
        else:
            self.log_test(
                "Docker Compose File", False, "containers/docker-compose.yml not found"
            )

    def test_service_discovery(self):
        """Test service discovery and networking"""
        print("\n🔍 Testing Service Discovery...")

        compose_path = Path("containers/docker-compose.yml")
        if compose_path.exists():
            with open(compose_path, "r") as f:
                content = f.read()

            checks = [
                ("Network configuration", "ai-coding-network" in content),
                ("Dependencies", "depends_on:" in content),
                ("Database dependency", "db:" in content),
                ("API dependency", "api:" in content),
                ("Health check dependency", "condition: service_healthy" in content),
            ]

            all_checks_passed = True
            for check_name, check_result in checks:
                self.log_test(f"Service Discovery {check_name}", check_result)
                if not check_result:
                    all_checks_passed = False

            self.log_test(
                "Service Discovery Complete",
                all_checks_passed,
                "Proper networking, dependencies, and health check conditions configured",
            )
        else:
            self.log_test(
                "Docker Compose File", False, "containers/docker-compose.yml not found"
            )

    async def test_container_build(self):
        """Test container build process"""
        print("\n🔍 Testing Container Build...")

        try:
            # Test Docker build
            build_cmd = [
                "docker",
                "build",
                "-f",
                "containers/Dockerfile.migration-runner",
                "-t",
                "ai-coding-migration-runner:test",
                ".",
            ]

            result = subprocess.run(
                build_cmd, capture_output=True, text=True, timeout=300
            )

            if result.returncode == 0:
                self.log_test(
                    "Container Build", True, "Docker build completed successfully"
                )

                # Test container startup
                run_cmd = [
                    "docker",
                    "run",
                    "--name",
                    "test-migration-runner",
                    "--rm",
                    "-d",
                    "-p",
                    "8087:8086",
                    "ai-coding-migration-runner:test",
                ]

                run_result = subprocess.run(
                    run_cmd, capture_output=True, text=True, timeout=60
                )

                if run_result.returncode == 0:
                    # Wait for container to start
                    time.sleep(10)

                    # Test health check
                    try:
                        import requests

                        response = requests.get(
                            "http://localhost:8087/health", timeout=10
                        )
                        health_ok = response.status_code == 200
                        self.log_test(
                            "Container Startup",
                            True,
                            "Container started and health check passed",
                        )
                        self.log_test(
                            "Health Check Endpoint",
                            health_ok,
                            f"Status: {response.status_code}",
                        )
                    except Exception as e:
                        self.log_test("Health Check Endpoint", False, f"Error: {e}")

                    # Stop test container
                    subprocess.run(
                        ["docker", "stop", "test-migration-runner"],
                        capture_output=True,
                        timeout=30,
                    )
                else:
                    self.log_test(
                        "Container Startup", False, f"Error: {run_result.stderr}"
                    )
            else:
                self.log_test(
                    "Container Build", False, f"Build failed: {result.stderr}"
                )

        except subprocess.TimeoutExpired:
            self.log_test("Container Build", False, "Build timed out")
        except Exception as e:
            self.log_test("Container Build", False, f"Error: {e}")

    async def test_api_endpoints(self):
        """Test API endpoints functionality"""
        print("\n🔍 Testing API Endpoints...")

        try:
            import requests

            base_url = "http://localhost:8087"

            # Test health endpoint
            try:
                response = requests.get(f"{base_url}/health", timeout=10)
                health_ok = response.status_code == 200
                self.log_test(
                    "Health Endpoint", health_ok, f"Status: {response.status_code}"
                )
            except Exception as e:
                self.log_test("Health Endpoint", False, f"Error: {e}")

            # Test root endpoint
            try:
                response = requests.get(f"{base_url}/", timeout=10)
                root_ok = response.status_code == 200
                self.log_test(
                    "Root Endpoint", root_ok, f"Status: {response.status_code}"
                )
            except Exception as e:
                self.log_test("Root Endpoint", False, f"Error: {e}")

            # Test migration status endpoint
            try:
                response = requests.get(f"{base_url}/api/migration/status", timeout=10)
                status_ok = response.status_code in [
                    200,
                    500,
                ]  # 500 is expected if DB not connected
                self.log_test(
                    "Migration Status Endpoint",
                    status_ok,
                    f"Status: {response.status_code}",
                )
            except Exception as e:
                self.log_test("Migration Status Endpoint", False, f"Error: {e}")

            # Test backup list endpoint
            try:
                response = requests.get(
                    f"{base_url}/api/migration/backup/list", timeout=10
                )
                backup_list_ok = response.status_code in [
                    200,
                    500,
                ]  # 500 is expected if DB not connected
                self.log_test(
                    "Backup List Endpoint",
                    backup_list_ok,
                    f"Status: {response.status_code}",
                )
            except Exception as e:
                self.log_test("Backup List Endpoint", False, f"Error: {e}")

        except ImportError:
            self.log_test("API Endpoints", False, "requests library not available")
        except Exception as e:
            self.log_test("API Endpoints", False, f"Error: {e}")

    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all migration container tests"""
        print("🔒 Testing Migration Container Implementation...")
        print("=" * 60)

        # Create test directories
        for test_dir in self.test_dirs:
            Path(test_dir).mkdir(exist_ok=True)

        # Run test categories
        self.test_dockerfile_exists()
        self.test_docker_compose_integration()
        self.test_configuration_file()
        self.test_cli_commands()
        self.test_api_routes()
        self.test_migration_runner_script()
        self.test_resource_limits()
        self.test_service_discovery()
        await self.test_container_build()
        await self.test_api_endpoints()

        # Calculate results
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["passed"])
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        # Generate report
        report = {
            "test_suite": "Migration Container Implementation",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": success_rate,
            "results": self.test_results,
        }

        # Print summary
        print("\n" + "=" * 60)
        print("📊 MIGRATION CONTAINER TEST RESULTS")
        print("=" * 60)
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {total_tests - passed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")

        if success_rate >= 80:
            print("✅ MIGRATION CONTAINER IMPLEMENTATION SUCCESSFUL!")
        else:
            print("❌ MIGRATION CONTAINER IMPLEMENTATION NEEDS ATTENTION")

        # Save detailed report
        report_path = Path("test_reports/migration_container_test_report.json")
        report_path.parent.mkdir(exist_ok=True)
        with open(report_path, "w") as f:
            json.dump(report, f, indent=2)

        print(f"\n📄 Detailed report saved to: {report_path}")

        return report


async def main():
    """Main test runner"""
    tester = MigrationContainerTester()
    results = await tester.run_all_tests()

    # Exit with appropriate code
    sys.exit(0 if results["success_rate"] >= 80 else 1)


if __name__ == "__main__":
    asyncio.run(main())
