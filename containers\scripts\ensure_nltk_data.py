#!/usr/bin/env python3
"""
Idempotent NLTK data bootstrapper.
Ensures required corpora exist in NLTK_DATA path (defaults to /data/nltk).
"""
import os
import sys
from pathlib import Path

REQUIRED = [
    "wordnet",
    "omw-1.4",
    "punkt",
]


def main() -> int:
    target = Path(os.environ.get("NLTK_DATA", "/data/nltk")).resolve()
    target.mkdir(parents=True, exist_ok=True)

    try:
        import nltk  # type: ignore
    except Exception as e:
        print(f"NLTK not installed? {e}")
        return 0

    os.environ["NLTK_DATA"] = str(target)

    # Verify availability or download
    for pkg in REQUIRED:
        try:
            nltk.data.find(f"corpora/{pkg}")
            print(f"NLTK: '{pkg}' present")
        except LookupError:
            print(f"NLTK: downloading '{pkg}' to {target}")
            nltk.download(pkg, download_dir=str(target), quiet=True)

    return 0


if __name__ == "__main__":
    raise SystemExit(main())

