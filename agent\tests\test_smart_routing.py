#!/usr/bin/env python3
"""
Tests for Smart Task Router and related components

This module provides comprehensive tests for:
1. SmartTaskRouter functionality
2. AgentPerformanceTracker
3. ContextAnalyzer
4. AgentLoadBalancer
5. TaskDistribution
6. Integration with ArchitectAgent
"""

import asyncio
import json
import shutil
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, Mock, patch

import pytest

from agent.core.agents.agent_load_balancer import (
    AgentLoad,
    AgentLoadBalancer,
    LoadBalancingDecision,
    LoadBalancingStrategy,
    LoadStatus,
)
from agent.core.agents.agent_performance_tracker import (
    AgentPerformance,
    AgentPerformanceTracker,
    PerformanceMetric,
    TaskOutcome,
)
from agent.core.agents.context_analyzer import (
    ComplexityFactors,
    ComplexityLevel,
    ContextAnalyzer,
    TaskAnalysis,
    TaskCategory,
)
from agent.core.agents.smart_task_router import (
    RoutingDec<PERSON>,
    RoutingStrategy,
    SmartTaskRouter,
    TaskComplexity,
    TaskContext,
)
from agent.core.agents.task_distribution import (
    AgentCapability,
    DistributionDecision,
    DistributionStrategy,
    TaskDistribution,
    TaskPriority,
)


class TestSmartTaskRouter:
    """Test cases for SmartTaskRouter"""

    @pytest.fixture
    async def smart_router(self):
        """Create a SmartTaskRouter instance for testing"""
        router = SmartTaskRouter()
        yield router
        await router.cleanup()

    @pytest.fixture
    def task_context(self):
        """Create a sample task context"""
        return TaskContext(
            task_id="test_task_001",
            task_type="frontend",
            complexity=TaskComplexity.MODERATE,
            requirements=["react", "typescript", "responsive"],
            constraints=["mobile-first", "accessibility"],
            priority="high",
            estimated_duration=120,
            dependencies=["backend-api"],
            user_preferences={"framework": "react"},
        )

    async def test_register_agent(self, smart_router):
        """Test agent registration"""
        result = await smart_router.register_agent(
            agent_id="test_agent",
            agent_type="frontend",
            capabilities=["react", "typescript", "css"],
            specializations=["user_interface", "responsive_design"],
        )

        assert result is True
        assert "test_agent" in smart_router.agent_capabilities

    async def test_route_task_ai_powered(self, smart_router, task_context):
        """Test AI-powered task routing"""
        # Register test agents
        await smart_router.register_agent(
            "frontend_agent", "frontend", ["react", "typescript"]
        )
        await smart_router.register_agent(
            "backend_agent", "backend", ["api", "database"]
        )

        # Mock AI response
        with patch.object(smart_router.ollama_manager, "generate_response") as mock_ai:
            mock_ai.return_value = """
            {
                "selected_agent_id": "frontend_agent",
                "confidence_score": 0.85,
                "reasoning": "Frontend agent has React and TypeScript capabilities",
                "alternative_agents": ["backend_agent"],
                "estimated_duration": 120
            }
            """

            decision = await smart_router.route_task(task_context)

            assert decision is not None
            assert decision.selected_agent_id == "frontend_agent"
            assert decision.confidence_score == 0.85
            assert decision.strategy_used == RoutingStrategy.AI_POWERED

    async def test_route_task_rule_based(self, smart_router, task_context):
        """Test rule-based task routing"""
        # Register test agents
        await smart_router.register_agent(
            "frontend_agent", "frontend", ["react", "typescript"]
        )
        await smart_router.register_agent(
            "backend_agent", "backend", ["api", "database"]
        )

        # Disable AI routing
        smart_router.ai_routing_enabled = False

        decision = await smart_router.route_task(task_context)

        assert decision is not None
        assert decision.selected_agent_id in ["frontend_agent", "backend_agent"]
        assert decision.strategy_used in [
            RoutingStrategy.HYBRID,
            RoutingStrategy.LOAD_BALANCED,
        ]

    async def test_record_task_outcome(self, smart_router):
        """Test recording task outcomes"""
        await smart_router.register_agent("test_agent", "frontend")

        await smart_router.record_task_outcome(
            task_id="test_task", agent_id="test_agent", success=True, duration=60
        )

        # Verify outcome was recorded
        metrics = await smart_router.get_routing_metrics()
        assert (
            metrics["total_decisions"] >= 0
        )  # May be 0 if no routing decisions made yet


class TestAgentPerformanceTracker:
    """Test cases for AgentPerformanceTracker"""

    @pytest.fixture
    async def performance_tracker(self):
        """Create an AgentPerformanceTracker instance for testing"""
        tracker = AgentPerformanceTracker()
        yield tracker
        await tracker.cleanup()

    async def test_register_agent(self, performance_tracker):
        """Test agent registration"""
        result = await performance_tracker.register_agent("test_agent", "frontend")
        assert result is True
        assert "test_agent" in performance_tracker.agent_performances

    async def test_record_task_outcome(self, performance_tracker):
        """Test recording task outcomes"""
        await performance_tracker.register_agent("test_agent", "frontend")

        await performance_tracker.record_task_outcome(
            agent_id="test_agent", task_id="test_task", success=True, duration=60
        )

        performance = await performance_tracker.get_agent_performance("test_agent")
        assert performance["total_tasks"] == 1
        assert performance["successful_tasks"] == 1
        assert performance["success_rate"] == 1.0

    async def test_calculate_performance_score(self, performance_tracker):
        """Test performance score calculation"""
        await performance_tracker.register_agent("test_agent", "frontend")

        # Record multiple outcomes
        for i in range(10):
            await performance_tracker.record_task_outcome(
                agent_id="test_agent",
                task_id=f"task_{i}",
                success=i < 8,  # 80% success rate
                duration=60,
            )

        score = await performance_tracker.get_agent_score("test_agent")
        assert 0.0 <= score <= 1.0
        assert score > 0.5  # Should be good with 80% success rate


class TestContextAnalyzer:
    """Test cases for ContextAnalyzer"""

    @pytest.fixture
    async def context_analyzer(self):
        """Create a ContextAnalyzer instance for testing"""
        analyzer = ContextAnalyzer()
        yield analyzer

    @pytest.fixture
    def task_context(self):
        """Create a sample task context"""
        return TaskContext(
            task_id="test_task_001",
            task_type="frontend",
            complexity=TaskComplexity.MODERATE,
            requirements=["react", "typescript", "responsive"],
            constraints=["mobile-first", "accessibility"],
            priority="high",
            estimated_duration=120,
            dependencies=["backend-api"],
            user_preferences={"framework": "react"},
        )

    async def test_analyze_complexity(self, context_analyzer, task_context):
        """Test complexity analysis"""
        complexity_score = await context_analyzer.analyze_complexity(task_context)

        assert 0.0 <= complexity_score <= 1.0
        assert complexity_score > 0.0  # Should have some complexity

    async def test_analyze_task(self, context_analyzer, task_context):
        """Test comprehensive task analysis"""
        analysis = await context_analyzer.analyze_task(task_context)

        assert analysis is not None
        assert analysis.task_id == task_context.task_id
        assert analysis.complexity_score > 0.0
        assert analysis.estimated_duration > 0
        assert analysis.analysis_confidence > 0.0

    async def test_extract_complexity_factors(self, context_analyzer, task_context):
        """Test complexity factor extraction"""
        factors = await context_analyzer._extract_complexity_factors(task_context)

        assert factors is not None
        assert isinstance(factors, ComplexityFactors)
        assert factors.lines_of_code_estimate >= 0
        assert factors.dependencies_count >= 0


class TestAgentLoadBalancer:
    """Test cases for AgentLoadBalancer"""

    @pytest.fixture
    async def load_balancer(self):
        """Create an AgentLoadBalancer instance for testing"""
        balancer = AgentLoadBalancer()
        yield balancer
        await balancer.cleanup()

    async def test_register_agent(self, load_balancer):
        """Test agent registration"""
        result = await load_balancer.register_agent("test_agent", "frontend", 10)
        assert result is True
        assert "test_agent" in load_balancer.agent_loads

    async def test_update_agent_load(self, load_balancer):
        """Test agent load updates"""
        await load_balancer.register_agent("test_agent", "frontend")

        result = await load_balancer.update_agent_load(
            agent_id="test_agent", current_load=5, active_tasks=3, queued_tasks=2
        )

        assert result is True

        load_info = await load_balancer.get_agent_load("test_agent")
        assert load_info["current_load"] == 5
        assert load_info["active_tasks"] == 3
        assert load_info["queued_tasks"] == 2

    async def test_select_agent_round_robin(self, load_balancer):
        """Test round-robin agent selection"""
        await load_balancer.register_agent("agent1", "frontend")
        await load_balancer.register_agent("agent2", "backend")

        decision = await load_balancer.select_agent(
            available_agents=["agent1", "agent2"],
            strategy=LoadBalancingStrategy.ROUND_ROBIN,
        )

        assert decision is not None
        assert decision.selected_agent_id in ["agent1", "agent2"]
        assert decision.strategy_used == LoadBalancingStrategy.ROUND_ROBIN

    async def test_select_agent_least_connections(self, load_balancer):
        """Test least connections agent selection"""
        await load_balancer.register_agent("agent1", "frontend")
        await load_balancer.register_agent("agent2", "backend")

        # Set different loads
        await load_balancer.update_agent_load("agent1", current_load=5)
        await load_balancer.update_agent_load("agent2", current_load=2)

        decision = await load_balancer.select_agent(
            available_agents=["agent1", "agent2"],
            strategy=LoadBalancingStrategy.LEAST_CONNECTIONS,
        )

        assert decision is not None
        assert (
            decision.selected_agent_id == "agent2"
        )  # Should select agent with lower load


class TestTaskDistribution:
    """Test cases for TaskDistribution"""

    @pytest.fixture
    async def task_distribution(self):
        """Create a TaskDistribution instance for testing"""
        distribution = TaskDistribution()
        yield distribution
        await distribution.cleanup()

    async def test_register_agent(self, task_distribution):
        """Test agent registration"""
        result = await task_distribution.register_agent(
            agent_id="test_agent",
            agent_type="frontend",
            capabilities=["react", "typescript"],
            specializations=["user_interface"],
        )
        assert result is True
        assert "test_agent" in task_distribution.agent_capabilities

    async def test_distribute_task_round_robin(self, task_distribution):
        """Test round-robin task distribution"""
        await task_distribution.register_agent("agent1", "frontend")
        await task_distribution.register_agent("agent2", "backend")

        decision = await task_distribution.distribute_task(
            task_id="test_task",
            task_type="frontend",
            strategy=DistributionStrategy.ROUND_ROBIN,
        )

        assert decision is not None
        assert decision.selected_agent_id in ["agent1", "agent2"]
        assert decision.strategy_used == DistributionStrategy.ROUND_ROBIN

    async def test_distribute_task_capability_based(self, task_distribution):
        """Test capability-based task distribution"""
        await task_distribution.register_agent(
            "frontend_agent",
            "frontend",
            capabilities=["react", "typescript"],
            specializations=["user_interface"],
        )
        await task_distribution.register_agent(
            "backend_agent",
            "backend",
            capabilities=["api", "database"],
            specializations=["server_architecture"],
        )

        decision = await task_distribution.distribute_task(
            task_id="test_task",
            task_type="frontend",
            requirements=["react", "typescript"],
            strategy=DistributionStrategy.CAPABILITY_BASED,
        )

        assert decision is not None
        assert (
            decision.selected_agent_id == "frontend_agent"
        )  # Should match capabilities
        assert decision.strategy_used == DistributionStrategy.CAPABILITY_BASED


class TestIntegration:
    """Integration tests for smart routing system"""

    @pytest.fixture
    async def integrated_system(self):
        """Create an integrated system for testing"""
        # Create components
        smart_router = SmartTaskRouter()
        performance_tracker = AgentPerformanceTracker()
        context_analyzer = ContextAnalyzer()
        load_balancer = AgentLoadBalancer()
        task_distribution = TaskDistribution()

        # Register agents
        agents = ["frontend_agent", "backend_agent", "container_agent"]
        for agent_id in agents:
            await smart_router.register_agent(agent_id, agent_id)
            await performance_tracker.register_agent(agent_id, agent_id)
            await load_balancer.register_agent(agent_id, agent_id)
            await task_distribution.register_agent(agent_id, agent_id)

        yield {
            "smart_router": smart_router,
            "performance_tracker": performance_tracker,
            "context_analyzer": context_analyzer,
            "load_balancer": load_balancer,
            "task_distribution": task_distribution,
        }

        # Cleanup
        await smart_router.cleanup()
        await performance_tracker.cleanup()
        await load_balancer.cleanup()
        await task_distribution.cleanup()

    async def test_end_to_end_routing(self, integrated_system):
        """Test end-to-end smart routing workflow"""
        smart_router = integrated_system["smart_router"]
        performance_tracker = integrated_system["performance_tracker"]

        # Create task context
        task_context = TaskContext(
            task_id="integration_test_task",
            task_type="frontend",
            complexity=TaskComplexity.MODERATE,
            requirements=["react", "typescript"],
            constraints=["responsive"],
            priority="high",
        )

        # Route task
        routing_decision = await smart_router.route_task(task_context)

        assert routing_decision is not None
        assert routing_decision.selected_agent_id in [
            "frontend_agent",
            "backend_agent",
            "container_agent",
        ]

        # Record outcome
        await smart_router.record_task_outcome(
            task_id=task_context.task_id,
            agent_id=routing_decision.selected_agent_id,
            success=True,
            duration=120,
        )

        # Verify performance tracking
        performance = await performance_tracker.get_agent_performance(
            routing_decision.selected_agent_id
        )
        assert performance["total_tasks"] >= 1

    async def test_learning_and_improvement(self, integrated_system):
        """Test learning and improvement capabilities"""
        smart_router = integrated_system["smart_router"]
        performance_tracker = integrated_system["performance_tracker"]

        # Record multiple outcomes for learning
        for i in range(5):
            await smart_router.record_task_outcome(
                task_id=f"learning_task_{i}",
                agent_id="frontend_agent",
                success=i < 4,  # 80% success rate
                duration=60 + i * 10,
            )

        # Get performance insights
        insights = await performance_tracker.get_performance_insights()

        assert insights["total_agents"] >= 1
        assert insights["average_success_rate"] > 0.0


def run_smart_routing_tests():
    """Run all smart routing tests"""
    import os
    import sys

    # Add project root to path
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root))

    # Run tests
    pytest.main([__file__, "-v"])


if __name__ == "__main__":
    run_smart_routing_tests()
