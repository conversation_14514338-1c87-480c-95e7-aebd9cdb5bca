# SSL & Security Guide

This guide covers the SSL certificate management and security features implemented in Phase 1.4 of the AI Coding Agent project.

## Overview

The AI Coding Agent now includes comprehensive SSL certificate management and security features:

- **SSL Certificate Management**: Automatic Let's Encrypt certificate generation and renewal
- **Security Management**: Rate limiting, IP blocking, security headers, and threat monitoring
- **Home Network Deployment**: Automated setup for home server environments

## SSL Certificate Management

### Features

- **Let's Encrypt Integration**: Automatic certificate generation and renewal
- **Self-Signed Certificates**: Development certificates for local testing
- **Certificate Monitoring**: Expiry tracking and renewal notifications
- **Multi-Domain Support**: Manage certificates for multiple domains

### Configuration

SSL settings are configured in `config/ssl_config.json`:

```json
{
    "cert_dir": "ssl",
    "lets_encrypt": {
        "enabled": true,
        "email": "<EMAIL>",
        "staging": false,
        "webroot_path": "/var/www/html"
    },
    "self_signed": {
        "enabled": false,
        "country": "US",
        "state": "State",
        "locality": "City",
        "organization": "AI Coding Agent",
        "common_name": "localhost"
    },
    "auto_renewal": {
        "enabled": true,
        "days_before_expiry": 30
    }
}
```

### CLI Commands

#### Setup SSL Certificate

```bash
# Let's Encrypt certificate
python -m src.cli.ssl_commands setup example.com --email <EMAIL>

# Self-signed certificate for development
python -m src.cli.ssl_commands setup localhost --self-signed
```

#### Check Certificate Status

```bash
# Check certificate expiry
python -m src.cli.ssl_commands check example.com

# List all certificates
python -m src.cli.ssl_commands list

# Renew certificate
python -m src.cli.ssl_commands renew example.com
```

#### Show Configuration

```bash
python -m src.cli.ssl_commands config-show
```

### Programmatic Usage

```python
from src.ssl_manager import SSLManager

# Initialize SSL manager
ssl_manager = SSLManager("config/ssl_config.json")

# Setup certificate
result = ssl_manager.setup_ssl("example.com", "<EMAIL>")

# Check expiry
expiry = ssl_manager.check_certificate_expiry("example.com")

# List certificates
certificates = ssl_manager.list_certificates()
```

## Security Management

### Features

- **Rate Limiting**: Configurable request limits per IP
- **IP Blocking**: Manual and automatic IP blocking
- **Security Headers**: Comprehensive HTTP security headers
- **Threat Detection**: Suspicious activity monitoring
- **Security Logging**: Detailed security event logging

### Configuration

Security settings are configured in `config/security_config.json`:

```json
{
    "db_path": "security.db",
    "rate_limiting": {
        "enabled": true,
        "requests_per_minute": 60,
        "burst_limit": 100,
        "block_duration_minutes": 15
    },
    "security_headers": {
        "enabled": true,
        "hsts_max_age": 31536000,
        "content_security_policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
        "x_frame_options": "SAMEORIGIN",
        "x_content_type_options": "nosniff",
        "x_xss_protection": "1; mode=block",
        "referrer_policy": "strict-origin-when-cross-origin"
    },
    "ip_whitelist": [],
    "ip_blacklist": [],
    "blocked_user_agents": ["bot", "crawler", "spider", "scraper"],
    "monitoring": {
        "enabled": true,
        "log_suspicious_activity": true,
        "alert_on_attack": true
    }
}
```

### CLI Commands

#### Rate Limiting

```bash
# Check rate limit status
python -m src.cli.security_commands check-rate-limit *************
```

#### IP Management

```bash
# Block IP address
python -m src.cli.security_commands block-ip ************* "Suspicious activity" --duration 60

# Unblock IP address
python -m src.cli.security_commands unblock-ip *************
```

#### Security Statistics

```bash
# Show security statistics
python -m src.cli.security_commands stats --hours 24

# Clean up old data
python -m src.cli.security_commands cleanup --days 30
```

#### Security Headers

```bash
# Show security headers configuration
python -m src.cli.security_commands headers
```

#### Request Validation

```bash
# Validate a request
python -m src.cli.security_commands validate-request ************* "Mozilla/5.0" "/api/users" "GET"
```

### Programmatic Usage

```python
from src.security_manager import SecurityManager

# Initialize security manager
security_manager = SecurityManager("config/security_config.json")

# Check rate limit
rate_limit = security_manager.check_rate_limit("*************")

# Get security headers
headers = security_manager.get_security_headers()

# Validate request
validation = security_manager.validate_request(
    "*************",
    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
    "/api/users",
    "GET"
)

# Block IP
security_manager.block_ip("*************", "Suspicious activity", 60)

# Get statistics
stats = security_manager.get_security_stats(hours=24)
```

## Home Network Deployment

### Features

- **Automated Setup**: Complete home server configuration
- **SSL Integration**: Automatic certificate setup
- **Firewall Configuration**: UFW and iptables support
- **Monitoring Setup**: Log rotation and monitoring scripts
- **Backup System**: Automated backup scripts

### Usage

```bash
# Setup home network for a domain
python scripts/setup_home_network.py example.com --email <EMAIL>

# Setup with custom configuration
python scripts/setup_home_network.py example.com --config config/custom_home_network_config.json
```

### Configuration

Home network settings are configured in `config/home_network_config.json`:

```json
{
    "domain": "localhost",
    "port": 5000,
    "ssl": {
        "enabled": true,
        "lets_encrypt": true,
        "email": ""
    },
    "nginx": {
        "enabled": true,
        "config_path": "/etc/nginx/sites-available",
        "sites_enabled_path": "/etc/nginx/sites-enabled"
    },
    "firewall": {
        "enabled": true,
        "allowed_ports": [22, 80, 443, 5000]
    },
    "monitoring": {
        "enabled": true,
        "log_retention_days": 30
    },
    "backup": {
        "enabled": true,
        "backup_dir": "backups",
        "schedule": "daily"
    }
}
```

## Integration with Web Server

### Nginx Integration

The SSL and security managers integrate with Nginx through the home network deployment script. The script:

1. **Generates Nginx Configuration**: Creates optimized Nginx config with SSL and security headers
2. **Sets Up SSL**: Configures SSL certificates and redirects
3. **Configures Security**: Implements rate limiting and security headers
4. **Enables Monitoring**: Sets up log rotation and monitoring

### Example Nginx Configuration

```nginx
# HTTP to HTTPS redirect
server {
    listen 80;
    server_name example.com;
    return 301 https://$host$request_uri;
}

# HTTPS server with security
server {
    listen 443 ssl http2;
    server_name example.com;

    # SSL Configuration
    ssl_certificate /path/to/ssl/example.com/fullchain.pem;
    ssl_certificate_key /path/to/ssl/example.com/privkey.pem;

    # Security Headers
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header Content-Security-Policy "default-src 'self'" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Rate Limiting
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/s;

    location /api/ {
        limit_req zone=api_limit burst=20 nodelay;
        proxy_pass http://localhost:5000;
    }

    location / {
        proxy_pass http://localhost:5000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
```

## Testing

### Running Tests

```bash
# Run SSL and security tests
pytest tests/test_ssl_security.py -v

# Run specific test classes
pytest tests/test_ssl_security.py::TestSSLManager -v
pytest tests/test_ssl_security.py::TestSecurityManager -v
```

### Test Coverage

The tests cover:

- **SSL Manager**: Certificate creation, expiry checking, renewal
- **Security Manager**: Rate limiting, IP blocking, request validation
- **Integration**: End-to-end SSL and security workflows

## Troubleshooting

### SSL Issues

1. **Certbot Not Found**: Install certbot: `sudo apt install certbot`
2. **Certificate Renewal Fails**: Check webroot path and permissions
3. **Self-Signed Certificate Issues**: Ensure OpenSSL is installed

### Security Issues

1. **Rate Limiting Too Strict**: Adjust `requests_per_minute` in config
2. **False Positives**: Review IP whitelist/blacklist settings
3. **Database Errors**: Check SQLite permissions and disk space

### Deployment Issues

1. **Nginx Configuration Errors**: Run `nginx -t` to test configuration
2. **Firewall Blocking**: Check UFW/iptables rules
3. **Permission Errors**: Ensure script is run with appropriate privileges

## Best Practices

### SSL Security

1. **Use Let's Encrypt**: For production domains
2. **Enable Auto-Renewal**: Set up cron jobs for certificate renewal
3. **Monitor Expiry**: Regular checks for certificate expiration
4. **Secure Private Keys**: Proper file permissions (600)

### Security Hardening

1. **Rate Limiting**: Configure appropriate limits for your use case
2. **IP Management**: Regular review of blocked IPs
3. **Security Headers**: Keep Content Security Policy updated
4. **Monitoring**: Regular review of security logs
5. **Backup**: Regular backups of security databases

### Deployment

1. **Test in Staging**: Use Let's Encrypt staging environment first
2. **Gradual Rollout**: Deploy to subset of users first
3. **Monitoring**: Set up alerts for security events
4. **Documentation**: Keep deployment procedures updated

## Future Enhancements

### Planned Features

1. **Wildcard Certificates**: Support for *.example.com certificates
2. **Advanced Threat Detection**: Machine learning-based threat detection
3. **Security Dashboard**: Web interface for security management
4. **Integration APIs**: REST APIs for external security tools
5. **Compliance Reporting**: GDPR, SOC2 compliance features

### Contributing

To contribute to SSL and security features:

1. Follow the existing code style and patterns
2. Add comprehensive tests for new features
3. Update documentation for any changes
4. Test thoroughly in development environment
5. Submit pull requests with detailed descriptions

## Support

For issues and questions:

1. Check the troubleshooting section above
2. Review the test files for usage examples
3. Check the configuration files for settings
4. Open an issue with detailed error information
5. Include relevant logs and configuration files
