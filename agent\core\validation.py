"""
Core validation module for AI Coding Agent.
Provides base validation classes and registry.
Enhanced with comprehensive validation for all system components and operations.
"""

import json
import logging
import os
import re
import shutil
import subprocess
import sys
from abc import ABC, abstractmethod
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Union

from agent.core.error_handling import ValidationError

logger = logging.getLogger(__name__)


class ValidationLevel(Enum):
    """Validation severity levels"""

    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


@dataclass
class ValidationResult:
    """Result of a validation operation"""

    is_valid: bool = True
    errors: Optional[List[str]] = None
    warnings: Optional[List[str]] = None
    success: bool = True
    level: ValidationLevel = ValidationLevel.INFO
    message: str = ""
    details: Optional[Dict[str, Any]] = None
    timestamp: Optional[datetime] = None

    def __post_init__(self):
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []
        if self.timestamp is None:
            self.timestamp = datetime.now()
        if self.success is None:
            self.success = self.is_valid


class BaseValidator(ABC):
    """Base class for all validators"""

    def __init__(self, name: str, description: str = ""):
        self.name = name
        self.description = description

    def validate(self, data: Any) -> ValidationResult:
        """Validate the given data (synchronous)"""
        raise NotImplementedError

    async def validate_async(self, context: Dict[str, Any]) -> ValidationResult:
        """Validate the given context (asynchronous)"""
        # Default implementation calls synchronous validate
        return self.validate(context)

    def __str__(self) -> str:
        return f"{self.__class__.__name__}({self.name})"


class ValidationRegistry:
    """Registry for validators"""

    def __init__(self):
        self._validators: Dict[str, BaseValidator] = {}

    def register(self, name: str, validator: BaseValidator) -> None:
        """Register a validator"""
        self._validators[name] = validator

    def get(self, name: str) -> Optional[BaseValidator]:
        """Get a validator by name"""
        return self._validators.get(name)

    def validate(self, name: str, data: Any) -> ValidationResult:
        """Validate data using a registered validator"""
        validator = self.get(name)
        if validator is None:
            return ValidationResult(False, [f"Validator '{name}' not found"])
        return validator.validate(data)

    def list_validators(self) -> List[str]:
        """List all registered validator names"""
        return list(self._validators.keys())


# Global validation registry
validation_registry = ValidationRegistry()


# Validation functions - consolidated from cli/validation_proxy.py
def validate_not_none(field: str, value: Any) -> None:
    """Validate that a value is not None"""
    if value is None:
        raise ValidationError(f"{field}: Value cannot be None", field, value, "ERROR")


def validate_positive(field: str, value: int) -> None:
    """Validate that a value is positive"""
    if value <= 0:
        raise ValidationError(
            f"{field}: Value must be positive, got {value}", field, value, "ERROR"
        )


def validate_enum(field: str, value: str, allowed_values: List[str]) -> None:
    """Validate that a value is one of the allowed enum values"""
    if value not in allowed_values:
        raise ValidationError(
            f"{field}: must be one of: {', '.join(allowed_values)}",
            field,
            value,
            "ERROR",
        )


def validate_string_length(
    field: str, value: str, min_length: int = 0, max_length: Optional[int] = None
) -> None:
    """Validate string length constraints"""
    if not isinstance(value, str):
        raise ValidationError(
            f"{field}: Value must be a string, got {type(value)}", field, value, "ERROR"
        )
    if len(value) < min_length:
        raise ValidationError(
            f"{field}: must be at least {min_length} characters", field, value, "ERROR"
        )
    if max_length and len(value) > max_length:
        raise ValidationError(
            f"{field}: String too long (max {max_length} chars)", field, value, "ERROR"
        )


def validate_file_exists(field: str, file_path: str) -> None:
    """Validate that a file exists"""
    path = Path(file_path)
    if not path.exists():
        raise ValidationError(
            f"{field}: File does not exist: {file_path}", field, file_path, "ERROR"
        )
    if not path.is_file():
        raise ValidationError(
            f"{field}: Path is not a file: {file_path}", field, file_path, "ERROR"
        )


def validate_directory_exists(field: str, dir_path: str) -> None:
    """Validate that a directory exists"""
    path = Path(dir_path)
    if not path.exists():
        raise ValidationError(
            f"{field}: Directory does not exist: {dir_path}", field, dir_path, "ERROR"
        )
    if not path.is_dir():
        raise ValidationError(
            f"{field}: Path is not a directory: {dir_path}", field, dir_path, "ERROR"
        )


def validate_url(field: str, url: str) -> None:
    """Validate URL format"""
    if not url.startswith(("http://", "https://")):
        raise ValidationError(
            f"{field}: Invalid URL format: {url}", field, url, "ERROR"
        )


def validate_email(field: str, email: str) -> None:
    """Validate email format"""
    # Basic email validation pattern
    email_pattern = r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"
    if not re.match(email_pattern, email):
        raise ValidationError(
            f"{field}: Invalid email format: {email}", field, email, "ERROR"
        )


def validate_numeric_range(
    field: str,
    value: Union[int, float],
    min_val: Optional[Union[int, float]] = None,
    max_val: Optional[Union[int, float]] = None,
) -> None:
    """Validate numeric value is within specified range"""
    if min_val is not None and value < min_val:
        raise ValidationError(
            f"{field}: must be at least {min_val}", field, value, "ERROR"
        )
    if max_val is not None and value > max_val:
        raise ValidationError(
            f"{field}: must be at most {max_val}", field, value, "ERROR"
        )


def validate_port(field: str, port: int) -> None:
    """Validate port number is within valid range (1-65535)"""
    if port < 1 or port > 65535:
        raise ValidationError(
            f"{field}: Port must be between 1 and 65535, got {port}",
            field,
            port,
            "ERROR",
        )


# Additional validation functions for tests
def validate_config(config: Dict[str, Any]) -> ValidationResult:
    """Validate configuration object"""
    errors = []

    # Check for required database configuration
    if "database" not in config:
        errors.append("Missing database configuration")
    else:
        db_config = config["database"]
        if "host" not in db_config:
            errors.append("Database host is required")
        if "port" not in db_config:
            errors.append("Database port is required")
        if "name" not in db_config:
            errors.append("Database name is required")

    # Check for required API configuration
    if "api" not in config:
        errors.append("Missing API configuration")
    else:
        api_config = config["api"]
        if "host" not in api_config:
            errors.append("API host is required")
        if "port" not in api_config:
            errors.append("API port is required")

    if errors:
        return ValidationResult(
            success=False,
            level=ValidationLevel.ERROR,
            message="Configuration validation failed: missing required fields",
            errors=errors,
        )

    return ValidationResult(
        success=True,
        level=ValidationLevel.INFO,
        message="Configuration validation passed",
    )


def validate_file_path(file_path: str) -> ValidationResult:
    """Validate file path"""
    path = Path(file_path)
    if not path.exists():
        return ValidationResult(
            success=False,
            level=ValidationLevel.ERROR,
            message=f"File does not exist: {file_path}",
            errors=[f"File not found: {file_path}"],
        )
    if not path.is_file():
        return ValidationResult(
            success=False,
            level=ValidationLevel.ERROR,
            message=f"Path is a directory: {file_path}",
            errors=[f"Not a file: {file_path}"],
        )

    return ValidationResult(
        success=True,
        level=ValidationLevel.INFO,
        message=f"File path is valid: {file_path}",
    )


def validate_directory(dir_path: str) -> ValidationResult:
    """Validate directory path"""
    path = Path(dir_path)
    if not path.exists():
        return ValidationResult(
            success=False,
            level=ValidationLevel.ERROR,
            message=f"Directory does not exist: {dir_path}",
            errors=[f"Directory not found: {dir_path}"],
        )
    if not path.is_dir():
        return ValidationResult(
            success=False,
            level=ValidationLevel.ERROR,
            message=f"Path is not a directory: {dir_path}",
            errors=[f"Not a directory: {dir_path}"],
        )

    return ValidationResult(
        success=True,
        level=ValidationLevel.INFO,
        message=f"Directory path is valid: {dir_path}",
    )


def validate_json_schema(
    data: Dict[str, Any], schema: Dict[str, Any]
) -> ValidationResult:
    """Validate JSON data against schema"""
    errors = []

    # Simple schema validation - check if data has all required fields from schema
    for key, expected_type in schema.items():
        if key not in data:
            errors.append(f"Missing required field: {key}")
        elif not isinstance(data[key], type(expected_type)):
            errors.append(
                f"Invalid type for {key}: expected {type(expected_type).__name__}, got {type(data[key]).__name__}"
            )

    if errors:
        return ValidationResult(
            success=False,
            level=ValidationLevel.ERROR,
            message="JSON schema validation failed",
            errors=errors,
        )

    return ValidationResult(
        success=True,
        level=ValidationLevel.INFO,
        message="JSON schema validation passed",
    )


def validate_environment_variables(required_vars: List[str]) -> ValidationResult:
    """Validate that required environment variables are set"""
    missing_vars = []
    empty_vars = []

    for var in required_vars:
        if var not in os.environ:
            missing_vars.append(var)
        elif not os.environ[var]:
            empty_vars.append(var)

    errors = []
    if missing_vars:
        errors.append(
            f"Missing required environment variables: {', '.join(missing_vars)}"
        )
    if empty_vars:
        errors.append(f"Empty environment variables: {', '.join(empty_vars)}")

    if errors:
        return ValidationResult(
            success=False,
            level=ValidationLevel.ERROR,
            message="Environment variables validation failed: " + "; ".join(errors),
            errors=errors,
        )

    return ValidationResult(
        success=True,
        level=ValidationLevel.INFO,
        message="Environment variables validation passed",
    )


def validate_database_connection(config: Dict[str, Any]) -> ValidationResult:
    """Validate database connection"""
    try:
        from sqlalchemy import create_engine

        # Build connection URL from config
        if "host" in config and "port" in config and "database" in config:
            username = config.get("username", "")
            password = config.get("password", "")
            host = config["host"]
            port = config["port"]
            database = config["database"]

            if username and password:
                db_url = f"postgresql://{username}:{password}@{host}:{port}/{database}"
            else:
                db_url = f"postgresql://{host}:{port}/{database}"
        else:
            return ValidationResult(
                success=False,
                level=ValidationLevel.ERROR,
                message="Invalid database configuration: missing required fields",
                errors=["Missing required database configuration fields"],
            )

        engine = create_engine(db_url)
        with engine.connect() as conn:
            from sqlalchemy import text
            conn.execute(text("SELECT 1"))
        return ValidationResult(
            success=True,
            level=ValidationLevel.INFO,
            message="Database connection successful",
        )
    except Exception as e:
        return ValidationResult(
            success=False,
            level=ValidationLevel.ERROR,
            message=f"Database connection failed: {str(e)}",
            errors=[str(e)],
        )


def validate_api_endpoint(url: str) -> ValidationResult:
    """Validate API endpoint"""
    try:
        import requests

        response = requests.get(url, timeout=5)
        if response.status_code == 200:
            return ValidationResult(
                success=True,
                level=ValidationLevel.INFO,
                message="API endpoint is accessible",
            )
        else:
            return ValidationResult(
                success=False,
                level=ValidationLevel.ERROR,
                message=f"API endpoint returned invalid status code: {response.status_code}",
                errors=[f"HTTP {response.status_code}"],
            )
    except Exception as e:
        return ValidationResult(
            success=False,
            level=ValidationLevel.ERROR,
            message=f"API endpoint validation failed: {str(e)}",
            errors=[str(e)],
        )


def validate_security_config(config: Dict[str, Any]) -> ValidationResult:
    """Validate security configuration"""
    errors = []

    # Check SSL configuration
    if config.get("ssl_enabled", False):
        ssl_config = config.get("ssl", {})
        cert_file = ssl_config.get("cert_file") or config.get("ssl_cert_path")
        key_file = ssl_config.get("key_file") or config.get("ssl_key_path")

        if cert_file and not Path(cert_file).exists():
            errors.append(f"SSL certificate file not found: {cert_file}")
        if key_file and not Path(key_file).exists():
            errors.append(f"SSL key file not found: {key_file}")

    # Check timeout configuration
    if "timeout" in config:
        timeout = config["timeout"]
        if not isinstance(timeout, (int, float)) or timeout <= 0:
            errors.append("Timeout must be a positive number")

    if errors:
        return ValidationResult(
            success=False,
            level=ValidationLevel.ERROR,
            message="Security configuration validation failed",
            errors=errors,
        )

    return ValidationResult(
        success=True,
        level=ValidationLevel.INFO,
        message="Security configuration validation passed",
    )


def validate_performance_config(config: Dict[str, Any]) -> ValidationResult:
    """Validate performance configuration"""
    errors = []

    # Check connection pool configuration
    if "max_connections" in config:
        max_conn = config["max_connections"]
        if not isinstance(max_conn, int) or max_conn <= 0:
            errors.append("max_connections must be a positive integer")

    # Check timeout configuration
    if "timeout" in config:
        timeout = config["timeout"]
        if not isinstance(timeout, (int, float)) or timeout <= 0:
            errors.append("timeout must be a positive number")

    if errors:
        return ValidationResult(
            success=False,
            level=ValidationLevel.ERROR,
            message="Performance configuration validation failed: " + "; ".join(errors),
            errors=errors,
        )

    return ValidationResult(
        success=True,
        level=ValidationLevel.INFO,
        message="Performance configuration validation passed",
    )


# Enhanced Validators
class FileSystemValidator(BaseValidator):
    """Validates file system integrity and permissions"""

    def __init__(self):
        super().__init__("file_system", "File system integrity and permissions")

    async def validate_async(self, context: Dict[str, Any]) -> ValidationResult:
        issues = []

        # Check critical directories
        critical_dirs = [
            "core",
            "api",
            "cli",
            "config",
            "data",
            "docs",
            "scripts",
            "tests",
        ]

        for dir_name in critical_dirs:
            dir_path = Path(dir_name)
            if not dir_path.exists():
                issues.append(f"Critical directory missing: {dir_name}")
            elif not dir_path.is_dir():
                issues.append(f"Critical directory is not a directory: {dir_name}")
            elif not os.access(dir_path, os.R_OK | os.W_OK):
                issues.append(f"Insufficient permissions for directory: {dir_name}")

        # Check critical files
        critical_files = ["core/agent.py", "api/main.py", "config/main_config.json"]

        for file_path in critical_files:
            if not os.path.exists(file_path):
                issues.append(f"Critical file missing: {file_path}")
            elif not os.access(file_path, os.R_OK):
                issues.append(f"Cannot read critical file: {file_path}")

        # Check disk space
        disk_usage = shutil.disk_usage(".")
        free_space_gb = disk_usage.free / (1024**3)
        if free_space_gb < 1.0:
            issues.append(f"Low disk space: {free_space_gb:.2f}GB free")

        if issues:
            return ValidationResult(
                is_valid=False,
                success=False,
                level=ValidationLevel.CRITICAL,
                message="File system validation failed",
                details={"issues": issues, "free_space_gb": free_space_gb},
            )
        else:
            return ValidationResult(
                is_valid=True,
                success=True,
                level=ValidationLevel.INFO,
                message="File system validation passed",
                details={"free_space_gb": free_space_gb},
            )


class ConfigurationValidator(BaseValidator):
    """Validates configuration files and settings"""

    def __init__(self):
        super().__init__("configuration", "Configuration files and settings")

    async def validate_async(self, context: Dict[str, Any]) -> ValidationResult:
        issues = []
        warnings = []

        # Check main configuration file
        config_file = "config/main_config.json"
        if not os.path.exists(config_file):
            issues.append(f"Main configuration file missing: {config_file}")
        else:
            try:
                with open(config_file, "r") as f:
                    config = json.load(f)

                # Validate required configuration sections
                required_sections = ["database", "api", "security"]
                for section in required_sections:
                    if section not in config:
                        issues.append(f"Missing configuration section: {section}")
                    elif not isinstance(config[section], dict):
                        issues.append(
                            f"Invalid configuration section format: {section}"
                        )

                # Check for deprecated settings
                deprecated_settings = ["legacy_mode", "old_api_version"]
                for setting in deprecated_settings:
                    if setting in config:
                        warnings.append(f"Deprecated configuration setting: {setting}")

            except json.JSONDecodeError as e:
                issues.append(f"Invalid JSON in configuration file: {e}")
            except Exception as e:
                issues.append(f"Error reading configuration file: {e}")

        if issues:
            return ValidationResult(
                is_valid=False,
                success=False,
                level=ValidationLevel.ERROR,
                message="Configuration validation failed",
                details={"issues": issues, "warnings": warnings},
            )
        else:
            return ValidationResult(
                is_valid=True,
                success=True,
                level=ValidationLevel.INFO,
                message="Configuration validation passed",
                details={"warnings": warnings},
            )


class DependencyValidator(BaseValidator):
    """Validates system dependencies and requirements"""

    def __init__(self):
        super().__init__("dependencies", "System dependencies and requirements")

    async def validate_async(self, context: Dict[str, Any]) -> ValidationResult:
        issues = []
        warnings = []

        # Check Python version
        python_version = sys.version_info
        if python_version < (3, 8):
            issues.append(
                f"Python version too old: {python_version.major}.{python_version.minor} (requires 3.8+)"
            )
        elif python_version < (3, 9):
            warnings.append(
                f"Python version {python_version.major}.{python_version.minor} is supported but 3.9+ recommended"
            )

        # Check required packages
        required_packages = [
            "fastapi",
            "uvicorn",
            "sqlalchemy",
            "pydantic",
            "aiofiles",
            "aiohttp",
        ]

        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)

        if missing_packages:
            issues.append(f"Missing required packages: {', '.join(missing_packages)}")

        # Check optional packages
        optional_packages = ["pytest", "pytest-asyncio", "black", "flake8", "mypy"]

        missing_optional = []
        for package in optional_packages:
            try:
                __import__(package)
            except ImportError:
                missing_optional.append(package)

        if missing_optional:
            warnings.append(f"Missing optional packages: {', '.join(missing_optional)}")

        # Check system tools
        system_tools = ["git", "docker"]
        missing_tools = []
        for tool in system_tools:
            try:
                subprocess.run([tool, "--version"], capture_output=True, check=True)
            except (subprocess.CalledProcessError, FileNotFoundError):
                missing_tools.append(tool)

        if missing_tools:
            warnings.append(f"Missing system tools: {', '.join(missing_tools)}")

        if issues:
            return ValidationResult(
                is_valid=False,
                success=False,
                level=ValidationLevel.ERROR,
                message="Dependency validation failed",
                details={"issues": issues, "warnings": warnings},
            )
        else:
            return ValidationResult(
                is_valid=True,
                success=True,
                level=ValidationLevel.INFO,
                message="Dependency validation passed",
                details={"warnings": warnings},
            )


class ValidationSystem:
    """Comprehensive validation system for the AI Coding Agent"""

    def __init__(self, config: Optional[Dict[str, Any]] = None):
        self.config = config or {}
        self.validators = self._setup_validators()
        self.validation_history: List[ValidationResult] = []

    def _setup_validators(self) -> List[BaseValidator]:
        """Setup all validators"""
        return [FileSystemValidator(), ConfigurationValidator(), DependencyValidator()]

    async def run_full_validation(self) -> Dict[str, Any]:
        """Run full system validation"""
        results = {}
        overall_success = True
        critical_issues = 0
        high_issues = 0
        warnings = 0

        for validator in self.validators:
            try:
                result = await validator.validate_async({})
                results[validator.name] = result
                self.validation_history.append(result)

                if not result.success:
                    overall_success = False
                    if result.level == ValidationLevel.CRITICAL:
                        critical_issues += 1
                    elif result.level == ValidationLevel.ERROR:
                        high_issues += 1

                if result.warnings:
                    warnings += len(result.warnings)

            except Exception as e:
                logger.error(f"Validator {validator.name} failed: {e}")
                error_result = ValidationResult(
                    is_valid=False,
                    success=False,
                    level=ValidationLevel.CRITICAL,
                    message=f"Validator error: {str(e)}",
                )
                results[validator.name] = error_result
                self.validation_history.append(error_result)
                overall_success = False
                critical_issues += 1

        return {
            "overall_success": overall_success,
            "critical_issues": critical_issues,
            "high_issues": high_issues,
            "warnings": warnings,
            "results": results,
            "timestamp": datetime.now().isoformat(),
        }

    async def validate_specific_component(
        self, component_name: str
    ) -> ValidationResult:
        """Validate a specific component"""
        for validator in self.validators:
            if validator.name == component_name:
                result = await validator.validate_async({})
                self.validation_history.append(result)
                return result

        return ValidationResult(
            is_valid=False,
            success=False,
            level=ValidationLevel.ERROR,
            message=f"Unknown component: {component_name}",
        )

    def get_validation_history(self, hours: int = 24) -> List[ValidationResult]:
        """Get validation history for the last N hours"""
        cutoff = datetime.now().timestamp() - (hours * 3600)
        return [r for r in self.validation_history if r.timestamp and r.timestamp.timestamp() > cutoff]

    def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health summary"""
        recent_results = self.get_validation_history(1)  # Last hour

        if not recent_results:
            return {
                "status": "unknown",
                "last_validation": None,
                "issues": 0,
                "warnings": 0,
            }

        latest_result = recent_results[-1]
        total_issues = sum(1 for r in recent_results if not r.success)
        total_warnings = sum(len(r.warnings) for r in recent_results if r.warnings)

        if total_issues == 0:
            status = "healthy"
        elif total_issues <= 2:
            status = "warning"
        else:
            status = "critical"

        return {
            "status": status,
            "last_validation": latest_result.timestamp.isoformat() if latest_result.timestamp else "unknown",
            "issues": total_issues,
            "warnings": total_warnings,
        }


# Global validation system instance
_validation_system = None


async def run_system_validation() -> Dict[str, Any]:
    """Run full system validation"""
    global _validation_system
    if _validation_system is None:
        _validation_system = ValidationSystem()
    return await _validation_system.run_full_validation()


def get_validation_system() -> ValidationSystem:
    """Get the global validation system instance"""
    global _validation_system
    if _validation_system is None:
        _validation_system = ValidationSystem()
    return _validation_system
