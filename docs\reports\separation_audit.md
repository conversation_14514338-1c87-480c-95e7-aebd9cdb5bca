# Agent vs. Project Separation Audit

## Summary

**Critical Issue**: The repository lacks clear separation between AI Coding Agent core code and user project files. There is no top-level `agent/` or `projects/` directory structure.

**Violations Found**: 47 mislocated directories/files
- **Agent code in root**: 32 directories that should be under `agent/`
- **User project files in root**: 15 directories that should be under `projects/`

## Detailed Violations

### Agent Code Mislocated (Should be in `agent/`)

| Current Path | Type | Recommended Target |
|--------------|------|-------------------|
| `api/` | Agent API | `agent/api/` |
| `cli/` | Agent CLI | `agent/cli/` |
| `models/` | Agent AI Models | `agent/models/` |
| `monitoring/` | Agent Monitoring | `agent/monitoring/` |
| `security/` | Agent Security | `agent/security/` |
| `utils/` | Agent Utilities | `agent/utils/` |
| `config/` | Agent Configuration | `agent/config/` |
| `database/` | Agent DB Management | `agent/database/` |
| `db/` | Agent DB Models | `agent/db/` |
| `scripts/` | Agent Scripts | `agent/scripts/` |
| `fine_tuning/` | Agent Model Training | `agent/fine_tuning/` |
| `learning/` | Agent Learning | `agent/learning/` |
| `code_generation/` | Agent Code Gen | `agent/code_generation/` |
| `code_review/` | Agent Code Review | `agent/code_review/` |
| `complex_tasks/` | Agent Task Mgmt | `agent/complex_tasks/` |
| `documentation/` | Agent Doc Gen | `agent/documentation/` |
| `error_detection/` | Agent Error Detection | `agent/error_detection/` |
| `performance/` | Agent Performance | `agent/performance/` |
| `validation/` | Agent Validation | `agent/validation/` |
| `disaster_recovery/` | Agent DR | `agent/disaster_recovery/` |
| `containerization/` | Agent Container Logic | `agent/containerization/` |
| `ai_optimization/` | Agent AI Optimization | `agent/ai_optimization/` |
| `multimodal/` | Agent Multimodal | `agent/multimodal/` |
| `framework_monitoring/` | Agent Framework Mon | `agent/framework_monitoring/` |
| `frontend_optimization/` | Agent Frontend Opt | `agent/frontend_optimization/` |
| `database_optimization/` | Agent DB Optimization | `agent/database_optimization/` |
| `static_analysis/` | Agent Static Analysis | `agent/static_analysis/` |
| `debugger/` | Agent Debugging | `agent/debugger/` |
| `trend_monitoring/` | Agent Trend Monitoring | `agent/trend_monitoring/` |
| `components/` | Agent Dashboard UI | `agent/frontend/components/` |
| `pages/` | Agent Dashboard Pages | `agent/frontend/pages/` |
| `contexts/` | Agent Dashboard Context | `agent/frontend/contexts/` |
| `hooks/` | Agent Dashboard Hooks | `agent/frontend/hooks/` |
| `services/` | Agent Frontend Services | `agent/frontend/services/` |
| `store/` | Agent State Management | `agent/frontend/store/` |
| `styles/` | Agent Dashboard Styles | `agent/frontend/styles/` |
| `types/` | Agent TypeScript Types | `agent/frontend/types/` |
| `lib/` | Agent Frontend Libraries | `agent/frontend/lib/` |
| `dashboard/` | Agent Dashboard Backend | `agent/dashboard/` |
| `tests/` | Agent Tests | `agent/tests/` |
| `docs/` | Agent Documentation | `agent/docs/` |

### User Project Files Mislocated (Should be in `projects/`)

| Current Path | Type | Recommended Target |
|--------------|------|-------------------|
| `sites/` | User Websites | `projects/` |
| `backups/` | User Project Backups | `projects/backups/` |
| `content/` | User CMS Content | `projects/content/` |
| `themes/` | User Website Themes | `projects/themes/` |
| `deployments/` | User Deployments | `projects/deployments/` |
| `exports/` | User Project Exports | `projects/exports/` |
| `dist/` | User Built Projects | `projects/dist/` |
| `uploads/` | User Uploads | `projects/uploads/` |
| `temp/` | User Temp Files | `projects/temp/` |

### Mixed Directories (Need Separation)

| Current Path | Issue | Recommendation |
|--------------|-------|----------------|
| `containers/` | Contains both agent containers and user project containers | Split: `agent/containers/` for agent infra, `projects/{name}/containers/` for user projects |
| `data/` | Contains both agent data and user project data | Split: `agent/data/` for agent data, `projects/data/` for user data |
| `logs/` | Contains both agent logs and user project logs | Split: `agent/logs/` for agent logs, `projects/logs/` for user logs |
| `templates/` | Could be agent or user templates | Evaluate and split appropriately |

## Missing Directory Structure

**No top-level `projects/` folder found** - This is the primary structural issue.

## Recommended Restructure

```
agent/                          # All AI Coding Agent code
├── api/                       # Agent REST API
├── cli/                       # Agent CLI commands  
├── core/                      # Agent core logic (already exists)
├── frontend/                  # Agent dashboard/UI
│   ├── components/
│   ├── pages/
│   ├── contexts/
│   └── ...
├── models/                    # AI models and training
├── monitoring/                # Agent monitoring
├── security/                  # Agent security
├── database/                  # Agent database management
├── containers/                # Agent infrastructure containers
├── data/                      # Agent data and state
├── logs/                      # Agent logs
├── tests/                     # Agent tests
├── docs/                      # Agent documentation
└── config/                    # Agent configuration

projects/                       # All user projects
├── {project-name}/            # Individual user projects
│   ├── src/                   # Project source code
│   ├── dist/                  # Built project
│   ├── containers/            # Project-specific containers
│   ├── backups/               # Project backups
│   ├── content/               # Project content
│   ├── themes/                # Project themes
│   ├── deployments/           # Project deployments
│   └── logs/                  # Project logs
├── shared/                    # Shared project resources
│   ├── templates/             # Project templates
│   └── themes/                # Shared themes
├── backups/                   # Cross-project backups
├── exports/                   # Project exports
└── uploads/                   # User uploads
```

## Next Steps

1. **Create top-level directories**: `agent/` and `projects/`
2. **Move agent code**: Relocate all agent-related directories to `agent/`
3. **Move user projects**: Relocate all user project files to `projects/`
4. **Split mixed directories**: Separate agent and user files in mixed directories
5. **Update import paths**: Fix all import statements after moves
6. **Update configuration**: Update paths in config files, Docker files, etc.
7. **Update documentation**: Reflect new structure in docs

**⚠️ Warning**: This is a major restructure that will require updating many import paths and configuration files. Recommend doing this in phases with comprehensive testing.
