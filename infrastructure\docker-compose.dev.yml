networks:
  ai-coding-network:
    driver: bridge
    driver_opts:
      com.docker.network.bridge.name: ai-coding-dev-bridge
    ipam:
      config:
      - subnet: **********/16
services:
  api:
    build:
      context: ..
      dockerfile: containers/Dockerfile.api
      target: development
    container_name: ai-coding-api-dev
    depends_on:
      db:
        condition: service_healthy
      ollama:
        condition: service_healthy
      redis:
        condition: service_healthy
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    env_file: ../.env
    environment:
      API_URL: ${API_URL:-http://localhost:8000}
      DATABASE_URL: postgresql://${DB_USER:-ai_coding_user}:${DB_PASSWORD:-ai_coding_password}@db:5432/${DB_NAME:-ai_coding_agent_dev}
      ENVIRONMENT: development
      JWT_SECRET: ${JWT_SECRET}
      LOG_LEVEL: DEBUG
      OLLAMA_URL: http://host.docker.internal:11434
      PYTHONPATH: /app
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY}
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      SUPABASE_AUTH_COOKIE_SECURE: ${SUPABASE_AUTH_COOKIE_SECURE:-false}
      SUPABASE_AUTH_ENABLED: ${SUPABASE_AUTH_ENABLED:-false}
      SUPABASE_AUTH_REDIRECT_URL: ${SUPABASE_AUTH_REDIRECT_URL:-http://localhost:3000/auth/callback}
      SUPABASE_DB_URL: ${SUPABASE_DB_URL}
      SUPABASE_REALTIME_CHANNELS: ${SUPABASE_REALTIME_CHANNELS:-chat,notifications,file-updates}
      SUPABASE_REALTIME_ENABLED: ${SUPABASE_REALTIME_ENABLED:-false}
      SUPABASE_SERVICE_ROLE_KEY: ${SUPABASE_SERVICE_ROLE_KEY}
      SUPABASE_STORAGE_BUCKET: ${SUPABASE_STORAGE_BUCKET:-ai-coding-agent-files-dev}
      SUPABASE_STORAGE_ENABLED: ${SUPABASE_STORAGE_ENABLED:-false}
      SUPABASE_URL: ${SUPABASE_URL}
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8000/health
      timeout: 30s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    stdin_open: true
    tty: true
    volumes:
    - ../api:/app/api:rw
    - ../core:/app/core:rw
    - ../cli:/app/cli:rw
    - ../models:/app/models:rw
    - ../security:/app/security:rw
    - ../utils:/app/utils:rw
    - ../scripts:/app/scripts:rw
    - ../config:/app/config:rw
    - ../templates:/app/templates:rw
    - ../db:/app/db:rw
    - ../data:/app/data:rw
    - ../database:/app/database:rw
    - ../content:/app/content:rw
    - ../tests:/app/tests:rw
    - ./data:/app/data:rw
    - ./logs:/app/logs:rw
    - ./backups:/app/backups:rw
    - ./sites:/app/sites:rw
    - ./uploads:/app/uploads:rw
    - ./ssl:/app/ssl:ro
    - ./test_reports:/app/test_reports:rw
    - containers\extracted\docker-compose.dev_api_ports.json:/app/config/ports.json:ro
  db:
    container_name: ai-coding-db-dev
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    environment:
      POSTGRES_DB: ${DB_NAME:-ai_coding_agent_dev}
      POSTGRES_INITDB_ARGS: --encoding=UTF-8
      POSTGRES_PASSWORD: ${DB_PASSWORD:-ai_coding_password}
      POSTGRES_USER: ${DB_USER:-ai_coding_user}
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
      test:
      - CMD-SHELL
      - pg_isready -U ${DB_USER:-ai_coding_user} -d ${DB_NAME:-ai_coding_agent_dev}
      timeout: 10s
    image: postgres:15-alpine
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - pgdata_dev:/var/lib/postgresql/data
    - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    - ./database/migrations:/docker-entrypoint-initdb.d/migrations
    - containers\extracted\docker-compose.dev_db_ports.json:/app/config/ports.json:ro
  db-admin:
    container_name: ai-coding-db-admin
    depends_on:
    - db
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M
    environment:
      ADMINER_DEFAULT_SERVER: db
      ADMINER_DESIGN: pepa-linha-dark
    image: adminer:latest
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - containers\extracted\docker-compose.dev_db-admin_ports.json:/app/config/ports.json:ro
  dev-tools:
    command:
    - tail
    - -f
    - /dev/null
    container_name: ai-coding-dev-tools
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    environment:
      NODE_ENV: development
    image: node:18-alpine
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - ../:/workspace:rw
    - /var/run/docker.sock:/var/run/docker.sock:ro
    - containers\extracted\docker-compose.dev_dev-tools_ports.json:/app/config/ports.json:ro
    working_dir: /workspace
  frontend:
    build:
      context: ..
      dockerfile: containers/Dockerfile.frontend
      target: development
    container_name: ai-coding-frontend-dev
    depends_on:
      api:
        condition: service_healthy
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    env_file: ../.env
    environment:
      NEXT_PUBLIC_API_URL: ${API_URL:-http://localhost:8000}
      NEXT_PUBLIC_SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      NEXT_PUBLIC_SUPABASE_URL: ${SUPABASE_URL}
      NODE_ENV: development
      PORT: '3000'
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:3000/api/health
      timeout: 30s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    stdin_open: true
    tty: true
    volumes:
    - ../components:/app/components:rw
    - ../pages:/app/pages:rw
    - ../public:/app/public:rw
    - ../styles:/app/styles:rw
    - ../contexts:/app/contexts:rw
    - ../hooks:/app/hooks:rw
    - ../lib:/app/lib:rw
    - ../services:/app/services:rw
    - ../store:/app/store:rw
    - ../types:/app/types:rw
    - ../utils:/app/utils:rw
    - ../config/next.config.js:/app/next.config.js:ro
    - ../config/tailwind.config.js:/app/tailwind.config.js:ro
    - ../config/postcss.config.js:/app/postcss.config.js:ro
    - ../config/tsconfig.json:/app/tsconfig.json:ro
    - ../config/package.json:/app/package.json:ro
    - containers\extracted\docker-compose.dev_frontend_ports.json:/app/config/ports.json:ro
  ollama:
    build:
      context: ..
      dockerfile: containers/Dockerfile.ollama.optimized
      target: development
    container_name: ai-coding-ollama-dev
    deploy:
      resources:
        limits:
          cpus: '4.0'
          memory: 8G
        reservations:
          cpus: '2.0'
          devices:
          - capabilities:
            - gpu
            count: 1
            driver: nvidia
            options:
              memory: 4GB
          memory: 4G
    environment:
      CUDA_VISIBLE_DEVICES: '0'
      NVIDIA_DRIVER_CAPABILITIES: compute,utility
      NVIDIA_VISIBLE_DEVICES: '0'
      OLLAMA_HOST: 0.0.0.0
      OLLAMA_ORIGINS: '*'
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 60s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:11434/api/tags
      timeout: 30s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - ./models:/root/.ollama/models
    - ollama_data_dev:/root/.ollama
    - containers\extracted\docker-compose.dev_ollama_ports.json:/app/config/ports.json:ro
  redis:
    command:
    - redis-server
    - --appendonly
    - 'yes'
    - --save
    - '900'
    - '1'
    - --save
    - '300'
    - '10'
    - --save
    - '60'
    - '10000'
    container_name: ai-coding-redis-dev
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 30s
      test:
      - CMD
      - redis-cli
      - ping
      timeout: 10s
    image: redis:7-alpine
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - redis_data_dev:/data
    - containers\extracted\docker-compose.dev_redis_ports.json:/app/config/ports.json:ro
  redis-commander:
    container_name: ai-coding-redis-commander
    depends_on:
    - redis
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M
    environment:
      REDIS_HOSTS: local:redis:6379
    image: rediscommander/redis-commander:latest
    networks:
    - ai-coding-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - containers\extracted\docker-compose.dev_redis-commander_ports.json:/app/config/ports.json:ro
version: '3.8'
volumes:
  ollama_data_dev:
    driver: local
    driver_opts:
      device: ./data/ollama_data_dev
      o: bind
      type: none
  pgdata_dev:
    driver: local
    driver_opts:
      device: ./database/postgres_data_dev
      o: bind
      type: none
  redis_data_dev:
    driver: local
    driver_opts:
      device: ./data/redis_data_dev
      o: bind
      type: none
