# Nginx Configuration for AI Coding Agent

This directory contains the Nginx configuration for the AI Coding Agent application.

## Files

- `ai-coding-agent.conf`: Main Nginx configuration file with SSL and security settings

## Prerequisites

- Nginx installed on your server
- Domain name pointing to your server's IP address
- SSL certificates (recommended: Let's Encrypt)

## Installation Instructions

### 1. Install Nginx

On Ubuntu/Debian:
```bash
sudo apt update
sudo apt install nginx
```

On RHEL/CentOS:
```bash
sudo yum install epel-release
sudo yum install nginx
```

### 2. Install SSL Certificate (Let's Encrypt)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx  # For Ubuntu/Debian
# OR
sudo yum install certbot python3-certbot-nginx  # For RHEL/CentOS

# Get SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com
```

### 3. Install Configuration

1. Copy the configuration file to the appropriate directory:

   On Ubuntu/Debian:
   ```bash
   sudo cp config/nginx/ai-coding-agent.conf /etc/nginx/sites-available/
   sudo ln -s /etc/nginx/sites-available/ai-coding-agent.conf /etc/nginx/sites-enabled/
   ```

   On RHEL/CentOS:
   ```bash
   sudo cp config/nginx/ai-coding-agent.conf /etc/nginx/conf.d/
   ```

2. Update the configuration file with your domain name and paths:
   - Replace `your-domain.com` with your actual domain
   - Update SSL certificate paths if not using Let's Encrypt
   - Update the `proxy_pass` port if your application runs on a different port

3. Test the configuration:
   ```bash
   sudo nginx -t
   ```

4. Reload Nginx:
   ```bash
   sudo systemctl reload nginx
   ```

## Security Considerations

1. **Firewall**: Ensure your firewall allows traffic on ports 80 (HTTP) and 443 (HTTPS)
2. **Updates**: Keep Nginx and your server's OS updated
3. **Monitoring**: Set up log monitoring for security events
4. **Rate Limiting**: Adjust rate limiting settings in the config as needed

## Troubleshooting

- Check Nginx error logs: `sudo tail -f /var/log/nginx/error.log`
- Check Nginx access logs: `sudo tail -f /var/log/nginx/access.log`
- Check if Nginx is running: `sudo systemctl status nginx`
