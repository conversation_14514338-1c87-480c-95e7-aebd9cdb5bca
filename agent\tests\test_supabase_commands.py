"""
Unit tests for Supabase CLI commands.

This module tests the refactored CLI commands including:
- Decorator functionality
- Validation callbacks
- Error handling
- Command execution
- Task tracking
"""

from unittest.mock import MagicMock, Mock, patch

import click
import pytest
from click.testing import C<PERSON><PERSON>unner

from agent.cli.commands.migration import migration
from agent.cli.commands.schema import schema
from agent.cli.commands.supabase import supabase
from db.models import Project, User
from agent.utils.decorators import (
    CliError,
    get_user_context,
    require_project_access,
    validate_migration_sql,
    with_db,
)
from agent.utils.tasks import add_db_task, add_infra_task, add_task, add_typescript_task


class TestDecorators:
    """Test the CLI decorators functionality."""

    def test_with_db_decorator(self):
        """Test that @with_db correctly injects database session into context."""

        @with_db
        def test_func(ctx, project_id):
            # Check that db is stored in context
            assert ctx.obj is not None
            assert "db" in ctx.obj
            assert ctx.obj["db"] is not None
            assert project_id == 123
            return "success"

        ctx = Mock()
        ctx.obj = {}

        with patch("utils.decorators.get_db") as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db

            result = test_func(ctx, 123)
            assert result == "success"
            mock_get_db.assert_called_once()

    def test_require_project_access_success(self):
        """Test that @require_project_access allows access for authorized users."""

        @with_db
        @require_project_access
        def test_func(ctx, project_id):
            # Check that both db and project are stored in context
            assert ctx.obj is not None
            assert "db" in ctx.obj
            assert "project" in ctx.obj
            assert ctx.obj["project"].id == 123
            return "success"

        ctx = Mock()
        ctx.obj = {}

        mock_project = Mock()
        mock_project.id = 123
        mock_project.owner_id = 1

        with patch("utils.decorators.project_manager") as mock_project_manager, patch(
            "utils.decorators.get_user_context"
        ) as mock_get_user:

            mock_project_manager.get.return_value = mock_project
            mock_get_user.return_value = None  # Development mode

            with patch("utils.decorators.get_db") as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__enter__.return_value = mock_db

                result = test_func(ctx, 123)
                assert result == "success"

    def test_require_project_access_project_not_found(self):
        """Test that @require_project_access raises error for non-existent project."""

        @with_db
        @require_project_access
        def test_func(ctx, project_id):
            return "success"

        ctx = Mock()
        ctx.obj = {}

        with patch("utils.decorators.project_manager") as mock_project_manager:
            mock_project_manager.get.return_value = None

            with patch("utils.decorators.get_db") as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__enter__.return_value = mock_db

                with pytest.raises(CliError, match="Project 123 not found"):
                    test_func(ctx, 123)

    def test_require_project_access_unauthorized(self):
        """Test that @require_project_access blocks unauthorized access."""

        @with_db
        @require_project_access
        def test_func(ctx, project_id):
            return "success"

        ctx = Mock()
        ctx.obj = {}

        mock_project = Mock()
        mock_project.id = 123
        mock_project.owner_id = 1

        mock_user = Mock()
        mock_user.id = 2
        mock_user.is_superuser = False

        with patch("utils.decorators.project_manager") as mock_project_manager, patch(
            "utils.decorators.get_user_context"
        ) as mock_get_user:

            mock_project_manager.get.return_value = mock_project
            mock_get_user.return_value = mock_user

            with patch("utils.decorators.get_db") as mock_get_db:
                mock_db = Mock()
                mock_get_db.return_value.__enter__.return_value = mock_db

                with pytest.raises(CliError, match="Access denied"):
                    test_func(ctx, 123)

    def test_validate_migration_sql_with_template_no_sql(self):
        """Test that validate_migration_sql raises error when template used without SQL."""
        ctx = Mock()
        ctx.params = {"template": "test_template"}

        with pytest.raises(click.BadParameter, match="SQL content is required"):
            validate_migration_sql(ctx, None, None)

    def test_validate_migration_sql_with_template_with_sql(self):
        """Test that validate_migration_sql allows SQL when template is provided."""
        ctx = Mock()
        ctx.params = {"template": "test_template"}

        result = validate_migration_sql(ctx, None, "SELECT * FROM users")
        assert result == "SELECT * FROM users"

    def test_validate_migration_sql_no_template(self):
        """Test that validate_migration_sql allows no SQL when no template."""
        ctx = Mock()
        ctx.params = {}

        result = validate_migration_sql(ctx, None, None)
        assert result is None


class TestTaskTracking:
    """Test the task tracking functionality."""

    @patch("complex_tasks.ComplexTaskManager")
    def test_add_task_success(self, mock_task_manager):
        """Test successful task addition."""
        mock_instance = Mock()
        mock_task_manager.return_value = mock_instance

        add_task("Test task", "test_agent", "high")

        mock_instance.add.assert_called_once_with(
            description="Test task", assignee="test_agent", priority="high"
        )

    @patch("complex_tasks.ComplexTaskManager")
    def test_add_task_exception_handling(self, mock_task_manager):
        """Test task addition with exception handling."""
        mock_task_manager.side_effect = Exception("Task manager error")

        # Should not raise exception, just log warning
        add_task("Test task")

    def test_add_infra_task(self):
        """Test infrastructure task addition."""
        with patch("utils.tasks.add_task") as mock_add_task:
            add_infra_task("Test infra task")
            mock_add_task.assert_called_once_with(
                "Test infra task", assignee="infra_agent", priority="medium"
            )

    def test_add_db_task(self):
        """Test database task addition."""
        with patch("utils.tasks.add_task") as mock_add_task:
            add_db_task("Test db task")
            mock_add_task.assert_called_once_with(
                "Test db task", assignee="db_agent", priority="medium"
            )

    def test_add_typescript_task(self):
        """Test TypeScript task addition."""
        with patch("utils.tasks.add_task") as mock_add_task:
            add_typescript_task("Test ts task")
            mock_add_task.assert_called_once_with(
                "Test ts task", assignee="typescript_agent", priority="medium"
            )


class TestSupabaseCommands:
    """Test the Supabase command group."""

    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()

    @patch("utils.decorators.project_manager")
    @patch("cli.commands.supabase.supabase_config_manager")
    @patch("cli.commands.supabase.project_manager")
    @patch("cli.commands.supabase.add_infra_task")
    def test_link_command_success(
        self,
        mock_add_task,
        mock_project_manager,
        mock_config_manager,
        mock_decorator_project_manager,
    ):
        """Test successful project linking."""
        mock_project = Mock()
        mock_project.id = 123
        mock_project.owner_id = 1

        # Mock both the decorator and command project managers
        mock_decorator_project_manager.get.return_value = mock_project
        mock_project_manager.get.return_value = mock_project
        mock_config_manager.get_by_project.return_value = None
        mock_config_manager.create.return_value = Mock()

        with patch("utils.decorators.get_db") as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db

            result = self.runner.invoke(
                supabase,
                [
                    "link",
                    "123",
                    "--project-url",
                    "https://test.supabase.co",
                    "--api-key",
                    "test-key",
                ],
            )

            assert result.exit_code == 0
            assert "Project successfully linked to Supabase" in result.output
            mock_add_task.assert_called_once()

    @patch("utils.decorators.project_manager")
    @patch("cli.commands.supabase.supabase_config_manager")
    @patch("cli.commands.supabase.project_manager")
    def test_link_command_project_not_found(
        self, mock_project_manager, mock_config_manager, mock_decorator_project_manager
    ):
        """Test linking with non-existent project."""
        # Mock both project managers to return None
        mock_decorator_project_manager.get.return_value = None
        mock_project_manager.get.return_value = None

        with patch("utils.decorators.get_db") as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db

            result = self.runner.invoke(
                supabase,
                [
                    "link",
                    "999",
                    "--project-url",
                    "https://test.supabase.co",
                    "--api-key",
                    "test-key",
                ],
            )

            assert result.exit_code != 0
            assert "Project 999 not found" in result.output


class TestMigrationCommands:
    """Test the Migration command group."""

    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()

    @patch("utils.decorators.project_manager")
    @patch("cli.commands.migration.database_migration_manager")
    @patch("cli.commands.migration.MigrationFileManager")
    @patch("cli.commands.migration.add_db_task")
    def test_create_migration_success(
        self,
        mock_add_task,
        mock_migration_manager,
        mock_db_manager,
        mock_decorator_project_manager,
    ):
        """Test successful migration creation."""
        mock_project = Mock()
        mock_project.id = 123
        mock_project.path = "/test/path"

        # Mock the decorator project manager
        mock_decorator_project_manager.get.return_value = mock_project

        mock_file_manager = Mock()
        mock_file_manager.create_custom_migration.return_value = (
            True,
            "/test/migration.sql",
        )
        mock_migration_manager.return_value = mock_file_manager

        mock_db_manager.create.return_value = Mock()

        with patch("utils.decorators.get_db") as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db

            result = self.runner.invoke(
                migration,
                [
                    "create",
                    "123",
                    "test_migration",
                    "--sql",
                    "CREATE TABLE test (id SERIAL PRIMARY KEY);",
                ],
            )

            assert result.exit_code == 0
            assert "Migration 'test_migration' created successfully" in result.output
            mock_add_task.assert_called_once()

    def test_create_migration_missing_sql_with_template(self):
        """Test migration creation with template but no SQL raises error."""
        result = self.runner.invoke(
            migration,
            ["create", "123", "test_migration", "--template", "test_template"],
        )

        assert result.exit_code != 0
        assert "SQL content is required when using a template" in result.output

    @patch("utils.decorators.project_manager")
    def test_create_migration_missing_sql_no_template(
        self, mock_decorator_project_manager
    ):
        """Test migration creation without SQL and no template raises error."""
        mock_project = Mock()
        mock_project.id = 123
        mock_project.path = "/test/path"

        # Mock the decorator project manager
        mock_decorator_project_manager.get.return_value = mock_project

        with patch("utils.decorators.get_db") as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db

            result = self.runner.invoke(migration, ["create", "123", "test_migration"])

            assert result.exit_code != 0
            assert "SQL content is required" in result.output


class TestSchemaCommands:
    """Test the Schema command group."""

    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()

    @patch("utils.decorators.project_manager")
    @patch("cli.commands.schema.SupabaseCLI")
    @patch("cli.commands.schema.supabase_config_manager")
    @patch("cli.commands.schema.add_typescript_task")
    def test_generate_types_success(
        self,
        mock_add_task,
        mock_config_manager,
        mock_supabase_cli,
        mock_decorator_project_manager,
    ):
        """Test successful type generation."""
        mock_project = Mock()
        mock_project.id = 123
        mock_project.path = "/test/path"

        # Mock the decorator project manager
        mock_decorator_project_manager.get.return_value = mock_project

        mock_config = Mock()
        mock_config_manager.get_by_project.return_value = mock_config

        mock_cli_instance = Mock()
        mock_cli_instance.generate_types.return_value = Mock(success=True)
        mock_supabase_cli.return_value = mock_cli_instance

        with patch("utils.decorators.get_db") as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db

            result = self.runner.invoke(schema, ["generate-types", "123"])

            assert result.exit_code == 0
            assert "TypeScript types generated successfully" in result.output
            mock_add_task.assert_called_once()

    @patch("utils.decorators.project_manager")
    @patch("cli.commands.schema.supabase_config_manager")
    def test_sync_schema_not_linked(
        self, mock_config_manager, mock_decorator_project_manager
    ):
        """Test schema sync with unlinked project."""
        mock_project = Mock()
        mock_project.id = 123
        mock_project.path = "/test/path"

        # Mock the decorator project manager
        mock_decorator_project_manager.get.return_value = mock_project

        mock_config_manager.get_by_project.return_value = None

        with patch("utils.decorators.get_db") as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db

            result = self.runner.invoke(schema, ["sync", "123"])

            assert result.exit_code != 0
            assert "Project is not linked to Supabase" in result.output


class TestIntegration:
    """Integration tests for the complete CLI system."""

    def setup_method(self):
        """Set up test fixtures."""
        self.runner = CliRunner()

    @patch("utils.decorators.project_manager")
    @patch("cli.commands.supabase.add_infra_task")
    @patch("cli.commands.supabase.supabase_config_manager")
    @patch("cli.commands.supabase.project_manager")
    def test_full_workflow(
        self,
        mock_project_manager,
        mock_config_manager,
        mock_add_task,
        mock_decorator_project_manager,
    ):
        """Test a complete workflow from linking to migration creation."""
        # Setup mocks
        mock_project = Mock()
        mock_project.id = 123
        mock_project.owner_id = 1

        # Mock both project managers
        mock_decorator_project_manager.get.return_value = mock_project
        mock_project_manager.get.return_value = mock_project
        mock_config_manager.get_by_project.return_value = None
        mock_config_manager.create.return_value = Mock()

        with patch("utils.decorators.get_db") as mock_get_db:
            mock_db = Mock()
            mock_get_db.return_value.__enter__.return_value = mock_db

            # Test link command
            result = self.runner.invoke(
                supabase,
                [
                    "link",
                    "123",
                    "--project-url",
                    "https://test.supabase.co",
                    "--api-key",
                    "test-key",
                ],
            )

            assert result.exit_code == 0
            assert "Project successfully linked to Supabase" in result.output

            # Verify task was added
            mock_add_task.assert_called_once()
            call_args = mock_add_task.call_args[0]
            assert "Review Supabase link for project 123" in call_args[0]
