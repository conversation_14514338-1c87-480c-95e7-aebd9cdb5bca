import os
import sys

import pytest

# Test file for check_compliance

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_check_compliance_imports():
    """Test that check_compliance module imports correctly."""
    try:
        import agent.scripts.check_compliance as check_compliance
        assert hasattr(check_compliance, 'main')
        assert callable(check_compliance.main)
    except ImportError as e:
        pytest.fail(f"Failed to import check_compliance: {e}")


def test_check_compliance_main_function():
    """Test that main function executes without errors."""
    try:
        import agent.scripts.check_compliance as check_compliance
        result = check_compliance.main()
        assert isinstance(result, dict)
        assert 'compliance_score' in result
        assert 'status' in result
        assert isinstance(result['compliance_score'], (int, float))
    except Exception as e:
        pytest.fail(f"Main function failed: {e}")


def test_check_compliance_enforcer_creation():
    """Test that CursorRulesEnforcer can be created."""
    try:
        from agent.core.cursor_rules_enforcer import CursorRulesEnforcer
        enforcer = CursorRulesEnforcer()
        assert enforcer is not None
        assert hasattr(enforcer, 'check_compliance')
    except ImportError as e:
        pytest.fail(f"Failed to import CursorRulesEnforcer: {e}")
    except Exception as e:
        pytest.fail(f"Failed to create enforcer: {e}")
