networks:
  ai-coding-test-network:
    driver: bridge
services:
  test-api:
    build:
      context: .
      dockerfile: containers/Dockerfile.api
      target: development
    command:
    - uvicorn
    - api.main:app
    - --host
    - 0.0.0.0
    - --port
    - '8000'
    - --reload
    container_name: ai-coding-test-api
    depends_on:
    - test-db
    - test-redis
    - test-ollama
    env_file: ../.env
    environment:
      API_URL: http://localhost:8000
      DATABASE_URL: postgresql://${DB_USER:-ai_coding_user}:${DB_PASSWORD:-ai_coding_password}@test-db:5432/${DB_NAME:-ai_coding_agent}_test
      ENVIRONMENT: test
      LOG_LEVEL: DEBUG
      OLLAMA_URL: http://test-ollama:11434
      PYTHONPATH: /app
      REDIS_URL: redis://test-redis:6379
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8000/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-test-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - .:/app
    - /app/node_modules
    - /app/__pycache__
    - /app/.pytest_cache
    - containers\extracted\docker-compose.test_test-api_ports.json:/app/config/ports.json:ro
  test-db:
    container_name: ai-coding-test-db
    environment:
      POSTGRES_DB: ${DB_NAME:-ai_coding_agent}_test
      POSTGRES_INITDB_ARGS: --encoding=UTF-8
      POSTGRES_PASSWORD: ${DB_PASSWORD:-ai_coding_password}
      POSTGRES_USER: ${DB_USER:-ai_coding_user}
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
    # logging added for rotation
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
      test:
      - CMD-SHELL
      - pg_isready -U ${DB_USER:-ai_coding_user} -d ${DB_NAME:-ai_coding_agent}_test
      timeout: 10s
    image: postgres:15-alpine
    networks:
    - ai-coding-test-network
    restart: unless-stopped
    volumes:
    - test_pgdata:/var/lib/postgresql/data
    - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    - ./database/migrations:/docker-entrypoint-initdb.d/migrations
  test-ollama:
    build:
      context: .
      dockerfile: ollama/Dockerfile
    container_name: ai-coding-test-ollama
    environment:
      OLLAMA_HOST: 0.0.0.0
      OLLAMA_ORIGINS: '*'
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 60s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:11434/api/tags
      timeout: 30s
    networks:
    - ai-coding-test-network
    ports: '# Placeholder: moved config to /app/config/ports.json'
    restart: unless-stopped
    volumes:
    - ./models:/root/.ollama/models
    - test_ollama_data:/root/.ollama
    - containers\extracted\docker-compose.test_test-ollama_ports.json:/app/config/ports.json:ro
  test-redis:
    command:
    - redis-server
    - --appendonly
    - 'yes'
    - --loglevel
    - verbose
    container_name: ai-coding-test-redis
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 30s
      test:
      - CMD
      - redis-cli
      - ping
      timeout: 10s
    image: redis:7-alpine
    networks:
    - ai-coding-test-network
    restart: unless-stopped
    volumes:
    - test_redis_data:/data
  test-runner:
    build:
      context: .
      dockerfile: containers/Dockerfile.api
      target: development
    command:
    - tail
    - -f
    - /dev/null
    container_name: ai-coding-test-runner
    depends_on:
      test-api:
        condition: service_healthy
      test-db:
        condition: service_healthy
      test-ollama:
        condition: service_healthy
      test-redis:
        condition: service_healthy
    env_file: ../.env
    environment:
      DATABASE_URL: postgresql://${DB_USER:-ai_coding_user}:${DB_PASSWORD:-ai_coding_password}@test-db:5432/${DB_NAME:-ai_coding_agent}_test
      ENVIRONMENT: test
      LOG_LEVEL: DEBUG
      OLLAMA_URL: http://test-ollama:11434
      PYTHONPATH: /app
      REDIS_URL: redis://test-redis:6379
    networks:
    - ai-coding-test-network
    profiles:
    - test
    restart: unless-stopped
    volumes:
    - .:/app
    - /app/node_modules
    - /app/__pycache__
    - /app/.pytest_cache
version: '3.8'
volumes:
  test_ollama_data: null
  test_pgdata: null
  test_redis_data: null
