#!/usr/bin/env python3
"""
Simple Phase 22 Test: Complex Code Tasks

This script demonstrates the basic functionality of Phase 22
without complex import dependencies.
"""

import asyncio
import json
import sys
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict, List

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


def test_phase_22_components():
    """Test the basic components of Phase 22"""
    print("🚀 Phase 22 Simple Test: Complex Code Tasks")
    print("=" * 50)

    try:
        # Test 1: Import complex_tasks module
        print("\n📦 Testing imports...")
        from complex_tasks import (
            ComplexTask,
            ProgressReport,
            QualityMetrics,
            ResourceAllocation,
            TaskComplexity,
            TaskStatus,
            TaskType,
        )

        print("✅ All imports successful")

        # Test 2: Create data models
        print("\n🏗️  Testing data models...")

        # Create resource allocation
        resource_allocation = ResourceAllocation(
            cpu_cores=4, memory_gb=8, gpu_required=False, estimated_duration_hours=12.0
        )
        print(
            f"✅ Resource allocation created: {resource_allocation.cpu_cores} cores, {resource_allocation.memory_gb}GB RAM"
        )

        # Create quality metrics
        quality_metrics = QualityMetrics(
            code_quality_score=85.0,
            performance_improvement=25.0,
            test_coverage=90.0,
            maintainability_score=88.0,
            security_score=92.0,
            documentation_quality=87.0,
        )
        overall_score = quality_metrics.calculate_overall_score()
        print(f"✅ Quality metrics created: Overall score {overall_score:.1f}/100")

        # Create progress report
        progress_report = ProgressReport(
            task_id="test-task-001",
            progress_percentage=75.0,
            current_phase="Implementation",
            status=TaskStatus.IN_PROGRESS,
        )
        print(
            f"✅ Progress report created: {progress_report.progress_percentage}% complete"
        )

        # Create complex task
        task = ComplexTask(
            task_id="test-task-001",
            title="Test Architecture Design",
            description="Test task for Phase 22 demonstration",
            task_type=TaskType.ARCHITECTURE_DESIGN,
            complexity=TaskComplexity.COMPLEX,
            resource_allocation=resource_allocation,
            requirements=["Scalable design", "High availability", "Security"],
            constraints=["Budget limit", "Time constraint"],
            priority=8,
            tags=["test", "architecture", "phase22"],
        )
        print(f"✅ Complex task created: {task.title}")

        # Test 3: Test task methods
        print("\n🔧 Testing task methods...")

        # Add progress report
        task.add_progress_report(progress_report)
        print(f"✅ Progress report added to task")

        # Calculate completion percentage
        completion = task.calculate_completion_percentage()
        print(f"✅ Task completion: {completion}%")

        # Check if overdue
        is_overdue = task.is_overdue()
        print(f"✅ Task overdue: {is_overdue}")

        # Test 4: Test serialization
        print("\n💾 Testing serialization...")

        # Convert to dict
        task_dict = task.to_dict()
        print(f"✅ Task converted to dictionary: {len(task_dict)} fields")

        # Convert back from dict
        restored_task = ComplexTask.from_dict(task_dict)
        print(f"✅ Task restored from dictionary: {restored_task.title}")

        # Test 5: Test resource allocation serialization
        resource_dict = resource_allocation.to_dict()
        restored_resource = ResourceAllocation.from_dict(resource_dict)
        print(
            f"✅ Resource allocation serialization: {restored_resource.cpu_cores} cores"
        )

        # Test 6: Test quality metrics serialization
        quality_dict = quality_metrics.to_dict()
        restored_quality = QualityMetrics.from_dict(quality_dict)
        print(
            f"✅ Quality metrics serialization: {restored_quality.calculate_overall_score():.1f} score"
        )

        print("\n🎉 All Phase 22 component tests passed!")

        # Show summary
        print("\n📊 Summary:")
        print(f"   Task Types Available: {len(TaskType)}")
        print(f"   Complexity Levels: {len(TaskComplexity)}")
        print(f"   Status Types: {len(TaskStatus)}")
        print(f"   Task Created: {task.task_id}")
        print(
            f"   Resource Allocation: {resource_allocation.cpu_cores} cores, {resource_allocation.memory_gb}GB"
        )
        print(f"   Quality Score: {overall_score:.1f}/100")
        print(f"   Progress: {completion}%")

        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_configuration():
    """Test configuration loading"""
    print("\n⚙️  Testing configuration...")

    try:
        config_path = (
            Path(__file__).parent.parent / "config" / "complex_tasks_config.json"
        )

        if config_path.exists():
            with open(config_path, "r") as f:
                config_data = json.load(f)

            complex_tasks_config = config_data.get("complex_tasks", {})

            print(f"✅ Configuration loaded: {len(complex_tasks_config)} sections")

            # Show some config details
            if "general" in complex_tasks_config:
                general = complex_tasks_config["general"]
                print(
                    f"   Max concurrent tasks: {general.get('max_concurrent_tasks', 'N/A')}"
                )
                print(
                    f"   Default timeout: {general.get('default_timeout_seconds', 'N/A')} seconds"
                )

            if "starcoder2" in complex_tasks_config:
                starcoder2 = complex_tasks_config["starcoder2"]
                print(f"   Model: {starcoder2.get('model_name', 'N/A')}")
                print(f"   Max tokens: {starcoder2.get('max_tokens', 'N/A')}")

            return True
        else:
            print(f"⚠️  Configuration file not found: {config_path}")
            return False

    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


def main():
    """Main test function"""
    print("🧪 Phase 22 Simple Test Suite")
    print("=" * 40)

    # Test 1: Component functionality
    components_ok = test_phase_22_components()

    # Test 2: Configuration
    config_ok = test_configuration()

    # Summary
    print("\n" + "=" * 40)
    print("📋 Test Results:")
    print(f"   Components: {'✅ PASS' if components_ok else '❌ FAIL'}")
    print(f"   Configuration: {'✅ PASS' if config_ok else '❌ FAIL'}")

    if components_ok and config_ok:
        print("\n🎉 All tests passed! Phase 22 is ready.")
    else:
        print("\n⚠️  Some tests failed. Please check the implementation.")

    print("\n📚 Phase 22 Features Demonstrated:")
    print("   • Complex task data models")
    print("   • Resource allocation management")
    print("   • Quality metrics calculation")
    print("   • Progress tracking")
    print("   • Task serialization/deserialization")
    print("   • Configuration management")
    print("   • Architecture design tasks")
    print("   • System integration tasks")
    print("   • Performance optimization tasks")
    print("   • Complex problem solving tasks")


if __name__ == "__main__":
    main()
