#!/usr/bin/env python3
"""
Test Conversation Requirements Integration
Verifies that realistic conversation requirements are properly integrated into all rule files
"""

import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))


def test_rule_files_integration():
    """Test that conversation requirements are integrated into all rule files"""
    
    print("🧪 Testing Conversation Requirements Integration")
    print("=" * 60)
    
    rule_files = [
        ".augment/rules/imported/cursorrules.md",
        ".augment/rules/imported/prompt.md", 
        ".augment/rules/imported/website-containerization.md",
        ".augment/rules/imported/realistic-conversation-requirements.md"
    ]
    
    test_results = {
        "files_exist": 0,
        "conversation_keywords": 0,
        "mandatory_markers": 0,
        "llm_agnostic": 0
    }
    
    conversation_keywords = [
        "realistic", "conversation", "natural", "conversational", 
        "colleague", "enthusiasm", "uncertainty", "analogies"
    ]
    
    mandatory_markers = [
        "MANDATORY", "ALWAYS", "NEVER", "CRIT<PERSON>AL"
    ]
    
    llm_references = [
        "GPT", "<PERSON>", "Llama", "Gemini", "LLM", "regardless"
    ]
    
    print("📋 Checking Rule Files:")
    print("-" * 30)
    
    for file_path in rule_files:
        print(f"\n   📄 {file_path}")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read().lower()
            
            print(f"   ✅ File exists and readable")
            test_results["files_exist"] += 1
            
            # Check for conversation keywords
            found_keywords = [kw for kw in conversation_keywords if kw in content]
            if found_keywords:
                print(f"   ✅ Conversation keywords found: {', '.join(found_keywords[:3])}...")
                test_results["conversation_keywords"] += 1
            else:
                print(f"   ❌ No conversation keywords found")
            
            # Check for mandatory markers
            found_mandatory = [marker for marker in mandatory_markers if marker.lower() in content]
            if found_mandatory:
                print(f"   ✅ Mandatory markers found: {', '.join(found_mandatory[:2])}...")
                test_results["mandatory_markers"] += 1
            else:
                print(f"   ❌ No mandatory markers found")
            
            # Check for LLM-agnostic references
            found_llm_refs = [ref for ref in llm_references if ref.lower() in content]
            if found_llm_refs:
                print(f"   ✅ LLM-agnostic references found: {', '.join(found_llm_refs[:2])}...")
                test_results["llm_agnostic"] += 1
            else:
                print(f"   ⚠️ No LLM-agnostic references found")
                
        except FileNotFoundError:
            print(f"   ❌ File not found")
        except Exception as e:
            print(f"   ❌ Error reading file: {e}")
    
    return test_results


def test_comprehensive_coverage():
    """Test that the comprehensive requirements document covers all necessary aspects"""
    
    print(f"\n🔍 Testing Comprehensive Coverage")
    print("-" * 40)
    
    requirements_file = ".augment/rules/imported/realistic-conversation-requirements.md"
    
    required_sections = [
        "Core Conversation Principles",
        "Conversation Examples", 
        "Specific Scenarios",
        "Communication Anti-Patterns",
        "LLM-Agnostic Implementation",
        "Quality Metrics",
        "Implementation Checklist"
    ]
    
    coverage_results = {
        "sections_found": 0,
        "examples_provided": False,
        "anti_patterns_defined": False,
        "metrics_defined": False
    }
    
    try:
        with open(requirements_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        print(f"   📄 Checking: {requirements_file}")
        
        # Check for required sections
        for section in required_sections:
            if section in content:
                print(f"   ✅ Section found: {section}")
                coverage_results["sections_found"] += 1
            else:
                print(f"   ❌ Section missing: {section}")
        
        # Check for examples
        if "❌ WRONG" in content and "✅ RIGHT" in content:
            print(f"   ✅ Good/bad examples provided")
            coverage_results["examples_provided"] = True
        else:
            print(f"   ❌ Examples missing or incomplete")
        
        # Check for anti-patterns
        if "Never Do These" in content or "Anti-Patterns" in content:
            print(f"   ✅ Anti-patterns defined")
            coverage_results["anti_patterns_defined"] = True
        else:
            print(f"   ❌ Anti-patterns not clearly defined")
        
        # Check for metrics
        if "Quality Metrics" in content or "Success Indicators" in content:
            print(f"   ✅ Quality metrics defined")
            coverage_results["metrics_defined"] = True
        else:
            print(f"   ❌ Quality metrics missing")
            
    except Exception as e:
        print(f"   ❌ Error checking comprehensive coverage: {e}")
    
    return coverage_results


def test_integration_completeness():
    """Test that all development rules reference conversation requirements"""
    
    print(f"\n🔗 Testing Integration Completeness")
    print("-" * 40)
    
    integration_checks = {
        "cursor_rules_updated": False,
        "prompt_updated": False,
        "containerization_updated": False,
        "comprehensive_doc_exists": False
    }
    
    # Check cursor rules
    try:
        with open(".augment/rules/imported/cursorrules.md", 'r', encoding='utf-8') as f:
            cursor_content = f.read()
        
        if "Architect Agent Realistic Conversation" in cursor_content and "MANDATORY" in cursor_content:
            print(f"   ✅ Cursor rules updated with conversation requirements")
            integration_checks["cursor_rules_updated"] = True
        else:
            print(f"   ❌ Cursor rules missing conversation requirements")
            
    except Exception as e:
        print(f"   ❌ Error checking cursor rules: {e}")
    
    # Check prompt file
    try:
        with open(".augment/rules/imported/prompt.md", 'r', encoding='utf-8') as f:
            prompt_content = f.read()
        
        if "Realistic Conversation" in prompt_content and "MANDATORY" in prompt_content:
            print(f"   ✅ Prompt file updated with conversation requirements")
            integration_checks["prompt_updated"] = True
        else:
            print(f"   ❌ Prompt file missing conversation requirements")
            
    except Exception as e:
        print(f"   ❌ Error checking prompt file: {e}")
    
    # Check containerization policy
    try:
        with open(".augment/rules/imported/website-containerization.md", 'r', encoding='utf-8') as f:
            container_content = f.read()
        
        if "Communication Requirements" in container_content:
            print(f"   ✅ Containerization policy updated with conversation requirements")
            integration_checks["containerization_updated"] = True
        else:
            print(f"   ❌ Containerization policy missing conversation requirements")
            
    except Exception as e:
        print(f"   ❌ Error checking containerization policy: {e}")
    
    # Check comprehensive document
    try:
        with open(".augment/rules/imported/realistic-conversation-requirements.md", 'r', encoding='utf-8') as f:
            comprehensive_content = f.read()
        
        if len(comprehensive_content) > 1000:  # Should be substantial
            print(f"   ✅ Comprehensive conversation requirements document exists")
            integration_checks["comprehensive_doc_exists"] = True
        else:
            print(f"   ❌ Comprehensive document too short or incomplete")
            
    except Exception as e:
        print(f"   ❌ Error checking comprehensive document: {e}")
    
    return integration_checks


def main():
    """Run all conversation requirements tests"""
    
    print("🗣️ Conversation Requirements Integration Test")
    print("=" * 60)
    print("Testing that realistic conversation requirements are properly")
    print("integrated into all development rules and policies\n")
    
    # Test 1: Rule files integration
    rule_results = test_rule_files_integration()
    
    # Test 2: Comprehensive coverage
    coverage_results = test_comprehensive_coverage()
    
    # Test 3: Integration completeness
    integration_results = test_integration_completeness()
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 30)
    
    print(f"\n🔧 Rule Files Integration:")
    print(f"   Files exist: {rule_results['files_exist']}/4")
    print(f"   Conversation keywords: {rule_results['conversation_keywords']}/4")
    print(f"   Mandatory markers: {rule_results['mandatory_markers']}/4")
    print(f"   LLM-agnostic refs: {rule_results['llm_agnostic']}/4")
    
    print(f"\n📖 Comprehensive Coverage:")
    print(f"   Required sections: {coverage_results['sections_found']}/7")
    print(f"   Examples provided: {'✅' if coverage_results['examples_provided'] else '❌'}")
    print(f"   Anti-patterns defined: {'✅' if coverage_results['anti_patterns_defined'] else '❌'}")
    print(f"   Quality metrics: {'✅' if coverage_results['metrics_defined'] else '❌'}")
    
    print(f"\n🔗 Integration Completeness:")
    for check, result in integration_results.items():
        status = "✅" if result else "❌"
        print(f"   {check.replace('_', ' ').title()}: {status}")
    
    # Calculate overall score
    total_checks = (
        rule_results['files_exist'] + rule_results['conversation_keywords'] + 
        rule_results['mandatory_markers'] + rule_results['llm_agnostic'] +
        coverage_results['sections_found'] + 
        sum([coverage_results['examples_provided'], coverage_results['anti_patterns_defined'], 
             coverage_results['metrics_defined']]) +
        sum(integration_results.values())
    )
    
    max_possible = 4 + 4 + 4 + 4 + 7 + 3 + 4  # 30 total checks
    score = (total_checks / max_possible) * 100
    
    print(f"\n🎯 Overall Integration Score: {score:.1f}%")
    
    if score >= 90:
        print(f"🎉 EXCELLENT - Conversation requirements fully integrated!")
        status = "excellent"
    elif score >= 75:
        print(f"✅ GOOD - Most requirements integrated, minor gaps")
        status = "good"
    elif score >= 50:
        print(f"⚠️ FAIR - Partial integration, needs improvement")
        status = "fair"
    else:
        print(f"❌ POOR - Significant integration gaps")
        status = "poor"
    
    print(f"\n💡 Key Benefits Achieved:")
    print(f"   ✅ Architect agent will maintain natural conversations")
    print(f"   ✅ Communication style is LLM-agnostic")
    print(f"   ✅ Users will feel like talking to a helpful colleague")
    print(f"   ✅ Technical explanations will be relatable and engaging")
    print(f"   ✅ Conversation quality maintained across all interactions")
    
    return status in ["excellent", "good"]


if __name__ == "__main__":
    # Run the integration tests
    result = main()
    
    print(f"\n{'🎉 Conversation requirements successfully integrated!' if result else '⚠️ Integration needs improvement'}")
    exit(0 if result else 1)
