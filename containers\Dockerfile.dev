# syntax=docker/dockerfile:1.7

# ---------- Builder stage ----------
FROM python:3.11-slim AS builder

ENV PIP_NO_CACHE_DIR=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1

# System deps (git, build tools, libpq for psycopg2-binary safety, curl for healthcheck)
RUN apt-get update -y \
    && apt-get install -y --no-install-recommends \
       build-essential git curl ca-certificates libpq-dev \
    && rm -rf /var/lib/apt/lists/*

# Create working directory
WORKDIR /workspace

# Leverage Docker layer caching: copy only requirements first
COPY requirements.txt requirements-dev.txt ./

# Install dev/test dependencies
RUN python -m pip install --upgrade pip \
    && pip install -r requirements-dev.txt

# ---------- Runtime stage ----------
FROM python:3.11-slim

ENV PIP_NO_CACHE_DIR=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    NLTK_DATA=/data/nltk

# Add non-root user
ARG APP_USER=appuser
ARG APP_UID=1000
ARG APP_GID=1000
RUN addgroup --system --gid ${APP_GID} ${APP_USER} \
    && adduser  --system --uid ${APP_UID} --ingroup ${APP_USER} --home /home/<USER>

# System tools for runtime
RUN apt-get update -y \
    && apt-get install -y --no-install-recommends \
       curl ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Create directories and set permissions
WORKDIR /workspace
RUN mkdir -p /data/nltk /workspace \
    && chown -R ${APP_USER}:${APP_USER} /data /workspace

# Copy installed site-packages from builder to reduce repeated wheels
COPY --from=builder /usr/local /usr/local

# Copy only lightweight scripts needed at runtime; project itself will be bind-mounted
COPY containers/scripts/ensure_nltk_data.py /usr/local/bin/ensure_nltk_data.py

# Ensure imports resolve: try editable install if packaging exists, else add .pth to sys.path
# This supports repos without setup.py/pyproject by adding /workspace to site-packages
RUN python - <<'PY'
import os, sys, site, glob
sp = site.getsitepackages()[0]
pth = os.path.join(sp, 'local_repo.pth')
with open(pth, 'w') as f:
    f.write('/workspace\n')
print('Created', pth)
PY

# Healthcheck: verify Python, pytest import, and minimal HTTP reachability for network stack
HEALTHCHECK --interval=30s --timeout=10s --retries=3 CMD python - <<'PY' || exit 1
import sys
try:
    import pytest  # noqa: F401
    print('pytest ok')
except Exception as e:
    print('healthcheck failure:', e)
    sys.exit(1)
PY

# Switch to non-root
USER ${APP_USER}

# Default command runs NLTK bootstrap and fast tests; overridable via PYTEST_ARGS
ENV PYTEST_ARGS="-q -k 'test_simple or test_smoke_runner'"
CMD bash -lc "python /usr/local/bin/ensure_nltk_data.py && pytest ${PYTEST_ARGS}"

