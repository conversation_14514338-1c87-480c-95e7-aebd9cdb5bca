#!/usr/bin/env python3
"""
Frontend Integration Test Script
Tests the complete upload system integration
"""

import json
import os
import shutil
import sys
import tempfile
import time
from pathlib import Path

import requests


def test_backend_api():
    """Test backend API endpoints"""
    print("🔧 Testing Backend API...")

    base_url = "http://127.0.0.1:8000"

    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend API is running")
        else:
            print(f"❌ Backend API returned status {response.status_code}")
            return False
    except requests.exceptions.RequestException as e:
        print(f"❌ Backend API not accessible: {e}")
        return False

    # Test sites list endpoint
    try:
        response = requests.get(f"{base_url}/api/sites/list", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(
                f"✅ Sites list endpoint working - {len(data.get('uploaded_sites', []))} sites found"
            )
        else:
            print(f"❌ Sites list endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Sites list endpoint error: {e}")

    # Test upload statistics endpoint
    try:
        response = requests.get(f"{base_url}/api/upload/statistics", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Upload statistics endpoint working")
        else:
            print(f"❌ Upload statistics endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Upload statistics endpoint error: {e}")

    return True


def test_frontend_files():
    """Test frontend files exist"""
    print("\n🎨 Testing Frontend Files...")

    required_files = [
        "src/frontend/components/UploadZone.tsx",
        "src/frontend/components/UploadForm.tsx",
        "src/frontend/components/SecurityReport.tsx",
        "src/frontend/components/SiteList.tsx",
        "src/frontend/pages/UploadPage.tsx",
        "src/frontend/pages/upload.tsx",
        "src/frontend/pages/index.tsx",
        "src/api/upload_routes.py",
        "package.json",
        "next.config.js",
        "tsconfig.json",
    ]

    missing_files = []
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - MISSING")
            missing_files.append(file_path)

    if missing_files:
        print(f"\n❌ {len(missing_files)} files are missing")
        return False
    else:
        print(f"\n✅ All {len(required_files)} required files exist")
        return True


def test_upload_directories():
    """Test upload directories exist"""
    print("\n📁 Testing Upload Directories...")

    required_dirs = [
        "uploads",
        "uploads/pending",
        "uploads/imported",
        "uploads/validated",
    ]

    missing_dirs = []
    for dir_path in required_dirs:
        if Path(dir_path).exists():
            print(f"✅ {dir_path}/")
        else:
            print(f"❌ {dir_path}/ - MISSING")
            missing_dirs.append(dir_path)

    if missing_dirs:
        print(f"\n❌ {len(missing_dirs)} directories are missing")
        return False
    else:
        print(f"\n✅ All {len(required_dirs)} required directories exist")
        return True


def test_dependencies():
    """Test Node.js dependencies"""
    print("\n📦 Testing Dependencies...")

    # Check if package.json exists
    if not Path("package.json").exists():
        print("❌ package.json not found")
        return False

    # Check if node_modules exists
    if not Path("node_modules").exists():
        print("❌ node_modules not found - run 'npm install'")
        return False

    # Check for react-dropzone
    dropzone_path = Path("node_modules/react-dropzone")
    if dropzone_path.exists():
        print("✅ react-dropzone installed")
    else:
        print("❌ react-dropzone not installed")
        return False

    print("✅ Dependencies check passed")
    return True


def test_configuration():
    """Test configuration files"""
    print("\n⚙️ Testing Configuration...")

    # Test package.json
    try:
        with open("package.json", "r") as f:
            package_data = json.load(f)

        required_scripts = ["dev", "build", "start"]
        missing_scripts = []
        for script in required_scripts:
            if script in package_data.get("scripts", {}):
                print(f"✅ package.json script: {script}")
            else:
                print(f"❌ package.json script: {script} - MISSING")
                missing_scripts.append(script)

        if missing_scripts:
            print(f"❌ {len(missing_scripts)} scripts missing from package.json")
            return False

    except Exception as e:
        print(f"❌ Error reading package.json: {e}")
        return False

    # Test next.config.js
    if Path("next.config.js").exists():
        print("✅ next.config.js exists")
    else:
        print("❌ next.config.js missing")
        return False

    # Test tsconfig.json
    if Path("tsconfig.json").exists():
        print("✅ tsconfig.json exists")
    else:
        print("❌ tsconfig.json missing")
        return False

    print("✅ Configuration check passed")
    return True


def main():
    """Run all integration tests"""
    print("🚀 Frontend Integration Test Suite")
    print("=" * 50)

    tests = [
        ("Backend API", test_backend_api),
        ("Frontend Files", test_frontend_files),
        ("Upload Directories", test_upload_directories),
        ("Dependencies", test_dependencies),
        ("Configuration", test_configuration),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary")
    print("=" * 50)

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1

    print(f"\n🎯 Overall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Frontend integration is ready.")
        print("\n🚀 Next steps:")
        print(
            "1. Start the backend: python -m uvicorn src.dashboard.minimal_api:app --host 127.0.0.1 --port 8000 --reload"
        )
        print("2. Start the frontend: npm run dev")
        print("3. Visit http://localhost:3000/upload to test the upload interface")
    else:
        print("⚠️ Some tests failed. Please fix the issues before proceeding.")
        return 1

    return 0


if __name__ == "__main__":
    sys.exit(main())
