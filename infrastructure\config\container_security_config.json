{"image_scanning": {"enabled": true, "scan_on_pull": true, "scan_on_build": true, "scan_schedule": "daily", "vulnerability_threshold": "medium", "auto_block_critical": true, "scanners": {"trivy": {"enabled": true, "command": "trivy", "severity_levels": ["CRITICAL", "HIGH", "MEDIUM"], "scan_options": ["--severity", "CRITICAL,HIGH,MEDIUM", "--format", "json"]}, "clair": {"enabled": false, "endpoint": "http://localhost:6060", "api_version": "v1"}, "anchore": {"enabled": false, "endpoint": "http://localhost:8228", "username": "", "password": ""}, "snyk": {"enabled": false, "api_token": "", "org_id": ""}}, "registry_integration": {"enabled": true, "registries": {"docker_hub": {"enabled": true, "username": "", "password": ""}, "private_registry": {"enabled": false, "url": "", "username": "", "password": ""}}}}, "runtime_monitoring": {"enabled": true, "monitoring_interval": 30, "events_to_monitor": ["container_start", "container_stop", "file_access", "network_access", "process_creation", "privilege_escalation", "system_call", "memory_access", "device_access"], "anomaly_detection": {"enabled": true, "baseline_duration_hours": 24, "threshold": 0.8, "algorithms": {"isolation_forest": {"enabled": true, "contamination": 0.1}, "one_class_svm": {"enabled": false, "nu": 0.1}}}, "real_time_monitoring": {"enabled": true, "syscall_monitoring": true, "network_monitoring": true, "file_monitoring": true, "process_monitoring": true}}, "isolation": {"enabled": true, "default_isolation_level": "medium", "levels": {"low": {"description": "Basic isolation", "seccomp_profile": "default", "capabilities": ["CHOWN", "SETGID", "SETUID", "NET_BIND_SERVICE"], "readonly_root": false, "no_new_privileges": false, "user_namespace": false, "apparmor_profile": "docker-default"}, "medium": {"description": "Enhanced isolation", "seccomp_profile": "restricted", "capabilities": ["CHOWN", "NET_BIND_SERVICE"], "readonly_root": true, "no_new_privileges": true, "user_namespace": false, "apparmor_profile": "docker-default", "selinux": "enforcing"}, "high": {"description": "Maximum isolation", "seccomp_profile": "custom", "capabilities": [], "readonly_root": true, "no_new_privileges": true, "user_namespace": true, "apparmor_profile": "custom-restricted", "selinux": "enforcing", "cgroup_v2": true}}, "network_isolation": {"enabled": true, "default_network": "bridge", "allowed_networks": ["bridge", "host"], "network_policies": {"ingress": {"enabled": true, "allowed_ports": ["80", "443", "8080"], "allowed_protocols": ["tcp", "udp"]}, "egress": {"enabled": true, "allowed_destinations": ["0.0.0.0/0"], "blocked_destinations": []}}}}, "policies": {"default": {"allowed_images": ["*"], "blocked_images": [], "allowed_ports": ["80", "443", "8080", "3000", "5000"], "blocked_ports": ["22", "23", "3389", "5900"], "allowed_volumes": ["/tmp", "/var/log", "/app/data"], "blocked_volumes": ["/proc", "/sys", "/dev", "/boot"], "resource_limits": {"memory": "512m", "cpu": "0.5", "pids": 100, "disk_io": "100m", "network_io": "100m"}, "security_opt": ["no-new-privileges", "seccomp=unconfined"]}, "strict": {"allowed_images": ["alpine:latest", "nginx:alpine", "python:3.9-alpine"], "blocked_images": ["*"], "allowed_ports": ["80", "443"], "blocked_ports": ["*"], "allowed_volumes": ["/tmp"], "blocked_volumes": ["*"], "resource_limits": {"memory": "256m", "cpu": "0.25", "pids": 50, "disk_io": "50m", "network_io": "50m"}, "security_opt": ["no-new-privileges", "seccomp=restricted", "apparmor=docker-default"]}, "ai_models": {"allowed_images": ["python:3.9", "tensorflow/tensorflow", "pytorch/pytorch"], "blocked_images": ["*"], "allowed_ports": ["8080", "5000", "8000"], "blocked_ports": ["22", "23", "3389"], "allowed_volumes": ["/app/models", "/app/data", "/tmp"], "blocked_volumes": ["/proc", "/sys", "/dev"], "resource_limits": {"memory": "2g", "cpu": "1.0", "pids": 200, "disk_io": "200m", "network_io": "200m"}, "security_opt": ["no-new-privileges", "seccomp=restricted"]}}, "forensics": {"enabled": true, "evidence_collection": {"container_logs": true, "filesystem_snapshot": true, "memory_dump": false, "network_capture": true, "process_list": true, "open_files": true, "network_connections": true}, "retention": {"logs_days": 30, "snapshots_days": 7, "captures_days": 3, "memory_dumps_days": 1}, "automated_analysis": {"enabled": true, "malware_scanning": true, "ioc_extraction": true, "timeline_analysis": true, "network_analysis": true}}, "incident_response": {"enabled": true, "automated_response": {"enabled": true, "container_isolation": true, "network_quarantine": true, "process_termination": false, "alert_notification": true, "evidence_preservation": true}, "escalation": {"levels": [{"level": 1, "description": "Low severity", "response_time_minutes": 60, "notify": ["admin"], "actions": ["log", "monitor"]}, {"level": 2, "description": "Medium severity", "response_time_minutes": 30, "notify": ["admin", "security_team"], "actions": ["log", "alert", "isolate"]}, {"level": 3, "description": "High severity", "response_time_minutes": 15, "notify": ["admin", "security_team", "management"], "actions": ["log", "alert", "isolate", "terminate"]}, {"level": 4, "description": "Critical severity", "response_time_minutes": 5, "notify": ["admin", "security_team", "management", "incident_response"], "actions": ["log", "alert", "isolate", "terminate", "forensics"]}]}, "documentation": {"incident_logging": true, "post_incident_reviews": true, "lessons_learned": true, "improvement_tracking": true, "compliance_reporting": true}}, "compliance": {"enabled": true, "standards": {"cis_docker": {"enabled": true, "version": "1.6.0", "auto_remediation": false}, "nist_cybersecurity": {"enabled": true, "framework": "1.1", "controls": ["AC", "SC", "SI"]}, "iso27001": {"enabled": true, "controls": ["A.12.2", "A.12.3", "A.12.4"]}}, "reporting": {"automated_reports": true, "compliance_dashboard": true, "audit_trail": true, "certification_support": true}}, "monitoring": {"enabled": true, "metrics": {"container_health": true, "security_events": true, "resource_usage": true, "network_activity": true, "file_activity": true}, "alerting": {"email": {"enabled": true, "recipients": [], "smtp_config": {}}, "webhook": {"enabled": false, "url": "", "headers": {}}, "slack": {"enabled": false, "webhook_url": "", "channel": "#container-security"}}, "dashboard": {"enabled": true, "real_time_updates": true, "historical_data": true, "custom_views": true, "security_overview": true}}, "integration": {"docker": {"enabled": true, "socket": "/var/run/docker.sock", "api_version": "1.41"}, "kubernetes": {"enabled": false, "config_path": "", "namespace": "default"}, "security_tools": {"falco": {"enabled": false, "endpoint": "http://localhost:17676"}, "sysdig": {"enabled": false, "endpoint": ""}}}}