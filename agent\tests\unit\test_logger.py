"""
Unit tests for centralized logger utility
"""

import os
import tempfile
from pathlib import Path
from unittest.mock import patch

import pytest

from agent.utils.logger import AI<PERSON>og<PERSON>, get_logger


class TestAILogger:
    """Test cases for AILogger"""

    def test_singleton_behavior(self):
        """Test that AILogger is a singleton"""
        logger1 = AILogger()
        logger2 = AILogger()
        assert logger1 is logger2

    def test_get_logger_creates_instance(self):
        """Test getting a logger instance"""
        logger = get_logger("test_component")
        assert logger is not None
        assert logger.name == "test_component"

    def test_logger_has_handlers(self):
        """Test that logger has both console and file handlers"""
        logger = get_logger("test_handlers")
        assert len(logger.handlers) >= 2  # Console + file

    def test_logger_levels(self):
        """Test different log levels"""
        logger = get_logger("test_levels")

        # Test that all levels work
        logger.debug("Debug message")
        logger.info("Info message")
        logger.warning("Warning message")
        logger.error("Error message")
        logger.critical("Critical message")

    def test_configure_from_config(self):
        """Test configuring logger from config"""
        logger = AILogger()
        test_config = {"logging": {"level": "DEBUG", "log_dir": "test_logs"}}

        logger.configure_from_config(test_config)

        # Verify configuration was applied
        test_logger = logger.get_logger("test_config")
        assert test_logger.level == 10  # DEBUG level

    def test_environment_log_level(self):
        """Test log level from environment variable"""
        with patch.dict(os.environ, {"LOG_LEVEL": "WARNING"}):
            logger = get_logger("test_env")
            assert logger.level == 30  # WARNING level

    def test_log_file_creation(self, temp_dir):
        """Test that log files are created"""
        log_dir = temp_dir / "test_logs"
        log_dir.mkdir()

        # Configure logger to use temp directory
        config = {"logging": {"level": "INFO", "log_dir": str(log_dir)}}

        logger_instance = AILogger()
        logger_instance.configure_from_config(config)
        logger = logger_instance.get_logger("test_file_creation")

        # Log a message
        logger.info("Test message")

        # Check if log file was created
        log_files = list(log_dir.glob("*.log"))
        assert len(log_files) > 0

    def test_log_format(self, temp_dir):
        """Test log message formatting"""
        log_dir = temp_dir / "test_logs"
        log_dir.mkdir()

        logger = get_logger("test_format")
        test_message = "Test formatted message"

        logger.info(test_message)

        # Check log file content
        log_files = list(log_dir.glob("*.log"))
        if log_files:
            content = log_files[0].read_text()
            assert "Test formatted message" in content

    def test_multiple_loggers(self):
        """Test multiple logger instances"""
        logger1 = get_logger("component1")
        logger2 = get_logger("component2")

        assert logger1.name == "component1"
        assert logger2.name == "component2"
        assert logger1 is not logger2

    def test_error_handling(self):
        """Test error handling in logger"""
        logger = get_logger("test_error")

        # Should not raise exception
        try:
            logger.info("Test error handling")
        except Exception as e:
            pytest.fail(f"Logger raised unexpected exception: {e}")

    def test_extra_context(self):
        """Test logging with extra context"""
        logger = get_logger("test_context")

        # Test with extra context
        logger.info("Test message", extra={"user_id": 123, "action": "test"})

    def test_configure_empty_config(self):
        """Test configuring with empty config"""
        logger = AILogger()
        logger.configure_from_config({})

        # Should not raise exception
        test_logger = logger.get_logger("test_empty")
        assert test_logger is not None


class TestLoggerIntegration:
    """Integration tests for logger"""

    def test_full_logging_workflow(self, temp_dir):
        """Test complete logging workflow"""
        log_dir = temp_dir / "integration_logs"
        log_dir.mkdir()

        config = {"logging": {"level": "INFO", "log_dir": str(log_dir)}}

        logger = AILogger()
        logger.configure_from_config(config)

        test_logger = logger.get_logger("integration_test")

        # Log various messages
        test_logger.info("Starting test")
        test_logger.warning("Warning message")
        test_logger.error("Error occurred")

        # Verify logs were written
        log_files = list(log_dir.glob("*.log"))
        assert len(log_files) > 0

        content = log_files[0].read_text()
        assert "Starting test" in content
        assert "Warning message" in content
        assert "Error occurred" in content
