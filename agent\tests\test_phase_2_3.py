#!/usr/bin/env python3
"""
Phase 2.3: CMS & Content Management Test
Simple test script to demonstrate CMS functionality.
"""

import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from projects.content.cms_content_manager import ContentManager

    print("✅ Successfully imported CMS Content Manager")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


def test_cms_functionality():
    """Test basic CMS functionality"""
    print("\n🚀 Testing Phase 2.3: CMS & Content Management")
    print("=" * 60)

    try:
        # Initialize CMS
        print("\n📋 Initializing CMS...")
        cms = ContentManager("config/cms_config.json")
        print("✅ CMS initialized successfully")

        # Test content creation
        print("\n📝 Testing content creation...")
        article = cms.create_content(
            title="Test Article - Phase 2.3",
            content="<h1>Test Content</h1><p>This is a test article for Phase 2.3 CMS functionality.</p>",
            content_type="article",
            author="Test User",
            tags=["test", "phase-2-3", "cms"],
        )
        print(f"✅ Created article: {article.id}")
        print(f"   Title: {article.title}")
        print(f"   Status: {article.status}")

        # Test content retrieval
        print("\n🔍 Testing content retrieval...")
        retrieved = cms.get_content(article.id)
        if retrieved:
            print(f"✅ Retrieved article: {retrieved.title}")
        else:
            print("❌ Failed to retrieve article")

        # Test content listing
        print("\n📋 Testing content listing...")
        articles = cms.list_content(content_type="article")
        print(f"✅ Found {len(articles)} articles")

        # Test content search
        print("\n🔍 Testing content search...")
        search_results = cms.search_content("test")
        print(f"✅ Search found {len(search_results)} results")

        # Test content update
        print("\n✏️ Testing content update...")
        updated = cms.update_content(
            content_id=article.id,
            title="Updated Test Article - Phase 2.3",
            content="<h1>Updated Test Content</h1><p>This article has been updated to test CMS functionality.</p>",
        )
        print(f"✅ Updated article: {updated.title}")
        print(f"   Version: {updated.version}")

        # Test content publishing
        print("\n📤 Testing content publishing...")
        published = cms.publish_content(article.id)
        print(f"✅ Published article: {published.status}")

        # Test statistics
        print("\n📊 Testing content statistics...")
        stats = cms.get_content_statistics()
        print(f"✅ Content statistics:")
        print(f"   Total items: {stats.get('total_items', 0)}")
        print(f"   Published: {stats.get('published', 0)}")
        print(f"   Drafts: {stats.get('drafts', 0)}")
        print(f"   Articles: {stats.get('articles', 0)}")

        # Test AI content generation (fallback mode)
        print("\n🤖 Testing AI content generation (fallback mode)...")
        try:
            ai_article = cms.generate_ai_content(
                topic="AI in Content Management",
                content_type="article",
                length="short",
                style="informative",
            )
            print(f"✅ Generated AI article: {ai_article.title}")
            print(f"   AI Generated: {ai_article.metadata.get('ai_generated', False)}")
        except Exception as e:
            print(f"⚠️ AI generation test (expected in demo mode): {e}")

        print("\n🎉 Phase 2.3 CMS & Content Management test completed successfully!")
        print("=" * 60)

    except Exception as e:
        print(f"❌ Error during CMS test: {e}")
        return False

    return True


if __name__ == "__main__":
    success = test_cms_functionality()
    sys.exit(0 if success else 1)
