# cli/site_container_commands.py
"""
Site Container Management CLI Commands
Provides command-line interface for managing site containers.
"""

import asyncio
import logging
from pathlib import Path
from typing import Any, Dict, Optional

from agent.core.site_container_manager import SiteContainerManager

logger = logging.getLogger(__name__)


class SiteContainerCommands:
    """CLI commands for site container management"""

    def __init__(self, agent):
        self.agent = agent
        self.container_manager = SiteContainerManager()

    async def create_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Create a Docker container for a website"""
        try:
            site_config = {
                "name": site_name,
                "port": kwargs.get("port"),
                "environment": kwargs.get("environment", "production"),
            }

            result = await self.container_manager.create_site_container(
                site_name, site_config
            )

            if result["success"]:
                logger.info(f"✅ Container created for site {site_name}")
                return {
                    "success": True,
                    "message": f"Container created for site {site_name}",
                    "container": result["container"],
                    "port": result["port"],
                }
            else:
                logger.error(f"❌ Failed to create container: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error creating site container: {e}")
            return {"success": False, "error": str(e)}

    async def start_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Start a site container"""
        try:
            result = await self.container_manager.start_site_container(site_name)

            if result["success"]:
                logger.info(f"✅ Container started for site {site_name}")
                return {
                    "success": True,
                    "message": f"Container started for site {site_name}",
                    "url": result["url"],
                }
            else:
                logger.error(f"❌ Failed to start container: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error starting site container: {e}")
            return {"success": False, "error": str(e)}

    async def stop_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Stop a site container"""
        try:
            result = await self.container_manager.stop_site_container(site_name)

            if result["success"]:
                logger.info(f"✅ Container stopped for site {site_name}")
                return {
                    "success": True,
                    "message": f"Container stopped for site {site_name}",
                }
            else:
                logger.error(f"❌ Failed to stop container: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error stopping site container: {e}")
            return {"success": False, "error": str(e)}

    async def delete_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Delete a site container"""
        try:
            result = await self.container_manager.delete_site_container(site_name)

            if result["success"]:
                logger.info(f"✅ Container deleted for site {site_name}")
                return {
                    "success": True,
                    "message": f"Container deleted for site {site_name}",
                }
            else:
                logger.error(f"❌ Failed to delete container: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error deleting site container: {e}")
            return {"success": False, "error": str(e)}

    async def list_site_containers(self, **kwargs) -> Dict[str, Any]:
        """List all site containers"""
        try:
            result = await self.container_manager.list_containers()

            if result["success"]:
                containers = result["containers"]
                logger.info(f"�� Found {len(containers)} site containers")

                container_list = []
                for container in containers:
                    container_list.append(
                        {
                            "site_name": container["site_name"],
                            "status": container["status"],
                            "port": container["port"],
                            "url": f"http://localhost:{container['port']}",
                            "health": container["health_status"],
                        }
                    )

                return {
                    "success": True,
                    "containers": container_list,
                    "total": len(container_list),
                }
            else:
                logger.error(f"❌ Failed to list containers: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error listing site containers: {e}")
            return {"success": False, "error": str(e)}

    async def handle_error_with_escalation(
        self, site_name: str, error_title: str = None, error_description: str = None,
        error_category: str = "unknown", error_severity: str = "medium"
    ) -> Dict[str, Any]:
        """Handle an error with automatic fixes and user escalation"""
        try:
            error_details = {
                "title": error_title or "Unknown Error",
                "description": error_description or "An error occurred",
                "category": error_category,
                "severity": error_severity
            }

            logger.info(f"Handling error for site {site_name}: {error_details['title']}")

            result = await self.container_manager.handle_error_with_user_escalation(
                site_name=site_name,
                error_details=error_details
            )

            if result.get("success"):
                logger.info(f"✅ Error resolved automatically for {site_name}")
                return {
                    "success": True,
                    "resolution": "automatic",
                    "fix_applied": result.get("fix_applied", "unknown"),
                    "attempts": result.get("attempts", 0)
                }
            elif result.get("requires_user_input"):
                logger.info(f"🤖 Error escalated to user for {site_name}")
                return {
                    "success": False,
                    "requires_user_input": True,
                    "user_message": result.get("user_message", ""),
                    "suggested_actions": result.get("suggested_actions", []),
                    "error_details": error_details
                }
            else:
                logger.error(f"❌ Error handling failed for {site_name}")
                return result

        except Exception as e:
            logger.error(f"Error in error handling for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def apply_user_suggested_fix(
        self, site_name: str, user_suggestion: str, error_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Apply a user-suggested fix to a site"""
        try:
            logger.info(f"Applying user suggestion for {site_name}: {user_suggestion}")

            result = await self.container_manager.apply_user_suggested_fix(
                site_name=site_name,
                user_suggestion=user_suggestion,
                error_context=error_context
            )

            if result.get("success"):
                logger.info(f"✅ User suggestion applied successfully for {site_name}")
                return {
                    "success": True,
                    "fix_description": result.get("fix_description", "Fix applied"),
                    "fix_type": result.get("fix_type", "user_suggested")
                }
            elif result.get("requires_clarification"):
                logger.info(f"🤔 User suggestion needs clarification for {site_name}")
                return {
                    "success": False,
                    "requires_clarification": True,
                    "message": result.get("message", "Please provide more specific instructions")
                }
            else:
                logger.error(f"❌ User suggestion failed for {site_name}")
                return result

        except Exception as e:
            logger.error(f"Error applying user suggestion for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def get_error_status(self, site_name: str) -> Dict[str, Any]:
        """Get current error status for a site"""
        try:
            # This would integrate with error detection system
            from agent.core.error_detection_system import ErrorDetectionSystem

            error_detector = ErrorDetectionSystem()
            recent_errors = error_detector.get_recent_errors(hours=24)

            # Filter errors for this site
            site_errors = [
                error for error in recent_errors
                if error.context.get("site_name") == site_name
            ]

            system_health = error_detector.get_system_health()

            return {
                "success": True,
                "site_name": site_name,
                "error_count": len(site_errors),
                "recent_errors": [
                    {
                        "id": error.id,
                        "title": error.title,
                        "severity": error.severity.value,
                        "category": error.category.value,
                        "timestamp": error.timestamp.isoformat(),
                        "fix_applied": error.fix_applied
                    }
                    for error in site_errors[-5:]  # Last 5 errors
                ],
                "system_health": {
                    "status": system_health.overall_status,
                    "performance_score": system_health.performance_score,
                    "error_count": system_health.error_count
                }
            }

        except Exception as e:
            logger.error(f"Error getting error status for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def get_container_status(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Get status of a site container"""
        try:
            result = await self.container_manager.get_container_status(site_name)

            if result["success"]:
                container = result["container"]
                logger.info(
                    f"📊 Container status for {site_name}: {container['status']}"
                )

                return {
                    "success": True,
                    "container": {
                        "site_name": container["site_name"],
                        "status": container["status"],
                        "port": container["port"],
                        "url": f"http://localhost:{container['port']}",
                        "health": container["health_status"],
                        "resource_usage": container["resource_usage"],
                    },
                }
            else:
                logger.error(f"❌ Failed to get container status: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error getting container status: {e}")
            return {"success": False, "error": str(e)}

    async def rebuild_site_container(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Rebuild a site container"""
        try:
            logger.info(f"🔨 Rebuilding container for site {site_name}")

            result = await self.container_manager.rebuild_site_container(site_name)

            if result["success"]:
                logger.info(f"✅ Container rebuilt for site {site_name}")
                return {
                    "success": True,
                    "message": f"Container rebuilt for site {site_name}",
                    "url": result.get("url"),
                }
            else:
                logger.error(f"❌ Failed to rebuild container: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error rebuilding site container: {e}")
            return {"success": False, "error": str(e)}

    async def get_container_logs(self, site_name: str, **kwargs) -> Dict[str, Any]:
        """Get logs from a site container"""
        try:
            lines = kwargs.get("lines", 100)
            result = await self.container_manager.get_container_logs(site_name, lines)

            if result["success"]:
                logs = result["logs"]
                logger.info(f"�� Retrieved {len(logs)} log lines for {site_name}")

                return {
                    "success": True,
                    "logs": logs,
                    "container_name": result["container_name"],
                }
            else:
                logger.error(f"❌ Failed to get container logs: {result['error']}")
                return result

        except Exception as e:
            logger.error(f"Error getting container logs: {e}")
            return {"success": False, "error": str(e)}
