#!/usr/bin/env python3
"""
Tests for ArchitectAgent delegation via SmartTaskRouter and AgentMessageBus
"""
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from agent.core.agents.architect_agent import ArchitectAgent
from agent.core.project_models import Roadmap, PhaseSpec, StepSpec, TaskSpec


@pytest.fixture
def temp_project_dir():
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir


@pytest.fixture
def architect_agent(temp_project_dir):
    # Create a minimal config for testing
    config_path = Path(temp_project_dir) / "test_config.json"
    config_path.write_text('{"model_settings": {"model_name": "test-model"}}')
    
    agent = ArchitectAgent(str(config_path))
    return agent


@pytest.fixture
def sample_roadmap():
    """Create a sample roadmap for testing"""
    return Roadmap(
        id="test_roadmap",
        title="Test Project",
        phases=[
            PhaseSpec(
                id="phase1",
                title="Setup Phase",
                order=0,
                steps=[
                    StepSpec(
                        id="step1",
                        title="Environment Setup",
                        order=0,
                        tasks=[
                            TaskSpec(
                                id="task1",
                                title="Initialize repository",
                                description="Set up git repository and basic structure",
                                agent_type="shell",
                                requirements=["git", "basic tooling"],
                                dependencies=[]
                            ),
                            TaskSpec(
                                id="task2",
                                title="Setup backend API",
                                description="Create FastAPI backend with authentication",
                                agent_type="backend",
                                requirements=["FastAPI", "JWT auth", "PostgreSQL"],
                                dependencies=["task1"]
                            )
                        ]
                    )
                ]
            )
        ]
    )


def test_initialize_routing_components(architect_agent):
    """Test routing components initialization"""
    # Should not have routing components initially
    assert not hasattr(architect_agent, '_smart_router')
    assert not hasattr(architect_agent, '_message_bus')
    
    # Initialize routing components
    architect_agent._initialize_routing_components()
    
    # Should have routing components after initialization
    assert hasattr(architect_agent, '_smart_router')
    assert hasattr(architect_agent, '_message_bus')


@pytest.mark.asyncio
async def test_create_task_context(architect_agent, sample_roadmap):
    """Test TaskContext creation from roadmap tasks"""
    phase = sample_roadmap.phases[0]
    step = phase.steps[0]
    task = step.tasks[0]  # Shell task
    
    task_context = await architect_agent._create_task_context(task, phase, step)
    
    assert task_context.task_id == task.id
    assert task_context.task_type == task.agent_type
    assert task_context.requirements == task.requirements
    assert task_context.dependencies == task.dependencies
    assert task_context.priority == "high"  # First phase should be high priority
    assert task_context.estimated_duration == 30  # Simple task = 30 minutes


@pytest.mark.asyncio
async def test_create_task_context_complexity_mapping(architect_agent, sample_roadmap):
    """Test complexity mapping in TaskContext creation"""
    from agent.core.agents.smart_task_router import TaskComplexity
    
    phase = sample_roadmap.phases[0]
    step = phase.steps[0]
    
    # Test simple task
    simple_task = step.tasks[0]
    context = await architect_agent._create_task_context(simple_task, phase, step)
    assert context.complexity == TaskComplexity.SIMPLE
    
    # Test complex task (backend with many requirements)
    complex_task = step.tasks[1]
    context = await architect_agent._create_task_context(complex_task, phase, step)
    assert context.complexity == TaskComplexity.MODERATE  # Has 3+ requirements


@pytest.mark.asyncio
@patch('core.agents.architect_agent.SmartTaskRouter')
@patch('core.agents.architect_agent.AgentMessageBus')
async def test_delegate_tasks_to_agents(mock_message_bus_class, mock_router_class, architect_agent, sample_roadmap):
    """Test task delegation with mocked components"""
    # Setup mocks
    mock_router = AsyncMock()
    mock_message_bus = AsyncMock()
    mock_router_class.return_value = mock_router
    mock_message_bus_class.return_value = mock_message_bus
    
    # Mock routing decision
    from agent.core.agents.smart_task_router import RoutingDecision, RoutingStrategy
    mock_routing_decision = RoutingDecision(
        task_id="task1",
        selected_agent_id="shell_agent",
        strategy_used=RoutingStrategy.AI_POWERED,
        confidence_score=0.95,
        reasoning="Best match for shell operations"
    )
    mock_router.route_task.return_value = mock_routing_decision
    
    # Mock message bus publish
    mock_message_bus.publish.return_value = True
    mock_message_bus.subscribe.return_value = True
    
    # Set current project for delegation
    architect_agent._current_project_id = "test_project"
    
    # Test delegation
    result = await architect_agent._delegate_tasks_to_agents(sample_roadmap)
    
    # Verify results
    assert result["success_rate"] > 0
    assert len(result["delegated_tasks"]) == 2  # Two tasks in sample roadmap
    assert len(result["routing_failures"]) == 0
    
    # Verify first delegated task
    first_task = result["delegated_tasks"][0]
    assert first_task["task_id"] == "task1"
    assert first_task["agent"] == "shell_agent"
    assert first_task["routing_confidence"] == 0.95
    assert "correlation_id" in first_task


@pytest.mark.asyncio
async def test_send_task_message(architect_agent):
    """Test task message creation and sending"""
    # Mock message bus
    mock_message_bus = AsyncMock()
    mock_message_bus.publish.return_value = True
    architect_agent._message_bus = mock_message_bus
    architect_agent._current_project_id = "test_project"
    
    # Create mock task and routing decision
    from agent.core.project_models import TaskSpec
    from agent.core.agents.smart_task_router import RoutingDecision, RoutingStrategy
    
    task = TaskSpec(
        id="test_task",
        title="Test Task",
        description="Test description",
        agent_type="shell",
        requirements=["git"],
        dependencies=[]
    )
    
    routing_decision = RoutingDecision(
        task_id="test_task",
        selected_agent_id="shell_agent",
        strategy_used=RoutingStrategy.AI_POWERED,
        confidence_score=0.9,
        reasoning="Test routing"
    )
    
    correlation_id = "test_correlation_123"
    
    # Test message sending
    result = await architect_agent._send_task_message(task, routing_decision, correlation_id)
    
    assert result is True
    mock_message_bus.publish.assert_called_once()
    
    # Verify message content
    call_args = mock_message_bus.publish.call_args[0][0]  # First positional argument
    assert call_args.sender_id == "architect_agent"
    assert call_args.recipient_id == "shell_agent"
    assert call_args.correlation_id == correlation_id
    assert call_args.content["task_id"] == "test_task"
    assert call_args.content["project_id"] == "test_project"
    assert call_args.requires_acknowledgment is True


@pytest.mark.asyncio
async def test_handle_agent_response(architect_agent):
    """Test handling of agent responses"""
    # Mock project store
    mock_store = MagicMock()
    architect_agent._project_store = mock_store
    architect_agent._current_project_id = "test_project"
    
    # Create mock response message
    from agent.core.agents.agent_message_bus import AgentMessage, MessageType
    
    response_message = AgentMessage(
        id="response_123",
        message_type=MessageType.TASK_RESPONSE,
        sender_id="shell_agent",
        recipient_id="architect_agent",
        subject="Task Completed",
        content={
            "task_id": "task1",
            "status": "completed",
            "artifacts": ["setup_complete.log"]
        }
    )
    
    # Test response handling
    result = await architect_agent._handle_agent_response(response_message)
    
    assert result is True


@pytest.mark.asyncio
async def test_update_task_status(architect_agent, sample_roadmap):
    """Test task status updates in roadmap"""
    # Mock project store
    mock_store = MagicMock()
    mock_store.load_roadmap.return_value = sample_roadmap
    architect_agent._project_store = mock_store
    architect_agent._current_project_id = "test_project"
    
    # Test status update
    await architect_agent._update_task_status(
        "task1", 
        "completed", 
        {"artifacts": ["test.log"]}
    )
    
    # Verify roadmap was loaded and saved
    mock_store.load_roadmap.assert_called_once_with("test_project")
    mock_store.save_roadmap.assert_called_once()
    
    # Verify task status was updated
    task = sample_roadmap.phases[0].steps[0].tasks[0]
    from agent.core.project_models import ItemStatus
    assert task.status == ItemStatus.COMPLETED
    assert "test.log" in task.artifacts
