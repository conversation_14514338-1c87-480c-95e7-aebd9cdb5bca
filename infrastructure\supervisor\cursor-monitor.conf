[supervisord]
nodaemon=true
user=cursor_monitor
logfile=/app/logs/supervisord.log
logfile_maxbytes=50MB
logfile_backups=10
loglevel=info
pidfile=/app/tmp/supervisord.pid

[program:cursor-monitor]
command=python scripts/cursor_rules_monitor.py --daemon --strict --log-level INFO
directory=/app
user=cursor_monitor
autostart=true
autorestart=true
startretries=5
startsecs=10
stopwaitsecs=10
stdout_logfile=/app/logs/cursor-monitor.log
stdout_logfile_maxbytes=50MB
stdout_logfile_backups=10
stderr_logfile=/app/logs/cursor-monitor-error.log
stderr_logfile_maxbytes=50MB
stderr_logfile_backups=10
environment=PYTHONPATH="/app",PYTHONUNBUFFERED="1",LOG_LEVEL="INFO",MONITOR_INTERVAL="30",STRICT_MODE="true"
redirect_stderr=true

[program:health-check]
command=python scripts/extended_health_check.py
directory=/app
user=cursor_monitor
autostart=true
autorestart=true
startretries=3
startsecs=5
stopwaitsecs=5
stdout_logfile=/app/logs/health-check.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stderr_logfile=/app/logs/health-check-error.log
stderr_logfile_maxbytes=10MB
stderr_logfile_backups=5
environment=PYTHONPATH="/app",PYTHONUNBUFFERED="1"
redirect_stderr=true

[program:monitor-http]
command=python scripts/monitor_http_server.py
directory=/app
user=cursor_monitor
autostart=true
autorestart=true
startretries=3
startsecs=5
stopwaitsecs=5
stdout_logfile=/app/logs/monitor-http.log
stdout_logfile_maxbytes=10MB
stdout_logfile_backups=5
stderr_logfile=/app/logs/monitor-http-error.log
stderr_logfile_maxbytes=10MB
stderr_logfile_backups=5
environment=PYTHONPATH="/app",PYTHONUNBUFFERED="1"
redirect_stderr=true
