import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import TestResults from '../TestResults';

// Mock WebSocket
class MockWebSocket {
  static CONNECTING = 0;
  static OPEN = 1;
  static CLOSING = 2;
  static CLOSED = 3;

  readyState = MockWebSocket.CONNECTING;
  onopen: ((event: Event) => void) | null = null;
  onmessage: ((event: MessageEvent) => void) | null = null;
  onerror: ((event: Event) => void) | null = null;
  onclose: ((event: CloseEvent) => void) | null = null;

  constructor(public url: string) {
    // Simulate connection opening
    setTimeout(() => {
      this.readyState = MockWebSocket.OPEN;
      if (this.onopen) {
        this.onopen(new Event('open'));
      }
    }, 10);
  }

  send(data: string) {
    // Mock sending data
  }

  close() {
    this.readyState = MockWebSocket.CLOSED;
    if (this.onclose) {
      this.onclose(new CloseEvent('close'));
    }
  }

  // Helper method to simulate receiving messages
  simulateMessage(data: any) {
    if (this.onmessage) {
      this.onmessage(new MessageEvent('message', { data: JSON.stringify(data) }));
    }
  }

  // Helper method to simulate errors
  simulateError() {
    if (this.onerror) {
      this.onerror(new Event('error'));
    }
  }
}

// Mock global WebSocket
(global as any).WebSocket = MockWebSocket;

describe('TestResults Component', () => {
  beforeEach(() => {
    // Reset WebSocket mock
    jest.clearAllMocks();
  });

  test('renders initial state correctly', () => {
    render(<TestResults />);
    
    expect(screen.getByText('Test Results')).toBeInTheDocument();
    expect(screen.getByText('No test results yet')).toBeInTheDocument();
    expect(screen.getByText('Click "Run Smoke Tests" to start')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: 'Run Smoke Tests' })).toBeInTheDocument();
  });

  test('renders configuration options', () => {
    render(<TestResults />);
    
    expect(screen.getByLabelText('Backend')).toBeInTheDocument();
    expect(screen.getByLabelText('Base URL')).toBeInTheDocument();
    expect(screen.getByLabelText('Timeout (s)')).toBeInTheDocument();
    
    // Check default values
    expect(screen.getByDisplayValue('http')).toBeInTheDocument();
    expect(screen.getByDisplayValue('http://localhost:8000')).toBeInTheDocument();
    expect(screen.getByDisplayValue('30')).toBeInTheDocument();
  });

  test('allows changing configuration', () => {
    render(<TestResults />);
    
    const backendSelect = screen.getByLabelText('Backend') as HTMLSelectElement;
    const baseUrlInput = screen.getByLabelText('Base URL') as HTMLInputElement;
    const timeoutInput = screen.getByLabelText('Timeout (s)') as HTMLInputElement;
    
    fireEvent.change(backendSelect, { target: { value: 'playwright' } });
    fireEvent.change(baseUrlInput, { target: { value: 'http://localhost:3000' } });
    fireEvent.change(timeoutInput, { target: { value: '60' } });
    
    expect(backendSelect.value).toBe('playwright');
    expect(baseUrlInput.value).toBe('http://localhost:3000');
    expect(timeoutInput.value).toBe('60');
  });

  test('starts WebSocket connection when Run Smoke Tests is clicked', async () => {
    render(<TestResults />);
    
    const runButton = screen.getByRole('button', { name: 'Run Smoke Tests' });
    fireEvent.click(runButton);
    
    // Wait for WebSocket connection
    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument();
    });
    
    expect(runButton).toHaveTextContent('Running Tests...');
    expect(runButton).toBeDisabled();
  });

  test('displays WebSocket events correctly', async () => {
    render(<TestResults />);
    
    const runButton = screen.getByRole('button', { name: 'Run Smoke Tests' });
    fireEvent.click(runButton);
    
    // Wait for connection
    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument();
    });
    
    // Get the WebSocket instance and simulate messages
    const wsInstance = (global as any).WebSocket.mock?.instances?.[0] || new MockWebSocket('test');
    
    // Simulate connection established
    wsInstance.simulateMessage({
      type: 'connection_established',
      backend: 'http',
      config: { base_url: 'http://localhost:8000' },
      timestamp: new Date().toISOString()
    });
    
    await waitFor(() => {
      expect(screen.getByText('Connection Established')).toBeInTheDocument();
    });
    
    // Simulate run start
    wsInstance.simulateMessage({
      type: 'run_start',
      run_id: 'test_run_123',
      backend: 'http',
      total_scenarios: 2,
      timestamp: new Date().toISOString()
    });
    
    await waitFor(() => {
      expect(screen.getByText('Run Start')).toBeInTheDocument();
      expect(screen.getByText('Test Run: test_run_123')).toBeInTheDocument();
    });
  });

  test('displays error messages correctly', async () => {
    render(<TestResults />);
    
    const runButton = screen.getByRole('button', { name: 'Run Smoke Tests' });
    fireEvent.click(runButton);
    
    // Wait for connection
    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument();
    });
    
    // Get the WebSocket instance and simulate error
    const wsInstance = (global as any).WebSocket.mock?.instances?.[0] || new MockWebSocket('test');
    
    wsInstance.simulateMessage({
      type: 'error',
      error: 'Test error message',
      timestamp: new Date().toISOString()
    });
    
    await waitFor(() => {
      expect(screen.getByText('Test error message')).toBeInTheDocument();
    });
  });

  test('handles scenario and step events', async () => {
    render(<TestResults />);
    
    const runButton = screen.getByRole('button', { name: 'Run Smoke Tests' });
    fireEvent.click(runButton);
    
    // Wait for connection
    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument();
    });
    
    const wsInstance = (global as any).WebSocket.mock?.instances?.[0] || new MockWebSocket('test');
    
    // Simulate scenario start
    wsInstance.simulateMessage({
      type: 'scenario_start',
      run_id: 'test_run_123',
      scenario: {
        id: 'health_check',
        name: 'Health Check',
        description: 'Verify basic application health',
        steps: [],
        status: 'running',
        passed_steps: 0,
        failed_steps: 0
      },
      timestamp: new Date().toISOString()
    });
    
    await waitFor(() => {
      expect(screen.getByText('Scenario Start')).toBeInTheDocument();
      expect(screen.getByText('Scenario: Health Check')).toBeInTheDocument();
    });
    
    // Simulate step start
    wsInstance.simulateMessage({
      type: 'step_start',
      run_id: 'test_run_123',
      scenario_id: 'health_check',
      step: {
        id: 'api_health',
        name: 'API Health Check',
        description: 'Check if API is responding',
        status: 'running'
      },
      timestamp: new Date().toISOString()
    });
    
    await waitFor(() => {
      expect(screen.getByText('Step Start')).toBeInTheDocument();
      expect(screen.getByText('Step: API Health Check - running')).toBeInTheDocument();
    });
  });

  test('clears results when Clear button is clicked', async () => {
    render(<TestResults />);
    
    const runButton = screen.getByRole('button', { name: 'Run Smoke Tests' });
    fireEvent.click(runButton);
    
    // Wait for connection and add some events
    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument();
    });
    
    const wsInstance = (global as any).WebSocket.mock?.instances?.[0] || new MockWebSocket('test');
    
    wsInstance.simulateMessage({
      type: 'run_start',
      run_id: 'test_run_123',
      timestamp: new Date().toISOString()
    });
    
    await waitFor(() => {
      expect(screen.getByText('Run Start')).toBeInTheDocument();
    });
    
    // Click clear button
    const clearButton = screen.getByRole('button', { name: 'Clear' });
    fireEvent.click(clearButton);
    
    // Events should be cleared
    expect(screen.queryByText('Run Start')).not.toBeInTheDocument();
    expect(screen.getByText('No test results yet')).toBeInTheDocument();
  });

  test('calls onClose when close button is clicked', () => {
    const mockOnClose = jest.fn();
    render(<TestResults onClose={mockOnClose} />);
    
    const closeButton = screen.getByRole('button', { name: '' }); // Close button has no text, just an icon
    fireEvent.click(closeButton);
    
    expect(mockOnClose).toHaveBeenCalledTimes(1);
  });

  test('disables configuration inputs when tests are running', async () => {
    render(<TestResults />);
    
    const runButton = screen.getByRole('button', { name: 'Run Smoke Tests' });
    const backendSelect = screen.getByLabelText('Backend');
    const baseUrlInput = screen.getByLabelText('Base URL');
    const timeoutInput = screen.getByLabelText('Timeout (s)');
    
    // Initially enabled
    expect(backendSelect).not.toBeDisabled();
    expect(baseUrlInput).not.toBeDisabled();
    expect(timeoutInput).not.toBeDisabled();
    
    fireEvent.click(runButton);
    
    // Wait for connection
    await waitFor(() => {
      expect(screen.getByText('Connected')).toBeInTheDocument();
    });
    
    // Should be disabled when running
    expect(backendSelect).toBeDisabled();
    expect(baseUrlInput).toBeDisabled();
    expect(timeoutInput).toBeDisabled();
    expect(runButton).toBeDisabled();
  });
});
