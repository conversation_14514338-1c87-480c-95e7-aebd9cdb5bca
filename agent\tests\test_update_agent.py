#!/usr/bin/env python3
"""
Test Update Agent - Demonstrates the new update agent functionality
"""

import asyncio
import json
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from agent.core.agents.update_agent import (
    UpdateAgent,
    check_updates_only,
    run_security_audit_only,
    run_update_cycle,
)


async def test_security_audit():
    """Test security audit functionality"""
    print("🔒 Testing Security Audit...")
    print("=" * 50)

    try:
        result = await run_security_audit_only(".")
        print(f"✅ Security audit completed")
        print(f"   Overall status: {result['overall_status']}")
        print(f"   Python vulnerabilities: {result['pip']['status']}")
        print(f"   Node.js vulnerabilities: {result['npm']['status']}")
        return True
    except Exception as e:
        print(f"❌ Security audit failed: {e}")
        return False


async def test_update_check():
    """Test update checking functionality"""
    print("\n📦 Testing Update Check...")
    print("=" * 50)

    try:
        result = await check_updates_only(".")
        print(f"✅ Update check completed")

        python_updates = len(result["python"]["updates_available"])
        node_updates = len(result["node"]["updates_available"])

        print(f"   Python packages with updates: {python_updates}")
        print(f"   Node.js packages with updates: {node_updates}")

        if python_updates > 0:
            print("   Python updates available:")
            for update in result["python"]["updates_available"][:5]:  # Show first 5
                print(
                    f"     - {update['package']}: {update['current']} → {update['latest']} ({update['update_type']})"
                )

        if node_updates > 0:
            print("   Node.js updates available:")
            for update in result["node"]["updates_available"][:5]:  # Show first 5
                print(
                    f"     - {update['package']}: {update['current']} → {update['latest']} ({update['update_type']})"
                )

        return True
    except Exception as e:
        print(f"❌ Update check failed: {e}")
        return False


async def test_update_agent():
    """Test the complete update agent"""
    print("\n🤖 Testing Update Agent...")
    print("=" * 50)

    try:
        agent = UpdateAgent(".")
        print("✅ Update agent initialized")

        # Test individual components
        print("\n   Testing security auditor...")
        security_result = await agent.run_security_audit()
        print(f"   ✅ Security audit: {security_result['overall_status']}")

        print("\n   Testing dependency checker...")
        update_check = await agent.check_for_updates()
        python_updates = len(update_check["python"]["updates_available"])
        node_updates = len(update_check["node"]["updates_available"])
        print(
            f"   ✅ Update check: {python_updates} Python, {node_updates} Node.js updates"
        )

        print("\n   Testing test runner...")
        test_result = await agent.run_tests()
        print(f"   ✅ Test suite: {test_result['overall_status']}")

        print("\n   Testing Git manager...")
        git_status = agent.git_manager.check_git_status()
        print(f"   ✅ Git status: {git_status['status']}")

        return True
    except Exception as e:
        print(f"❌ Update agent test failed: {e}")
        return False


async def test_update_cycle_dry_run():
    """Test update cycle in dry-run mode (no actual updates)"""
    print("\n🔄 Testing Update Cycle (Dry Run)...")
    print("=" * 50)

    try:
        # Create a temporary agent for testing
        agent = UpdateAgent(".")

        # Run security audit
        print("   🔒 Running security audit...")
        security_result = await agent.run_security_audit()

        # Check for updates
        print("   📦 Checking for updates...")
        update_check = await agent.check_for_updates()

        # Run tests (without updates)
        print("   🧪 Running test suite...")
        test_result = await agent.run_tests()

        # Generate summary
        summary = {
            "security_status": security_result["overall_status"],
            "python_updates": len(update_check["python"]["updates_available"]),
            "node_updates": len(update_check["node"]["updates_available"]),
            "tests_passed": test_result["overall_status"] == "passed",
            "timestamp": agent.update_session["start_time"],
        }

        print(f"✅ Update cycle dry run completed")
        print(f"   Security: {summary['security_status']}")
        print(
            f"   Updates available: {summary['python_updates']} Python, {summary['node_updates']} Node.js"
        )
        print(f"   Tests: {'✅ Passed' if summary['tests_passed'] else '❌ Failed'}")

        return True
    except Exception as e:
        print(f"❌ Update cycle dry run failed: {e}")
        return False


async def test_cli_integration():
    """Test CLI integration"""
    print("\n🖥️ Testing CLI Integration...")
    print("=" * 50)

    try:
        from agent.core.agents.agent_main import AIAgent

        # Initialize agent
        agent = AIAgent("config/smart_routing_config.json")
        await agent.initialize()

        # Test security audit command
        print("   Testing security-audit command...")
        result = await agent.execute_command("security-audit", {})
        if result.get("success"):
            print("   ✅ security-audit command works")
        else:
            print(f"   ❌ security-audit command failed: {result.get('error')}")

        # Test check-updates command
        print("   Testing check-updates command...")
        result = await agent.execute_command("check-updates", {})
        if result.get("success"):
            print("   ✅ check-updates command works")
        else:
            print(f"   ❌ check-updates command failed: {result.get('error')}")

        # Test update-cycle command
        print("   Testing update-cycle command...")
        result = await agent.execute_command("update-cycle", {"auto_commit": False})
        if result.get("success"):
            print("   ✅ update-cycle command works")
        else:
            print(f"   ❌ update-cycle command failed: {result.get('error')}")

        await agent.shutdown()
        return True
    except Exception as e:
        print(f"❌ CLI integration test failed: {e}")
        return False


async def main():
    """Run all update agent tests"""
    print("🚀 UPDATE AGENT TEST SUITE")
    print("=" * 60)
    print("Testing the new update agent functionality...")
    print()

    tests = [
        ("Security Audit", test_security_audit),
        ("Update Check", test_update_check),
        ("Update Agent", test_update_agent),
        ("Update Cycle (Dry Run)", test_update_cycle_dry_run),
        ("CLI Integration", test_cli_integration),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            success = await test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)

    passed = 0
    failed = 0

    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if success:
            passed += 1
        else:
            failed += 1

    print(f"\nTotal: {passed + failed} tests")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed / (passed + failed) * 100):.1f}%")

    if failed == 0:
        print("\n🎉 ALL TESTS PASSED! Update agent is working correctly.")
    else:
        print(f"\n⚠️ {failed} test(s) failed. Please check the implementation.")

    return failed == 0


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
