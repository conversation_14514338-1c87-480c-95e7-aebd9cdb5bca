# AI Coding Agent - Environment Configuration Example
# Copy this file to .env and update with your actual values

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DB_NAME=ai_coding_agent
DB_USER=ai_coding_user
DB_PASSWORD=your_secure_password_here
DB_HOST=db
DB_PORT=5432

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
# Supabase Project Settings (get these from your Supabase dashboard)
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key_here

# Supabase Database Connection (if using Supabase as primary database)
SUPABASE_DB_URL=postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres

# Supabase Auth Settings
SUPABASE_AUTH_ENABLED=true
SUPABASE_AUTH_REDIRECT_URL=http://localhost:3000/auth/callback
SUPABASE_AUTH_COOKIE_SECURE=false  # Set to true in production

# Supabase Storage Settings
SUPABASE_STORAGE_BUCKET=ai-coding-agent-files
SUPABASE_STORAGE_ENABLED=true

# Supabase Realtime Settings
SUPABASE_REALTIME_ENABLED=true
SUPABASE_REALTIME_CHANNELS=chat,notifications,file-updates

# =============================================================================
# REDIS CONFIGURATION
# =============================================================================
REDIS_URL=redis://redis:6379
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# =============================================================================
# API CONFIGURATION
# =============================================================================
API_URL=http://localhost:8000
API_HOST=0.0.0.0
API_PORT=8000
ENVIRONMENT=development
LOG_LEVEL=INFO

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
NEXT_PUBLIC_API_URL=http://localhost:8000
NEXT_PUBLIC_WS_URL=ws://localhost:8000
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
NODE_ENV=development

# =============================================================================
# OLLAMA CONFIGURATION
# =============================================================================
OLLAMA_URL=http://ollama:11434
OLLAMA_HOST=ollama
OLLAMA_PORT=11434
OLLAMA_MODELS=deepseek-coder:1.3b,yi-coder:1.5b,qwen2.5-coder:3b,starcoder2:3b,mistral:7b-instruct-q4_0

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
SECRET_KEY=your_secret_key_here_change_this_in_production
JWT_SECRET=your_jwt_secret_here_change_this_in_production
ENCRYPTION_KEY=your_encryption_key_here_change_this_in_production

# =============================================================================
# MONITORING CONFIGURATION
# =============================================================================
PROMETHEUS_PORT=9090
GRAFANA_PORT=3001
GRAFANA_PASSWORD=admin
MONITORING_ENABLED=true

# =============================================================================
# BACKUP CONFIGURATION
# =============================================================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *  # Daily at 2 AM
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=/app/backups

# =============================================================================
# FINE-TUNING CONFIGURATION
# =============================================================================
FINE_TUNING_ENABLED=true
GPU_ENABLED=false
MODEL_CACHE_PATH=/app/models
TRAINING_DATA_PATH=/app/data/fine_tuning

# =============================================================================
# WEB SEARCH CONFIGURATION
# =============================================================================
DUCKDUCKGO_API_KEY=your_duckduckgo_api_key_here
WEB_SEARCH_ENABLED=true
RSS_FEED_ENABLED=true

# =============================================================================
# NOTIFICATION CONFIGURATION
# =============================================================================
EMAIL_ENABLED=false
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your_app_password_here

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
MAX_FILE_SIZE=100MB
ALLOWED_FILE_TYPES=py,js,ts,tsx,jsx,json,md,txt,csv
UPLOAD_PATH=/app/uploads

# =============================================================================
# SITE GENERATION CONFIGURATION
# =============================================================================
SITES_PATH=/app/sites
TEMPLATES_PATH=/app/templates
STATIC_PATH=/app/static

# =============================================================================
# LOGGING CONFIGURATION
# =============================================================================
LOG_PATH=/app/logs
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_ROTATION=daily

# =============================================================================
# CACHE CONFIGURATION
# =============================================================================
CACHE_ENABLED=true
CACHE_TTL=3600
CACHE_MAX_SIZE=1000

# =============================================================================
# PERFORMANCE CONFIGURATION
# =============================================================================
WORKER_PROCESSES=4
MAX_CONNECTIONS=100
REQUEST_TIMEOUT=30
RESPONSE_TIMEOUT=30

# =============================================================================
# DEVELOPMENT CONFIGURATION
# =============================================================================
DEBUG=false
HOT_RELOAD=true
AUTO_RESTART=true
TEST_MODE=false

# =============================================================================
# PRODUCTION OVERRIDES (uncomment for production)
# =============================================================================
# ENVIRONMENT=production
# LOG_LEVEL=WARNING
# DEBUG=false
# HOT_RELOAD=false
# AUTO_RESTART=false
# API_URL=https://api.aicodingagent.com
# NEXT_PUBLIC_API_URL=https://api.aicodingagent.com
# NEXT_PUBLIC_WS_URL=wss://api.aicodingagent.com
# SUPABASE_AUTH_COOKIE_SECURE=true
# SUPABASE_AUTH_REDIRECT_URL=https://yourdomain.com/auth/callback
