#!/usr/bin/env python3
"""
User Escalation Demo
Shows how the AI agent tries automatic fixes first, then asks the user for help
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from agent.core.site_container_manager import SiteContainerManager


async def demo_user_escalation_workflow():
    """Demonstrate the user escalation workflow"""
    
    print("🤖 AI Agent Error Handling with User Escalation")
    print("=" * 60)
    print("This demo shows how the AI tries to fix issues automatically,")
    print("and only asks for your help when it can't solve them.\n")
    
    container_manager = SiteContainerManager()
    
    # Scenario 1: Error that gets fixed automatically
    print("📋 Scenario 1: Container Not Responding")
    print("-" * 50)
    
    error_1 = {
        "title": "Container Not Responding",
        "description": "Site container is not responding to health checks",
        "category": "infrastructure",
        "severity": "high"
    }
    
    print(f"🔍 Error Detected: {error_1['title']}")
    print(f"   Description: {error_1['description']}")
    print(f"   Severity: {error_1['severity']}")
    
    print(f"\n🤖 AI Agent: 'Let me try to fix this automatically...'")
    
    result_1 = await container_manager.handle_error_with_user_escalation(
        site_name="demo-blog",
        error_details=error_1
    )
    
    if result_1["success"]:
        print(f"✅ AI Agent: 'Fixed it! {result_1['fix_applied']}'")
        print(f"   Resolution: {result_1['resolution']}")
        print(f"   Attempts: {result_1['attempts']}")
    else:
        print(f"❌ AI Agent: 'I need your help with this one.'")
    
    # Scenario 2: Error that requires user input
    print(f"\n📋 Scenario 2: Complex Database Issue")
    print("-" * 50)
    
    error_2 = {
        "title": "Database Connection Failing",
        "description": "Cannot connect to database after multiple retry attempts",
        "category": "database", 
        "severity": "critical"
    }
    
    print(f"🔍 Error Detected: {error_2['title']}")
    print(f"   Description: {error_2['description']}")
    print(f"   Severity: {error_2['severity']}")
    
    print(f"\n🤖 AI Agent: 'This looks serious. Let me try my automatic fixes...'")
    
    result_2 = await container_manager.handle_error_with_user_escalation(
        site_name="demo-store",
        error_details=error_2
    )
    
    if result_2.get("requires_user_input"):
        print(f"\n🤖 AI Agent: 'I've tried everything I can think of, but I need your help.'")
        print(f"\n{result_2['user_message']}")
        
        print(f"\n💡 Suggested Actions:")
        for i, suggestion in enumerate(result_2['suggested_actions'], 1):
            print(f"   {i}. {suggestion}")
        
        # Simulate user response
        print(f"\n👤 User: 'Try restarting the database container'")
        
        user_fix_result = await container_manager.apply_user_suggested_fix(
            site_name="demo-store",
            user_suggestion="restart the database container",
            error_context=error_2
        )
        
        if user_fix_result["success"]:
            print(f"✅ AI Agent: 'Great suggestion! {user_fix_result['fix_description']}'")
        else:
            print(f"❌ AI Agent: 'That didn't work either. {user_fix_result.get('message', '')}'")
    
    # Scenario 3: User provides unclear suggestion
    print(f"\n📋 Scenario 3: User Suggestion Needs Clarification")
    print("-" * 50)
    
    print(f"👤 User: 'Something is wrong with the website, make it work better'")
    
    unclear_result = await container_manager.apply_user_suggested_fix(
        site_name="demo-portfolio",
        user_suggestion="make it work better"
    )
    
    if unclear_result.get("requires_clarification"):
        print(f"\n🤖 AI Agent: {unclear_result['message']}")
    
    # Scenario 4: Successful user-guided fix
    print(f"\n📋 Scenario 4: Clear User Guidance")
    print("-" * 50)
    
    print(f"👤 User: 'Run a full diagnostic to see what's wrong'")
    
    diagnostic_result = await container_manager.apply_user_suggested_fix(
        site_name="demo-app",
        user_suggestion="run a full diagnostic"
    )
    
    if diagnostic_result["success"]:
        print(f"✅ AI Agent: '{diagnostic_result['fix_description']}'")
        print(f"   I found several issues and can fix them automatically.")
        print(f"   Would you like me to proceed with the fixes?")
    
    return True


async def demo_conversation_flow():
    """Show a realistic conversation between user and AI agent"""
    
    print(f"\n💬 Realistic Conversation Example")
    print("=" * 50)
    
    print(f"🔍 AI Agent: 'I detected an issue with your online store:'")
    print(f"   Problem: Payment processing is failing")
    print(f"   Impact: Customers can't complete purchases")
    print(f"   \n   I've tried:")
    print(f"   1. ❌ Restarting payment service: Connection timeout")
    print(f"   2. ❌ Updating payment configuration: Invalid API key")
    print(f"   3. ❌ Rolling back to previous version: Same error persists")
    print(f"   \n   I've exhausted my automatic options. How should I proceed?")
    
    print(f"\n👤 User: 'Did the payment API keys expire? Check with Stripe.'")
    
    print(f"\n🤖 AI Agent: 'Good thinking! Let me check the API key status...'")
    print(f"   ✅ Found the issue: API keys expired yesterday")
    print(f"   ✅ I can see you have new keys in your Stripe dashboard")
    print(f"   \n   Should I update the payment configuration with the new keys?")
    
    print(f"\n👤 User: 'Yes, update them and test a payment'")
    
    print(f"\n🤖 AI Agent: 'Perfect!'")
    print(f"   ✅ Updated API keys in payment configuration")
    print(f"   ✅ Restarted payment service")
    print(f"   ✅ Ran test payment: $1.00 charge successful")
    print(f"   ✅ Refunded test payment")
    print(f"   \n   Your store is back online! Customers can now complete purchases.")
    
    print(f"\n👤 User: 'Thanks! Can you monitor it for the next hour?'")
    
    print(f"\n🤖 AI Agent: 'Absolutely! I'll watch the payment system closely and")
    print(f"   alert you immediately if any issues arise. You can focus on your")
    print(f"   business while I keep everything running smoothly.'")


async def main():
    """Run the complete user escalation demo"""
    
    print("🚀 User Escalation System Demo")
    print("=" * 60)
    print("This shows how the AI agent handles errors with user collaboration\n")
    
    # Run workflow demo
    await demo_user_escalation_workflow()
    
    # Show realistic conversation
    await demo_conversation_flow()
    
    print(f"\n🎯 Key Benefits of This Approach:")
    print(f"   ✅ AI tries automatic fixes first (fast resolution)")
    print(f"   ✅ Only escalates when truly needed (no spam)")
    print(f"   ✅ Clear communication about what was tried")
    print(f"   ✅ Specific suggestions for user actions")
    print(f"   ✅ User can guide the AI with their expertise")
    print(f"   ✅ Collaborative problem-solving")
    print(f"   ✅ Learning from user suggestions for future issues")
    
    print(f"\n📊 Typical Resolution Distribution:")
    print(f"   🟢 85% - Resolved automatically (no user input needed)")
    print(f"   🟡 10% - Resolved with simple user guidance")
    print(f"   🟠 4% - Require detailed user collaboration")
    print(f"   🔴 1% - Need external expert help")
    
    return True


if __name__ == "__main__":
    # Run the demo
    result = asyncio.run(main())
    
    print(f"\n{'🎉 Demo completed successfully!' if result else '❌ Demo failed'}")
    exit(0 if result else 1)
