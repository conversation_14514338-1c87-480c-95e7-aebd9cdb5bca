"""
Error Analysis and Explanation System
Provides detailed error explanations and solutions
"""

import ast
import json
import re
import traceback
from pathlib import Path
from typing import Any, Dict, List, Optional


class ErrorExplainer:
    """Main error explanation engine"""

    def __init__(self):
        self.knowledge_base = self._load_error_patterns()
        self.solution_templates = self._load_solution_templates()

    def _load_error_patterns(self) -> Dict[str, Any]:
        """Load error pattern knowledge base"""
        patterns = {
            "ImportError": {
                "patterns": [
                    r"No module named '(.+)'",
                    r"cannot import name '(.+)'",
                    r"ModuleNotFoundError: No module named '(.+)'",
                ],
                "common_causes": [
                    "Missing package installation",
                    "Incorrect import statement",
                    "Package not in PYTHONPATH",
                    "Virtual environment not activated",
                ],
                "solutions": [
                    "Install missing package: pip install {module}",
                    "Check import statement syntax",
                    "Verify package is installed in correct environment",
                    "Add package path to PYTHONPATH",
                ],
            },
            "AttributeError": {
                "patterns": [
                    r"'(.+)' object has no attribute '(.+)'",
                    r"module '(.+)' has no attribute '(.+)'",
                ],
                "common_causes": [
                    "Typo in attribute/method name",
                    "Wrong object type",
                    "Outdated package version",
                    "Missing initialization",
                ],
                "solutions": [
                    "Check attribute/method spelling",
                    "Verify object type with type()",
                    "Update package: pip install --upgrade {package}",
                    "Check object initialization",
                ],
            },
            "TypeError": {
                "patterns": [
                    r"takes (\d+) positional argument[s]? but (\d+) (was|were) given",
                    r"unsupported operand type\(s\)",
                    r"'(.+)' object is not (callable|iterable|subscriptable)",
                ],
                "common_causes": [
                    "Incorrect number of arguments",
                    "Wrong data type provided",
                    "Misuse of operators",
                    "Missing method implementation",
                ],
                "solutions": [
                    "Check function signature and arguments",
                    "Verify data types with isinstance()",
                    "Review operator usage documentation",
                    "Implement required methods",
                ],
            },
            "SyntaxError": {
                "patterns": [
                    r"invalid syntax",
                    r"unexpected EOF while parsing",
                    r"EOL while scanning string literal",
                ],
                "common_causes": [
                    "Missing parentheses, brackets, or quotes",
                    "Incorrect indentation",
                    "Invalid Python syntax",
                    "Unclosed strings or brackets",
                ],
                "solutions": [
                    "Check for balanced brackets and quotes",
                    "Verify indentation consistency",
                    "Review Python syntax rules",
                    "Use IDE syntax highlighting",
                ],
            },
            "ValueError": {
                "patterns": [
                    r"invalid literal for int\(\) with base \d+",
                    r"could not convert string to float",
                    r"not enough values to unpack",
                ],
                "common_causes": [
                    "Invalid data format",
                    "Type conversion failure",
                    "Incorrect data structure",
                ],
                "solutions": [
                    "Validate input data format",
                    "Use try-except for type conversion",
                    "Check data structure before processing",
                ],
            },
        }
        return patterns

    def _load_solution_templates(self) -> Dict[str, str]:
        """Load solution templates"""
        return {
            "install_package": "pip install {package_name}",
            "upgrade_package": "pip install --upgrade {package_name}",
            "check_import": "from {module} import {name}",
            "type_check": "isinstance({variable}, {expected_type})",
            "debug_print": "print(f'{variable} = {{{variable}}}')",
            "exception_handler": """
try:
    {code_block}
except {exception_type} as e:
    print(f"Error: {e}")
    # Handle error appropriately
            """.strip(),
        }

    def analyze_error(
        self, error: Exception, context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Analyze error and provide detailed explanation"""
        error_type = type(error).__name__
        error_message = str(error)

        # Get stack trace
        stack_trace = traceback.format_exc()

        # Find matching pattern
        pattern_match = self._find_pattern_match(error_type, error_message)

        # Generate explanation
        explanation = {
            "error_type": error_type,
            "error_message": error_message,
            "stack_trace": stack_trace,
            "root_cause": self._identify_root_cause(error, context),
            "common_causes": self.knowledge_base.get(error_type, {}).get(
                "common_causes", []
            ),
            "solutions": self._generate_solutions(error, context, pattern_match),
            "documentation_links": self._get_documentation_links(error_type),
            "code_context": self._extract_code_context(context),
            "similar_errors": self._find_similar_errors(error, context),
        }

        return explanation

    def _find_pattern_match(
        self, error_type: str, error_message: str
    ) -> Optional[Dict[str, Any]]:
        """Find matching error pattern"""
        if error_type not in self.knowledge_base:
            return None

        patterns = self.knowledge_base[error_type]["patterns"]
        for pattern in patterns:
            match = re.search(pattern, error_message)
            if match:
                return {"pattern": pattern, "groups": match.groups()}
        return None

    def _identify_root_cause(self, error: Exception, context: Dict[str, Any]) -> str:
        """Identify the root cause of the error"""
        error_type = type(error).__name__

        # Analyze code context
        code_context = context.get("code_context", {})
        file_path = code_context.get("file_path", "")
        line_number = code_context.get("line_number", 0)

        # Specific analysis based on error type
        if error_type == "ImportError":
            return self._analyze_import_error(error, context)
        elif error_type == "AttributeError":
            return self._analyze_attribute_error(error, context)
        elif error_type == "TypeError":
            return self._analyze_type_error(error, context)
        elif error_type == "SyntaxError":
            return self._analyze_syntax_error(error, context)
        else:
            return f"General {error_type}: {str(error)}"

    def _analyze_import_error(self, error: Exception, context: Dict[str, Any]) -> str:
        """Analyze import errors specifically"""
        error_msg = str(error)

        if "No module named" in error_msg:
            module = error_msg.split("'")[1]
            return f"Missing Python module: {module}"
        elif "cannot import name" in error_msg:
            name = error_msg.split("'")[1]
            return f"Cannot import name '{name}' from module"
        else:
            return "Import configuration issue"

    def _analyze_attribute_error(
        self, error: Exception, context: Dict[str, Any]
    ) -> str:
        """Analyze attribute errors specifically"""
        error_msg = str(error)
        parts = error_msg.split("'")
        if len(parts) >= 4:
            obj_type = parts[1]
            attr_name = parts[3]
            return f"Object of type '{obj_type}' does not have attribute '{attr_name}'"
        return "Attribute access issue"

    def _analyze_type_error(self, error: Exception, context: Dict[str, Any]) -> str:
        """Analyze type errors specifically"""
        error_msg = str(error)

        if "takes" in error_msg and "argument" in error_msg:
            return "Function argument mismatch"
        elif "unsupported operand type" in error_msg:
            return "Incompatible data types for operation"
        elif "object is not" in error_msg:
            return "Object type does not support required operation"
        else:
            return "Type-related operation failure"

    def _analyze_syntax_error(self, error: Exception, context: Dict[str, Any]) -> str:
        """Analyze syntax errors specifically"""
        error_msg = str(error)

        if "invalid syntax" in error_msg:
            return "Python syntax violation"
        elif "unexpected EOF" in error_msg:
            return "Incomplete statement or missing closing bracket"
        elif "EOL while scanning string literal" in error_msg:
            return "Unclosed string literal"
        else:
            return "Syntax structure issue"

    def _generate_solutions(
        self,
        error: Exception,
        context: Dict[str, Any],
        pattern_match: Optional[Dict[str, Any]],
    ) -> List[Dict[str, Any]]:
        """Generate specific solutions for the error"""
        error_type = type(error).__name__
        solutions = []

        # Get base solutions
        base_solutions = self.knowledge_base.get(error_type, {}).get("solutions", [])

        # Generate specific solutions
        for solution in base_solutions:
            specific_solution = self._customize_solution(
                solution, error, context, pattern_match
            )
            solutions.append(
                {
                    "description": specific_solution,
                    "code_example": self._generate_code_example(
                        error, context, specific_solution
                    ),
                    "difficulty": self._estimate_difficulty(specific_solution),
                    "estimated_time": self._estimate_time(specific_solution),
                }
            )

        return solutions

    def _customize_solution(
        self,
        solution: str,
        error: Exception,
        context: Dict[str, Any],
        pattern_match: Optional[Dict[str, Any]],
    ) -> str:
        """Customize solution based on error context"""
        error_msg = str(error)

        # Replace placeholders
        if pattern_match and "groups" in pattern_match:
            groups = pattern_match["groups"]
            if len(groups) >= 1:
                solution = solution.replace("{module}", groups[0])
                solution = solution.replace("{package}", groups[0])
            if len(groups) >= 2:
                solution = solution.replace("{name}", groups[1])

        return solution

    def _generate_code_example(
        self, error: Exception, context: Dict[str, Any], solution: str
    ) -> str:
        """Generate code example for the solution"""
        error_type = type(error).__name__

        if error_type == "ImportError":
            return self._generate_import_example(error, context)
        elif error_type == "AttributeError":
            return self._generate_attribute_example(error, context)
        elif error_type == "TypeError":
            return self._generate_type_example(error, context)
        else:
            return "# Example solution would be generated here"

    def _generate_import_example(
        self, error: Exception, context: Dict[str, Any]
    ) -> str:
        """Generate import error solution example"""
        return """
# Solution for ImportError
# 1. Install missing package
#    pip install missing-package-name

# 2. Check import statement
try:
    import missing_package
except ImportError:
    print("Package not found. Install with: pip install missing-package-name")
        """.strip()

    def _generate_attribute_example(
        self, error: Exception, context: Dict[str, Any]
    ) -> str:
        """Generate attribute error solution example"""
        return """
# Solution for AttributeError
# Check if attribute exists before accessing
obj = some_object()
if hasattr(obj, 'desired_attribute'):
    value = obj.desired_attribute
else:
    print("Attribute not found")
    # Alternative: use getattr with default
    value = getattr(obj, 'desired_attribute', default_value)
        """.strip()

    def _generate_type_example(self, error: Exception, context: Dict[str, Any]) -> str:
        """Generate type error solution example"""
        return """
# Solution for TypeError
# Check types before operations
def safe_add(a, b):
    if isinstance(a, (int, float)) and isinstance(b, (int, float)):
        return a + b
    else:
        raise TypeError("Both arguments must be numbers")

# Or use type conversion
try:
    result = int(user_input)
except ValueError:
    print("Invalid input: please enter a valid number")
        """.strip()

    def _get_documentation_links(self, error_type: str) -> List[str]:
        """Get relevant documentation links"""
        base_links = {
            "ImportError": [
                "https://docs.python.org/3/library/exceptions.html#ImportError",
                "https://pip.pypa.io/en/stable/user_guide/",
            ],
            "AttributeError": [
                "https://docs.python.org/3/library/exceptions.html#AttributeError",
                "https://docs.python.org/3/tutorial/classes.html",
            ],
            "TypeError": [
                "https://docs.python.org/3/library/exceptions.html#TypeError",
                "https://docs.python.org/3/library/functions.html#type",
            ],
            "SyntaxError": [
                "https://docs.python.org/3/library/exceptions.html#SyntaxError",
                "https://docs.python.org/3/reference/grammar.html",
            ],
        }

        return base_links.get(
            error_type, ["https://docs.python.org/3/library/exceptions.html"]
        )

    def _extract_code_context(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Extract relevant code context"""
        code_context = context.get("code_context", {})

        # Try to read file content
        file_path = code_context.get("file_path")
        if file_path and Path(file_path).exists():
            try:
                with open(file_path, "r") as f:
                    lines = f.readlines()
                    line_number = code_context.get("line_number", 1)

                    # Get context around error line
                    start = max(0, line_number - 3)
                    end = min(len(lines), line_number + 3)

                    return {
                        "file_path": file_path,
                        "line_number": line_number,
                        "context_lines": lines[start:end],
                        "error_line": (
                            lines[line_number - 1] if line_number <= len(lines) else ""
                        ),
                    }
            except Exception:
                pass

        return code_context

    def _find_similar_errors(
        self, error: Exception, context: Dict[str, Any]
    ) -> List[Dict[str, Any]]:
        """Find similar errors from knowledge base"""
        # This would typically query a database or external service
        return []

    def _estimate_difficulty(self, solution: str) -> str:
        """Estimate solution difficulty"""
        if "pip install" in solution:
            return "easy"
        elif "check" in solution or "verify" in solution:
            return "medium"
        else:
            return "hard"

    def _estimate_time(self, solution: str) -> str:
        """Estimate time to implement solution"""
        if "pip install" in solution:
            return "1-2 minutes"
        elif "check" in solution or "verify" in solution:
            return "5-10 minutes"
        else:
            return "15-30 minutes"


# Global error explainer instance
error_explainer = ErrorExplainer()
