#!/usr/bin/env python3
"""
Simple test script to verify monitoring endpoints work correctly.
"""

import asyncio
import json
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agent.monitoring.monitoring_agent import (
    MonitoringAgent,
    start_monitoring_agent,
    stop_monitoring_agent,
)


async def test_monitoring_endpoints():
    """Test monitoring endpoints directly"""
    print("🧪 Testing Monitoring Endpoints")
    print("=" * 50)

    try:
        # Start monitoring agent
        await start_monitoring_agent()
        print("✅ Monitoring agent started")

        # Wait for some metrics to be collected
        await asyncio.sleep(3)

        # Test health endpoint
        from monitoring_agent import monitoring_agent

        if monitoring_agent:
            # Test async health endpoint
            health_data = await monitoring_agent.get_current_health()
            print("✅ Async health endpoint working")
            print(f"   Status: {health_data.status}")
            print(f"   CPU: {health_data.metrics.cpu_percent:.1f}%")
            print(f"   Memory: {health_data.metrics.memory_percent:.1f}%")
            print(f"   Disk: {health_data.metrics.disk_percent:.1f}%")
            print(f"   Alerts: {len(health_data.alerts)}")

            # Test sync health endpoint
            sync_health = monitoring_agent.get_current_health_sync()
            print("✅ Sync health endpoint working")
            print(f"   Status: {sync_health['status']}")
            print(f"   CPU: {sync_health['metrics']['cpu_percent']:.1f}%")
            print(f"   Memory: {sync_health['metrics']['memory_percent']:.1f}%")
            print(f"   Disk: {sync_health['metrics']['disk_percent']:.1f}%")

            # Test metrics history
            history = monitoring_agent.get_metrics_history(limit=5)
            print(f"✅ Metrics history working: {len(history)} metrics")

            # Test configuration
            config = monitoring_agent.config
            print("✅ Configuration working")
            print(f"   CPU threshold: {config.cpu_threshold}%")
            print(f"   Memory threshold: {config.memory_threshold}%")
            print(f"   Disk threshold: {config.disk_threshold}%")

        else:
            print("❌ Monitoring agent not available")
            return False

        # Stop monitoring agent
        await stop_monitoring_agent()
        print("✅ Monitoring agent stopped")

        print("\n🎉 All endpoint tests passed!")
        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


if __name__ == "__main__":
    success = asyncio.run(test_monitoring_endpoints())
    sys.exit(0 if success else 1)
