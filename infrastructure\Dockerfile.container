FROM python:3.11-slim AS builder
ENV PIP_NO_CACHE_DIR=1
WORKDIR /app
RUN python -m venv /opt/venv \
 && . /opt/venv/bin/activate \
 && pip install --upgrade pip \
 && pip install --no-cache-dir fastapi uvicorn docker PyY<PERSON><PERSON> requests

FROM python:3.11-slim AS runtime
ENV PATH="/opt/venv/bin:$PATH" PYTHONUNBUFFERED=1 PYTHONPATH=/app
WORKDIR /app
COPY --from=builder /opt/venv /opt/venv
COPY core/agents/container_agent.py /app/core/agents/container_agent.py
COPY core/website_generator.py /app/core/website_generator.py
COPY config/container_agent_config.json /app/config/container_agent_config.json
RUN adduser --system appuser \
 && chown -R appuser:appuser /app
USER appuser
EXPOSE 5001
HEALTHCHECK --interval=30s --timeout=10s --retries=3 CMD curl -f http://localhost:5001/health || exit 1
ENTRYPOINT ["python", "-u", "/app/core/agents/container_agent.py"]
