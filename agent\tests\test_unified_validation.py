#!/usr/bin/env python3
"""
Tests for unified_validation module
"""

import json
import os
import tempfile
from datetime import datetime
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

import pytest

from agent.utils.unified_validation import (
    UnifiedValidation,
    ValidationLevel,
    ValidationResult,
)


class TestValidationLevel:
    """Test ValidationLevel enum"""

    def test_validation_levels(self):
        """Test that all validation levels are defined"""
        assert ValidationLevel.CRITICAL.value == "critical"
        assert ValidationLevel.HIGH.value == "high"
        assert ValidationLevel.MEDIUM.value == "medium"
        assert ValidationLevel.LOW.value == "low"
        assert ValidationLevel.INFO.value == "info"


class TestValidationResult:
    """Test ValidationResult class"""

    def test_validation_result_creation(self):
        """Test creating a ValidationResult"""
        result = ValidationResult(
            success=True, level=ValidationLevel.INFO, message="Test message"
        )
        assert result.success is True
        assert result.level == ValidationLevel.INFO
        assert result.message == "Test message"
        assert result.timestamp is not None

    def test_validation_result_with_optional_fields(self):
        """Test creating a ValidationResult with optional fields"""
        details = {"key": "value"}
        timestamp = datetime.now()
        result = ValidationResult(
            success=False,
            level=ValidationLevel.HIGH,
            message="Error message",
            details=details,
            timestamp=timestamp,
        )
        assert result.details == details
        assert result.timestamp == timestamp


class TestUnifiedValidation:
    """Test UnifiedValidation class"""

    def test_unified_validation_creation(self):
        """Test creating a UnifiedValidation instance"""
        validator = UnifiedValidation()
        assert validator is not None
        assert hasattr(validator, "dangerous_patterns")
        assert hasattr(validator, "dangerous_extensions")
        assert hasattr(validator, "allowed_directories")

    def test_validate_path_safety_success(self):
        """Test successful path safety validation"""
        validator = UnifiedValidation()
        result = validator.validate_path_safety("projects/test.txt", "read")
        assert result.success is True
        assert result.level == ValidationLevel.INFO

    def test_validate_path_safety_dangerous_pattern(self):
        """Test path safety validation with dangerous pattern"""
        validator = UnifiedValidation()
        result = validator.validate_path_safety("../../../etc/passwd", "read")
        assert result.success is False
        assert result.level == ValidationLevel.HIGH
        assert "path safety validation failed" in result.message.lower()

    def test_validate_path_safety_dangerous_extension(self):
        """Test path safety validation with dangerous extension"""
        validator = UnifiedValidation()
        result = validator.validate_path_safety("projects/test.exe", "write")
        assert result.success is False
        assert result.level == ValidationLevel.HIGH
        assert "path safety validation failed" in result.message.lower()

    def test_validate_file_operation_success(self):
        """Test successful file operation validation"""
        validator = UnifiedValidation()
        result = validator.validate_file_operation(
            "copy", "projects/source.txt", "projects/dest.txt"
        )
        assert result.success is True
        assert result.level == ValidationLevel.INFO

    def test_validate_file_operation_dangerous_source(self):
        """Test file operation validation with dangerous source"""
        validator = UnifiedValidation()
        result = validator.validate_file_operation(
            "copy", "../../../etc/passwd", "projects/dest.txt"
        )
        assert result.success is False
        assert result.level == ValidationLevel.HIGH

    def test_validate_security_configuration(self):
        """Test security configuration validation"""
        validator = UnifiedValidation()
        result = validator.validate_security_configuration()
        # This test may pass or fail depending on the actual system state
        assert isinstance(result, ValidationResult)
        assert result.level in [ValidationLevel.INFO, ValidationLevel.MEDIUM]

    def test_validate_system_health(self):
        """Test system health validation"""
        validator = UnifiedValidation()
        result = validator.validate_system_health()
        # This test may pass or fail depending on the actual system state
        assert isinstance(result, ValidationResult)
        assert result.level in [ValidationLevel.INFO, ValidationLevel.MEDIUM]

    def test_validate_network_connectivity(self):
        """Test network connectivity validation"""
        validator = UnifiedValidation()
        result = validator.validate_network_connectivity()
        # This test may pass or fail depending on the actual system state
        assert isinstance(result, ValidationResult)
        assert result.level in [ValidationLevel.INFO, ValidationLevel.MEDIUM]

    def test_validate_database(self):
        """Test database validation"""
        validator = UnifiedValidation()
        result = validator.validate_database()
        # This test may pass or fail depending on the actual system state
        assert isinstance(result, ValidationResult)
        assert result.level in [ValidationLevel.INFO, ValidationLevel.MEDIUM]

    def test_validate_configuration(self):
        """Test configuration validation"""
        validator = UnifiedValidation()
        result = validator.validate_configuration()
        # This test may pass or fail depending on the actual system state
        assert isinstance(result, ValidationResult)
        assert result.level in [ValidationLevel.INFO, ValidationLevel.MEDIUM]

    def test_run_full_validation(self):
        """Test running full validation"""
        validator = UnifiedValidation()
        results = validator.run_full_validation()
        assert isinstance(results, dict)
        assert "timestamp" in results
        assert "validations" in results

    def test_validate_command_success(self):
        """Test successful command validation"""
        validator = UnifiedValidation()
        result = validator.validate_command("ls -la")
        assert result.success is True
        assert result.level == ValidationLevel.INFO

    def test_validate_command_dangerous(self):
        """Test command validation with dangerous command"""
        validator = UnifiedValidation()
        result = validator.validate_command("rm -rf /")
        assert result.success is False
        assert result.level == ValidationLevel.HIGH

    def test_is_in_allowed_directory(self):
        """Test allowed directory checking"""
        validator = UnifiedValidation()

        # Test allowed directory
        allowed_path = Path("projects/test.txt")
        assert validator._is_in_allowed_directory(allowed_path) is True

        # Test disallowed directory
        disallowed_path = Path("/etc/test.txt")
        assert validator._is_in_allowed_directory(disallowed_path) is False


if __name__ == "__main__":
    pytest.main([__file__])
