{"exported_at": "2025-07-23T17:33:28.206747", "model_performance": {"deepseek-coder:1.3b": {"model_name": "deepseek-coder:1.3b", "response_time": 0.10888791084289551, "token_count": 40, "success_rate": 0.984, "error_count": 2, "last_used": "2025-07-23 17:33:23.183758", "total_requests": 125, "average_response_time": 0.5468794437255861}, "yi-coder:1.5b": {"model_name": "yi-coder:1.5b", "response_time": 1.2, "token_count": 180, "success_rate": 0.92, "error_count": 3, "last_used": "2025-07-23 17:33:22.630260", "total_requests": 95, "average_response_time": 1.15}, "qwen2.5-coder:3b": {"model_name": "qwen2.5-coder:3b", "response_time": 0.9, "token_count": 160, "success_rate": 0.94, "error_count": 1, "last_used": "2025-07-23 17:33:22.630263", "total_requests": 110, "average_response_time": 0.92}, "starcoder2:3b": {"model_name": "starcoder2:3b", "response_time": 1.1, "token_count": 170, "success_rate": 0.93, "error_count": 2, "last_used": "2025-07-23 17:33:22.630266", "total_requests": 105, "average_response_time": 1.08}, "mistral:7b-instruct-q4_0": {"model_name": "mistral:7b-instruct-q4_0", "response_time": 1.5, "token_count": 200, "success_rate": 0.96, "error_count": 1, "last_used": "2025-07-23 17:33:22.630268", "total_requests": 130, "average_response_time": 1.48}}, "metrics": {"total_requests": 5, "cache_hits": 0, "cache_misses": 5, "average_response_time": 0.1093971488237381, "optimization_cycles": 1}, "request_history": []}