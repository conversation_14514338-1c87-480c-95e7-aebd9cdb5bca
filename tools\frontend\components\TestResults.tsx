import React, { useState, useEffect, useRef, useCallback, memo } from 'react';

interface TestResultsProps {
  onClose?: () => void;
}

interface TestStep {
  id: string;
  name: string;
  description: string;
  status: 'pending' | 'running' | 'passed' | 'failed' | 'skipped';
  start_time?: number;
  end_time?: number;
  error?: string;
  details?: Record<string, any>;
  duration?: number;
}

interface TestScenario {
  id: string;
  name: string;
  description: string;
  steps: TestStep[];
  status: 'pending' | 'running' | 'passed' | 'failed';
  start_time?: number;
  end_time?: number;
  duration?: number;
  passed_steps: number;
  failed_steps: number;
}

interface TestEvent {
  type: 'connection_established' | 'run_start' | 'scenario_start' | 'step_start' | 'step_complete' | 'scenario_complete' | 'run_complete' | 'error' | 'stream_complete';
  run_id?: string;
  backend?: string;
  config?: Record<string, any>;
  scenario?: TestScenario;
  scenario_id?: string;
  step?: TestStep;
  duration?: number;
  total_scenarios?: number;
  total_steps?: number;
  passed_steps?: number;
  success_rate?: number;
  error?: string;
  timestamp: string;
}

interface TestRun {
  run_id: string;
  backend: string;
  status: 'connecting' | 'running' | 'completed' | 'error';
  scenarios: TestScenario[];
  start_time?: number;
  end_time?: number;
  duration?: number;
  total_steps: number;
  passed_steps: number;
  success_rate: number;
}

const TestResults = memo<TestResultsProps>(({ onClose }) => {
  const [isConnected, setIsConnected] = useState(false);
  const [isRunning, setIsRunning] = useState(false);
  const [currentRun, setCurrentRun] = useState<TestRun | null>(null);
  const [events, setEvents] = useState<TestEvent[]>([]);
  const [error, setError] = useState<string | null>(null);
  const [backend, setBackend] = useState<'http' | 'playwright'>('http');
  const [baseUrl, setBaseUrl] = useState('http://localhost:8000');
  const [timeout, setTimeout] = useState(30);

  const wsRef = useRef<WebSocket | null>(null);
  const eventsEndRef = useRef<HTMLDivElement>(null);

  // Auto-scroll to bottom when new events arrive
  const scrollToBottom = useCallback(() => {
    eventsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [events, scrollToBottom]);

  // Connect to WebSocket
  const connectWebSocket = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    const wsUrl = `ws://localhost:8000/api/v1/orchestration/smoke/stream?backend=${backend}&base_url=${encodeURIComponent(baseUrl)}&timeout=${timeout}`;

    try {
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        setIsConnected(true);
        setError(null);
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data: TestEvent = JSON.parse(event.data);
          setEvents(prev => [...prev, data]);

          // Update current run state based on event type
          switch (data.type) {
            case 'connection_established':
              setCurrentRun({
                run_id: data.run_id || 'unknown',
                backend: data.backend || backend,
                status: 'connecting',
                scenarios: [],
                total_steps: 0,
                passed_steps: 0,
                success_rate: 0
              });
              break;

            case 'run_start':
              setIsRunning(true);
              setCurrentRun(prev => prev ? {
                ...prev,
                run_id: data.run_id || prev.run_id,
                status: 'running',
                start_time: Date.now()
              } : null);
              break;

            case 'scenario_start':
              if (data.scenario) {
                setCurrentRun(prev => prev ? {
                  ...prev,
                  scenarios: [...prev.scenarios.filter(s => s.id !== data.scenario!.id), data.scenario!]
                } : null);
              }
              break;

            case 'scenario_complete':
              if (data.scenario) {
                setCurrentRun(prev => prev ? {
                  ...prev,
                  scenarios: prev.scenarios.map(s => s.id === data.scenario!.id ? data.scenario! : s)
                } : null);
              }
              break;

            case 'run_complete':
              setIsRunning(false);
              setCurrentRun(prev => prev ? {
                ...prev,
                status: 'completed',
                end_time: Date.now(),
                duration: data.duration,
                total_steps: data.total_steps || 0,
                passed_steps: data.passed_steps || 0,
                success_rate: data.success_rate || 0
              } : null);
              break;

            case 'error':
              setError(data.error || 'Unknown error');
              setIsRunning(false);
              setCurrentRun(prev => prev ? { ...prev, status: 'error' } : null);
              break;

            case 'stream_complete':
              setIsConnected(false);
              break;
          }
        } catch (err) {
          console.error('Error parsing WebSocket message:', err);
          setError('Error parsing test results');
        }
      };

      wsRef.current.onerror = (err) => {
        console.error('WebSocket error:', err);
        setError('WebSocket connection error');
        setIsConnected(false);
      };

      wsRef.current.onclose = () => {
        setIsConnected(false);
        setIsRunning(false);
      };

    } catch (err) {
      console.error('Error creating WebSocket:', err);
      setError('Failed to create WebSocket connection');
    }
  }, [backend, baseUrl, timeout]);

  // Disconnect WebSocket
  const disconnectWebSocket = useCallback(() => {
    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }
    setIsConnected(false);
    setIsRunning(false);
  }, []);

  // Start smoke tests
  const startTests = useCallback(() => {
    setEvents([]);
    setError(null);
    setCurrentRun(null);
    connectWebSocket();
  }, [connectWebSocket]);

  // Clear results
  const clearResults = useCallback(() => {
    setEvents([]);
    setCurrentRun(null);
    setError(null);
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      disconnectWebSocket();
    };
  }, [disconnectWebSocket]);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'passed': return 'text-green-600';
      case 'failed': return 'text-red-600';
      case 'running': return 'text-blue-600';
      case 'pending': return 'text-gray-500';
      case 'skipped': return 'text-yellow-600';
      default: return 'text-gray-500';
    }
  };

  // Get status icon
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'passed':
        return <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" /></svg>;
      case 'failed':
        return <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" /></svg>;
      case 'running':
        return <svg className="w-4 h-4 animate-spin" fill="currentColor" viewBox="0 0 20 20"><path d="M10 3v3m0 8v3m7-7h-3M7 10H4m11.314-5.314l-2.121 2.121M8.807 13.193l-2.121 2.121m8.485 0l-2.121-2.121M8.807 6.807L6.686 4.686" stroke="currentColor" strokeWidth="2" strokeLinecap="round" /></svg>;
      case 'pending':
        return <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z" clipRule="evenodd" /></svg>;
      default:
        return <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" /></svg>;
    }
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-gray-800">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center space-x-2">
          <svg className="w-5 h-5 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M12.316 3.051a1 1 0 01.633 1.265l-4 12a1 1 0 11-1.898-.632l4-12a1 1 0 011.265-.633zM5.707 6.293a1 1 0 010 1.414L3.414 10l2.293 2.293a1 1 0 11-1.414 1.414l-3-3a1 1 0 010-1.414l3-3a1 1 0 011.414 0zm8.586 0a1 1 0 011.414 0l3 3a1 1 0 010 1.414l-3 3a1 1 0 11-1.414-1.414L16.586 10l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
          </svg>
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Test Results</h2>
          {isConnected && (
            <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full">
              Connected
            </span>
          )}
          {isRunning && (
            <span className="inline-flex items-center px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full">
              Running
            </span>
          )}
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={clearResults}
            className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 dark:text-gray-400 dark:hover:text-gray-200"
            disabled={isRunning}
          >
            Clear
          </button>
          {onClose && (
            <button
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
            >
              <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Configuration Panel */}
      <div className="p-4 bg-gray-50 dark:bg-gray-700 border-b border-gray-200 dark:border-gray-600">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Backend
            </label>
            <select
              value={backend}
              onChange={(e) => setBackend(e.target.value as 'http' | 'playwright')}
              disabled={isRunning}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
            >
              <option value="http">HTTP</option>
              <option value="playwright">Playwright</option>
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Base URL
            </label>
            <input
              type="text"
              value={baseUrl}
              onChange={(e) => setBaseUrl(e.target.value)}
              disabled={isRunning}
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
              placeholder="http://localhost:8000"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
              Timeout (s)
            </label>
            <input
              type="number"
              value={timeout}
              onChange={(e) => setTimeout(parseInt(e.target.value) || 30)}
              disabled={isRunning}
              min="1"
              max="300"
              className="w-full px-3 py-2 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 dark:bg-gray-600 dark:border-gray-500 dark:text-white"
            />
          </div>
        </div>

        <div className="mt-4">
          <button
            onClick={startTests}
            disabled={isRunning}
            className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isRunning ? 'Running Tests...' : 'Run Smoke Tests'}
          </button>
        </div>
      </div>

      {/* Results Content */}
      <div className="flex-1 overflow-hidden">
        {error && (
          <div className="p-4 bg-red-50 border-l-4 border-red-400">
            <div className="flex">
              <svg className="w-5 h-5 text-red-400" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              <div className="ml-3">
                <p className="text-sm text-red-700">{error}</p>
              </div>
            </div>
          </div>
        )}

        {currentRun && (
          <div className="p-4 bg-blue-50 dark:bg-blue-900/20 border-b border-gray-200 dark:border-gray-600">
            <div className="flex items-center justify-between">
              <div>
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">
                  Test Run: {currentRun.run_id}
                </h3>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Backend: {currentRun.backend} | Status: {currentRun.status}
                </p>
              </div>

              {currentRun.status === 'completed' && (
                <div className="text-right">
                  <div className="text-2xl font-bold text-gray-900 dark:text-white">
                    {currentRun.success_rate.toFixed(1)}%
                  </div>
                  <div className="text-sm text-gray-600 dark:text-gray-400">
                    {currentRun.passed_steps}/{currentRun.total_steps} passed
                  </div>
                </div>
              )}
            </div>

            {currentRun.scenarios.length > 0 && (
              <div className="mt-4 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {currentRun.scenarios.map((scenario) => (
                  <div key={scenario.id} className="bg-white dark:bg-gray-800 rounded-lg p-3 border border-gray-200 dark:border-gray-600">
                    <div className="flex items-center space-x-2 mb-2">
                      <span className={getStatusColor(scenario.status)}>
                        {getStatusIcon(scenario.status)}
                      </span>
                      <h4 className="font-medium text-gray-900 dark:text-white">{scenario.name}</h4>
                    </div>
                    <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">{scenario.description}</p>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {scenario.passed_steps}/{scenario.steps.length} steps passed
                      {scenario.duration && ` • ${scenario.duration.toFixed(2)}s`}
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        )}

        {/* Event Stream */}
        <div className="flex-1 overflow-y-auto p-4">
          {events.length === 0 ? (
            <div className="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
              <div className="text-center">
                <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm8 0a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h4a1 1 0 011 1v2a1 1 0 01-1 1h-4a1 1 0 01-1-1v-2z" clipRule="evenodd" />
                </svg>
                <p>No test results yet</p>
                <p className="text-sm">Click "Run Smoke Tests" to start</p>
              </div>
            </div>
          ) : (
            <div className="space-y-2">
              {events.map((event, index) => (
                <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex-shrink-0 w-2 h-2 mt-2 bg-blue-500 rounded-full"></div>
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2">
                      <span className="text-sm font-medium text-gray-900 dark:text-white">
                        {event.type.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </span>
                      <span className="text-xs text-gray-500 dark:text-gray-400">
                        {new Date(event.timestamp).toLocaleTimeString()}
                      </span>
                    </div>

                    {event.scenario && (
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        Scenario: {event.scenario.name}
                      </p>
                    )}

                    {event.step && (
                      <p className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        Step: {event.step.name} - {event.step.status}
                        {event.step.error && (
                          <span className="text-red-600 dark:text-red-400 ml-2">
                            Error: {event.step.error}
                          </span>
                        )}
                      </p>
                    )}

                    {event.type === 'run_complete' && (
                      <div className="text-sm text-gray-600 dark:text-gray-300 mt-1">
                        <p>Duration: {event.duration?.toFixed(2)}s</p>
                        <p>Success Rate: {event.success_rate?.toFixed(1)}%</p>
                        <p>Steps: {event.passed_steps}/{event.total_steps}</p>
                      </div>
                    )}

                    {event.error && (
                      <p className="text-sm text-red-600 dark:text-red-400 mt-1">
                        Error: {event.error}
                      </p>
                    )}
                  </div>
                </div>
              ))}
              <div ref={eventsEndRef} />
            </div>
          )}
        </div>
      </div>
    </div>
  );
});

export default TestResults;
