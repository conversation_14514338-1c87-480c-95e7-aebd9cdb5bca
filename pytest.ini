[pytest]
addopts = -ra -q --strict-markers --strict-config -m "not gpu and not win32 and not slow"

# Test discovery
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

testpaths = 
    agent/tests
    tools/tests

markers =
    gpu: GPU/accelerator dependent tests
    win32: Windows-only or pywin32 dependent tests
    slow: Long-running integration or E2E tests
    integration: Integration tests (may require external services)
    unit: Unit tests

