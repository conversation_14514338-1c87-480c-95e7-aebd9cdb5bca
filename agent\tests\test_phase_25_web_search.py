#!/usr/bin/env python3
"""
Test Script for Phase 25 - Web Search and Update Monitoring

Tests all web search and update monitoring functionality to ensure
100% test success before marking TODO-004 as complete.
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agent.cli.web_search_commands import WebSearchCommands
from trend_monitoring.update_monitor import (
    BestPracticeChange,
    SecurityVulnerability,
    UpdateInfo,
    UpdateMonitor,
)
from trend_monitoring.web_search import (
    RSSFeed,
    ScrapedContent,
    SearchResult,
    WebSearchEngine,
)


class Phase25Tester:
    """Test suite for Phase 25 web search and update monitoring"""

    def __init__(self):
        self.test_results = []
        self.start_time = datetime.now()
        self.web_search_engine = None
        self.update_monitor = None
        self.web_search_commands = None

    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": datetime.now().isoformat(),
        }
        self.test_results.append(result)

        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   Details: {details}")

    async def setup_test_environment(self):
        """Setup test environment"""
        print("🔧 Setting up test environment...")

        # Web search engine config
        web_search_config = {
            "trusted_domains": [
                "developer.mozilla.org",
                "docs.python.org",
                "owasp.org",
                "stackoverflow.com",
                "github.com",
                "dev.to",
            ],
            "blocked_domains": [],
            "user_agent": "AICodingAgent/1.0",
            "rate_limit_delay": 0.1,  # Faster for testing
            "max_retries": 2,
            "timeout": 10,
            "cache_ttl": 300,
            "max_workers": 3,
            "rss_feeds": {
                "test_mdn": {
                    "name": "Test MDN",
                    "url": "https://developer.mozilla.org/en-US/sitemap.xml",
                    "category": "documentation",
                    "check_interval": 3600,
                    "priority": "high",
                }
            },
        }

        # Update monitor config
        update_monitor_config = {
            "check_intervals": {
                "framework_updates": 3600,
                "security_vulnerabilities": 1800,
                "best_practices": 7200,
                "dependency_updates": 3600,
            },
            "api_endpoints": {
                "github": "https://api.github.com",
                "pypi": "https://pypi.org/pypi",
                "npm": "https://registry.npmjs.org",
                "nvd": "https://services.nvd.nist.gov/rest/json/cves/2.0",
            },
            "max_workers": 3,
            "frameworks": {
                "python": {
                    "django": {"current_version": "4.2.0"},
                    "flask": {"current_version": "2.3.0"},
                    "fastapi": {"current_version": "0.100.0"},
                },
                "javascript": {
                    "react": {"current_version": "18.2.0"},
                    "vue": {"current_version": "3.3.0"},
                    "angular": {"current_version": "16.0.0"},
                },
            },
            "requirements_file": "requirements.txt",
        }

        try:
            # Initialize web search engine
            self.web_search_engine = WebSearchEngine(web_search_config)
            await self.web_search_engine.start()

            # Initialize update monitor
            self.update_monitor = UpdateMonitor(update_monitor_config)
            await self.update_monitor.start_monitoring()

            # Initialize CLI commands
            self.web_search_commands = WebSearchCommands(None)

            self.log_test(
                "Setup Test Environment",
                True,
                "All components initialized successfully",
            )
            return True

        except Exception as e:
            self.log_test("Setup Test Environment", False, f"Setup failed: {str(e)}")
            return False

    async def test_web_search_engine_initialization(self):
        """Test web search engine initialization"""
        try:
            # Test basic initialization
            assert self.web_search_engine is not None
            assert hasattr(self.web_search_engine, "session")
            assert hasattr(self.web_search_engine, "rss_feeds")
            assert hasattr(self.web_search_engine, "trusted_domains")

            # Test RSS feeds loading
            assert len(self.web_search_engine.rss_feeds) > 0
            assert all(
                isinstance(feed, RSSFeed) for feed in self.web_search_engine.rss_feeds
            )

            self.log_test("Web Search Engine Initialization", True)
            return True

        except Exception as e:
            self.log_test("Web Search Engine Initialization", False, str(e))
            return False

    async def test_update_monitor_initialization(self):
        """Test update monitor initialization"""
        try:
            # Test basic initialization
            assert self.update_monitor is not None
            assert hasattr(self.update_monitor, "scheduler")
            assert hasattr(self.update_monitor, "updates")
            assert hasattr(self.update_monitor, "vulnerabilities")
            assert hasattr(self.update_monitor, "best_practice_changes")

            # Test configuration loading
            assert "frameworks" in self.update_monitor.config
            assert "api_endpoints" in self.update_monitor.config

            self.log_test("Update Monitor Initialization", True)
            return True

        except Exception as e:
            self.log_test("Update Monitor Initialization", False, str(e))
            return False

    async def test_search_result_dataclass(self):
        """Test SearchResult dataclass"""
        try:
            # Create a test search result
            result = SearchResult(
                url="https://example.com",
                title="Test Title",
                snippet="Test snippet",
                source="test",
                relevance_score=0.8,
                timestamp=datetime.now(),
                content_type="article",
                language="python",
                framework="django",
            )

            # Test to_dict method
            result_dict = result.to_dict()
            assert isinstance(result_dict, dict)
            assert result_dict["url"] == "https://example.com"
            assert result_dict["title"] == "Test Title"
            assert result_dict["relevance_score"] == 0.8

            self.log_test("SearchResult Dataclass", True)
            return True

        except Exception as e:
            self.log_test("SearchResult Dataclass", False, str(e))
            return False

    async def test_rss_feed_dataclass(self):
        """Test RSSFeed dataclass"""
        try:
            # Create a test RSS feed
            feed = RSSFeed(
                feed_id="test_feed",
                name="Test Feed",
                url="https://example.com/feed",
                category="documentation",
                last_checked=datetime.now(),
                last_updated=datetime.now(),
                is_active=True,
                check_interval=3600,
                priority="high",
            )

            # Test to_dict method
            feed_dict = feed.to_dict()
            assert isinstance(feed_dict, dict)
            assert feed_dict["feed_id"] == "test_feed"
            assert feed_dict["name"] == "Test Feed"
            assert feed_dict["category"] == "documentation"

            self.log_test("RSSFeed Dataclass", True)
            return True

        except Exception as e:
            self.log_test("RSSFeed Dataclass", False, str(e))
            return False

    async def test_update_info_dataclass(self):
        """Test UpdateInfo dataclass"""
        try:
            # Create a test update info
            update = UpdateInfo(
                update_id="test_update",
                name="Test Package",
                current_version="1.0.0",
                new_version="1.1.0",
                update_type="minor",
                severity="medium",
                description="Test update",
                changelog="Test changelog",
                release_date=datetime.now(),
                discovered_at=datetime.now(),
                source="pypi",
                url="https://example.com",
                impact_score=0.5,
            )

            # Test to_dict method
            update_dict = update.to_dict()
            assert isinstance(update_dict, dict)
            assert update_dict["update_id"] == "test_update"
            assert update_dict["name"] == "Test Package"
            assert update_dict["update_type"] == "minor"
            assert update_dict["severity"] == "medium"

            self.log_test("UpdateInfo Dataclass", True)
            return True

        except Exception as e:
            self.log_test("UpdateInfo Dataclass", False, str(e))
            return False

    async def test_security_vulnerability_dataclass(self):
        """Test SecurityVulnerability dataclass"""
        try:
            # Create a test vulnerability
            vuln = SecurityVulnerability(
                cve_id="CVE-2023-1234",
                title="Test Vulnerability",
                description="Test vulnerability description",
                severity="high",
                cvss_score=7.5,
                affected_versions=["1.0.0", "1.1.0"],
                fixed_versions=["1.2.0"],
                published_date=datetime.now(),
                discovered_at=datetime.now(),
                source="nvd",
                references=["https://example.com"],
            )

            # Test to_dict method
            vuln_dict = vuln.to_dict()
            assert isinstance(vuln_dict, dict)
            assert vuln_dict["cve_id"] == "CVE-2023-1234"
            assert vuln_dict["severity"] == "high"
            assert vuln_dict["cvss_score"] == 7.5

            self.log_test("SecurityVulnerability Dataclass", True)
            return True

        except Exception as e:
            self.log_test("SecurityVulnerability Dataclass", False, str(e))
            return False

    async def test_best_practice_change_dataclass(self):
        """Test BestPracticeChange dataclass"""
        try:
            # Create a test best practice change
            change = BestPracticeChange(
                change_id="test_change",
                title="Test Best Practice Change",
                description="Test description",
                category="security",
                old_practice="Old practice",
                new_practice="New practice",
                rationale="Test rationale",
                impact="medium",
                published_date=datetime.now(),
                discovered_at=datetime.now(),
                source="mdn",
                url="https://example.com",
                examples=["Example 1", "Example 2"],
                migration_steps=["Step 1", "Step 2"],
            )

            # Test to_dict method
            change_dict = change.to_dict()
            assert isinstance(change_dict, dict)
            assert change_dict["change_id"] == "test_change"
            assert change_dict["category"] == "security"
            assert change_dict["impact"] == "medium"
            assert len(change_dict["examples"]) == 2
            assert len(change_dict["migration_steps"]) == 2

            self.log_test("BestPracticeChange Dataclass", True)
            return True

        except Exception as e:
            self.log_test("BestPracticeChange Dataclass", False, str(e))
            return False

    async def test_web_search_commands_initialization(self):
        """Test web search commands initialization"""
        try:
            # Test CLI commands initialization
            assert self.web_search_commands is not None
            assert hasattr(self.web_search_commands, "web_search_engine")
            assert hasattr(self.web_search_commands, "update_monitor")

            self.log_test("Web Search Commands Initialization", True)
            return True

        except Exception as e:
            self.log_test("Web Search Commands Initialization", False, str(e))
            return False

    async def test_version_comparison_methods(self):
        """Test version comparison methods"""
        try:
            # Test _is_newer_version method
            assert self.update_monitor._is_newer_version("1.1.0", "1.0.0") == True
            assert self.update_monitor._is_newer_version("1.0.0", "1.0.0") == False
            assert self.update_monitor._is_newer_version("1.0.0", "1.1.0") == False

            # Test _determine_update_type method
            assert (
                self.update_monitor._determine_update_type("1.0.0", "1.0.1") == "patch"
            )
            assert (
                self.update_monitor._determine_update_type("1.0.0", "1.1.0") == "minor"
            )
            assert (
                self.update_monitor._determine_update_type("1.0.0", "2.0.0") == "major"
            )

            # Test _determine_severity method
            assert self.update_monitor._determine_severity("1.0.0", "1.0.1") == "low"
            assert self.update_monitor._determine_severity("1.0.0", "1.1.0") == "medium"
            assert self.update_monitor._determine_severity("1.0.0", "2.0.0") == "high"

            # Test _calculate_impact_score method
            assert self.update_monitor._calculate_impact_score("1.0.0", "1.0.1") == 0.2
            assert self.update_monitor._calculate_impact_score("1.0.0", "1.1.0") == 0.5
            assert self.update_monitor._calculate_impact_score("1.0.0", "2.0.0") == 0.8

            self.log_test("Version Comparison Methods", True)
            return True

        except Exception as e:
            self.log_test("Version Comparison Methods", False, str(e))
            return False

    async def test_cvss_severity_determination(self):
        """Test CVSS severity determination"""
        try:
            # Test _determine_cvss_severity method
            test_cve = {
                "metrics": {"cvssMetricV31": [{"cvssData": {"baseScore": 9.5}}]}
            }
            assert self.update_monitor._determine_cvss_severity(test_cve) == "critical"

            test_cve["metrics"]["cvssMetricV31"][0]["cvssData"]["baseScore"] = 7.5
            assert self.update_monitor._determine_cvss_severity(test_cve) == "high"

            test_cve["metrics"]["cvssMetricV31"][0]["cvssData"]["baseScore"] = 5.0
            assert self.update_monitor._determine_cvss_severity(test_cve) == "medium"

            test_cve["metrics"]["cvssMetricV31"][0]["cvssData"]["baseScore"] = 2.0
            assert self.update_monitor._determine_cvss_severity(test_cve) == "low"

            self.log_test("CVSS Severity Determination", True)
            return True

        except Exception as e:
            self.log_test("CVSS Severity Determination", False, str(e))
            return False

    async def test_best_practice_category_determination(self):
        """Test best practice category determination"""
        try:
            # Test _determine_best_practice_category method
            test_doc = {
                "title": "Security best practices",
                "excerpt": "Security guidelines",
            }
            assert (
                self.update_monitor._determine_best_practice_category(test_doc)
                == "security"
            )

            test_doc = {
                "title": "Performance optimization",
                "excerpt": "Performance tips",
            }
            assert (
                self.update_monitor._determine_best_practice_category(test_doc)
                == "performance"
            )

            test_doc = {
                "title": "Accessibility guidelines",
                "excerpt": "Accessibility tips",
            }
            assert (
                self.update_monitor._determine_best_practice_category(test_doc)
                == "accessibility"
            )

            test_doc = {"title": "General coding tips", "excerpt": "General guidelines"}
            assert (
                self.update_monitor._determine_best_practice_category(test_doc)
                == "maintainability"
            )

            self.log_test("Best Practice Category Determination", True)
            return True

        except Exception as e:
            self.log_test("Best Practice Category Determination", False, str(e))
            return False

    async def test_cli_commands_methods(self):
        """Test CLI commands methods"""
        try:
            # Test that all CLI methods exist and are callable
            methods = [
                "search_best_practices",
                "get_update_summary",
                "get_update_recommendations",
                "get_critical_vulnerabilities",
                "get_recent_best_practice_changes",
                "get_monitoring_status",
                "start_monitoring",
                "stop_monitoring",
                "manual_check_updates",
                "health_check",
                "get_available_sources",
                "cleanup",
            ]

            for method_name in methods:
                assert hasattr(self.web_search_commands, method_name)
                method = getattr(self.web_search_commands, method_name)
                assert callable(method)

            self.log_test("CLI Commands Methods", True)
            return True

        except Exception as e:
            self.log_test("CLI Commands Methods", False, str(e))
            return False

    async def test_data_persistence(self):
        """Test data persistence methods"""
        try:
            # Test save_data and load_data methods
            test_file = "test_phase_25_data.json"

            # Add some test data
            test_update = UpdateInfo(
                update_id="test_persistence",
                name="Test Package",
                current_version="1.0.0",
                new_version="1.1.0",
                update_type="minor",
                severity="medium",
                description="Test",
                changelog="Test",
                release_date=datetime.now(),
                discovered_at=datetime.now(),
                source="test",
                url="https://example.com",
            )

            self.update_monitor.updates[test_update.update_id] = test_update

            # Save data
            self.update_monitor.save_data(test_file)

            # Verify file exists
            assert Path(test_file).exists()

            # Load data into new instance
            new_monitor = UpdateMonitor({})
            new_monitor.load_data(test_file)

            # Verify data was loaded
            assert test_update.update_id in new_monitor.updates

            # Cleanup
            Path(test_file).unlink(missing_ok=True)

            self.log_test("Data Persistence", True)
            return True

        except Exception as e:
            self.log_test("Data Persistence", False, str(e))
            return False

    async def test_health_check(self):
        """Test health check functionality"""
        try:
            # Test health check method
            health_result = await self.web_search_commands.health_check()

            assert isinstance(health_result, dict)
            assert "success" in health_result
            assert "status" in health_result

            self.log_test("Health Check", True)
            return True

        except Exception as e:
            self.log_test("Health Check", False, str(e))
            return False

    async def test_get_available_sources(self):
        """Test get available sources functionality"""
        try:
            # Test get available sources method
            sources_result = await self.web_search_commands.get_available_sources()

            assert isinstance(sources_result, dict)
            assert "success" in sources_result
            assert "sources" in sources_result

            sources = sources_result["sources"]
            assert "web_search_sources" in sources
            assert "update_monitoring_sources" in sources

            self.log_test("Get Available Sources", True)
            return True

        except Exception as e:
            self.log_test("Get Available Sources", False, str(e))
            return False

    async def cleanup_test_environment(self):
        """Cleanup test environment"""
        print("🧹 Cleaning up test environment...")

        try:
            if self.web_search_engine:
                await self.web_search_engine.stop()

            if self.update_monitor:
                await self.update_monitor.stop_monitoring()

            if self.web_search_commands:
                await self.web_search_commands.cleanup()

            self.log_test("Cleanup Test Environment", True)
            return True

        except Exception as e:
            self.log_test("Cleanup Test Environment", False, str(e))
            return False

    def calculate_success_rate(self) -> float:
        """Calculate test success rate"""
        if not self.test_results:
            return 0.0

        passed_tests = sum(1 for result in self.test_results if result["success"])
        total_tests = len(self.test_results)

        return (passed_tests / total_tests) * 100

    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 PHASE 25 - WEB SEARCH AND UPDATE MONITORING TEST SUMMARY")
        print("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        success_rate = self.calculate_success_rate()

        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")

        if failed_tests > 0:
            print("\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   - {result['test']}: {result['details']}")

        duration = datetime.now() - self.start_time
        print(f"\n⏱️  Test Duration: {duration.total_seconds():.2f} seconds")

        if success_rate == 100.0:
            print(
                "\n🎉 ALL TESTS PASSED! Phase 25 implementation is complete and ready."
            )
        else:
            print(
                f"\n⚠️  {failed_tests} test(s) failed. Please fix issues before proceeding."
            )

        return success_rate == 100.0

    async def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Phase 25 - Web Search and Update Monitoring Tests")
        print("=" * 60)

        # Setup
        if not await self.setup_test_environment():
            return False

        # Run tests
        test_methods = [
            self.test_web_search_engine_initialization,
            self.test_update_monitor_initialization,
            self.test_search_result_dataclass,
            self.test_rss_feed_dataclass,
            self.test_update_info_dataclass,
            self.test_security_vulnerability_dataclass,
            self.test_best_practice_change_dataclass,
            self.test_web_search_commands_initialization,
            self.test_version_comparison_methods,
            self.test_cvss_severity_determination,
            self.test_best_practice_category_determination,
            self.test_cli_commands_methods,
            self.test_data_persistence,
            self.test_health_check,
            self.test_get_available_sources,
        ]

        for test_method in test_methods:
            try:
                await test_method()
            except Exception as e:
                self.log_test(
                    test_method.__name__, False, f"Test method failed: {str(e)}"
                )

        # Cleanup
        await self.cleanup_test_environment()

        # Print summary
        return self.print_summary()


async def main():
    """Main test runner"""
    tester = Phase25Tester()

    try:
        success = await tester.run_all_tests()

        if success:
            print(
                "\n✅ TODO-004: Complete Phase 25 - Web Search and Update Monitoring - COMPLETED"
            )
            print("✅ All components implemented and tested successfully")
            print("✅ Web search functionality implemented")
            print("✅ Update monitoring system working")
            print("✅ Integration with existing core systems")
            print("✅ Tests passing 100%")
            return 0
        else:
            print(
                "\n❌ TODO-004: Complete Phase 25 - Web Search and Update Monitoring - FAILED"
            )
            print("❌ Some tests failed. Please fix issues before marking as complete.")
            return 1

    except Exception as e:
        print(f"\n💥 Test runner failed: {e}")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
