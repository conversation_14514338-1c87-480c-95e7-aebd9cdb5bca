@echo off
setlocal enabledelayedexpansion

REM Build script for site containers with conditional nginx.conf handling (Windows)

set SITE_NAME=%1
if "%SITE_NAME%"=="" set SITE_NAME=test-site

set IMAGE_NAME=%2
if "%IMAGE_NAME%"=="" set IMAGE_NAME=site-%SITE_NAME%

set TAG=%3
if "%TAG%"=="" set TAG=latest

echo Building container for site: %SITE_NAME%

REM Create temporary directory for build context
set BUILD_DIR=%TEMP%\docker-build-%SITE_NAME%
if exist "%BUILD_DIR%" rmdir /s /q "%BUILD_DIR%"
mkdir "%BUILD_DIR%"

REM Copy site files
if exist "sites\%SITE_NAME%" (
    echo Copying site files from sites\%SITE_NAME%
    xcopy "sites\%SITE_NAME%" "%BUILD_DIR%\site\" /e /i /y
) else (
    echo Warning: sites\%SITE_NAME% not found, creating empty site
    mkdir "%BUILD_DIR%\site"
)

REM Handle nginx.conf conditionally
if exist "sites\%SITE_NAME%\nginx.conf" (
    echo Using custom nginx.conf from sites\%SITE_NAME%\nginx.conf
    copy "sites\%SITE_NAME%\nginx.conf" "%BUILD_DIR%\default.conf" >nul
) else (
    echo No custom nginx.conf found, using default nginx configuration
    REM Create a basic default nginx.conf
    (
        echo server {
        echo     listen 80;
        echo     server_name localhost;
        echo.
        echo     location / {
        echo         root /usr/share/nginx/html;
        echo         index index.html index.htm;
        echo         try_files $uri $uri/ /index.html;
        echo     }
        echo.
        echo     # Health check endpoint
        echo     location /health {
        echo         access_log off;
        echo         return 200 "healthy\n";
        echo         add_header Content-Type text/plain;
        echo     }
        echo }
    ) > "%BUILD_DIR%\default.conf"
)

REM Create Dockerfile in build context
(
    echo FROM nginx:alpine
    echo.
    echo # Copy site files
    echo COPY site/ /usr/share/nginx/html/
    echo.
    echo # Copy nginx configuration
    echo COPY default.conf /etc/nginx/conf.d/default.conf
    echo.
    echo # Expose port 80
    echo EXPOSE 80
    echo.
    echo # Health check
    echo HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    echo   CMD curl -f http://localhost/health ^|^| exit 1
    echo.
    echo # Start nginx
    echo CMD ["nginx", "-g", "daemon off;"]
) > "%BUILD_DIR%\Dockerfile"

REM Build the Docker image
echo Building Docker image: %IMAGE_NAME%:%TAG%
docker build -t "%IMAGE_NAME%:%TAG%" "%BUILD_DIR%"

REM Clean up build context
rmdir /s /q "%BUILD_DIR%"

echo Successfully built %IMAGE_NAME%:%TAG%
echo To run the container:
echo   docker run -d -p 8080:80 --name %SITE_NAME%-container %IMAGE_NAME%:%TAG%
echo   Then visit: http://localhost:8080

endlocal
