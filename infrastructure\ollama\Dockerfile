FROM ollama/ollama:latest

# Create non-root user for security
RUN groupadd --system --gid 1000 ollama \
    && useradd --system --uid 1000 --gid ollama --create-home --shell /bin/bash ollama \
    && mkdir -p /home/<USER>/.ollama \
    && chown -R ollama:ollama /home/<USER>

# Switch to non-root user
USER ollama

# Set working directory
WORKDIR /home/<USER>

# Expose the default Ollama port
EXPOSE 11434

# Healthcheck to ensure the service is running
HEALTHCHECK --interval=30s --timeout=10s --retries=3 \
  CMD ollama ps || exit 1

# Start the Ollama server
CMD ["ollama", "serve"]
