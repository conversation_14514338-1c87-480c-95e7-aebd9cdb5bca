import pytest
from fastapi.testclient import TestClient

from agent.api.main import app

client = TestClient(app)


@pytest.mark.asyncio
async def test_process_command():
    # Add authentication header with default API key
    headers = {"Authorization": "Bearer default-api-key"}

    response = client.post(
        "/api/v1/architect/process",
        json={"command": "Make me a portfolio website with a blog and contact form"},
        headers=headers,
    )
    assert response.status_code == 200
    data = response.json()
    assert data["success"] is True
    assert "task_id" in data
    task_id = data["task_id"]

    # Check status endpoint
    status_response = client.get(f"/api/v1/architect/status/{task_id}", headers=headers)
    assert status_response.status_code == 200
    status_data = status_response.json()
    assert status_data["success"] is True

    # Check all endpoint
    all_response = client.get("/api/v1/architect/all", headers=headers)
    assert all_response.status_code == 200
    all_data = all_response.json()
    assert "active_tasks" in all_data or "completed_tasks" in all_data
