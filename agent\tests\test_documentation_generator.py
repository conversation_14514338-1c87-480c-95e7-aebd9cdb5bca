"""
Test suite for Phase 21: Documentation Generation

This module provides comprehensive tests for all documentation generation components
including the main orchestrator, AI model integration, and specialized generators.
"""

import asyncio
import json
import os
import tempfile
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from documentation.access_manager import AccessManager
from documentation.api_documentation_generator import APIDocumentationGenerator
from documentation.code_comment_generator import CodeCommentGenerator
from documentation.documentation_dashboard import DocumentationDashboard
from documentation.documentation_generator import (
    DocumentationGenerator,
    DocumentationRequest,
    DocumentationResponse,
    DocumentationVersion,
)
from documentation.documentation_maintainer import DocumentationMaintainer
from documentation.qwen_integration import ModelConfig, QwenIntegration
from documentation.technical_doc_generator import TechnicalDocGenerator
from documentation.user_guide_generator import UserGuideGenerator
from documentation.version_control_manager import VersionControlManager


@pytest.fixture
def sample_config_file():
    """Create a temporary config file for testing"""
    config = {
        "enabled_features": {
            "api_documentation": True,
            "user_guides": True,
            "technical_documentation": True,
            "code_comments": True,
            "documentation_maintenance": True,
            "version_control": True,
            "access_management": True,
            "analytics": True,
        },
        "model_config": {
            "qwen_coder": {
                "model_name": "qwen2.5-coder:3b",
                "temperature": 0.7,
                "max_tokens": 2048,
                "top_p": 0.9,
            }
        },
        "documentation_settings": {
            "output_formats": ["markdown", "html"],
            "supported_languages": ["python", "javascript", "typescript"],
            "style_guides": {
                "python": ["google", "numpy"],
                "javascript": ["jsdoc"],
                "typescript": ["jsdoc"],
                "java": ["javadoc"],
                "cpp": ["doxygen"],
            },
            "quality_threshold": 0.8,
            "generation_settings": {
                "include_examples": True,
                "include_diagrams": True,
                "auto_update": True,
            },
        },
        "version_control": {
            "auto_commit": True,
            "branch_strategy": "feature-based",
            "review_required": True,
        },
        "access_control": {
            "roles": ["admin", "writer", "reviewer", "viewer"],
            "permissions": {
                "admin": ["read", "write", "delete", "approve"],
                "writer": ["read", "write"],
                "reviewer": ["read", "approve"],
                "viewer": ["read"],
            },
        },
    }

    with tempfile.NamedTemporaryFile(mode="w", suffix=".json", delete=False) as f:
        json.dump(config, f)
        temp_file = f.name

    yield temp_file

    # Cleanup
    if os.path.exists(temp_file):
        os.unlink(temp_file)


@pytest.fixture
def sample_source_code():
    """Sample source code for testing"""
    return '''
def calculate_fibonacci(n: int) -> int:
    """Calculate the nth Fibonacci number."""
    if n <= 1:
        return n
    return calculate_fibonacci(n - 1) + calculate_fibonacci(n - 2)

class MathUtils:
    """Utility class for mathematical operations."""

    def __init__(self):
        self.cache = {}

    def factorial(self, n: int) -> int:
        """Calculate factorial of n."""
        if n in self.cache:
            return self.cache[n]
        if n <= 1:
            result = 1
        else:
            result = n * self.factorial(n - 1)
        self.cache[n] = result
        return result
'''


@pytest.fixture
def sample_context():
    """Sample context for testing"""
    return {
        "project_name": "Test Project",
        "version": "1.0.0",
        "description": "A test project for documentation generation",
        "author": "Test Author",
        "license": "MIT",
    }


class TestDocumentationGenerator:
    """Test cases for the main DocumentationGenerator class"""

    @pytest.mark.asyncio
    async def test_initialization(self, sample_config_file):
        """Test DocumentationGenerator initialization"""
        generator = DocumentationGenerator(sample_config_file)

        assert generator.config is not None
        assert generator.qwen_integration is not None
        assert generator.api_doc_generator is not None
        assert generator.user_guide_generator is not None
        assert generator.technical_doc_generator is not None
        assert generator.code_comment_generator is not None
        assert generator.documentation_maintainer is not None
        assert generator.version_control_manager is not None
        assert generator.access_manager is not None
        assert generator.documentation_dashboard is not None

    @pytest.mark.asyncio
    async def test_generate_documentation(
        self, sample_config_file, sample_source_code, sample_context
    ):
        """Test main documentation generation workflow"""
        generator = DocumentationGenerator(sample_config_file)

        request = DocumentationRequest(
            content_type="api",
            source_code=sample_source_code,
            context=sample_context,
            output_format="markdown",
        )

        response = await generator.generate_documentation(request)

        assert isinstance(response, DocumentationResponse)
        assert response.documentation is not None
        assert len(response.documentation) > 0
        assert response.quality_score >= 0.0
        assert response.suggestions is not None


class TestQwenIntegration:
    """Test cases for QwenIntegration class"""

    @pytest.mark.asyncio
    async def test_initialization(self, sample_config_file):
        """Test QwenIntegration initialization"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        qwen = QwenIntegration(config["model_config"])

        assert qwen.config.model_name == "qwen2.5-coder:3b"
        assert qwen.config.temperature == 0.7
        assert qwen.config.max_tokens == 2048
        assert qwen.config.top_p == 0.9

    @pytest.mark.asyncio
    async def test_initialize_model(self, sample_config_file):
        """Test model initialization"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        qwen = QwenIntegration(config["model_config"])

        result = await qwen.initialize_model()

        assert result is True
        assert qwen.model_loaded is True


class TestAPIDocumentationGenerator:
    """Test cases for APIDocumentationGenerator class"""

    @pytest.mark.asyncio
    async def test_initialization(self, sample_config_file):
        """Test APIDocumentationGenerator initialization"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        generator = APIDocumentationGenerator(config["documentation_settings"])

        assert generator.supported_formats == ["markdown", "html"]

    @pytest.mark.asyncio
    async def test_generate_api_documentation(
        self, sample_config_file, sample_source_code
    ):
        """Test API documentation generation"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        generator = APIDocumentationGenerator(config["documentation_settings"])

        result = await generator.generate_api_documentation(
            sample_source_code, {"project_name": "Test"}, {}, "markdown"
        )

        assert isinstance(result, str)
        assert len(result) > 0
        assert "# " in result  # Markdown header


class TestUserGuideGenerator:
    """Test cases for UserGuideGenerator class"""

    @pytest.mark.asyncio
    async def test_initialization(self, sample_config_file):
        """Test UserGuideGenerator initialization"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        generator = UserGuideGenerator(config["documentation_settings"])

        assert generator.supported_formats == ["markdown", "html"]

    @pytest.mark.asyncio
    async def test_generate_user_guide(
        self, sample_config_file, sample_source_code, sample_context
    ):
        """Test user guide generation"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        generator = UserGuideGenerator(config["documentation_settings"])

        result = await generator.generate_user_guide(
            sample_source_code, sample_context, {}, "markdown"
        )

        assert isinstance(result, str)
        assert len(result) > 0
        assert "# " in result  # Markdown header


class TestTechnicalDocGenerator:
    """Test cases for TechnicalDocGenerator class"""

    @pytest.mark.asyncio
    async def test_initialization(self, sample_config_file):
        """Test TechnicalDocGenerator initialization"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        generator = TechnicalDocGenerator(config["documentation_settings"])

        assert generator.supported_formats == ["markdown", "html"]

    @pytest.mark.asyncio
    async def test_generate_technical_documentation(
        self, sample_config_file, sample_source_code, sample_context
    ):
        """Test technical documentation generation"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        generator = TechnicalDocGenerator(config["documentation_settings"])

        result = await generator.generate_technical_documentation(
            sample_source_code, sample_context, {}, "markdown"
        )

        assert isinstance(result, str)
        assert len(result) > 0
        assert "# " in result  # Markdown header


class TestCodeCommentGenerator:
    """Test cases for CodeCommentGenerator class"""

    @pytest.mark.asyncio
    async def test_initialization(self, sample_config_file):
        """Test CodeCommentGenerator initialization"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        generator = CodeCommentGenerator(config["documentation_settings"])

        assert "google" in generator.style_guides["python"]

    @pytest.mark.asyncio
    async def test_generate_code_comments(self, sample_config_file, sample_source_code):
        """Test code comment generation"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        generator = CodeCommentGenerator(config["documentation_settings"])

        result = await generator.generate_code_comments(
            sample_source_code,
            {"style_guide": "google"},
            {"style_guide": "google"},
            "python",
        )

        assert isinstance(result, str)
        assert len(result) > 0
        assert "def " in result


class TestDocumentationMaintainer:
    """Test cases for DocumentationMaintainer class"""

    @pytest.mark.asyncio
    async def test_initialization(self, sample_config_file):
        """Test DocumentationMaintainer initialization"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        maintainer = DocumentationMaintainer(config["version_control"])

        assert maintainer.auto_commit is True
        assert maintainer.branch_strategy == "feature-based"

    @pytest.mark.asyncio
    async def test_update_documentation(self, sample_config_file, sample_source_code):
        """Test documentation update"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        maintainer = DocumentationMaintainer(config["version_control"])

        result = await maintainer.update_documentation(
            sample_source_code, "Updated source code", {"user_id": "test"}
        )

        assert result is not None
        assert "updated" in result
        assert "changes" in result

    @pytest.mark.asyncio
    async def test_get_outdated_documentation(self, sample_config_file):
        """Test outdated documentation detection"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        maintainer = DocumentationMaintainer(config["version_control"])

        result = await maintainer.get_outdated_documentation(
            "old_content", "new_content"
        )

        assert isinstance(result, list)


class TestVersionControlManager:
    """Test cases for VersionControlManager class"""

    @pytest.mark.asyncio
    async def test_initialization(self, sample_config_file):
        """Test VersionControlManager initialization"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        manager = VersionControlManager(config["version_control"])

        assert manager.auto_commit is True
        assert manager.branch_strategy == "feature-based"
        assert manager.current_branch == "main"

    @pytest.mark.asyncio
    async def test_create_version(self, sample_config_file):
        """Test version creation"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        manager = VersionControlManager(config["version_control"])

        content = {"api_docs": "content", "user_guide": "content"}
        metadata = {"author": "test", "description": "Test version"}
        version = await manager.create_version("test_version", content, metadata)

        assert version is not None
        assert version["success"] is True
        assert version["version_info"]["version_id"] == "test_version"

    @pytest.mark.asyncio
    async def test_get_version_history(self, sample_config_file):
        """Test version history retrieval"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        manager = VersionControlManager(config["version_control"])

        content = {"api_docs": "content", "user_guide": "content"}
        metadata = {"author": "test", "description": "Test version"}
        await manager.create_version("test_version", content, metadata)

        history = await manager.get_version_history()

        assert history is not None
        assert isinstance(history, list)
        assert len(history) > 0

    @pytest.mark.asyncio
    async def test_create_branch(self, sample_config_file):
        """Test branch creation"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        manager = VersionControlManager(config["version_control"])

        result = await manager.create_branch("feature-branch", "main")

        assert result is not None
        assert result["success"] is True
        assert result["branch_name"] == "feature-branch"


class TestAccessManager:
    """Test cases for AccessManager class"""

    @pytest.mark.asyncio
    async def test_initialization(self, sample_config_file):
        """Test AccessManager initialization"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        manager = AccessManager(config["access_control"])

        assert "admin" in manager.roles
        assert "writer" in manager.roles
        assert "read" in manager.permissions["admin"]

    @pytest.mark.asyncio
    async def test_add_user(self, sample_config_file):
        """Test user addition"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        manager = AccessManager(config["access_control"])

        result = await manager.add_user(
            "testuser", "writer", {"email": "<EMAIL>"}
        )

        assert result is not None
        assert result["success"] is True
        assert result["user_info"]["user_id"] == "testuser"
        assert result["user_info"]["role"] == "writer"

    @pytest.mark.asyncio
    async def test_check_permission(self, sample_config_file):
        """Test permission checking"""
        with open(sample_config_file, "r") as f:
            config = json.load(f)

        manager = AccessManager(config["access_control"])

        # Add a user first
        await manager.add_user("testuser", "writer", {"email": "<EMAIL>"})

        # Test valid permission
        has_permission = await manager.check_permission("testuser", "write")
        assert has_permission is True

        # Test invalid permission
        has_permission = await manager.check_permission("testuser", "delete")
        assert has_permission is False


class TestDocumentationDashboard:
    """Test cases for DocumentationDashboard class"""

    @pytest.mark.asyncio
    async def test_initialization(self, sample_config_file):
        """Test DocumentationDashboard initialization"""
        dashboard = DocumentationDashboard()

        assert dashboard is not None
        assert hasattr(dashboard, "metrics_data")
        assert hasattr(dashboard, "trends_data")
        assert hasattr(dashboard, "issues_data")
        assert hasattr(dashboard, "recommendations_data")

    @pytest.mark.asyncio
    async def test_add_metric_data(self, sample_config_file):
        """Test metric data addition"""
        dashboard = DocumentationDashboard()

        result = await dashboard.add_metric_data(
            "quality_score", 0.85, {"document_type": "api_doc"}
        )

        assert result is not None
        assert result["success"] is True
        assert "metric_id" in result

    @pytest.mark.asyncio
    async def test_get_dashboard_data(self, sample_config_file):
        """Test dashboard data retrieval"""
        dashboard = DocumentationDashboard()

        # Add some test data
        await dashboard.add_metric_data("quality_score", 0.85)
        await dashboard.add_issue_data("missing_documentation", "high", "Test issue")

        result = await dashboard.get_dashboard_data(days=30)

        assert result is not None
        assert result["success"] is True
        assert "dashboard_data" in result
        assert "summary" in result["dashboard_data"]
        assert "trends" in result["dashboard_data"]
        assert "top_issues" in result["dashboard_data"]


# Integration tests
class TestDocumentationIntegration:
    """Integration tests for the complete documentation system"""

    @pytest.mark.asyncio
    async def test_full_documentation_workflow(
        self, sample_config_file, sample_source_code, sample_context
    ):
        """Test complete documentation generation workflow"""
        generator = DocumentationGenerator(sample_config_file)

        # Generate documentation
        request = DocumentationRequest(
            content_type="api",
            source_code=sample_source_code,
            context=sample_context,
            output_format="markdown",
        )

        response = await generator.generate_documentation(request)
        assert response.documentation is not None

        # Add admin user and check permissions
        await generator.access_manager.add_user("admin", "admin")
        has_permission = await generator.access_manager.check_permission(
            "admin", "write"
        )
        assert has_permission is True

    @pytest.mark.asyncio
    async def test_documentation_maintenance_workflow(
        self, sample_config_file, sample_source_code
    ):
        """Test documentation maintenance workflow"""
        generator = DocumentationGenerator(sample_config_file)

        # Generate initial documentation
        request = DocumentationRequest(
            content_type="api",
            source_code=sample_source_code,
            context={},
            output_format="markdown",
        )

        response = await generator.generate_documentation(request)
        assert response.documentation is not None

        # Update source code
        updated_code = sample_source_code + "\n\ndef new_function():\n    return 'new'"

        # Update documentation
        update_result = await generator.documentation_maintainer.update_documentation(
            sample_source_code, updated_code, {"user_id": "test"}
        )
        assert update_result is not None

        # Check for outdated content
        outdated_result = (
            await generator.documentation_maintainer.get_outdated_documentation(
                response.documentation, updated_code
            )
        )
        assert isinstance(outdated_result, list)


# Performance tests
class TestDocumentationPerformance:
    """Performance tests for documentation generation"""

    @pytest.mark.asyncio
    async def test_large_codebase_performance(self, sample_config_file):
        """Test performance with large codebase"""
        generator = DocumentationGenerator(sample_config_file)

        # Generate large source code
        large_code = ""
        for i in range(100):
            large_code += f'''
def function_{i}(param: str) -> str:
    """Function {i} documentation."""
    return f"result_{i}"

class Class{i}:
    """Class {i} documentation."""

    def method_{i}(self) -> int:
        """Method {i} documentation."""
        return {i}
'''

        request = DocumentationRequest(
            content_type="api",
            source_code=large_code,
            context={"project_name": "Large Project"},
            output_format="markdown",
        )

        import time

        start_time = time.time()

        response = await generator.generate_documentation(request)

        end_time = time.time()
        execution_time = end_time - start_time

        assert response.documentation is not None
        assert execution_time < 30  # Should complete within 30 seconds
        assert len(response.documentation) > 0


# Error handling tests
class TestDocumentationErrorHandling:
    """Error handling tests for documentation generation"""

    @pytest.mark.asyncio
    async def test_invalid_model_config(self):
        """Test handling of invalid model configuration"""
        invalid_config = {
            "model_name": "invalid-model",
            "temperature": 2.0,  # Invalid temperature
            "max_tokens": -1,  # Invalid max_tokens
        }

        qwen = QwenIntegration(invalid_config)

        result = await qwen.initialize_model()

        # Should handle gracefully even with invalid config
        assert isinstance(result, bool)

    @pytest.mark.asyncio
    async def test_malformed_source_code(self, sample_config_file):
        """Test handling of malformed source code"""
        generator = DocumentationGenerator(sample_config_file)

        malformed_code = """
def incomplete_function(
    # Missing closing parenthesis and body
"""

        request = DocumentationRequest(
            content_type="api",
            source_code=malformed_code,
            context={},
            output_format="markdown",
        )

        response = await generator.generate_documentation(request)

        # Should handle gracefully
        assert response.documentation is not None

    @pytest.mark.asyncio
    async def test_unsupported_language(self, sample_config_file):
        """Test handling of unsupported programming language"""
        generator = DocumentationGenerator(sample_config_file)

        unsupported_code = """
// This is C++ code, not supported in current config
#include <iostream>

int main() {
    std::cout << "Hello World" << std::endl;
    return 0;
}
"""

        request = DocumentationRequest(
            content_type="code_comment",
            source_code=unsupported_code,
            context={},
            output_format="markdown",
        )

        response = await generator.generate_documentation(request)

        # Should handle gracefully
        assert response.documentation is not None


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
