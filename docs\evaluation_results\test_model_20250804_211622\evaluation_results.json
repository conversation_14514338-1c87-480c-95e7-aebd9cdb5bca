{"model_name": "test_model", "model_path": "models/trained/test_model", "evaluation_time": "2025-08-04T21:16:22.569450", "metrics": {"bleu": {"score": 0.85, "details": {"total_samples": 3, "predictions": ["default response", "default response", "default response"]}}, "rouge": {"score": 0.7799999999999999, "details": {"total_samples": 3, "rouge_type": "ROUGE-L"}}, "code_quality": {"score": 0.6999999999999998, "details": {"total_samples": 3, "quality_breakdown": {"high_quality": 0, "medium_quality": 3, "low_quality": 0}}}, "perplexity": {"score": 0.2857142857142857, "details": {"perplexity": 2.5, "total_samples": 3}}, "accuracy": {"score": 0.0, "details": {"correct_predictions": 0, "total_predictions": 3, "accuracy_percentage": 0.0}}}, "predictions": [{"id": 0, "prompt": "Write a Python function to sort a list", "expected": "", "predicted": "def example_function():\n    return 'Hello World'", "timestamp": "2025-08-04T21:16:22.579473"}, {"id": 1, "prompt": "Create a React component for a button", "expected": "", "predicted": "function MyComponent() {\n    return <div>Hello World</div>;\n}", "timestamp": "2025-08-04T21:16:22.579482"}, {"id": 2, "prompt": "Review this code for best practices", "expected": "", "predicted": "", "timestamp": "2025-08-04T21:16:22.579485"}], "analysis": {"model_performance": {"average_score": 0.5231428571428571, "best_metric": "bleu", "worst_metric": "accuracy", "score_distribution": {"excellent": 0, "good": 2, "average": 1, "poor": 2}}, "data_analysis": {"total_samples": 3, "sample_types": {"code_generation": 0, "code_review": 0, "general": 3}}, "recommendations": ["Model performs poorly - requires major retraining"]}, "overall_score": 0.5788571428571428, "evaluation_duration": 0.011011123657226562}