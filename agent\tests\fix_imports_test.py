import os
import sys

import pytest

# Test file for fix_imports

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def test_fix_imports_module_imports():
    """Test that fix_imports module imports correctly."""
    try:
        import agent.scripts.fix_imports as fix_imports
        assert hasattr(fix_imports, 'main')
        assert hasattr(fix_imports, 'find_python_files')
        assert hasattr(fix_imports, 'convert_relative_to_absolute_import')
        assert callable(fix_imports.main)
        assert callable(fix_imports.find_python_files)
        assert callable(fix_imports.convert_relative_to_absolute_import)
    except ImportError as e:
        pytest.fail(f"Failed to import fix_imports: {e}")


def test_find_python_files():
    """Test that find_python_files function works."""
    try:
        import agent.scripts.fix_imports as fix_imports
        files = fix_imports.find_python_files(".")
        assert isinstance(files, list)
        # Should find at least some Python files in the project
        assert len(files) > 0
        # All items should be Path objects
        from pathlib import Path
        for file in files[:5]:  # Check first 5
            assert isinstance(file, Path)
            assert file.suffix == '.py'
    except Exception as e:
        pytest.fail(f"find_python_files failed: {e}")


def test_convert_relative_to_absolute_import():
    """Test that convert_relative_to_absolute_import function works."""
    try:
        import agent.scripts.fix_imports as fix_imports
        from pathlib import Path
        # Test with a known Python file
        test_file = Path("scripts/fix_imports.py")
        if test_file.exists():
            result = fix_imports.convert_relative_to_absolute_import(test_file)
            assert isinstance(result, list)
            # Result should be a list of strings (changes made)
            for change in result:
                assert isinstance(change, str)
    except Exception as e:
        pytest.fail(f"convert_relative_to_absolute_import failed: {e}")
