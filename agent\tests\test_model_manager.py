import asyncio
import json
import unittest
from typing import Any, Dict, List
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from agent.models.model_manager import (
    ModelInputError,
    ModelManager,
    ModelProvider,
    ModelProviderError,
    ModelType,
)


class TestModelManager(unittest.TestCase):
    def setUp(self):
        self.manager = ModelManager()
        self.test_prompt = "Test prompt"
        self.test_messages = [
            {"role": "system", "content": "You are a test assistant."},
            {"role": "user", "content": "Hello!"},
        ]
        self.test_model = "test-model"

        # Clear any existing providers to avoid conflicts
        self.manager._providers.clear()

    def test_get_provider(self):
        """Test getting a model provider"""

        # Create a test provider class that inherits from ModelProvider
        class TestProvider(ModelProvider):
            def __init__(self, model_name, **kwargs):
                super().__init__(model_name, **kwargs)

            async def generate(self, prompt: str, **kwargs) -> str:
                return "Generated text"

            async def chat(self, messages, **kwargs) -> str:
                return "Chat response"

            async def embeddings(self, text: str) -> list[float]:
                return [0.1, 0.2, 0.3]

            @classmethod
            def list_models(cls) -> List[Dict[str, Any]]:
                return [{"name": "test-model", "type": "local"}]

        # Register the test provider
        self.manager.register_provider("test", TestProvider)
        provider = self.manager.get_provider("test", self.test_model)
        self.assertIsNotNone(provider)
        self.assertEqual(provider.model_name, self.test_model)

    def test_get_nonexistent_provider(self):
        """Test getting a non-existent provider"""
        with self.assertRaises(ModelProviderError):
            self.manager.get_provider("nonexistent", self.test_model)

    def test_generate(self):
        """Test text generation"""

        # Create a proper mock provider class
        class MockLocalProvider(ModelProvider):
            def __init__(self, model_name: str, **kwargs):
                super().__init__(model_name, **kwargs)
                self.generate_result = "Generated text"
                self.chat_result = "Chat response"
                self.embeddings_result = [0.1, 0.2, 0.3]

            async def generate(self, prompt: str, **kwargs) -> str:
                return self.generate_result

            async def chat(self, messages, **kwargs) -> str:
                return self.chat_result

            async def embeddings(self, text: str) -> list[float]:
                return self.embeddings_result

            @classmethod
            def list_models(cls) -> List[Dict[str, Any]]:
                return [{"name": "test-model", "type": "local"}]

        # Register the mock provider
        self.manager.register_provider("local", MockLocalProvider)

        # Test using asyncio.run
        async def run_test():
            result = await self.manager.generate(self.test_prompt, provider="local")
            self.assertEqual(result, "Generated text")

        asyncio.run(run_test())

    def test_chat(self):
        """Test chat completion"""

        # Create a proper mock provider class
        class MockLocalProvider(ModelProvider):
            def __init__(self, model_name: str, **kwargs):
                super().__init__(model_name, **kwargs)
                self.generate_result = "Generated text"
                self.chat_result = "Chat response"
                self.embeddings_result = [0.1, 0.2, 0.3]

            async def generate(self, prompt: str, **kwargs) -> str:
                return self.generate_result

            async def chat(self, messages, **kwargs) -> str:
                return self.chat_result

            async def embeddings(self, text: str) -> list[float]:
                return self.embeddings_result

            @classmethod
            def list_models(cls) -> List[Dict[str, Any]]:
                return [{"name": "test-model", "type": "local"}]

        # Register the mock provider
        self.manager.register_provider("local", MockLocalProvider)

        # Test using asyncio.run
        async def run_test():
            result = await self.manager.chat(self.test_messages, provider="local")
            self.assertEqual(result, "Chat response")

        asyncio.run(run_test())

    def test_embeddings(self):
        """Test embeddings generation"""

        # Create a proper mock provider class
        class MockLocalProvider(ModelProvider):
            def __init__(self, model_name: str, **kwargs):
                super().__init__(model_name, **kwargs)
                self.generate_result = "Generated text"
                self.chat_result = "Chat response"
                self.embeddings_result = [0.1, 0.2, 0.3]

            async def generate(self, prompt: str, **kwargs) -> str:
                return self.generate_result

            async def chat(self, messages, **kwargs) -> str:
                return self.chat_result

            async def embeddings(self, text: str) -> list[float]:
                return self.embeddings_result

            @classmethod
            def list_models(cls) -> List[Dict[str, Any]]:
                return [{"name": "test-model", "type": "local"}]

        # Register the mock provider
        self.manager.register_provider("local", MockLocalProvider)

        # Test using asyncio.run
        async def run_test():
            result = await self.manager.get_embeddings("test text", provider="local")
            self.assertEqual(result, [0.1, 0.2, 0.3])

        asyncio.run(run_test())

    def test_list_models(self):
        """Test listing available models"""

        # Create a test provider class with list_models method
        class TestProvider(ModelProvider):
            def __init__(self, model_name, **kwargs):
                super().__init__(model_name, **kwargs)

            async def generate(self, prompt: str, **kwargs) -> str:
                return "Generated text"

            async def chat(self, messages, **kwargs) -> str:
                return "Chat response"

            async def embeddings(self, text: str) -> list[float]:
                return [0.1, 0.2, 0.3]

            @classmethod
            def list_models(cls):
                return [{"name": "test-model", "type": ModelType.LOCAL.value}]

        # Register the test provider
        self.manager.register_provider("test", TestProvider)

        # Test
        models = self.manager.list_models("test")
        self.assertEqual(
            models, [{"name": "test-model", "type": ModelType.LOCAL.value}]
        )

    def test_register_provider(self):
        """Test registering a provider"""

        # Create a test provider class
        class TestProvider(ModelProvider):
            def __init__(self, model_name, **kwargs):
                super().__init__(model_name, **kwargs)

        # Test with valid provider
        self.manager.register_provider("test_provider", TestProvider)
        self.assertIn("test_provider", self.manager._providers)

        # Test with invalid provider
        with self.assertRaises(ModelInputError):
            self.manager.register_provider("invalid", object)


# Helper to run async tests
def async_test(coro):
    def wrapper(*args, **kwargs):
        loop = asyncio.get_event_loop()
        return loop.run_until_complete(coro(*args, **kwargs))

    return wrapper


if __name__ == "__main__":
    unittest.main()
