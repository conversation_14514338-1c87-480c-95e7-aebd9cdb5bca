"""
Unified Configuration System for AI Coding Agent.
Consolidates multiple configuration implementations into a single comprehensive solution.
"""

try:
    from agent.utils.config_loader import ConfigLoader

    # Create wrapper functions for backward compatibility
    def load_config(config_path: str) -> dict:
        return ConfigLoader.load_config_with_defaults(config_path, {})

    def get_default_config() -> dict:
        return {}

    def save_config(config_path: str, config: dict) -> bool:
        return ConfigLoader.save_config(config_path, config)

    def update_config(config_path: str, updates: dict) -> dict:
        current = load_config(config_path)
        current.update(updates)
        save_config(config_path, current)
        return current

except ImportError as e:
    print(f"Warning: Could not import from utils.config_loader: {e}")

    # Provide fallback implementations
    def load_config(config_path: str) -> dict:
        import json
        from pathlib import Path

        try:
            config_file = Path(config_path)
            if config_file.exists():
                with open(config_file, "r", encoding="utf-8") as f:
                    return json.load(f)
        except Exception:
            pass
        return {}

    def get_default_config() -> dict:
        return {}

    def save_config(config_path: str, config: dict) -> bool:
        import json
        from pathlib import Path

        try:
            config_file = Path(config_path)
            config_file.parent.mkdir(parents=True, exist_ok=True)
            with open(config_file, "w", encoding="utf-8") as f:
                json.dump(config, f, indent=2)
            return True
        except Exception:
            return False

    def update_config(config_path: str, updates: dict) -> dict:
        current = load_config(config_path)
        current.update(updates)
        save_config(config_path, current)
        return current


try:
    from infrastructure.containers.config.security import secure_config
except ImportError as e:
    print(f"Warning: Could not import from security: {e}")
    secure_config = None

__all__ = [
    "load_config",
    "get_default_config",
    "save_config",
    "update_config",
    "secure_config",
]


class ConfigObject:
    """Configuration object that allows attribute access to dictionary values"""

    def __init__(self, config_dict: dict):
        for key, value in config_dict.items():
            if isinstance(value, dict):
                setattr(self, key, ConfigObject(value))
            else:
                setattr(self, key, value)

    def __getitem__(self, key):
        return getattr(self, key)

    def get(self, key, default=None):
        return getattr(self, key, default)


# Convenience function for backward compatibility
def get_config(config_file: str = "config.json"):
    """Load configuration from JSON file - backward compatibility"""
    from pathlib import Path

    config_path = Path(__file__).parent / config_file
    if not config_path.exists():
        raise FileNotFoundError(f"Config file not found: {config_file}")

    config_dict = load_config(str(config_path))
    return ConfigObject(config_dict)
