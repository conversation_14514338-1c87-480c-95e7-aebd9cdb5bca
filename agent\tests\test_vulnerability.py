# Test file with potential vulnerabilities
import os
import subprocess


def vulnerable_function(user_input):
    # SQL injection vulnerability
    query = f"SELECT * FROM users WHERE id = {user_input}"

    # Command injection vulnerability
    os.system(f"echo {user_input}")
    subprocess.run(f"ls {user_input}", shell=True)

    # Hardcoded secret
    password = os.getenv("PASSWORD")
    api_key = os.getenv("API_KEY")

    return query


# More vulnerabilities
eval("print('Hello')")
exec("print('World')")
