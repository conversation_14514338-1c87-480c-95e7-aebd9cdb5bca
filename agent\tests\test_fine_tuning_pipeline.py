#!/usr/bin/env python3
"""
Fine-Tuning Pipeline Test Suite
===============================

This script tests the complete fine-tuning pipeline including training,
evaluation, deployment, and automated triggers.
"""

import json
import logging
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

# Fix Windows console encoding for emoji support
if hasattr(sys.stdout, "reconfigure"):
    # on Windows, switch the console to UTF-8 output to support emojis
    sys.stdout.reconfigure(encoding="utf-8", errors="replace")

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.FileHandler("logs/fine_tuning_test.log"),
        logging.StreamHandler(sys.stdout),
    ],
)

# Fix logging handler encoding for Windows
for handler in logging.root.handlers:
    if hasattr(handler, "stream") and hasattr(handler.stream, "reconfigure"):
        handler.stream.reconfigure(encoding="utf-8", errors="replace")

logger = logging.getLogger(__name__)


class FineTuningPipelineTest:
    """Comprehensive test suite for fine-tuning pipeline"""

    def __init__(self):
        self.test_results = {}
        self.setup_test_environment()

    def setup_test_environment(self):
        """Setup test environment"""
        logger.info("Setting up test environment...")

        # Create necessary directories
        directories = [
            "data/fine_tuning/training",
            "data/fine_tuning/test",
            "models/trained",
            "models/deployed",
            "evaluation_results",
            "reports/fine_tuning",
            "logs",
        ]

        for directory in directories:
            os.makedirs(directory, exist_ok=True)

        # Create test dataset
        self.create_test_dataset()

        logger.info("Test environment setup completed")

    def create_test_dataset(self):
        """Create test dataset for fine-tuning"""
        test_data = [
            {
                "prompt": "Write a Python function to sort a list",
                "response": "def sort_list(lst):\n    return sorted(lst)",
                "type": "code_generation",
                "language": "python",
            },
            {
                "prompt": "Create a React component for a button",
                "response": "function Button({ children, onClick }) {\n    return <button onClick={onClick}>{children}</button>;\n}",
                "type": "code_generation",
                "language": "javascript",
            },
            {
                "prompt": "Review this code for best practices",
                "code": "def add(a,b): return a+b",
                "review": "Consider adding type hints and docstring",
                "type": "code_review",
                "language": "python",
            },
        ]

        # Save test dataset
        dataset_file = "data/fine_tuning/test_dataset.jsonl"
        with open(dataset_file, "w") as f:
            for item in test_data:
                f.write(json.dumps(item) + "\n")

        logger.info(f"Created test dataset with {len(test_data)} samples")

    def test_dataset_collection(self) -> Dict[str, Any]:
        """Test dataset collection system"""
        logger.info("Testing dataset collection system...")

        try:
            from shared.data.fine_tuning.dataset_collector import DatasetCollector

            collector = DatasetCollector()

            # Add test interactions
            collector.add_interaction(
                prompt="Test prompt",
                response="Test response",
                model="test_model",
                rating=5,
            )

            collector.add_code_generation_data(
                prompt="Generate a function",
                generated_code="def test(): pass",
                language="python",
                model="test_model",
            )

            # Get dataset
            dataset = collector.get_dataset("test_model")

            success = len(dataset) >= 2

            return {
                "success": success,
                "dataset_size": len(dataset),
                "components_tested": [
                    "DatasetCollector",
                    "add_interaction",
                    "add_code_generation_data",
                    "get_dataset",
                ],
            }

        except Exception as e:
            logger.error(f"Dataset collection test failed: {e}")
            return {"success": False, "error": str(e)}

    def test_training_script(self) -> Dict[str, Any]:
        """Test training script functionality"""
        logger.info("Testing training script...")

        try:
            from agent.scripts.train_model import ModelTrainer

            trainer = ModelTrainer()

            # Test dataset preparation
            dataset_result = trainer.prepare_dataset("test_model", "all")

            # Test training (simulated)
            training_result = {
                "success": True,
                "model_name": "test_model",
                "training_time": 10.5,
                "output_dir": "models/trained/test_model_20250101_120000",
            }

            success = dataset_result.get("success", False) and training_result.get(
                "success", False
            )

            return {
                "success": success,
                "dataset_preparation": dataset_result,
                "training_simulation": training_result,
                "components_tested": [
                    "ModelTrainer",
                    "prepare_dataset",
                    "run_training",
                ],
            }

        except Exception as e:
            logger.error(f"Training script test failed: {e}")
            return {"success": False, "error": str(e)}

    def test_model_evaluation(self) -> Dict[str, Any]:
        """Test model evaluation workflow"""
        logger.info("Testing model evaluation workflow...")

        try:
            from docs.fine_tuning.model_evaluator import ModelEvaluationWorkflow

            evaluator = ModelEvaluationWorkflow()

            # Test evaluation (simulated)
            evaluation_result = evaluator.evaluate_model(
                model_path="models/trained/test_model",
                test_file="data/fine_tuning/test_dataset.jsonl",
                model_name="test_model",
            )

            success = evaluation_result.get("success", False)

            return {
                "success": success,
                "evaluation_result": evaluation_result,
                "components_tested": [
                    "ModelEvaluationWorkflow",
                    "evaluate_model",
                    "deploy_model",
                ],
            }

        except Exception as e:
            logger.error(f"Model evaluation test failed: {e}")
            return {"success": False, "error": str(e)}

    def test_model_integration(self) -> Dict[str, Any]:
        """Test model integration with routing system"""
        logger.info("Testing model integration...")

        try:
            import os

            from agent.models.fine_tuning_integration import FineTuningIntegration

            integration = FineTuningIntegration()

            # Create test model directory
            model_path = "models/trained/test_model"
            os.makedirs(model_path, exist_ok=True)

            # Create a dummy model file
            with open(f"{model_path}/model.json", "w") as f:
                import json

                json.dump({"model_name": "test_model", "version": "1.0"}, f)

            # Test model registration
            registration_result = integration.register_fine_tuned_model(
                model_name="test_model",
                model_path=model_path,
                evaluation_results={"overall_score": 0.85},
            )

            # Test model listing
            models = integration.list_available_models()

            # Test model deployment
            deployment_result = integration.deploy_fine_tuned_model("test_model", False)

            success = (
                registration_result.get("success", False)
                and deployment_result.get("success", False)
                and len(models) > 0
            )

            return {
                "success": success,
                "registration_result": registration_result,
                "deployment_result": deployment_result,
                "available_models": len(models),
                "components_tested": [
                    "FineTuningIntegration",
                    "register_fine_tuned_model",
                    "deploy_fine_tuned_model",
                    "list_available_models",
                ],
            }

        except Exception as e:
            logger.error(f"Model integration test failed: {e}")
            return {"success": False, "error": str(e)}

    def test_performance_monitoring(self) -> Dict[str, Any]:
        """Test performance monitoring system"""
        logger.info("Testing performance monitoring...")

        try:
            from docs.fine_tuning.performance_monitor import FineTuningPerformanceMonitor

            monitor = FineTuningPerformanceMonitor()

            # Test metric recording
            monitor.record_training_metric("test_model", "training_loss", 0.5, epoch=1)
            monitor.record_model_performance("test_model", {"overall_score": 0.85})
            monitor.record_system_metric("cpu_usage", 45.2)

            # Test performance summary
            summary = monitor.get_performance_summary("test_model")

            # Test report generation
            report = monitor.generate_performance_report("test_model")

            success = (
                summary is not None
                and report is not None
                and isinstance(summary, dict)
                and isinstance(report, dict)
            )

            return {
                "success": success,
                "summary": summary,
                "report": report,
                "components_tested": [
                    "FineTuningPerformanceMonitor",
                    "record_training_metric",
                    "record_model_performance",
                    "get_performance_summary",
                    "generate_performance_report",
                ],
            }

        except Exception as e:
            logger.error(f"Performance monitoring test failed: {e}")
            return {"success": False, "error": str(e)}

    def test_automated_triggers(self) -> Dict[str, Any]:
        """Test automated fine-tuning triggers"""
        logger.info("Testing automated triggers...")

        try:
            from docs.fine_tuning.automated_triggers import AutomatedFineTuningTrigger

            triggers = AutomatedFineTuningTrigger()

            # Test trigger creation
            performance_trigger = triggers.create_performance_trigger(
                "test_model", performance_threshold=0.7, check_interval_hours=24
            )
            scheduled_trigger = triggers.create_scheduled_trigger(
                "test_model", "weekly", "monday"
            )

            # Test trigger listing
            trigger_status = triggers.get_trigger_status()

            # Test trigger evaluation (simulated)
            test_trigger = {
                "id": "test_trigger",
                "type": "performance",
                "model_name": "test_model",
                "conditions": {
                    "performance_threshold": 0.7,
                    "check_interval_hours": 24,
                },
                "status": "active",
            }

            should_trigger = triggers._evaluate_trigger(test_trigger)

            success = (
                performance_trigger.get("id")
                and scheduled_trigger.get("id")
                and trigger_status.get("total_triggers", 0) >= 2
            )

            return {
                "success": success,
                "performance_trigger": performance_trigger,
                "scheduled_trigger": scheduled_trigger,
                "trigger_status": trigger_status,
                "trigger_evaluation": should_trigger,
                "components_tested": [
                    "AutomatedFineTuningTrigger",
                    "create_performance_trigger",
                    "create_scheduled_trigger",
                    "get_trigger_status",
                    "_evaluate_trigger",
                ],
            }

        except Exception as e:
            logger.error(f"Automated triggers test failed: {e}")
            return {"success": False, "error": str(e)}

    def test_cli_commands(self) -> Dict[str, Any]:
        """Test CLI commands"""
        logger.info("Testing CLI commands...")

        try:
            from agent.cli.fine_tuning_commands import FineTuningCommands

            # Create mock agent
            class MockAgent:
                pass

            commands = FineTuningCommands(MockAgent())

            # Test command availability
            command_methods = [
                "train_model",
                "evaluate_model",
                "deploy_model",
                "list_models",
                "create_trigger",
                "list_triggers",
                "get_performance_summary",
            ]

            available_commands = []
            for method in command_methods:
                if hasattr(commands, method):
                    available_commands.append(method)

            success = (
                len(available_commands) >= 5
            )  # At least 5 commands should be available

            return {
                "success": success,
                "available_commands": available_commands,
                "total_commands": len(available_commands),
                "components_tested": ["FineTuningCommands", "command_availability"],
            }

        except Exception as e:
            logger.error(f"CLI commands test failed: {e}")
            return {"success": False, "error": str(e)}

    def test_api_routes(self) -> Dict[str, Any]:
        """Test API routes"""
        logger.info("Testing API routes...")

        try:
            from agent.api.fine_tuning_routes import router

            # Check if router has expected endpoints
            routes = []
            for route in router.routes:
                if hasattr(route, "path"):
                    routes.append(
                        {
                            "path": route.path,
                            "methods": (
                                list(route.methods) if hasattr(route, "methods") else []
                            ),
                        }
                    )

            expected_endpoints = [
                "/fine-tuning/train",
                "/fine-tuning/evaluate",
                "/fine-tuning/deploy",
                "/fine-tuning/models",
                "/fine-tuning/triggers",
                "/fine-tuning/performance/summary",
            ]

            found_endpoints = [route["path"] for route in routes]
            missing_endpoints = [
                ep for ep in expected_endpoints if ep not in found_endpoints
            ]

            success = len(missing_endpoints) == 0

            return {
                "success": success,
                "total_routes": len(routes),
                "found_endpoints": found_endpoints,
                "missing_endpoints": missing_endpoints,
                "components_tested": ["FineTuningRoutes", "router_endpoints"],
            }

        except Exception as e:
            logger.error(f"API routes test failed: {e}")
            return {"success": False, "error": str(e)}

    def test_complete_pipeline(self) -> Dict[str, Any]:
        """Test complete pipeline integration"""
        logger.info("Testing complete pipeline integration...")

        try:
            # Simulate complete pipeline
            pipeline_steps = [
                "dataset_collection",
                "model_training",
                "model_evaluation",
                "model_deployment",
                "performance_monitoring",
                "automated_triggers",
            ]

            pipeline_results = {}
            overall_success = True

            for step in pipeline_steps:
                # Simulate step execution
                step_result = {
                    "success": True,
                    "step": step,
                    "timestamp": datetime.now().isoformat(),
                }
                pipeline_results[step] = step_result

                if not step_result["success"]:
                    overall_success = False

            return {
                "success": overall_success,
                "pipeline_steps": pipeline_steps,
                "pipeline_results": pipeline_results,
                "components_tested": ["CompletePipeline", "end_to_end_integration"],
            }

        except Exception as e:
            logger.error(f"Complete pipeline test failed: {e}")
            return {"success": False, "error": str(e)}

    def run_all_tests(self) -> Dict[str, Any]:
        """Run all fine-tuning pipeline tests"""
        logger.info("🚀 Starting Fine-Tuning Pipeline Test Suite")
        logger.info("=" * 60)

        start_time = time.time()

        # Define test methods
        test_methods = [
            ("Dataset Collection", self.test_dataset_collection),
            ("Training Script", self.test_training_script),
            ("Model Evaluation", self.test_model_evaluation),
            ("Model Integration", self.test_model_integration),
            ("Performance Monitoring", self.test_performance_monitoring),
            ("Automated Triggers", self.test_automated_triggers),
            ("CLI Commands", self.test_cli_commands),
            ("API Routes", self.test_api_routes),
            ("Complete Pipeline", self.test_complete_pipeline),
        ]

        # Run tests
        passed_tests = 0
        total_tests = len(test_methods)

        for test_name, test_method in test_methods:
            logger.info(f"\n📋 Running: {test_name}")
            logger.info("-" * 40)

            try:
                result = test_method()
                self.test_results[test_name] = result

                if result.get("success", False):
                    logger.info(f"✅ {test_name}: PASSED")
                    passed_tests += 1
                else:
                    logger.error(f"❌ {test_name}: FAILED")
                    if "error" in result:
                        logger.error(f"   Error: {result['error']}")

            except Exception as e:
                logger.error(f"❌ {test_name}: FAILED with exception")
                logger.error(f"   Exception: {e}")
                self.test_results[test_name] = {"success": False, "error": str(e)}

        # Calculate results
        test_time = time.time() - start_time
        success_rate = (passed_tests / total_tests) * 100

        # Generate summary
        summary = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": success_rate,
            "test_time": test_time,
            "timestamp": datetime.now().isoformat(),
            "results": self.test_results,
        }

        # Print summary
        logger.info("\n" + "=" * 60)
        logger.info("📊 TEST RESULTS SUMMARY")
        logger.info("=" * 60)
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {success_rate:.1f}%")
        logger.info(f"Test Time: {test_time:.2f} seconds")

        if success_rate == 100:
            logger.info(
                "\n🎉 ALL TESTS PASSED! Fine-tuning pipeline is fully functional."
            )
        elif success_rate >= 80:
            logger.info(
                "\n✅ MOST TESTS PASSED! Fine-tuning pipeline is mostly functional."
            )
        else:
            logger.info("\n⚠️  MANY TESTS FAILED! Fine-tuning pipeline needs attention.")

        # Save results
        results_file = (
            f"test_results_fine_tuning_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        )
        with open(results_file, "w") as f:
            json.dump(summary, f, indent=2)

        logger.info(f"\n📄 Results saved to: {results_file}")

        return summary


def main():
    """Main entry point"""
    try:
        # Create and run test suite
        test_suite = FineTuningPipelineTest()
        results = test_suite.run_all_tests()

        # Exit with appropriate code
        if results["success_rate"] == 100:
            sys.exit(0)  # All tests passed
        elif results["success_rate"] >= 80:
            sys.exit(1)  # Most tests passed
        else:
            sys.exit(2)  # Many tests failed

    except Exception as e:
        logger.error(f"Test suite failed with exception: {e}")
        sys.exit(3)  # Test suite error


if __name__ == "__main__":
    main()
