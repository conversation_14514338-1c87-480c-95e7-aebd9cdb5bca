{"dashboard": {"service": {"name": "ai-coding-dashboard", "version": "1.0.0", "description": "Dashboard Backend with WebSocket support for real-time user interface", "port": 8080, "host": "0.0.0.0", "environment": "production"}, "resources": {"cpu_limit": "1.0", "memory_limit": "2G", "cpu_reservation": "0.5", "memory_reservation": "1G", "max_connections": 100}, "volumes": {"data": "./data:/app/data", "logs": "./logs:/app/logs", "backups": "./backups:/app/backups", "sites": "./sites:/app/sites", "uploads": "./uploads:/app/uploads", "ssl": "./ssl:/app/ssl", "database": "./database:/app/database", "config": "./config:/app/config", "test_reports": "./test_reports:/app/test_reports"}, "environment": {"PYTHONPATH": "/app", "DASHBOARD_ENVIRONMENT": "production", "LOG_LEVEL": "INFO", "DASHBOARD_PORT": "8088", "DATABASE_URL": "******************************************************/ai_coding_agent", "REDIS_URL": "redis://redis:6379", "API_URL": "http://api:8000", "WEBSOCKET_ENABLED": "true", "REAL_TIME_UPDATES": "true", "NOTIFICATIONS_ENABLED": "true", "METRICS_COLLECTION": "true"}, "security": {"non_root_user": true, "user": "dashboard", "group": "dashboard", "health_check_enabled": true, "health_check_interval": "30s", "health_check_timeout": "30s", "health_check_retries": 3, "health_check_start_period": "40s"}, "monitoring": {"metrics_collection": true, "update_interval": "1s", "cpu_monitoring": true, "memory_monitoring": true, "disk_monitoring": true, "network_monitoring": true, "connection_monitoring": true}, "websocket": {"enabled": true, "endpoint": "/ws", "max_connections": 100, "update_frequency": "1s", "supported_events": ["metrics", "notifications", "alerts", "system_status"]}, "notifications": {"enabled": true, "types": ["info", "warning", "error", "success"], "priorities": ["low", "medium", "high", "critical"], "retention_days": 30, "max_notifications": 1000}, "api": {"endpoints": ["/health", "/summary", "/metrics", "/notifications", "/real-time-status", "/config", "/export", "/ws"], "rate_limiting": {"enabled": true, "requests_per_minute": 1000}, "cors": {"enabled": true, "allowed_origins": ["*"], "allowed_methods": ["GET", "POST", "PUT", "DELETE"], "allowed_headers": ["*"]}}, "cli": {"commands": ["dashboard_status", "dashboard_summary", "dashboard_metrics", "dashboard_notifications", "dashboard_websocket_test", "dashboard_real_time_updates", "dashboard_config", "dashboard_export"]}, "dependencies": {"required_services": ["api", "db", "redis"], "health_check_dependencies": true, "startup_order": ["db", "redis", "api", "dashboard"]}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_logging": true, "log_file": "/app/logs/dashboard.log", "max_file_size": "10MB", "backup_count": 5}, "backup": {"enabled": true, "schedule": "daily", "retention_days": 7, "include_logs": true, "include_config": true}, "rollback": {"enabled": true, "max_versions": 5, "auto_rollback": false, "rollback_threshold": 3}}}