#!/usr/bin/env python3
"""
Comprehensive Error Detection System
Monitors and handles errors across the entire user web application stack
"""

import asyncio
import json
import logging
import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, field
from enum import Enum

import aiohttp
import psutil

logger = logging.getLogger(__name__)


class ErrorSeverity(Enum):
    """Error severity levels"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories"""
    FRONTEND = "frontend"
    BACKEND = "backend"
    DATABASE = "database"
    API = "api"
    AUTHENTICATION = "authentication"
    PERFORMANCE = "performance"
    SECURITY = "security"
    INFRASTRUCTURE = "infrastructure"


@dataclass
class DetectedError:
    """Represents a detected error in the system"""
    id: str
    timestamp: datetime
    category: ErrorCategory
    severity: ErrorSeverity
    title: str
    description: str
    source: str  # Component that detected the error
    context: Dict[str, Any] = field(default_factory=dict)
    stack_trace: Optional[str] = None
    user_impact: str = ""
    auto_fixable: bool = False
    fix_applied: bool = False
    fix_description: Optional[str] = None


@dataclass
class SystemHealth:
    """Overall system health status"""
    overall_status: str  # healthy, degraded, unhealthy, critical
    error_count: int
    critical_errors: int
    performance_score: float  # 0-100
    uptime_percentage: float
    last_check: datetime
    components: Dict[str, str] = field(default_factory=dict)


class ErrorDetectionSystem:
    """Comprehensive error detection and handling system"""

    def __init__(self):
        self.detected_errors: List[DetectedError] = []
        self.error_patterns = self._load_error_patterns()
        self.monitoring_active = False
        self.check_interval = 30  # seconds
        self.max_errors_stored = 1000

    def _load_error_patterns(self) -> Dict[str, Dict[str, Any]]:
        """Load error patterns for different types of issues"""
        return {
            # Frontend JavaScript Errors
            "frontend": {
                "patterns": [
                    r"Uncaught TypeError.*",
                    r"ReferenceError.*is not defined",
                    r"Cannot read property.*of undefined",
                    r"Failed to fetch",
                    r"Network request failed",
                    r"CORS.*blocked",
                    r"404.*Not Found",
                    r"500.*Internal Server Error"
                ],
                "severity_map": {
                    "TypeError": ErrorSeverity.MEDIUM,
                    "ReferenceError": ErrorSeverity.HIGH,
                    "fetch": ErrorSeverity.MEDIUM,
                    "CORS": ErrorSeverity.HIGH,
                    "404": ErrorSeverity.LOW,
                    "500": ErrorSeverity.CRITICAL
                }
            },

            # Backend Application Errors
            "backend": {
                "patterns": [
                    r"Exception.*",
                    r"Error.*",
                    r"Traceback.*",
                    r"ImportError.*",
                    r"ModuleNotFoundError.*",
                    r"AttributeError.*",
                    r"KeyError.*",
                    r"ValueError.*",
                    r"ConnectionError.*",
                    r"TimeoutError.*"
                ],
                "severity_map": {
                    "ImportError": ErrorSeverity.CRITICAL,
                    "ModuleNotFoundError": ErrorSeverity.CRITICAL,
                    "ConnectionError": ErrorSeverity.HIGH,
                    "TimeoutError": ErrorSeverity.MEDIUM,
                    "KeyError": ErrorSeverity.MEDIUM,
                    "ValueError": ErrorSeverity.LOW
                }
            },

            # Database Errors
            "database": {
                "patterns": [
                    r"connection.*refused",
                    r"authentication.*failed",
                    r"table.*does not exist",
                    r"column.*does not exist",
                    r"syntax error.*",
                    r"deadlock.*detected",
                    r"timeout.*expired",
                    r"disk.*full",
                    r"too many connections"
                ],
                "severity_map": {
                    "connection": ErrorSeverity.CRITICAL,
                    "authentication": ErrorSeverity.HIGH,
                    "syntax error": ErrorSeverity.MEDIUM,
                    "deadlock": ErrorSeverity.HIGH,
                    "disk": ErrorSeverity.CRITICAL,
                    "too many connections": ErrorSeverity.HIGH
                }
            },

            # API Errors
            "api": {
                "patterns": [
                    r"HTTP.*4\d\d",
                    r"HTTP.*5\d\d",
                    r"rate.*limit.*exceeded",
                    r"unauthorized",
                    r"forbidden",
                    r"not found",
                    r"method not allowed",
                    r"payload too large"
                ],
                "severity_map": {
                    "401": ErrorSeverity.MEDIUM,
                    "403": ErrorSeverity.HIGH,
                    "404": ErrorSeverity.LOW,
                    "429": ErrorSeverity.MEDIUM,
                    "500": ErrorSeverity.CRITICAL,
                    "502": ErrorSeverity.HIGH,
                    "503": ErrorSeverity.HIGH
                }
            },

            # Performance Issues
            "performance": {
                "patterns": [
                    r"slow.*query",
                    r"high.*cpu",
                    r"memory.*usage",
                    r"response.*time.*exceeded",
                    r"queue.*full",
                    r"cache.*miss.*rate"
                ],
                "severity_map": {
                    "slow": ErrorSeverity.MEDIUM,
                    "cpu": ErrorSeverity.HIGH,
                    "memory": ErrorSeverity.HIGH,
                    "response": ErrorSeverity.MEDIUM,
                    "queue": ErrorSeverity.HIGH
                }
            },

            # Security Issues
            "security": {
                "patterns": [
                    r"unauthorized.*access",
                    r"sql.*injection",
                    r"xss.*attack",
                    r"csrf.*token",
                    r"brute.*force",
                    r"suspicious.*activity",
                    r"invalid.*certificate"
                ],
                "severity_map": {
                    "injection": ErrorSeverity.CRITICAL,
                    "xss": ErrorSeverity.CRITICAL,
                    "brute": ErrorSeverity.HIGH,
                    "unauthorized": ErrorSeverity.HIGH,
                    "certificate": ErrorSeverity.HIGH
                }
            }
        }

    async def start_monitoring(self, site_name: str) -> None:
        """Start continuous monitoring for a site"""
        self.monitoring_active = True
        logger.info(f"Starting error monitoring for site: {site_name}")

        # Start monitoring tasks
        tasks = [
            self._monitor_container_logs(site_name),
            self._monitor_application_health(site_name),
            self._monitor_performance_metrics(site_name),
            self._monitor_security_events(site_name),
            self._monitor_database_health(site_name)
        ]

        await asyncio.gather(*tasks, return_exceptions=True)

    async def _monitor_container_logs(self, site_name: str) -> None:
        """Monitor container logs for errors"""
        while self.monitoring_active:
            try:
                # Get container logs
                import subprocess
                result = subprocess.run(
                    ["docker", "logs", "--tail", "100", f"site-{site_name}"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if result.returncode == 0:
                    logs = result.stdout + result.stderr
                    await self._analyze_logs(site_name, logs, "container")

            except Exception as e:
                logger.error(f"Error monitoring container logs for {site_name}: {e}")

            await asyncio.sleep(self.check_interval)

    async def _monitor_application_health(self, site_name: str) -> None:
        """Monitor application health endpoints"""
        while self.monitoring_active:
            try:
                # Check health endpoint
                from agent.core.site_container_manager import SiteContainerManager
                container_manager = SiteContainerManager()

                status = await container_manager.get_container_status(site_name)
                if status["success"]:
                    container = status["container"]
                    port = container["port"]

                    # Check application health
                    async with aiohttp.ClientSession() as session:
                        health_urls = [
                            f"http://localhost:{port}/health",
                            f"http://localhost:{port}/api/health",
                            f"http://localhost:{port}/"
                        ]

                        for url in health_urls:
                            try:
                                timeout = aiohttp.ClientTimeout(total=5)
                                async with session.get(url, timeout=timeout) as response:
                                    if response.status >= 500:
                                        await self._create_error(
                                            category=ErrorCategory.BACKEND,
                                            severity=ErrorSeverity.HIGH,
                                            title=f"Application Error {response.status}",
                                            description=f"Health check failed with status {response.status}",
                                            source="health_monitor",
                                            context={"url": url, "status": response.status}
                                        )
                                    break  # If one URL works, don't check others
                            except asyncio.TimeoutError:
                                await self._create_error(
                                    category=ErrorCategory.PERFORMANCE,
                                    severity=ErrorSeverity.MEDIUM,
                                    title="Application Timeout",
                                    description=f"Health check timeout for {url}",
                                    source="health_monitor",
                                    context={"url": url, "timeout": 5}
                                )
                            except Exception:
                                continue  # Try next URL

            except Exception as e:
                logger.error(f"Error monitoring application health for {site_name}: {e}")

            await asyncio.sleep(self.check_interval)

    async def _monitor_performance_metrics(self, site_name: str) -> None:
        """Monitor performance metrics"""
        while self.monitoring_active:
            try:
                # Get container resource usage
                import subprocess
                result = subprocess.run(
                    ["docker", "stats", "--no-stream", "--format", "json", f"site-{site_name}"],
                    capture_output=True,
                    text=True,
                    timeout=10
                )

                if result.returncode == 0 and result.stdout.strip():
                    stats = json.loads(result.stdout.strip())

                    # Parse CPU usage
                    cpu_percent = float(stats.get("CPUPerc", "0%").rstrip("%"))
                    memory_usage = stats.get("MemUsage", "0B / 0B")

                    # Check for performance issues
                    if cpu_percent > 80:
                        await self._create_error(
                            category=ErrorCategory.PERFORMANCE,
                            severity=ErrorSeverity.HIGH,
                            title="High CPU Usage",
                            description=f"CPU usage is {cpu_percent}%",
                            source="performance_monitor",
                            context={"cpu_percent": cpu_percent, "memory_usage": memory_usage},
                            auto_fixable=True
                        )

                    # Check memory usage
                    if "GiB" in memory_usage and float(memory_usage.split()[0]) > 1.0:
                        await self._create_error(
                            category=ErrorCategory.PERFORMANCE,
                            severity=ErrorSeverity.MEDIUM,
                            title="High Memory Usage",
                            description=f"Memory usage: {memory_usage}",
                            source="performance_monitor",
                            context={"memory_usage": memory_usage}
                        )

            except Exception as e:
                logger.error(f"Error monitoring performance for {site_name}: {e}")

            await asyncio.sleep(self.check_interval * 2)  # Check less frequently

    async def _monitor_security_events(self, site_name: str) -> None:
        """Monitor for security-related events"""
        while self.monitoring_active:
            try:
                # Check for suspicious access patterns
                # This would integrate with web server logs, fail2ban, etc.

                # For now, simulate security monitoring
                await asyncio.sleep(self.check_interval * 4)  # Check less frequently

            except Exception as e:
                logger.error(f"Error monitoring security for {site_name}: {e}")

    async def _monitor_database_health(self, site_name: str) -> None:
        """Monitor database health and connectivity"""
        while self.monitoring_active:
            try:
                # Use existing database error parser
                from agent.core.db.error_parser import DatabaseErrorParser
                error_parser = DatabaseErrorParser()

                # This would check database connectivity, slow queries, etc.
                # For now, we'll simulate database health checks

                await asyncio.sleep(self.check_interval * 3)

            except Exception as e:
                logger.error(f"Error monitoring database for {site_name}: {e}")

    async def _analyze_logs(self, site_name: str, logs: str, source: str) -> None:
        """Analyze logs for error patterns"""
        lines = logs.split('\n')

        for line in lines:
            if not line.strip():
                continue

            # Check against all error patterns
            for category_name, category_data in self.error_patterns.items():
                for pattern in category_data["patterns"]:
                    if re.search(pattern, line, re.IGNORECASE):
                        # Determine severity
                        severity = ErrorSeverity.MEDIUM  # default
                        for keyword, sev in category_data["severity_map"].items():
                            if keyword.lower() in line.lower():
                                severity = sev
                                break

                        await self._create_error(
                            category=ErrorCategory(category_name),
                            severity=severity,
                            title=f"{category_name.title()} Error Detected",
                            description=line.strip(),
                            source=source,
                            context={"log_line": line, "pattern": pattern},
                            auto_fixable=self._is_auto_fixable(line)
                        )
                        break

    def _is_auto_fixable(self, error_text: str) -> bool:
        """Determine if an error can be automatically fixed"""
        auto_fixable_patterns = [
            "module not found",
            "package not installed",
            "permission denied",
            "port already in use",
            "disk space",
            "memory limit",
            "connection timeout"
        ]

        return any(pattern in error_text.lower() for pattern in auto_fixable_patterns)

    async def _create_error(
        self,
        category: ErrorCategory,
        severity: ErrorSeverity,
        title: str,
        description: str,
        source: str,
        context: Dict[str, Any] = None,
        auto_fixable: bool = False
    ) -> DetectedError:
        """Create a new detected error"""
        error = DetectedError(
            id=f"err_{int(time.time())}_{hash(description) % 10000}",
            timestamp=datetime.now(),
            category=category,
            severity=severity,
            title=title,
            description=description,
            source=source,
            context=context or {},
            auto_fixable=auto_fixable,
            user_impact=self._determine_user_impact(severity, category)
        )

        self.detected_errors.append(error)

        # Keep only recent errors
        if len(self.detected_errors) > self.max_errors_stored:
            self.detected_errors = self.detected_errors[-self.max_errors_stored:]

        # Log the error
        logger.warning(f"Error detected: {error.title} - {error.description}")

        # Attempt auto-fix if possible
        if auto_fixable:
            await self._attempt_auto_fix(error)

        return error

    def _determine_user_impact(self, severity: ErrorSeverity, category: ErrorCategory) -> str:
        """Determine the impact on users"""
        impact_map = {
            (ErrorSeverity.CRITICAL, ErrorCategory.FRONTEND): "Site completely unusable",
            (ErrorSeverity.CRITICAL, ErrorCategory.BACKEND): "Site completely down",
            (ErrorSeverity.CRITICAL, ErrorCategory.DATABASE): "Data access completely blocked",
            (ErrorSeverity.HIGH, ErrorCategory.FRONTEND): "Major functionality broken",
            (ErrorSeverity.HIGH, ErrorCategory.BACKEND): "Core features unavailable",
            (ErrorSeverity.MEDIUM, ErrorCategory.PERFORMANCE): "Site running slowly",
            (ErrorSeverity.LOW, ErrorCategory.FRONTEND): "Minor visual issues"
        }

        return impact_map.get((severity, category), "Minimal impact")

    async def _attempt_auto_fix(self, error: DetectedError) -> None:
        """Attempt to automatically fix the error"""
        try:
            fix_applied = False
            fix_description = ""

            # Auto-fix logic based on error patterns
            if "module not found" in error.description.lower():
                # Auto-install missing Python packages
                fix_description = await self._fix_missing_module(error)
                fix_applied = True
            elif "port already in use" in error.description.lower():
                # Find and use alternative port
                fix_description = await self._fix_port_conflict(error)
                fix_applied = True
            elif "permission denied" in error.description.lower():
                # Fix file permissions
                fix_description = await self._fix_permissions(error)
                fix_applied = True
            elif "disk space" in error.description.lower():
                # Clean up temporary files
                fix_description = await self._fix_disk_space(error)
                fix_applied = True

            if fix_applied:
                error.fix_applied = True
                error.fix_description = fix_description
                logger.info(f"Auto-fix applied for error {error.id}: {fix_description}")
            else:
                # If auto-fix fails, escalate to user
                await self._escalate_to_user(error)

        except Exception as e:
            logger.error(f"Failed to auto-fix error {error.id}: {e}")
            # If auto-fix fails with exception, also escalate to user
            await self._escalate_to_user(error)

    async def _escalate_to_user(self, error: DetectedError) -> None:
        """Escalate error to user when automatic fixes fail"""
        try:
            # Use the site container manager's user escalation system
            from agent.core.site_container_manager import SiteContainerManager

            container_manager = SiteContainerManager()

            # Convert error to format expected by escalation system
            error_details = {
                "title": error.title,
                "description": error.description,
                "category": error.category.value,
                "severity": error.severity.value,
                "source": error.source,
                "user_impact": error.user_impact,
                "timestamp": error.timestamp.isoformat()
            }

            # Try to determine site name from context
            site_name = error.context.get("site_name", "unknown-site")

            escalation_result = await container_manager.handle_error_with_user_escalation(
                site_name=site_name,
                error_details=error_details
            )

            if escalation_result.get("requires_user_input"):
                logger.info(f"Error {error.id} escalated to user: {escalation_result.get('user_message', '')}")
                # Store escalation info in error for later reference
                error.context["escalated_to_user"] = True
                error.context["escalation_message"] = escalation_result.get("user_message", "")
                error.context["suggested_actions"] = escalation_result.get("suggested_actions", [])

        except Exception as e:
            logger.error(f"Failed to escalate error {error.id} to user: {e}")

    async def _fix_missing_module(self, error: DetectedError) -> str:
        """Fix missing Python module by installing it"""
        # Extract module name from error
        import re
        match = re.search(r"No module named '([^']+)'", error.description)
        if match:
            module_name = match.group(1)
            # Install the module in the container
            # This would use the container execution system
            return f"Installed missing module: {module_name}"
        return "Attempted to fix missing module"

    async def _fix_port_conflict(self, error: DetectedError) -> str:
        """Fix port conflict by finding alternative port"""
        # This would integrate with the port manager
        return "Switched to alternative port"

    async def _fix_permissions(self, error: DetectedError) -> str:
        """Fix file permission issues"""
        return "Fixed file permissions"

    async def _fix_disk_space(self, error: DetectedError) -> str:
        """Clean up disk space"""
        return "Cleaned up temporary files"

    def get_system_health(self) -> SystemHealth:
        """Get overall system health status"""
        now = datetime.now()
        recent_errors = [e for e in self.detected_errors if now - e.timestamp < timedelta(hours=1)]
        critical_errors = [e for e in recent_errors if e.severity == ErrorSeverity.CRITICAL]

        # Calculate health score
        if critical_errors:
            status = "critical"
            score = 0
        elif len(recent_errors) > 10:
            status = "unhealthy"
            score = 25
        elif len(recent_errors) > 5:
            status = "degraded"
            score = 60
        else:
            status = "healthy"
            score = 100

        return SystemHealth(
            overall_status=status,
            error_count=len(recent_errors),
            critical_errors=len(critical_errors),
            performance_score=score,
            uptime_percentage=99.9,  # This would be calculated from actual uptime
            last_check=now
        )

    def get_recent_errors(self, hours: int = 24) -> List[DetectedError]:
        """Get errors from the last N hours"""
        cutoff = datetime.now() - timedelta(hours=hours)
        return [e for e in self.detected_errors if e.timestamp > cutoff]

    def stop_monitoring(self) -> None:
        """Stop the monitoring system"""
        self.monitoring_active = False
        logger.info("Error monitoring stopped")
