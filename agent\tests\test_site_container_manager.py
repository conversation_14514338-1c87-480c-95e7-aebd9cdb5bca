# tests/test_site_container_manager.py
"""
Tests for SiteContainerManager
"""

import asyncio
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, <PERSON><PERSON>, patch

import pytest

from agent.core.site_container_manager import (
    ContainerStatus,
    SiteContainer,
    SiteContainerManager,
)


class TestSiteContainerManager:
    """Test cases for SiteContainerManager"""

    @pytest.fixture
    def container_manager(self, tmp_path):
        """Create a SiteContainerManager instance for testing"""
        sites_dir = tmp_path / "sites"
        containers_dir = tmp_path / "containers"
        sites_dir.mkdir()
        containers_dir.mkdir()

        with patch("core.site_container_manager.docker.from_env"):
            manager = SiteContainerManager(
                sites_dir=str(sites_dir), containers_dir=str(containers_dir)
            )
            return manager

    @pytest.fixture
    def mock_site(self, tmp_path):
        """Create a mock site directory"""
        site_dir = tmp_path / "sites" / "test-site"
        site_dir.mkdir(parents=True)

        # Create a simple HTML file
        (site_dir / "index.html").write_text("<html><body>Test Site</body></html>")

        return site_dir

    @pytest.mark.asyncio
    async def test_create_site_container_success(self, container_manager, mock_site):
        """Test successful site container creation"""
        site_name = "test-site"
        site_config = {"name": site_name}

        with patch("subprocess.run") as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Success"

            result = await container_manager.create_site_container(
                site_name, site_config
            )

            assert result["success"] is True
            assert result["port"] is not None
            assert site_name in container_manager.site_containers
            assert container_manager.site_containers[site_name].site_name == site_name

    @pytest.mark.asyncio
    async def test_create_site_container_site_not_found(self, container_manager):
        """Test container creation for non-existent site"""
        site_name = "non-existent-site"
        site_config = {"name": site_name}

        result = await container_manager.create_site_container(site_name, site_config)

        assert result["success"] is False
        assert "does not exist" in result["error"]

    @pytest.mark.asyncio
    async def test_start_site_container_success(self, container_manager, mock_site):
        """Test successful site container start"""
        # First create a container
        site_name = "test-site"
        site_config = {"name": site_name}

        with patch("subprocess.run") as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Success"

            # Create container
            await container_manager.create_site_container(site_name, site_config)

            # Start container
            result = await container_manager.start_site_container(site_name)

            assert result["success"] is True
            assert "url" in result
            assert (
                container_manager.site_containers[site_name].status
                == ContainerStatus.RUNNING
            )

    @pytest.mark.asyncio
    async def test_stop_site_container_success(self, container_manager, mock_site):
        """Test successful site container stop"""
        # First create and start a container
        site_name = "test-site"
        site_config = {"name": site_name}

        with patch("subprocess.run") as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Success"

            # Create and start container
            await container_manager.create_site_container(site_name, site_config)
            await container_manager.start_site_container(site_name)

            # Stop container
            result = await container_manager.stop_site_container(site_name)

            assert result["success"] is True
            assert (
                container_manager.site_containers[site_name].status
                == ContainerStatus.STOPPED
            )

    @pytest.mark.asyncio
    async def test_delete_site_container_success(self, container_manager, mock_site):
        """Test successful site container deletion"""
        # First create a container
        site_name = "test-site"
        site_config = {"name": site_name}

        with patch("subprocess.run") as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Success"

            # Create container
            await container_manager.create_site_container(site_name, site_config)

            # Delete container
            result = await container_manager.delete_site_container(site_name)

            assert result["success"] is True
            assert site_name not in container_manager.site_containers
            assert site_name not in container_manager.port_manager.port_assignments

    @pytest.mark.asyncio
    async def test_list_containers(self, container_manager, mock_site):
        """Test listing all containers"""
        # Create multiple containers
        sites = ["site1", "site2", "site3"]

        # Create site directories for each site
        for site_name in sites:
            site_dir = container_manager.sites_dir / site_name
            site_dir.mkdir(parents=True, exist_ok=True)
            (site_dir / "index.html").write_text(
                f"<html><body>{site_name}</body></html>"
            )

        with patch("subprocess.run") as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Success"

            # Mock the Docker client for get_container_status calls
            mock_docker_container = MagicMock()
            mock_docker_container.status = "running"
            mock_docker_container.attrs = {"State": {"Health": {"Status": "healthy"}}}
            mock_docker_container.stats.return_value = {
                "cpu_stats": {"cpu_usage": {"total_usage": 1000}},
                "memory_stats": {"usage": 1024, "limit": 2048},
            }
            container_manager.docker_client.containers.get.return_value = (
                mock_docker_container
            )

            for site_name in sites:
                site_config = {"name": site_name}
                await container_manager.create_site_container(site_name, site_config)

            # List containers
            result = await container_manager.list_containers()

            assert result["success"] is True
            assert result["total"] == 3
            assert len(result["containers"]) == 3

    @pytest.mark.asyncio
    async def test_get_container_status(self, container_manager, mock_site):
        """Test getting container status"""
        site_name = "test-site"
        site_config = {"name": site_name}

        with patch("subprocess.run") as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Success"

            # Create container
            await container_manager.create_site_container(site_name, site_config)

            # Get status
            result = await container_manager.get_container_status(site_name)

            assert result["success"] is True
            assert "container" in result
            assert result["container"]["site_name"] == site_name

    @pytest.mark.asyncio
    async def test_port_allocation(self, container_manager):
        """Test port allocation and release"""
        site_name = "test-site"

        # Allocate port
        port1 = container_manager.port_manager.allocate_port(site_name)
        assert port1 is not None
        assert port1 >= container_manager.port_manager.start_port
        assert port1 < container_manager.port_manager.end_port

        # Allocate another port for different site
        port2 = container_manager.port_manager.allocate_port("another-site")
        assert port2 != port1

        # Release port
        container_manager.port_manager.release_port(site_name)
        assert site_name not in container_manager.port_manager.port_assignments

        # Allocate again (should get same port back)
        port3 = container_manager.port_manager.allocate_port(site_name)
        assert port3 == port1

    @pytest.mark.asyncio
    async def test_container_state_persistence(
        self, container_manager, mock_site, tmp_path
    ):
        """Test that container states are persisted"""
        site_name = "test-site"
        site_config = {"name": site_name}

        with patch("subprocess.run") as mock_run:
            mock_run.return_value.returncode = 0
            mock_run.return_value.stdout = "Success"

            # Create container
            await container_manager.create_site_container(site_name, site_config)

            # Verify state file exists
            state_file = (
                Path(container_manager.containers_dir) / "container_states.json"
            )
            assert state_file.exists()
