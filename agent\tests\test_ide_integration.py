#!/usr/bin/env python3
"""
Test script to verify IDE integration with IDESidebar component
"""

import os
import sys
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


def test_ide_integration():
    """Test that the IDE integration components are working"""

    print("🧪 Testing IDE Integration...")

    # Test 1: Check if IDESidebar component exists
    print("\n1. Checking IDESidebar component...")
    idesidebar_path = Path("src/frontend/components/IDESidebar.tsx")
    if idesidebar_path.exists():
        print("   ✅ IDESidebar component found")
    else:
        print("   ❌ IDESidebar component not found")
        return False

    # Test 2: Check if IDELayout has been updated
    print("\n2. Checking IDELayout integration...")
    idelayout_path = Path("src/components/ide/IDELayout.tsx")
    if idelayout_path.exists():
        with open(idelayout_path, "r", encoding="utf-8") as f:
            content = f.read()
            if "IDESidebar" in content:
                print("   ✅ IDESidebar integrated into IDELayout")
            else:
                print("   ❌ IDESidebar not found in IDELayout")
                return False
    else:
        print("   ❌ IDELayout not found")
        return False

    # Test 3: Check if API routes are available
    print("\n3. Checking API routes...")
    api_routes_path = Path("src/api/upload_routes.py")
    if api_routes_path.exists():
        with open(api_routes_path, "r", encoding="utf-8") as f:
            content = f.read()
            required_routes = [
                "/upload-site",
                "/sites/list",
                "/sites/validate",
                "/sites/{site_name}/preview",
                "/sites/{site_name}/files",
                "/sites/{site_name}/upload-manifest",
            ]

            missing_routes = []
            for route in required_routes:
                if route not in content:
                    missing_routes.append(route)

            if not missing_routes:
                print("   ✅ All required API routes found")
            else:
                print(f"   ❌ Missing routes: {missing_routes}")
                return False
    else:
        print("   ❌ API routes file not found")
        return False

    # Test 4: Check if SiteUploadManager is available
    print("\n4. Checking SiteUploadManager...")
    upload_manager_path = Path("src/site_upload_manager.py")
    if upload_manager_path.exists():
        print("   ✅ SiteUploadManager found")
    else:
        print("   ❌ SiteUploadManager not found")
        return False

    # Test 5: Check if upload directories exist
    print("\n5. Checking upload directories...")
    upload_dirs = ["uploads", "sites"]
    missing_dirs = []
    for dir_name in upload_dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            missing_dirs.append(dir_name)

    if not missing_dirs:
        print("   ✅ All upload directories exist")
    else:
        print(f"   ⚠️  Missing directories: {missing_dirs}")
        print("   (These will be created automatically when needed)")

    print("\n🎉 IDE Integration Test Results:")
    print("   ✅ IDESidebar component integrated")
    print("   ✅ API routes available")
    print("   ✅ Upload manager ready")
    print("   ✅ Directory structure prepared")

    print("\n🚀 Integration Complete!")
    print("   - IDESidebar is now part of your IDE layout")
    print("   - Upload functionality is available in the sidebar")
    print("   - Site management features are ready to use")
    print("   - All security features are active")

    return True


if __name__ == "__main__":
    success = test_ide_integration()
    if success:
        print("\n✅ All tests passed - IDE integration is ready!")
        sys.exit(0)
    else:
        print("\n❌ Some tests failed - check the integration")
        sys.exit(1)
