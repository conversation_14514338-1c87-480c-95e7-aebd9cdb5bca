#!/usr/bin/env python3
"""
Final Learning System Integration Test

Tests the complete integration of the advanced learning system with the main AI Coding Agent,
handling missing dependencies gracefully.
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_learning_system_imports():
    """Test that all learning system components can be imported"""
    print("🧪 Testing Learning System Imports")
    print("=" * 50)

    try:
        # Test advanced learning enhancements
        from agent.learning.advanced_learning_enhancements import (
            AdversarialDetector,
            CapabilityDiscovery,
            CascadePredictor,
            DegradationManager,
            FederatedLearningManager,
            MetaLearningOptimizer,
            ParetoOptimizer,
            WorkloadPredictor,
        )

        print("✅ Advanced Learning Enhancements imported successfully")

        # Test adaptive learning system
        from agent.learning.adaptive_learning_system import AdaptiveLearningSystem

        print("✅ Adaptive Learning System imported successfully")

        # Test automated learner
        from agent.learning.automated_learner import AutomatedLearner

        print("✅ Automated Learner imported successfully")

        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False


def test_learning_system_initialization():
    """Test that learning systems can be initialized"""
    print("\n🧪 Testing Learning System Initialization")
    print("=" * 50)

    try:
        # Test automated learner initialization
        import json

        from agent.learning.automated_learner import AutomatedLearner

        with open("config/automated_learner_config.json", "r") as f:
            config = json.load(f)
        automated_learner = AutomatedLearner(config)
        print("✅ Automated Learner initialized successfully")

        # Test adaptive learning system initialization
        from agent.learning.adaptive_learning_system import AdaptiveLearningSystem

        adaptive_learning = AdaptiveLearningSystem("config/learning_config.json")
        print("✅ Adaptive Learning System initialized successfully")

        # Test advanced learning components
        from agent.learning.advanced_learning_enhancements import (
            AdversarialDetector,
            CapabilityDiscovery,
            CascadePredictor,
            DegradationManager,
            FederatedLearningManager,
            MetaLearningOptimizer,
            ParetoOptimizer,
            WorkloadPredictor,
        )

        meta_learning = MetaLearningOptimizer()
        print("✅ Meta Learning Optimizer initialized successfully")

        pareto_optimizer = ParetoOptimizer(
            [
                "speed",
                "quality",
                "resource_efficiency",
                "reliability",
                "user_satisfaction",
            ]
        )
        print("✅ Pareto Optimizer initialized successfully")

        workload_predictor = WorkloadPredictor()
        print("✅ Workload Predictor initialized successfully")

        cascade_predictor = CascadePredictor()
        print("✅ Cascade Predictor initialized successfully")

        federated_learning = FederatedLearningManager()
        print("✅ Federated Learning Manager initialized successfully")

        capability_discovery = CapabilityDiscovery()
        print("✅ Capability Discovery initialized successfully")

        adversarial_detector = AdversarialDetector()
        print("✅ Adversarial Detector initialized successfully")

        degradation_manager = DegradationManager()
        print("✅ Degradation Manager initialized successfully")

        return True
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return False


def test_agent_learning_integration():
    """Test that learning systems integrate with agent (handling missing dependencies)"""
    print("\n🧪 Testing Agent Learning Integration")
    print("=" * 50)

    try:
        # Test importing the agent with learning systems
        # Check if learning systems are available in the agent module
        import agent.core.agent as agent_module
        from agent.core.agent import AIAgent

        # Check if the learning imports are available
        if hasattr(agent_module, "ADVANCED_LEARNING_AVAILABLE"):
            print(
                f"✅ Advanced Learning Available: {agent_module.ADVANCED_LEARNING_AVAILABLE}"
            )
        else:
            print("❌ Advanced Learning availability not defined")
            return False

        # Test that learning classes are imported in agent module
        learning_classes = [
            "MetaLearningOptimizer",
            "ParetoOptimizer",
            "WorkloadPredictor",
            "CascadePredictor",
            "FederatedLearningManager",
            "CapabilityDiscovery",
            "AdversarialDetector",
            "DegradationManager",
            "AdaptiveLearningSystem",
            "AutomatedLearner",
        ]

        for class_name in learning_classes:
            if hasattr(agent_module, class_name):
                print(f"✅ {class_name} available in agent module")
            else:
                print(f"⚠️ {class_name} not available in agent module")

        # Test that learning methods are defined in AIAgent class
        learning_methods = [
            "get_learning_summary",
            "get_learning_recommendations",
            "record_learning_event",
            "learn_code_pattern",
            "learn_user_preference",
            "learn_performance_insight",
        ]

        for method_name in learning_methods:
            if hasattr(AIAgent, method_name):
                print(f"✅ {method_name} method available in AIAgent")
            else:
                print(f"❌ {method_name} method missing from AIAgent")
                return False

        return True
    except Exception as e:
        print(f"❌ Agent learning integration failed: {e}")
        return False


def test_learning_functionality():
    """Test basic learning functionality"""
    print("\n🧪 Testing Learning Functionality")
    print("=" * 50)

    try:
        # Test automated learner functionality
        import json

        from agent.learning.automated_learner import AutomatedLearner, LearningEvent

        with open("config/automated_learner_config.json", "r") as f:
            config = json.load(f)
        automated_learner = AutomatedLearner(config)
        automated_learner.start_learning()
        print("✅ Automated Learner started successfully")

        # Test recording an interaction
        event = LearningEvent(
            event_id="test_event_1",
            event_type="user_interaction",
            user_id="test_user",
            context={"test": True},
            data={
                "user_query": "test query",
                "ai_response": "test response",
                "response_time": 1.5,
            },
            outcome="success",
            feedback_score=1.0,
        )

        automated_learner.record_event(event)
        print("✅ Learning event recorded successfully")

        # Test getting learning summary
        summary = automated_learner.get_learning_summary()
        print("✅ Learning summary retrieved successfully")
        print(f"   Total events: {summary.get('total_events', 0)}")
        print(f"   Patterns learned: {summary.get('patterns_learned', 0)}")
        print(f"   Preferences learned: {summary.get('preferences_learned', 0)}")

        # Test generating recommendations
        recommendations = automated_learner.get_pattern_recommendations("test query")
        print("✅ Learning recommendations generated successfully")
        print(f"   Found {len(recommendations)} recommendations")

        # Stop learning
        automated_learner.stop_learning()
        print("✅ Automated Learner stopped successfully")

        return True
    except Exception as e:
        print(f"❌ Learning functionality test failed: {e}")
        return False


def test_advanced_learning_components():
    """Test advanced learning components"""
    print("\n🧪 Testing Advanced Learning Components")
    print("=" * 50)

    try:
        from agent.learning.advanced_learning_enhancements import (
            CascadePredictor,
            MetaLearningOptimizer,
            ParetoOptimizer,
            WorkloadPredictor,
        )

        # Test Meta Learning Optimizer
        meta_learning = MetaLearningOptimizer()
        model_performance = {"model1": [0.8, 0.85, 0.9], "model2": [0.7, 0.75, 0.8]}
        optimized_params = meta_learning.optimize_learning_rates(model_performance)
        print("✅ Meta Learning Optimizer working")
        print(f"   Optimized {len(optimized_params)} models")

        # Test Pareto Optimizer
        pareto_optimizer = ParetoOptimizer(
            [
                "speed",
                "quality",
                "resource_efficiency",
                "reliability",
                "user_satisfaction",
            ]
        )
        current_performance = {
            "speed": 0.8,
            "quality": 0.9,
            "resource_efficiency": 0.7,
            "reliability": 0.85,
            "user_satisfaction": 0.75,
        }
        constraints = {"max_memory": 4096, "max_response_time": 2000}
        pareto_solutions = pareto_optimizer.find_pareto_optimal_routing(
            ["speed", "quality"], current_performance, constraints
        )
        print("✅ Pareto Optimizer working")
        print(f"   Found {len(pareto_solutions)} Pareto solutions")

        # Test Workload Predictor
        workload_predictor = WorkloadPredictor()
        historical_patterns = {
            "requests_per_minute": [10, 15, 20, 25, 30],
            "response_times": [100, 120, 150, 180, 200],
        }
        external_factors = {"time_of_day": 0.8, "day_of_week": 0.6}
        forecast = workload_predictor.predict_demand_spikes(
            historical_patterns, external_factors
        )
        print("✅ Workload Predictor working")
        print(f"   Predicted demand: {forecast.predicted_demand:.2f}")

        # Test Cascade Predictor
        cascade_predictor = CascadePredictor()
        current_loads = {"model1": 0.8, "model2": 0.7, "model3": 0.9}
        failure_patterns = {"model1": [0.1, 0.15, 0.2], "model2": [0.05, 0.1, 0.15]}
        cascade_risk = cascade_predictor.detect_cascade_risk(
            current_loads, failure_patterns
        )
        print("✅ Cascade Predictor working")
        print(f"   Risk level: {cascade_risk.risk_level}")

        return True
    except Exception as e:
        print(f"❌ Advanced learning components test failed: {e}")
        return False


def test_cli_commands():
    """Test CLI commands for learning system"""
    print("\n🧪 Testing CLI Commands")
    print("=" * 50)

    try:
        from agent.cli.advanced_learning_commands import AdvancedLearningCommands

        # Create a mock agent for testing CLI commands
        class MockAgent:
            def __init__(self):
                self.automated_learner = None
                self.adaptive_learning = None
                self.advanced_learning = None

        mock_agent = MockAgent()
        cli_commands = AdvancedLearningCommands(mock_agent)

        # Test that CLI methods exist
        cli_methods = [
            "get_learning_summary",
            "get_learning_recommendations",
            "record_learning_event",
            "learn_code_pattern",
            "learn_user_preference",
            "learn_performance_insight",
        ]

        for method_name in cli_methods:
            if hasattr(cli_commands, method_name):
                print(f"✅ {method_name} CLI command available")
            else:
                print(f"❌ {method_name} CLI command missing")
                return False

        return True
    except Exception as e:
        print(f"❌ CLI commands test failed: {e}")
        return False


def test_api_routes():
    """Test API routes for learning system"""
    print("\n🧪 Testing API Routes")
    print("=" * 50)

    try:
        from agent.api.advanced_learning_routes import router

        # Test that API endpoints are defined
        api_endpoints = [
            "/api/advanced-learning/learning/summary",
            "/api/advanced-learning/learning/recommendations",
            "/api/advanced-learning/learning/event",
            "/api/advanced-learning/learning/code-pattern",
            "/api/advanced-learning/learning/user-preference",
            "/api/advanced-learning/learning/performance-insight",
        ]

        # Check if router has the expected routes
        routes = [route.path for route in router.routes]

        for endpoint in api_endpoints:
            if any(endpoint in route for route in routes):
                print(f"✅ {endpoint} API endpoint available")
            else:
                print(f"⚠️ {endpoint} API endpoint not found in routes")

        return True
    except Exception as e:
        print(f"❌ API routes test failed: {e}")
        return False


def test_configuration():
    """Test configuration for learning system"""
    print("\n🧪 Testing Configuration")
    print("=" * 50)

    try:
        # Test main config
        with open("config/config.json", "r") as f:
            config = json.load(f)

        if "learning" in config:
            learning_config = config["learning"]
            print("✅ Automated learning configuration found")
            print(f"   Enabled: {learning_config.get('enabled', False)}")
            print(f"   Data directory: {learning_config.get('data_dir', 'N/A')}")
            print(
                f"   Learning interval: {learning_config.get('learning_interval', 'N/A')}"
            )
        else:
            print("❌ Automated learning configuration missing")
            return False

        # Test learning config file
        if Path("config/learning_config.json").exists():
            print("✅ Learning config file exists")
        else:
            print("⚠️ Learning config file not found")

        return True
    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False


async def main():
    """Run all integration tests"""
    print("🚀 Final Learning System Integration Test Suite")
    print("=" * 60)

    tests = [
        ("Learning System Imports", test_learning_system_imports),
        ("Learning System Initialization", test_learning_system_initialization),
        ("Agent Learning Integration", test_agent_learning_integration),
        ("Learning Functionality", test_learning_functionality),
        ("Advanced Learning Components", test_advanced_learning_components),
        ("CLI Commands", test_cli_commands),
        ("API Routes", test_api_routes),
        ("Configuration", test_configuration),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 40)

        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()

            results.append((test_name, result))

            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")

        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))

    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 40)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")

    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Learning System is fully integrated.")
        print("\n📋 Integration Summary:")
        print("✅ Learning systems imported and initialized")
        print("✅ Agent integration completed")
        print("✅ CLI commands available")
        print("✅ API endpoints configured")
        print("✅ Configuration properly set up")
        print("✅ All advanced learning components operational")
        return True
    else:
        print("⚠️  Some tests failed. Please check the integration.")
        return False


if __name__ == "__main__":
    asyncio.run(main())
