#!/usr/bin/env python3
"""
Basic Update Agent Test - Core functionality without external dependencies
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

import os
import sys
import unittest
from unittest.mock import MagicMock, patch

# Add project root to path for imports
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), "..")))

from agent.core.agents.agent_main import AIAgent
from agent.core.agents.update_agent import (
    DependencyUpdater,
    GitManager,
    SecurityAuditor,
    TestRunner,
    UpdateAgent,
)


class TestUpdateAgentBasic(unittest.TestCase):

    def setUp(self):
        self.agent = UpdateAgent(".")
        self.security_auditor = SecurityAuditor(".")
        self.dependency_updater = DependencyUpdater(".")
        self.test_runner = TestRunner(".")
        self.git_manager = GitManager(".")

    def test_imports(self):
        """Test that all imports work correctly"""
        print("🔍 Testing imports...")

        try:
            from agent.core.agents.update_agent import UpdateAgent

            print("✅ UpdateAgent import successful")

            from agent.core.agents.update_agent import SecurityAuditor

            print("✅ SecurityAuditor import successful")

            from agent.core.agents.update_agent import DependencyUpdater

            print("✅ DependencyUpdater import successful")

            from agent.core.agents.update_agent import TestRunner

            print("✅ TestRunner import successful")

            from agent.core.agents.update_agent import GitManager

            print("✅ GitManager import successful")

            return True
        except Exception as e:
            print(f"❌ Import failed: {e}")
            return False

    def test_component_initialization(self):
        """Test that all components can be initialized"""
        print("\n🧪 Testing component initialization...")

        try:
            from agent.core.agents.update_agent import (
                DependencyUpdater,
                GitManager,
                SecurityAuditor,
                TestRunner,
            )

            # Test SecurityAuditor
            security_auditor = SecurityAuditor(".")
            print("✅ SecurityAuditor initialized")

            # Test DependencyUpdater
            dependency_updater = DependencyUpdater(".")
            print("✅ DependencyUpdater initialized")

            # Test TestRunner
            test_runner = TestRunner(".")
            print("✅ TestRunner initialized")

            # Test GitManager
            git_manager = GitManager(".")
            print("✅ GitManager initialized")

            return True
        except Exception as e:
            print(f"❌ Component initialization failed: {e}")
            return False

    def test_update_agent_initialization(self):
        """Test UpdateAgent initialization"""
        print("\n🤖 Testing UpdateAgent initialization...")

        try:
            from agent.core.agents.update_agent import UpdateAgent

            agent = UpdateAgent(".")
            print("✅ UpdateAgent initialized successfully")

            # Test that all components are available
            assert hasattr(agent, "security_auditor")
            assert hasattr(agent, "dependency_updater")
            assert hasattr(agent, "test_runner")
            assert hasattr(agent, "git_manager")
            print("✅ All components are properly initialized")

            # Test session ID generation
            assert hasattr(agent, "update_session")
            assert "session_id" in agent.update_session
            print("✅ Update session created with session ID")

            return True
        except Exception as e:
            print(f"❌ UpdateAgent initialization failed: {e}")
            return False

    def test_git_status(self):
        """Test Git status checking"""
        print("\n📝 Testing Git status...")

        try:
            from agent.core.agents.update_agent import GitManager

            git_manager = GitManager(".")
            git_status = git_manager.check_git_status()

            print(f"✅ Git status check completed: {git_status['status']}")
            return True
        except Exception as e:
            print(f"❌ Git status check failed: {e}")
            return False

    def test_requirements_parsing(self):
        """Test requirements.txt parsing"""
        print("\n📦 Testing requirements parsing...")

        try:
            from agent.core.agents.update_agent import DependencyUpdater

            updater = DependencyUpdater(".")
            packages = updater._parse_requirements_txt()

            print(f"✅ Requirements parsing completed: {len(packages)} packages found")
            return True
        except Exception as e:
            print(f"❌ Requirements parsing failed: {e}")
            return False

    def test_package_json_parsing(self):
        """Test package.json parsing"""
        print("\n📦 Testing package.json parsing...")

        try:
            from agent.core.agents.update_agent import DependencyUpdater

            updater = DependencyUpdater(".")
            packages = updater._parse_package_json()

            print(f"✅ Package.json parsing completed: {len(packages)} packages found")
            return True
        except Exception as e:
            print(f"❌ Package.json parsing failed: {e}")
            return False

    def test_cli_integration(self):
        """Test CLI integration"""
        print("\n🖥️ Testing CLI integration...")

        try:
            from agent.core.agents.agent_main import AIAgent

            # Test that the agent can be imported and initialized
            agent = AIAgent("config/smart_routing_config.json")
            print("✅ AIAgent imported and created successfully")

            # Test that update commands are registered in the command router
            update_commands = [
                "update-dependencies",
                "security-audit",
                "check-updates",
                "update-cycle",
            ]

            for cmd in update_commands:
                if cmd in agent.command_router.commands:
                    print(f"✅ {cmd} command registered")
                else:
                    print(f"❌ {cmd} command not registered")
                    return False

            # Test that the command handlers exist
            handler_methods = [
                "_handle_update_dependencies",
                "_handle_security_audit",
                "_handle_check_updates",
                "_handle_update_cycle",
            ]

            for method_name in handler_methods:
                if hasattr(agent.command_router, method_name):
                    print(f"✅ {method_name} handler exists")
                else:
                    print(f"❌ {method_name} handler missing")
                    return False

            return True
        except Exception as e:
            print(f"❌ CLI integration test failed: {e}")
            return False


def main():
    """Run all basic tests"""
    print("🚀 BASIC UPDATE AGENT TEST")
    print("=" * 50)

    tests = [
        ("Imports", test_imports),
        ("Component Initialization", test_component_initialization),
        ("UpdateAgent Initialization", test_update_agent_initialization),
        ("Git Status", test_git_status),
        ("Requirements Parsing", test_requirements_parsing),
        ("Package.json Parsing", test_package_json_parsing),
        ("CLI Integration", test_cli_integration),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Print summary
    print("\n" + "=" * 50)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 50)

    passed = 0
    failed = 0

    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if success:
            passed += 1
        else:
            failed += 1

    print(f"\nTotal: {passed + failed} tests")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed / (passed + failed) * 100):.1f}%")

    if failed == 0:
        print(
            "\n🎉 ALL TESTS PASSED! Update agent core functionality is working correctly."
        )
    else:
        print(f"\n⚠️ {failed} test(s) failed. Please check the implementation.")

    return failed == 0


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
