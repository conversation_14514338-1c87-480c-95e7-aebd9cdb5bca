#!/usr/bin/env python3
"""
Tests for ArchitectAgent state machine functionality
"""
import tempfile
from pathlib import Path

import pytest

from agent.core.agents.architect_agent import ArchitectAgent
from agent.core.project_models import Roadmap, PhaseSpec, StepSpec, TaskSpec


@pytest.fixture
def temp_project_dir():
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir


@pytest.fixture
def architect_agent(temp_project_dir):
    # Create a minimal config for testing
    config_path = Path(temp_project_dir) / "test_config.json"
    config_path.write_text('{"model_settings": {"model_name": "test-model"}}')
    
    agent = ArchitectAgent(str(config_path))
    return agent


def test_state_machine_initialization(architect_agent):
    """Test that state machine initializes correctly"""
    assert hasattr(architect_agent, '_current_state')
    assert hasattr(architect_agent, '_state_history')
    assert hasattr(architect_agent, '_state_transitions')
    assert architect_agent._current_state == architect_agent.ArchitectState.IDLE


def test_state_transitions(architect_agent):
    """Test valid state transitions"""
    # Test valid transition from IDLE to ROADMAP_ANALYSIS
    result = architect_agent.transition_to_state(architect_agent.ArchitectState.ROADMAP_ANALYSIS)
    assert result is True
    assert architect_agent._current_state == architect_agent.ArchitectState.ROADMAP_ANALYSIS
    
    # Test valid transition to TECH_STACK_SELECTION
    result = architect_agent.transition_to_state(architect_agent.ArchitectState.TECH_STACK_SELECTION)
    assert result is True
    assert architect_agent._current_state == architect_agent.ArchitectState.TECH_STACK_SELECTION


def test_invalid_state_transitions(architect_agent):
    """Test invalid state transitions are rejected"""
    # Try invalid transition from IDLE to COMPLETION
    result = architect_agent.transition_to_state(architect_agent.ArchitectState.COMPLETION)
    assert result is False
    assert architect_agent._current_state == architect_agent.ArchitectState.IDLE


def test_state_history_tracking(architect_agent):
    """Test that state transitions are tracked in history"""
    initial_history_length = len(architect_agent._state_history)
    
    architect_agent.transition_to_state(architect_agent.ArchitectState.ROADMAP_ANALYSIS)
    
    assert len(architect_agent._state_history) == initial_history_length + 1
    
    last_transition = architect_agent._state_history[-1]
    assert last_transition["from_state"] == architect_agent.ArchitectState.IDLE
    assert last_transition["to_state"] == architect_agent.ArchitectState.ROADMAP_ANALYSIS
    assert "timestamp" in last_transition


@pytest.mark.asyncio
async def test_start_with_roadmap(architect_agent, temp_project_dir):
    """Test roadmap-first entry point"""
    roadmap_data = {
        "id": "test_roadmap",
        "title": "Test Project",
        "phases": [
            {
                "id": "phase1",
                "title": "Setup",
                "steps": [
                    {
                        "id": "step1",
                        "title": "Initialize",
                        "tasks": [
                            {
                                "id": "task1",
                                "title": "Setup repo",
                                "description": "Initialize git repository",
                                "agent_type": "shell"
                            }
                        ]
                    }
                ]
            }
        ]
    }
    
    result = await architect_agent.start_with_roadmap("test_project", roadmap_data)
    
    # Should succeed if project store is available
    if architect_agent._project_store:
        assert result["success"] is True
        assert result["project_id"] == "test_project"
        assert architect_agent._current_project_id == "test_project"
    else:
        assert result["success"] is False
        assert "Project store unavailable" in result["error"]


def test_needs_roadmap_analysis(architect_agent):
    """Test roadmap analysis detection"""
    # Empty roadmap needs analysis
    empty_roadmap = Roadmap(id="test", title="Test")
    assert architect_agent._needs_roadmap_analysis(empty_roadmap) is True
    
    # Simple roadmap needs analysis
    simple_roadmap = Roadmap(
        id="test", 
        title="Test",
        phases=[PhaseSpec(
            id="p1", 
            title="Phase 1",
            steps=[StepSpec(id="s1", title="Step 1")]
        )]
    )
    assert architect_agent._needs_roadmap_analysis(simple_roadmap) is True
    
    # Auto-generated roadmap needs analysis
    auto_roadmap = Roadmap(
        id="test",
        title="Test", 
        phases=[PhaseSpec(
            id="p1",
            title="Phase 1",
            steps=[StepSpec(id="s1", title="Backend Analysis & Tasks")]
        )]
    )
    assert architect_agent._needs_roadmap_analysis(auto_roadmap) is True


@pytest.mark.asyncio
async def test_tech_stack_selection(architect_agent):
    """Test tech stack selection logic"""
    # Create a simple roadmap
    roadmap = Roadmap(id="test", title="Test")
    
    tech_stack = await architect_agent._select_optimal_tech_stack(roadmap)
    
    assert isinstance(tech_stack, dict)
    assert "frontend" in tech_stack
    assert "backend" in tech_stack
    assert "database" in tech_stack
    assert "containerization" in tech_stack
