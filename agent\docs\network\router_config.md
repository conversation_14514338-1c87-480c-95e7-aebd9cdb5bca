<!-- MOVED TO PROPER LOCATION PER CURSOR RULES -->
## AI Coding Agent Project Optimization

### Router Model: Netgear Nighthawk R7000
### Firmware: DD-WRT
### Project: AI Coding Agent (Phases 0-7)

---

## 📋 Pre-Configuration Checklist

### Hardware Requirements
- [ ] Netgear Nighthawk R7000 router
- [ ] DD-WRT firmware installed
- [ ] Ethernet cable for development machine
- [ ] USB drive (optional, for backups)

### Network Information
- [ ] Current router IP address
- [ ] Development machine MAC address
- [ ] Static IP range for your network
- [ ] Domain name (if applicable)

---

## 🔧 Basic Router Configuration

### 1. Access Router Admin Panel
```
URL: http://*********** (or your router's IP)
Username: root
Password: [your DD-WRT password]
```

### 2. Basic Network Settings
```
Router IP: ***********
Subnet Mask: *************
DHCP Server: Enabled
DHCP Range: ***********00 - *************
DNS Servers: *******, ******* (Google DNS)
```

### 3. Wireless Configuration
```
2.4GHz SSID: AI-Coding-Network-2.4G
5GHz SSID: AI-Coding-Network-5G
Security: WPA2-PSK
Encryption: AES
Channel Width: 20/40MHz (2.4GHz), 80MHz (5GHz)
```

---

## 🎯 AI Coding Agent Specific Configuration

### 1. Static IP Assignment
**Purpose**: Ensure your development machine always has the same IP address

```
Device Name: AI-Coding-Dev-Machine
MAC Address: [Your development machine's MAC]
IP Address: ***********0
Lease Time: Infinite
```

**Steps**:
1. Go to `Setup` → `Basic Setup`
2. Scroll to `Network Address Server Settings (DHCP)`
3. Click `Static Leases`
4. Add your development machine with the above settings

### 2. Port Forwarding Configuration
**Purpose**: Allow external access to your AI Coding Agent services

```
Service Name: AI-Coding-Web
Protocol: TCP
External Port: 80
Internal Port: 80
Internal IP: ***********0
Enabled: Yes

Service Name: AI-Coding-SSL
Protocol: TCP
External Port: 443
Internal Port: 443
Internal IP: ***********0
Enabled: Yes

Service Name: AI-Coding-Dev
Protocol: TCP
External Port: 5000
Internal Port: 5000
Internal IP: ***********0
Enabled: Yes

Service Name: AI-Coding-API
Protocol: TCP
External Port: 8000
Internal Port: 8000
Internal IP: ***********0
Enabled: Yes

Service Name: AI-Coding-WebSocket
Protocol: TCP
External Port: 8080
Internal Port: 8080
Internal IP: ***********0
Enabled: Yes
```

**Steps**:
1. Go to `Applications & Gaming` → `Port Forwarding`
2. Add each service with the above settings
3. Enable all services

### 3. DNS Configuration
**Purpose**: Set up local domain resolution for easier access

```
Local Domain: aicoding.local
Primary DNS: ***********
Secondary DNS: *******
```

**Steps**:
1. Go to `Setup` → `Basic Setup`
3. Set `Local Domain` to `aicoding.local`
4. Configure DNS servers

### 4. QoS (Quality of Service) Configuration
**Purpose**: Prioritize traffic for your development machine

```
QoS Policy: AI-Coding-Development
Priority: High
Source IP: ***********0
Destination: Any
Protocol: TCP/UDP
Port Range: 1-65535
```

**Steps**:
1. Go to `Applications & Gaming` → `QoS`
2. Enable QoS
3. Add policy for your development machine
4. Set priority to High

---

## 🔒 Security Configuration

### 1. Firewall Rules
**Purpose**: Secure your network while allowing necessary traffic

```
Rule Name: AI-Coding-Web-Access
Action: ACCEPT
Protocol: TCP
Source: ANY
Destination: ***********0
Port: 80,443,5000,8000,8080
Log: Yes

Rule Name: AI-Coding-Admin-Access
Action: ACCEPT
Protocol: TCP
Source: ***********/24
Destination: ***********0
Port: 22,23,80,443
Log: Yes
```

**Steps**:
1. Go to `Security` → `Firewall`
2. Add the above firewall rules
3. Enable logging for monitoring

### 2. Access Control
**Purpose**: Restrict access to development services

```
Service: SSH
Allowed IPs: ***********/24
Port: 22
Protocol: TCP

Service: Web Admin
Allowed IPs: ***********/24
Port: 80,443
Protocol: TCP
```

### 3. VPN Configuration (Optional)
**Purpose**: Secure remote access to your development environment

```
VPN Type: OpenVPN
Server IP: ***********
Port: 1194
Protocol: UDP
Cipher: AES-256-CBC
Authentication: SHA256
```

---

## 📊 Performance Optimization

### 1. Bandwidth Management
**Purpose**: Optimize bandwidth for AI model downloads and development

```
Download Speed: [Your ISP speed]
Upload Speed: [Your ISP upload speed]
QoS Type: HTB (Hierarchical Token Bucket)
```

### 2. Wireless Optimization
**Purpose**: Optimize wireless performance for development devices

```
2.4GHz Settings:
- Channel: Auto (or specific channel with least interference)
- Channel Width: 40MHz
- Transmit Power: 70%
- Beacon Interval: 100
- DTIM Interval: 3

5GHz Settings:
- Channel: Auto (or specific channel with least interference)
- Channel Width: 80MHz
- Transmit Power: 70%
- Beacon Interval: 100
- DTIM Interval: 3
```

### 3. Buffer Management
**Purpose**: Optimize network buffers for high-throughput operations

```
TCP Buffer Size: 65536
UDP Buffer Size: 65536
Connection Tracking: Enabled
Max Connections: 4096
```

---

## 🔄 Monitoring and Logging

### 1. System Logs
**Purpose**: Monitor router performance and security

```
Log Level: Info
Log Destination: Local
Log Rotation: Daily
Max Log Size: 1MB
```

### 2. Traffic Monitoring
**Purpose**: Monitor bandwidth usage and identify bottlenecks

```
Monitoring: Enabled
Interface: All
Statistics: Enabled
Graphs: Enabled
```

### 3. Alert Configuration
**Purpose**: Get notified of important events

```
Email Alerts: [Your email]
Alert Events:
- Failed login attempts
- Port scan detection
- High bandwidth usage
- System reboots
```

---

## 🚀 Advanced Configuration

### 1. Load Balancing (Future Use)
**Purpose**: Distribute traffic across multiple development machines

```
Load Balancer: Enabled
Algorithm: Round Robin
Backend Servers:
- ***********0:80 (Primary)
- ***********1:80 (Secondary)
Health Check: Enabled
Check Interval: 30s
```

### 2. Failover Configuration
**Purpose**: Automatic failover if primary connection fails

```
Primary WAN: [Your ISP]
Secondary WAN: [Backup ISP or Mobile]
Failover Detection: Ping
Check Host: *******
Check Interval: 30s
Failover Time: 60s
```

### 3. VLAN Configuration (Advanced)
**Purpose**: Separate development traffic from regular network traffic

```
VLAN ID: 100
VLAN Name: AI-Coding-Dev
Ports: 1,2,3
IP Range: *************/24
Gateway: *************
```

---

## 📝 Configuration Scripts

### 1. Startup Script
Add this to `Administration` → `Commands` → `Startup Script`:

```bash
#!/bin/sh

# AI Coding Agent Router Configuration
# Set up custom routes and firewall rules

# Enable IP forwarding
echo 1 > /proc/sys/net/ipv4/ip_forward

# Set up custom routing for development network
ip route add *************/24 dev br0

# Configure iptables for AI Coding Agent services
iptables -A FORWARD -p tcp --dport 80 -d ***********0 -j ACCEPT
iptables -A FORWARD -p tcp --dport 443 -d ***********0 -j ACCEPT
iptables -A FORWARD -p tcp --dport 5000 -d ***********0 -j ACCEPT
iptables -A FORWARD -p tcp --dport 8000 -d ***********0 -j ACCEPT
iptables -A FORWARD -p tcp --dport 8080 -d ***********0 -j ACCEPT

# Set up QoS rules
tc qdisc add dev eth0 root handle 1: htb default 30
tc class add dev eth0 parent 1: classid 1:1 htb rate 1000mbit
tc class add dev eth0 parent 1:1 classid 1:10 htb rate 100mbit
tc filter add dev eth0 protocol ip parent 1:0 prio 1 u32 match ip src ***********0/32 flowid 1:10

echo "AI Coding Agent router configuration completed"
```

### 2. Firewall Script
Add this to `Administration` → `Commands` → `Firewall`:

```bash
#!/bin/sh

# AI Coding Agent Firewall Rules

# Allow local development traffic
iptables -A INPUT -s ***********/24 -d ***********0 -p tcp --dport 80 -j ACCEPT
iptables -A INPUT -s ***********/24 -d ***********0 -p tcp --dport 443 -j ACCEPT
iptables -A INPUT -s ***********/24 -d ***********0 -p tcp --dport 5000 -j ACCEPT
iptables -A INPUT -s ***********/24 -d ***********0 -p tcp --dport 8000 -j ACCEPT
iptables -A INPUT -s ***********/24 -d ***********0 -p tcp --dport 8080 -j ACCEPT

# Allow SSH access from local network
iptables -A INPUT -s ***********/24 -p tcp --dport 22 -j ACCEPT

# Block external access to development ports (unless VPN)
iptables -A INPUT -p tcp --dport 22 -j DROP
iptables -A INPUT -p tcp --dport 80 -j DROP
iptables -A INPUT -p tcp --dport 443 -j DROP
iptables -A INPUT -p tcp --dport 5000 -j DROP
iptables -A INPUT -p tcp --dport 8000 -j DROP
iptables -A INPUT -p tcp --dport 8080 -j DROP

echo "AI Coding Agent firewall rules applied"
```

---

## 🔍 Testing and Verification

### 1. Network Connectivity Test
```bash
# Test local connectivity
ping ***********0

# Test DNS resolution
nslookup aicoding.local

# Test port forwarding
telnet [your-external-ip] 80
telnet [your-external-ip] 443
telnet [your-external-ip] 5000
```

### 2. Performance Test
```bash
# Test bandwidth to development machine
iperf -c ***********0

# Test latency
ping -c 100 ***********0
```

### 3. Security Test
```bash
# Test firewall rules
nmap -p 80,443,5000,8000,8080 ***********0

# Test external access (should be blocked)
nmap -p 80,443,5000,8000,8080 [your-external-ip]
```

---

## 📋 Configuration Checklist

### Basic Setup
- [ ] Router admin access configured
- [ ] Static IP assigned to development machine
- [ ] DNS servers configured
- [ ] Wireless networks configured

### AI Coding Agent Specific
- [ ] Port forwarding configured
- [ ] QoS rules applied
- [ ] Firewall rules configured
- [ ] Local domain configured

### Security
- [ ] Admin password changed
- [ ] Firewall enabled
- [ ] Access control configured
- [ ] VPN configured (optional)

### Performance
- [ ] Bandwidth management configured
- [ ] Wireless optimization applied
- [ ] Buffer settings optimized
- [ ] Monitoring enabled

### Testing
- [ ] Local connectivity verified
- [ ] Port forwarding tested
- [ ] Security rules verified
- [ ] Performance benchmarks completed

---

## 🚨 Important Notes

### Security Considerations
1. **Change default passwords** immediately
2. **Enable firewall** and configure rules
3. **Use strong encryption** for wireless networks
4. **Regular firmware updates** for security patches
5. **Monitor logs** for suspicious activity

### Performance Considerations
1. **QoS settings** may need adjustment based on your usage
2. **Channel selection** should be optimized for your environment
3. **Buffer sizes** may need tuning for your network conditions
4. **Monitoring** helps identify performance bottlenecks

### Backup and Recovery
1. **Backup configuration** before making changes
2. **Document all changes** for easy recovery
3. **Test configuration** in a safe environment first
4. **Keep firmware backups** for emergency recovery

---

## 📞 Troubleshooting

### Common Issues
1. **Port forwarding not working**: Check firewall rules and ISP restrictions
2. **Slow performance**: Verify QoS settings and channel selection
3. **Connection drops**: Check for interference and update firmware
4. **Security alerts**: Review logs and adjust firewall rules

### Support Resources
- DD-WRT Wiki: https://wiki.dd-wrt.com/
- Netgear Support: https://support.netgear.com/
- AI Coding Agent Documentation: [Project docs]

---

## 🔄 Maintenance Schedule

### Weekly
- [ ] Check system logs
- [ ] Monitor bandwidth usage
- [ ] Verify security settings
- [ ] Test connectivity

### Monthly
- [ ] Update firmware
- [ ] Review firewall rules
- [ ] Optimize performance settings
- [ ] Backup configuration

### Quarterly
- [ ] Security audit
- [ ] Performance optimization
- [ ] Hardware inspection
- [ ] Configuration review

---

*This configuration is optimized for the AI Coding Agent project and may need adjustment based on your specific network environment and requirements.*
