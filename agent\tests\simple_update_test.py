#!/usr/bin/env python3
"""
Simple Update Agent Test - Basic functionality verification
"""

import asyncio
import sys
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))


def test_imports():
    """Test that all imports work correctly"""
    print("🔍 Testing imports...")

    try:
        from update_agent import UpdateAgent

        print("✅ UpdateAgent import successful")

        from update_agent import run_security_audit_only

        print("✅ run_security_audit_only import successful")

        from update_agent import check_updates_only

        print("✅ check_updates_only import successful")

        from update_agent import run_update_cycle

        print("✅ run_update_cycle import successful")

        return True
    except Exception as e:
        print(f"❌ Import failed: {e}")
        return False


async def test_basic_functionality():
    """Test basic update agent functionality"""
    print("\n🧪 Testing basic functionality...")

    try:
        # Test UpdateAgent initialization
        agent = UpdateAgent(".")
        print("✅ UpdateAgent initialized successfully")

        # Test security auditor
        print("   Testing security auditor...")
        security_result = await agent.run_security_audit()
        print(f"   ✅ Security audit completed: {security_result['overall_status']}")

        # Test dependency checker
        print("   Testing dependency checker...")
        update_check = await agent.check_for_updates()
        python_updates = len(update_check["python"]["updates_available"])
        node_updates = len(update_check["node"]["updates_available"])
        print(
            f"   ✅ Update check completed: {python_updates} Python, {node_updates} Node.js updates"
        )

        # Test test runner
        print("   Testing test runner...")
        test_result = await agent.run_tests()
        print(f"   ✅ Test suite completed: {test_result['overall_status']}")

        # Test Git manager
        print("   Testing Git manager...")
        git_status = agent.git_manager.check_git_status()
        print(f"   ✅ Git status check completed: {git_status['status']}")

        return True
    except Exception as e:
        print(f"❌ Basic functionality test failed: {e}")
        return False


async def test_cli_commands():
    """Test CLI command integration"""
    print("\n🖥️ Testing CLI commands...")

    try:
        from agent.core.agents.agent_main import AIAgent

        # Initialize agent
        agent = AIAgent("config/smart_routing_config.json")
        await agent.initialize()

        # Test security audit command
        print("   Testing security-audit command...")
        result = await agent.execute_command("security-audit", {})
        if result.get("success"):
            print("   ✅ security-audit command works")
        else:
            print(f"   ❌ security-audit command failed: {result.get('error')}")

        # Test check-updates command
        print("   Testing check-updates command...")
        result = await agent.execute_command("check-updates", {})
        if result.get("success"):
            print("   ✅ check-updates command works")
        else:
            print(f"   ❌ check-updates command failed: {result.get('error')}")

        await agent.shutdown()
        return True
    except Exception as e:
        print(f"❌ CLI commands test failed: {e}")
        return False


async def main():
    """Run simple tests"""
    print("🚀 SIMPLE UPDATE AGENT TEST")
    print("=" * 50)

    # Test imports
    if not test_imports():
        print("❌ Import test failed - cannot continue")
        return False

    # Test basic functionality
    if not await test_basic_functionality():
        print("❌ Basic functionality test failed")
        return False

    # Test CLI commands
    if not await test_cli_commands():
        print("❌ CLI commands test failed")
        return False

    print("\n🎉 ALL TESTS PASSED! Update agent is working correctly.")
    return True


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
