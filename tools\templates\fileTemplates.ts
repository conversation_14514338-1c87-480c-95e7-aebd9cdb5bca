export interface FileTemplate {
  name: string;
  extension: string;
  content: string;
  description: string;
  category: string;
}

export const fileTemplates: FileTemplate[] = [
  {
    name: 'HTML5 Template',
    extension: 'html',
    content: `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Website</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <header>
        <nav>
            <ul>
                <li><a href="#home">Home</a></li>
                <li><a href="#about">About</a></li>
                <li><a href="#contact">Contact</a></li>
            </ul>
        </nav>
    </header>

    <main>
        <section id="home">
            <h1>Welcome to My Website</h1>
            <p>This is a beautiful and modern website template.</p>
        </section>

        <section id="about">
            <h2>About Us</h2>
            <p>Learn more about our company and mission.</p>
        </section>

        <section id="contact">
            <h2>Contact Us</h2>
            <form>
                <input type="text" placeholder="Name" required>
                <input type="email" placeholder="Email" required>
                <textarea placeholder="Message" required></textarea>
                <button type="submit">Send Message</button>
            </form>
        </section>
    </main>

    <footer>
        <p>&copy; 2024 My Website. All rights reserved.</p>
    </footer>

    <script src="script.js"></script>
</body>
</html>`,
    description: 'A modern HTML5 template with navigation and sections',
    category: 'HTML'
  },
  {
    name: 'CSS Reset & Base Styles',
    extension: 'css',
    content: `/* CSS Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Arial', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #fff;
}

/* Navigation Styles */
nav {
    background-color: #333;
    padding: 1rem;
}

nav ul {
    list-style: none;
    display: flex;
    justify-content: center;
    gap: 2rem;
}

nav a {
    color: white;
    text-decoration: none;
    font-weight: bold;
    transition: color 0.3s ease;
}

nav a:hover {
    color: #ffd700;
}

/* Main Content */
main {
    max-width: 1200px;
    margin: 0 auto;
    padding: 2rem;
}

section {
    margin-bottom: 3rem;
}

h1, h2, h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

h1 {
    font-size: 2.5rem;
    text-align: center;
}

h2 {
    font-size: 2rem;
}

p {
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

/* Form Styles */
form {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    max-width: 500px;
}

input, textarea {
    padding: 0.8rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

textarea {
    min-height: 120px;
    resize: vertical;
}

button {
    padding: 0.8rem 1.5rem;
    background-color: #3498db;
    color: white;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s ease;
}

button:hover {
    background-color: #2980b9;
}

/* Footer */
footer {
    background-color: #333;
    color: white;
    text-align: center;
    padding: 1rem;
    margin-top: 2rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    nav ul {
        flex-direction: column;
        text-align: center;
    }

    h1 {
        font-size: 2rem;
    }

    h2 {
        font-size: 1.5rem;
    }

    main {
        padding: 1rem;
    }
}`,
    description: 'Modern CSS with reset, navigation, and responsive design',
    category: 'CSS'
  },
  {
    name: 'JavaScript App',
    extension: 'js',
    content: `// Main JavaScript Application
class WebsiteApp {
    constructor() {
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeComponents();
        console.log('Website app initialized');
    }

    setupEventListeners() {
        // Smooth scrolling for navigation links
        document.querySelectorAll('nav a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Form submission handling
        const contactForm = document.querySelector('form');
        if (contactForm) {
            contactForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleFormSubmission(e.target);
            });
        }

        // Add scroll effects
        window.addEventListener('scroll', () => {
            this.handleScroll();
        });
    }

    initializeComponents() {
        // Add loading animations
        this.addLoadingAnimations();

        // Initialize any third-party libraries
        this.initializeLibraries();
    }

    handleFormSubmission(form) {
        const formData = new FormData(form);
        const data = Object.fromEntries(formData);

        // Simulate form submission
        console.log('Form submitted:', data);

        // Show success message
        this.showNotification('Message sent successfully!', 'success');

        // Reset form
        form.reset();
    }

    handleScroll() {
        // Add scroll-based animations
        const scrolled = window.pageYOffset;
        const parallax = document.querySelector('.parallax');

        if (parallax) {
            const speed = scrolled * 0.5;
            parallax.style.transform = \`translateY(\${speed}px)\`;
        }
    }

    addLoadingAnimations() {
        // Add fade-in animations to sections
        const sections = document.querySelectorAll('section');
        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.style.opacity = '1';
                    entry.target.style.transform = 'translateY(0)';
                }
            });
        });

        sections.forEach(section => {
            section.style.opacity = '0';
            section.style.transform = 'translateY(20px)';
            section.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
            observer.observe(section);
        });
    }

    initializeLibraries() {
        // Initialize any third-party libraries here
        // Example: analytics, chat widgets, etc.
    }

    showNotification(message, type = 'info') {
        const notification = document.createElement('div');
        notification.className = \`notification \${type}\`;
        notification.textContent = message;
        notification.style.cssText = \`
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            background-color: \${type === 'success' ? '#27ae60' : '#3498db'};
            color: white;
            border-radius: 4px;
            z-index: 1000;
            animation: slideIn 0.3s ease;
        \`;

        document.body.appendChild(notification);

        // Remove notification after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }
}

// Initialize the app when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new WebsiteApp();
});

// Add CSS for animations
const style = document.createElement('style');
style.textContent = \`
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }
\`;
document.head.appendChild(style);`,
    description: 'Modern JavaScript with classes, event handling, and animations',
    category: 'JavaScript'
  },
  {
    name: 'React Component',
    extension: 'jsx',
    content: `import React, { useState, useEffect } from 'react';
import './Component.css';

const Component = () => {
    const [state, setState] = useState({
        loading: false,
        data: null,
        error: null
    });

    useEffect(() => {
        // Component did mount
        console.log('Component mounted');

        // Cleanup function
        return () => {
            console.log('Component will unmount');
        };
    }, []);

    const handleClick = () => {
        setState(prev => ({
            ...prev,
            loading: true
        }));

        // Simulate API call
        setTimeout(() => {
            setState(prev => ({
                ...prev,
                loading: false,
                data: { message: 'Data loaded successfully!' }
            }));
        }, 1000);
    };

    const handleError = () => {
        setState(prev => ({
            ...prev,
            error: 'Something went wrong!'
        }));
    };

    if (state.loading) {
        return (
            <div className="loading">
                <div className="spinner"></div>
                <p>Loading...</p>
            </div>
        );
    }

    if (state.error) {
        return (
            <div className="error">
                <p>{state.error}</p>
                <button onClick={() => setState(prev => ({ ...prev, error: null }))}>
                    Try Again
                </button>
            </div>
        );
    }

    return (
        <div className="component">
            <h2>React Component</h2>
            <p>This is a modern React component with hooks.</p>

            <div className="actions">
                <button onClick={handleClick} className="btn btn-primary">
                    Load Data
                </button>
                <button onClick={handleError} className="btn btn-secondary">
                    Simulate Error
                </button>
            </div>

            {state.data && (
                <div className="data">
                    <h3>Data:</h3>
                    <p>{state.data.message}</p>
                </div>
            )}

            <div className="features">
                <h3>Features:</h3>
                <ul>
                    <li>✅ React Hooks (useState, useEffect)</li>
                    <li>✅ Loading states</li>
                    <li>✅ Error handling</li>
                    <li>✅ Modern ES6+ syntax</li>
                    <li>✅ Component lifecycle management</li>
                </ul>
            </div>
        </div>
    );
};

export default Component;`,
    description: 'Modern React component with hooks and state management',
    category: 'React'
  },
  {
    name: 'TypeScript Interface',
    extension: 'ts',
    content: `// TypeScript interfaces and types for a web application

// User related types
interface User {
    id: string;
    name: string;
    email: string;
    avatar?: string;
    role: UserRole;
    createdAt: Date;
    updatedAt: Date;
}

enum UserRole {
    ADMIN = 'admin',
    USER = 'user',
    MODERATOR = 'moderator'
}

// API response types
interface ApiResponse<T> {
    success: boolean;
    data?: T;
    error?: string;
    message?: string;
    timestamp: Date;
}

// Form types
interface FormField {
    name: string;
    label: string;
    type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select';
    required: boolean;
    validation?: ValidationRule[];
    options?: SelectOption[];
}

interface ValidationRule {
    type: 'required' | 'minLength' | 'maxLength' | 'pattern' | 'custom';
    value?: any;
    message: string;
}

interface SelectOption {
    value: string;
    label: string;
    disabled?: boolean;
}

// Component props types
interface ButtonProps {
    children: React.ReactNode;
    variant?: 'primary' | 'secondary' | 'danger' | 'success';
    size?: 'small' | 'medium' | 'large';
    disabled?: boolean;
    loading?: boolean;
    onClick?: () => void;
    className?: string;
}

interface CardProps {
    title?: string;
    children: React.ReactNode;
    variant?: 'default' | 'outlined' | 'elevated';
    padding?: 'none' | 'small' | 'medium' | 'large';
    className?: string;
}

// State management types
interface AppState {
    user: User | null;
    theme: 'light' | 'dark';
    language: string;
    notifications: Notification[];
    loading: boolean;
}

interface Notification {
    id: string;
    type: 'info' | 'success' | 'warning' | 'error';
    title: string;
    message: string;
    duration?: number;
    action?: {
        label: string;
        onClick: () => void;
    };
}

// Utility types
type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
type Required<T, K extends keyof T> = T & Required<Pick<T, K>>;
type DeepPartial<T> = {
    [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};

// API service types
interface ApiService {
    get<T>(url: string, params?: Record<string, any>): Promise<ApiResponse<T>>;
    post<T>(url: string, data: any): Promise<ApiResponse<T>>;
    put<T>(url: string, data: any): Promise<ApiResponse<T>>;
    delete<T>(url: string): Promise<ApiResponse<T>>;
}

// Event types
interface CustomEvent {
    type: string;
    payload: any;
    timestamp: Date;
    source: string;
}

// Export all types
export type {
    User,
    ApiResponse,
    FormField,
    ValidationRule,
    SelectOption,
    ButtonProps,
    CardProps,
    AppState,
    Notification,
    Optional,
    Required,
    DeepPartial,
    ApiService,
    CustomEvent
};

export { UserRole };`,
    description: 'Comprehensive TypeScript interfaces and types',
    category: 'TypeScript'
  },
  {
    name: 'JSON Configuration',
    extension: 'json',
    content: `{
  "name": "my-website",
  "version": "1.0.0",
  "description": "A modern website built with AI assistance",
  "main": "index.html",
  "scripts": {
    "dev": "live-server",
    "build": "echo 'Build process would go here'",
    "deploy": "echo 'Deployment process would go here'"
  },
  "dependencies": {
    "react": "^18.0.0",
    "react-dom": "^18.0.0",
    "typescript": "^5.0.0"
  },
  "devDependencies": {
    "live-server": "^1.2.2",
    "prettier": "^3.0.0",
    "eslint": "^8.0.0"
  },
  "config": {
    "theme": {
      "primary": "#3498db",
      "secondary": "#2ecc71",
      "accent": "#f39c12",
      "background": "#ffffff",
      "text": "#2c3e50"
    },
    "features": {
      "responsive": true,
      "darkMode": true,
      "animations": true,
      "accessibility": true
    },
    "seo": {
      "title": "My Website",
      "description": "A beautiful and modern website",
      "keywords": ["website", "modern", "responsive"],
      "author": "Your Name"
    }
  },
  "metadata": {
    "created": "2024-01-01T00:00:00Z",
    "lastModified": "2024-01-01T00:00:00Z",
    "generator": "AI Coding Agent",
    "version": "1.0.0"
  }
}`,
    description: 'Project configuration with dependencies and settings',
    category: 'Configuration'
  }
];

export const getTemplatesByCategory = (category: string): FileTemplate[] => {
  return fileTemplates.filter(template => template.category === category);
};

export const getTemplateByName = (name: string): FileTemplate | undefined => {
  return fileTemplates.find(template => template.name === name);
};

export const getAllCategories = (): string[] => {
  return [...new Set(fileTemplates.map(template => template.category))];
};
