#!/usr/bin/env python3
"""
Tests for the Web Error Detection and Auto-Fix System
"""

import os
import shutil
import tempfile
from datetime import datetime, timezone
from pathlib import Path
from unittest.mock import MagicMock, <PERSON><PERSON>, patch

import pytest

from error_detection import (
    AutoFixManager,
    AutoFixResult,
    FixSession,
    SafetyCheck,
    WebError,
    WebErrorDetector,
)


class TestWebErrorDetector:
    """Test the WebErrorDetector class."""

    @pytest.fixture
    def temp_project(self):
        """Create a temporary project directory for testing."""
        temp_dir = tempfile.mkdtemp()
        project_dir = Path(temp_dir) / "test-project"
        project_dir.mkdir()

        # Create some test files
        (project_dir / "src").mkdir()
        (project_dir / "public").mkdir()

        # Create test HTML file with accessibility issues
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Test Page</title>
        </head>
        <body>
            <img src="test.jpg">
            <button>Click me</button>
            <input type="text">
        </body>
        </html>
        """
        with open(project_dir / "public" / "index.html", "w") as f:
            f.write(html_content)

        # Create test CSS file with compatibility issues
        css_content = """
        .container {
            display: grid;
            backdrop-filter: blur(10px);
        }
        """
        with open(project_dir / "src" / "styles.css", "w") as f:
            f.write(css_content)

        # Create test JS file with security issues
        js_content = """
        const password=os.getenv("PASSWORD");
        const api_key=os.getenv("API_KEY");
        element.innerHTML = userInput;
        """
        with open(project_dir / "src" / "app.js", "w") as f:
            f.write(js_content)

        yield project_dir

        # Cleanup
        shutil.rmtree(temp_dir)

    def test_web_error_detector_initialization(self, temp_project):
        """Test WebErrorDetector initialization."""
        detector = WebErrorDetector(str(temp_project))
        assert detector.project_path == str(temp_project)
        assert detector.browser_scanner is not None
        assert detector.accessibility_scanner is not None
        assert detector.seo_validator is not None
        assert detector.performance_scanner is not None
        assert detector.security_scanner is not None

    def test_comprehensive_scan(self, temp_project):
        """Test comprehensive error scanning."""
        detector = WebErrorDetector(str(temp_project))
        report = detector.run_comprehensive_scan()

        assert "scan_timestamp" in report
        assert "scan_duration_seconds" in report
        assert "total_errors" in report
        assert "errors_by_category" in report
        assert "errors_by_severity" in report
        assert "auto_fixable_errors" in report
        assert "errors" in report

        # Should detect some errors in our test files
        assert report["total_errors"] > 0

    def test_accessibility_scanning(self, temp_project):
        """Test accessibility error detection."""
        detector = WebErrorDetector(str(temp_project))
        errors = detector.accessibility_scanner.scan_accessibility_issues()

        # Should detect missing alt attributes
        alt_errors = [e for e in errors if "alt attribute" in e.title]
        assert len(alt_errors) > 0

        # Should detect missing ARIA labels
        aria_errors = [e for e in errors if "ARIA label" in e.title]
        assert len(aria_errors) > 0

    def test_seo_scanning(self, temp_project):
        """Test SEO error detection."""
        detector = WebErrorDetector(str(temp_project))
        errors = detector.seo_validator.validate_seo()

        # Should detect missing meta description
        meta_errors = [e for e in errors if "meta description" in e.title]
        assert len(meta_errors) > 0

    def test_security_scanning(self, temp_project):
        """Test security error detection."""
        detector = WebErrorDetector(str(temp_project))
        errors = detector.security_scanner.scan_security_issues()

        # Should detect hardcoded secrets
        secret_errors = [e for e in errors if "hardcoded secret" in e.title.lower()]
        assert len(secret_errors) > 0

        # Should detect XSS vulnerabilities
        xss_errors = [e for e in errors if "XSS" in e.title.upper()]
        assert len(xss_errors) > 0

    def test_compatibility_scanning(self, temp_project):
        """Test compatibility error detection."""
        detector = WebErrorDetector(str(temp_project))
        errors = detector.browser_scanner.scan_compatibility_issues()

        # Should detect CSS compatibility issues
        css_errors = [e for e in errors if "CSS Compatibility" in e.title]
        assert len(css_errors) > 0

    def test_error_filtering(self, temp_project):
        """Test error filtering by risk level."""
        detector = WebErrorDetector(str(temp_project))
        scan_report = detector.run_comprehensive_scan()

        # Test filtering by risk level
        safe_errors = detector._filter_fixable_errors(scan_report["errors"], "safe")
        moderate_errors = detector._filter_fixable_errors(
            scan_report["errors"], "moderate"
        )
        risky_errors = detector._filter_fixable_errors(scan_report["errors"], "risky")

        # Safe should be subset of moderate, which should be subset of risky
        assert len(safe_errors) <= len(moderate_errors) <= len(risky_errors)

    def test_scan_summary(self, temp_project):
        """Test scan summary generation."""
        detector = WebErrorDetector(str(temp_project))
        summary = detector.get_scan_summary()

        assert "total_errors" in summary
        assert "errors_by_category" in summary
        assert "errors_by_severity" in summary
        assert "auto_fixable_count" in summary
        assert "fix_results_count" in summary
        assert "successful_fixes" in summary


class TestAutoFixManager:
    """Test the AutoFixManager class."""

    @pytest.fixture
    def temp_project(self):
        """Create a temporary project directory for testing."""
        temp_dir = tempfile.mkdtemp()
        project_dir = Path(temp_dir) / "test-project"
        project_dir.mkdir()

        # Create a simple test file
        with open(project_dir / "test.html", "w") as f:
            f.write("<html><body><img src='test.jpg'></body></html>")

        yield project_dir

        # Cleanup
        shutil.rmtree(temp_dir)

    def test_auto_fix_manager_initialization(self, temp_project):
        """Test AutoFixManager initialization."""
        manager = AutoFixManager(str(temp_project))
        assert manager.project_path == Path(temp_project)
        assert manager.error_detector is not None
        assert manager.current_session is None
        assert len(manager.fix_history) == 0

    def test_pre_fix_safety_checks(self, temp_project):
        """Test pre-fix safety checks."""
        manager = AutoFixManager(str(temp_project))
        checks = manager._run_pre_fix_safety_checks()

        assert len(checks) > 0
        for check in checks:
            assert isinstance(check, SafetyCheck)
            assert hasattr(check, "check_name")
            assert hasattr(check, "passed")
            assert hasattr(check, "details")
            assert hasattr(check, "critical")

    def test_project_backup_creation(self, temp_project):
        """Test project backup creation."""
        manager = AutoFixManager(str(temp_project))
        backup_path = manager._create_project_backup()

        assert backup_path is not None
        assert Path(backup_path).exists()

        # Check that backup contains project files
        backup_project = Path(backup_path) / Path(temp_project).name
        assert backup_project.exists()
        assert (backup_project / "test.html").exists()

    def test_safe_auto_fix_success(self, temp_project):
        """Test successful safe auto-fix."""
        manager = AutoFixManager(str(temp_project))

        # Mock the error detector to return known errors
        with patch.object(
            manager.error_detector, "run_comprehensive_scan"
        ) as mock_scan:
            mock_scan.return_value = {
                "total_errors": 1,
                "errors": [
                    {
                        "error_id": "test_error",
                        "category": "accessibility",
                        "severity": "medium",
                        "title": "Missing alt attribute",
                        "description": "Image missing alt attribute",
                        "file_path": str(temp_project / "test.html"),
                        "line_number": 1,
                        "column_number": None,
                        "detected_at": datetime.now(timezone.utc).isoformat(),
                        "auto_fixable": True,
                        "fix_description": "Add alt attribute",
                        "fix_impact": "safe",
                    }
                ],
                "errors_by_category": {"accessibility": 1},
                "errors_by_severity": {"medium": 1},
                "auto_fixable_errors": 1,
            }

            # Mock the validation step to always pass
            with patch.object(manager, "_validate_fixes") as mock_validate:
                mock_validate.return_value = {
                    "build_test": {"passed": True, "reason": "Mocked for test"},
                    "functionality_test": {"passed": True, "reason": "Mocked for test"},
                    "new_errors_count": 0,
                    "overall_passed": True,
                }

                result = manager.run_safe_auto_fix("safe")

            assert result["success"] is True
            assert "session_id" in result
            assert result["data"]["fix_results"]["successful_fixes"] >= 0

    def test_safe_auto_fix_failure(self, temp_project):
        """Test auto-fix failure handling."""
        manager = AutoFixManager(str(temp_project))

        # Mock the error detector to raise an exception
        with patch.object(
            manager.error_detector, "run_comprehensive_scan"
        ) as mock_scan:
            mock_scan.side_effect = Exception("Test error")

            result = manager.run_safe_auto_fix("safe")

            assert result["success"] is False
            assert "Test error" in result["message"]

    def test_fix_validation(self, temp_project):
        """Test fix validation."""
        manager = AutoFixManager(str(temp_project))

        # Mock fix results
        fix_results = [
            {
                "error_id": "test_error",
                "success": True,
                "fix_applied": True,
                "backup_created": True,
                "backup_path": "/backup/test",
                "changes_made": ["Added alt attribute"],
                "error_message": None,
                "fixed_at": datetime.now(timezone.utc).isoformat(),
            }
        ]

        validation = manager._validate_fixes(fix_results)

        assert "overall_passed" in validation
        assert isinstance(validation["overall_passed"], bool)

    def test_rollback_functionality(self, temp_project):
        """Test rollback functionality."""
        manager = AutoFixManager(str(temp_project))

        # Create a backup first
        backup_path = manager._create_project_backup()
        manager.current_session = FixSession(
            session_id="test_session",
            project_path=str(temp_project),
            started_at=datetime.now(timezone.utc),
            completed_at=None,
            total_errors=0,
            errors_fixed=0,
            errors_failed=0,
            backup_created=True,
            backup_path=backup_path,
            rollback_required=False,
            status="running",
        )

        # Test rollback
        manager._rollback_changes()

        # Session should be marked as rolled back
        assert manager.current_session.status == "rolled_back"

    def test_fix_history_management(self, temp_project):
        """Test fix history management."""
        manager = AutoFixManager(str(temp_project))

        # Add a mock session to history
        mock_session = FixSession(
            session_id="test_session",
            project_path=str(temp_project),
            started_at=datetime.now(timezone.utc),
            completed_at=datetime.now(timezone.utc),
            total_errors=5,
            errors_fixed=3,
            errors_failed=2,
            backup_created=True,
            backup_path="/backup/test",
            rollback_required=False,
            status="completed",
        )

        manager.fix_history.append(mock_session)

        # Test getting history
        history = manager.get_fix_history(limit=5)
        assert len(history) > 0

        # Test getting session details
        session_details = manager.get_session_details("test_session")
        assert session_details is not None
        assert session_details["session"]["session_id"] == "test_session"


class TestErrorDataStructures:
    """Test error data structures."""

    def test_web_error_creation(self):
        """Test WebError creation."""
        error = WebError(
            error_id="test_error",
            category="accessibility",
            severity="medium",
            title="Test Error",
            description="This is a test error",
            file_path="/test/file.html",
            line_number=10,
            column_number=5,
            detected_at=datetime.now(timezone.utc),
            auto_fixable=True,
            fix_description="Fix the error",
            fix_impact="safe",
        )

        assert error.error_id == "test_error"
        assert error.category == "accessibility"
        assert error.severity == "medium"
        assert error.auto_fixable is True
        assert error.fix_impact == "safe"

    def test_auto_fix_result_creation(self):
        """Test AutoFixResult creation."""
        result = AutoFixResult(
            error_id="test_error",
            success=True,
            fix_applied=True,
            backup_created=True,
            backup_path="/backup/test",
            changes_made=["Fixed the error"],
            error_message=None,
            fixed_at=datetime.now(timezone.utc),
        )

        assert result.error_id == "test_error"
        assert result.success is True
        assert result.fix_applied is True
        assert result.backup_created is True
        assert len(result.changes_made) == 1

    def test_fix_session_creation(self):
        """Test FixSession creation."""
        session = FixSession(
            session_id="test_session",
            project_path="/test/project",
            started_at=datetime.now(timezone.utc),
            completed_at=None,
            total_errors=5,
            errors_fixed=3,
            errors_failed=2,
            backup_created=True,
            backup_path="/backup/test",
            rollback_required=False,
            status="running",
        )

        assert session.session_id == "test_session"
        assert session.total_errors == 5
        assert session.errors_fixed == 3
        assert session.errors_failed == 2
        assert session.status == "running"

    def test_safety_check_creation(self):
        """Test SafetyCheck creation."""
        check = SafetyCheck(
            check_name="Test Check",
            passed=True,
            details="Check passed successfully",
            critical=True,
        )

        assert check.check_name == "Test Check"
        assert check.passed is True
        assert check.critical is True


class TestIntegration:
    """Integration tests for the error detection system."""

    @pytest.fixture
    def complex_project(self):
        """Create a complex test project with multiple error types."""
        temp_dir = tempfile.mkdtemp()
        project_dir = Path(temp_dir) / "complex-project"
        project_dir.mkdir()

        # Create project structure
        (project_dir / "src").mkdir()
        (project_dir / "public").mkdir()
        (project_dir / "config").mkdir()

        # Create HTML with multiple issues
        html_content = """
        <!DOCTYPE html>
        <html>
        <head>
            <title>Complex Test Page</title>
        </head>
        <body>
            <img src="large-image.jpg">
            <button>Submit</button>
            <input type="text">
            <div class="grid-container">
                <div class="item">Item 1</div>
                <div class="item">Item 2</div>
            </div>
        </body>
        </html>
        """
        with open(project_dir / "public" / "index.html", "w") as f:
            f.write(html_content)

        # Create CSS with compatibility issues
        css_content = """
        .grid-container {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            backdrop-filter: blur(10px);
            aspect-ratio: 16/9;
        }

        .large-image {
            width: 100%;
            height: auto;
        }
        """
        with open(project_dir / "src" / "styles.css", "w") as f:
            f.write(css_content)

        # Create JS with security issues
        js_content = """
        const config = {
            password: "secret123",
            api_key: "abc123def456",
            secret: "very_secret_key"
        };

        function displayUserData(userInput) {
            document.getElementById('output').innerHTML = userInput;
        }

        const userData = localStorage.getItem('userData');
        displayUserData(userData);
        """
        with open(project_dir / "src" / "app.js", "w") as f:
            f.write(js_content)

        yield project_dir

        # Cleanup
        shutil.rmtree(temp_dir)

    def test_full_error_detection_workflow(self, complex_project):
        """Test the complete error detection and auto-fix workflow."""
        # Initialize detector and manager
        detector = WebErrorDetector(str(complex_project))
        manager = AutoFixManager(str(complex_project))

        # Run comprehensive scan
        scan_report = detector.run_comprehensive_scan()
        assert scan_report["total_errors"] > 0

        # Check that we detect various types of errors
        categories = scan_report["errors_by_category"]
        assert len(categories) > 0

        # Run safe auto-fix
        fix_report = manager.run_safe_auto_fix("safe")
        assert fix_report["success"] is True

        # Verify that some errors were fixed
        assert fix_report["data"]["fix_results"]["successful_fixes"] >= 0

    def test_error_categorization(self, complex_project):
        """Test that errors are properly categorized."""
        detector = WebErrorDetector(str(complex_project))
        scan_report = detector.run_comprehensive_scan()

        # Check that we have errors in different categories
        categories = scan_report["errors_by_category"]

        # Should have accessibility errors (missing alt, ARIA labels)
        if "accessibility" in categories:
            assert categories["accessibility"] > 0

        # Should have security errors (hardcoded secrets, XSS)
        if "security" in categories:
            assert categories["security"] > 0

        # Should have compatibility errors (CSS Grid, backdrop-filter)
        if "compatibility" in categories:
            assert categories["compatibility"] > 0

    def test_auto_fix_safety(self, complex_project):
        """Test that auto-fix operations are safe."""
        manager = AutoFixManager(str(complex_project))

        # Run auto-fix with safety checks
        fix_report = manager.run_safe_auto_fix("safe")

        # Should have successful fixes
        assert fix_report["data"]["fix_results"]["successful_fixes"] >= 0

        # Should have session data
        assert "session_id" in fix_report


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
