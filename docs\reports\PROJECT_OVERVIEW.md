### PROJECT OVERVIEW

This document summarizes the AI Coding Agent system as implemented in this repository. It captures core capabilities, the current technology stack, and how local LLM models are configured and used across agents.

### 1. Core Functionality (Features)

- **Natural-language app building**: High-level requests are parsed by `ArchitectAgent` and decomposed into actionable subtasks, routed to specialized agents (`FrontendAgent`, `BackendAgent`, `ContainerAgent`, `ShellOpsAgent`, `SecurityAgent`, `LearningAgent`). See `core/agents/architect_agent.py` and `core/agents/`.
- **Upload and modify existing projects**: Sites can be imported into `sites/` and edited safely with pre-edit backups and framework-aware dispatching. See `core/site_editor.py`, `sites/`.
- **Local hosting and maintenance**: Primary backend services use FastAPI with Uvicorn; optional Gunicorn and Docker compose files are provided for production-like runs. See `api/main.py`, `dashboard/server.py`, `containers/docker-compose*.yml`.
- **Continuous monitoring, detection, and auto-fix**: Cursor rules and repository health are monitored continuously with automated fix loops and verification. Security monitoring also runs in cycles for system health, vulnerabilities, and code analysis.
  - Cursor rules monitor: `scripts/cursor_rules_monitor.py`, `core/cursor_rules_enforcer.py`
  - Security monitoring: `security/automated_security_monitor.py`
  - File and error watchers: `core/file_watcher_agent.py`, `core/error_watcher_agent.py` (uses `watchdog`)
- **Learning and continuous improvement**: The system learns from interactions, outcomes, and external sources to improve recommendations and routing over time.
  - Learning agent and integration: `core/agents/learning_agent.py`, `core/learning_integration.py`
  - Automated learner and adaptive learning: `learning/automated_learner.py`, `learning/adaptive_learning_system.py`
- **Real-time editing, testing, and debugging**: A web-based IDE built on Next.js + React + Tailwind provides Monaco editor, live preview, status bar, notifications, and WebSocket updates.
  - Frontend app shell: `pages/_app.tsx`, `components/ide/`
  - API and WebSockets: `api/main.py`, `api/websocket_agent_updates.py`
  - Tests and scripts include Uvicorn runs and end-to-end integration: `tests/`, `scripts/`

### 2. Tech Stack

- **Frontend**: Next.js (React 18 + TypeScript), Tailwind CSS, Zustand (stores), React Query; component library under `components/` and `components/ide/`.
- **Backend**: FastAPI (primary) served by Uvicorn; some auxiliary Flask utilities/scripts present for demos and legacy tooling.
- **Database**: SQLite by default (`DATABASE_URL` → `sqlite:///database/ai_coding_agent.db`), with some SQLAlchemy usage in tests/utilities.
- **Local hosting / orchestration**: Uvicorn (dev), optional Gunicorn (prod), Dockerfiles for services and `containers/docker-compose*.yml`; Nginx configs available under `nginx/`.
- **Agents**: Python modular agents in `core/agents/` with orchestration (`ArchitectAgent`), collaboration (`collaboration_manager.py`), smart routing (`smart_task_router.py`), and a real-time message bus (`agent_message_bus.py`).
- **Monitoring & automation**: `watchdog` for file changes, subprocess for controlled execution, automated security and framework/trend monitors (`security/`, `trend_monitoring/`, `framework_monitoring/`).
- **Testing & tooling**:
  - Frontend: Jest + React Testing Library (`__tests__/`, `components/__tests__/`)
  - Backend and system: pytest suites in `tests/`
  - E2E/Cypress scaffolding under `cypress/`

### 3. LLM and Model Configuration

- **Orchestration**: Local models are served by Ollama at `http://localhost:11434`. Centralized configuration lives in `config/ai_models_config.json` and `config/models.yaml` (modern model router, agent mapping, health checks). Cloud providers (OpenAI, Anthropic) are optionally supported but disabled by default.
- **Approved/installed local models (project-defaults)**:
  - `deepseek-coder:1.3b`
  - `yi-coder:1.5b`
  - `qwen2.5-coder:3b`
  - `starcoder2:3b`
  - `mistral:7b-instruct-q4_0`
- **Agent–model mapping**: Each specialized agent has `model_settings` in its config (e.g., `config/frontend_agent_config.json`, `config/backend_agent_config.json`, `config/container_agent_config.json`, `config/shell_ops_agent_config.json`). Defaults include:
  - `ArchitectAgent`: defaults to `deepseek-coder:1.3b` via its enhanced base-agent config
  - `FrontendAgent`: optimized for React/Next.js/Tailwind (commonly `starcoder2:3b` per docs)
  - `BackendAgent`, `ShellOpsAgent`, `SecurityAgent`: use agent-specific configs; security workflows rely on combinations of `deepseek-coder`, `mistral`, and `qwen2.5-coder` in tests
  - `LearningAgent`: supports multiple primary models and enables `model_switching`
- **Model switching and fallbacks**:
  - Programmatic selection via `models/model_router.py` (`ModernModelRouter.select_model`) and `models/ollama_manager.py`
  - API endpoints under `api/ollama_routes.py`
  - CLI via `cli/ollama_commands.py`
  - Health checks, performance tracking, and fallbacks are built-in; cloud providers can be enabled in `config/models.yaml` if needed

---

### Recommended .cursor-rules.yaml persona (suggested)

```yaml
version: 1
persona:
  name: AICodingAgent-Architect
  description: >-
    Cursor persona for an AI coding architect that builds and maintains full web apps
    from natural-language instructions using specialized Python agents and local
    Ollama models. Prioritizes safety, readability, and non-regressive edits.
  goals:
    - Build complete web apps from natural language
    - Safely import and modify existing projects under sites/
    - Maintain a local, container-friendly hosting setup (Uvicorn/Gunicorn)
    - Continuously monitor and auto-fix issues without breaking functionality
    - Learn from sessions to improve routing and recommendations
    - Support real-time editing, preview, testing, and debugging via the web IDE
  defaults:
    llm: ollama
    models:
      architect: deepseek-coder:1.3b
      frontend: starcoder2:3b
      backend: qwen2.5-coder:3b
      security: mistral:7b-instruct-q4_0
      learning: yi-coder:1.5b
    # Cloud providers are off by default; enable only via config/models.yaml
  constraints:
    - Preserve and enhance existing features; never regress IDE, API, or agents
    - Keep edits modular, documented, and easy to revert
    - Respect safety rails: path validation, backups, idempotent scripts
    - Prefer local models; do not call cloud providers unless explicitly enabled
    - Follow project coding style and directory conventions
  guardrails:
    code_style:
      - Use descriptive names; avoid cryptic abbreviations
      - Add concise docstrings for non-trivial logic
      - Early returns; handle edge cases first; avoid deep nesting
    architecture:
      - Keep agent boundaries clear (`core/agents/*`)
      - Centralize configs in `config/*`; avoid hardcoded settings
      - Use FastAPI for services; prefer Uvicorn for local runs
      - Prefer SQLite for local persistence; migrations via dedicated scripts
    testing:
      - All changes include or update tests when feasible
      - Keep test runtime under 5 minutes; prefer pytest, Jest/RTL
  workflows:
    - name: fast-check
      steps:
        - cmd: |
            # Windows
            if exist .venv\Scripts\python.exe .venv\Scripts\python.exe -m pytest -q || exit /b 1
            # Unix
            if [ -x .venv/bin/python ]; then .venv/bin/python -m pytest -q; fi
        - cmd: npm run test --silent || echo "(optional: frontend)"
    - name: run-backend-dev
      steps:
        - cmd: python -m uvicorn api.main:app --host 127.0.0.1 --port 8000 --reload
notes:
  - Agent routing and model choices should come from `config/models.yaml` and per-agent configs
  - Prefer `api/ollama_routes.py` and `cli/ollama_commands.py` for model ops
  - Use Docker compose files in `containers/` for service orchestration when needed
```
