# core/site_container_manager.py
"""
Site Container Manager
Handles individual Docker containers for each website with isolation and management capabilities.
Enhanced with SSL support, monitoring, hot reload, and comprehensive management features.

NOTE: This is the specialized website container manager.
For general Docker operations, use containerization/docker_manager.py
"""

import asyncio
import json
import logging
import os
import shutil
import socket
import infrastructure.ssl
import subprocess
import tempfile
import time
from dataclasses import asdict, dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import aiohttp
import docker
from docker import errors as docker_errors
import psutil

from agent.core.persona_manager import PersonaManager, AgentType, TaskCategory

# Import Docker-First policy and monitoring hooks
from agent.core.docker_first_policy import DockerFirstPolicyEnforcer, validate_container_security
from agent.core.container_monitor_hooks import container_monitor_hooks, monitor_container_lifecycle, monitor_container_performance
import yaml

logger = logging.getLogger(__name__)


class ContainerStatus(Enum):
    """Container status enumeration"""

    STOPPED = "stopped"
    RUNNING = "running"
    STARTING = "starting"
    STOPPING = "stopping"
    ERROR = "error"
    BUILDING = "building"
    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"


class EnvironmentType(Enum):
    """Environment type enumeration"""

    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


@dataclass
class SiteContainer:
    """Site container configuration and status"""

    site_name: str
    container_name: str
    port: int
    status: ContainerStatus
    image_name: str
    created_at: datetime
    environment: EnvironmentType = EnvironmentType.DEVELOPMENT
    last_started: Optional[datetime] = None
    last_health_check: Optional[datetime] = None
    health_status: str = "unknown"
    resource_usage: Dict[str, Any] = field(default_factory=dict)
    logs: List[str] = field(default_factory=list)
    ssl_enabled: bool = False
    ssl_cert_path: Optional[str] = None
    backup_enabled: bool = True
    last_backup: Optional[datetime] = None
    hot_reload_enabled: bool = False
    monitoring_enabled: bool = True
    last_migration_applied: Optional[datetime] = None


class PortManager:
    """Enhanced port allocation with persistence and conflict resolution"""

    def __init__(
        self,
        start_port: int = 8080,
        end_port: int = 9000,
        registry_file: str = "config/port_registry.json",
    ):
        self.start_port = start_port
        self.end_port = end_port
        self.allocated_ports = set()
        self.port_assignments = {}  # site_name -> port
        self.registry_file = Path(registry_file)
        self.registry_file.parent.mkdir(exist_ok=True)
        self._load_registry()

    def _load_registry(self):
        """Load port registry from disk"""
        if self.registry_file.exists():
            try:
                with open(self.registry_file, "r") as f:
                    data = json.load(f)
                    self.port_assignments = data.get("assignments", {})
                    self.allocated_ports = set(data.get("allocated", []))
                logger.info(
                    f"Loaded port registry with {len(self.port_assignments)} assignments"
                )
            except Exception as e:
                logger.error(f"Failed to load port registry: {e}")

    def _save_registry(self):
        """Save port registry to disk"""
        try:
            data = {
                "assignments": self.port_assignments,
                "allocated": list(self.allocated_ports),
                "last_updated": datetime.now().isoformat(),
            }
            with open(self.registry_file, "w") as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save port registry: {e}")

    def allocate_port(self, site_name: str) -> int:
        """Allocate a port for a site with conflict resolution"""
        if site_name in self.port_assignments:
            return self.port_assignments[site_name]

        for port in range(self.start_port, self.end_port):
            if port not in self.allocated_ports and not self._is_port_in_use(port):
                self.allocated_ports.add(port)
                self.port_assignments[site_name] = port
                self._save_registry()
                logger.info(f"Allocated port {port} for site {site_name}")
                return port

        raise RuntimeError(
            f"No available ports in range {self.start_port}-{self.end_port}"
        )

    def _is_port_in_use(self, port: int) -> bool:
        """Check if a port is currently in use"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(("localhost", port))
                return False
        except OSError:
            return True

    def release_port(self, site_name: str) -> None:
        """Release a port allocation"""
        if site_name in self.port_assignments:
            port = self.port_assignments[site_name]
            self.allocated_ports.discard(port)
            del self.port_assignments[site_name]
            self._save_registry()
            logger.info(f"Released port {port} for site {site_name}")

    def get_site_port(self, site_name: str) -> Optional[int]:
        """Get the allocated port for a site"""
        return self.port_assignments.get(site_name)

    def list_allocations(self) -> Dict[str, int]:
        """List all port allocations"""
        return self.port_assignments.copy()

    def get_available_ports(self) -> List[int]:
        """Get list of available ports in the range"""
        available = []
        for port in range(self.start_port, self.end_port):
            if port not in self.allocated_ports and not self._is_port_in_use(port):
                available.append(port)
        return available


class NginxManager:
    """Manages Nginx configuration for site containers"""

    def __init__(
        self,
        nginx_conf_dir: str = "nginx",
        sites_enabled_dir: str = "nginx/sites-enabled",
    ):
        self.nginx_conf_dir = Path(nginx_conf_dir)
        self.sites_enabled_dir = Path(sites_enabled_dir)
        self.nginx_conf_dir.mkdir(exist_ok=True)
        self.sites_enabled_dir.mkdir(exist_ok=True)

    async def add_site_config(
        self, site_name: str, port: int, ssl_enabled: bool = False
    ) -> Dict[str, Any]:
        """Add Nginx configuration for a site"""
        try:
            config_content = self._generate_site_config(site_name, port, ssl_enabled)
            config_file = self.sites_enabled_dir / f"{site_name}.conf"

            with open(config_file, "w") as f:
                f.write(config_content)

            await self._reload_nginx()

            return {
                "success": True,
                "message": f"Nginx config added for {site_name}",
                "config_file": str(config_file),
            }
        except Exception as e:
            logger.error(f"Failed to add Nginx config for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    def _generate_site_config(
        self, site_name: str, port: int, ssl_enabled: bool
    ) -> str:
        """Generate Nginx configuration for a site"""
        if ssl_enabled:
            return f"""
server {{
    listen 80;
    server_name {site_name}.localhost;
    return 301 https://$server_name$request_uri;
}}

server {{
    listen 443 ssl;
    server_name {site_name}.localhost;

    ssl_certificate /etc/ssl/certs/{site_name}.crt;
    ssl_certificate_key /etc/ssl/private/{site_name}.key;

    location / {{
        proxy_pass http://localhost:{port};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}
}}
"""
        else:
            return f"""
server {{
    listen 80;
    server_name {site_name}.localhost;

    location / {{
        proxy_pass http://localhost:{port};
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }}
}}
"""

    async def remove_site_config(self, site_name: str) -> Dict[str, Any]:
        """Remove Nginx configuration for a site"""
        try:
            config_file = self.sites_enabled_dir / f"{site_name}.conf"
            if config_file.exists():
                config_file.unlink()
                await self._reload_nginx()

            return {"success": True, "message": f"Nginx config removed for {site_name}"}
        except Exception as e:
            logger.error(f"Failed to remove Nginx config for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def _reload_nginx(self):
        """Reload Nginx configuration"""
        try:
            subprocess.run(["nginx", "-s", "reload"], check=True, capture_output=True)
            logger.info("Nginx configuration reloaded")
        except subprocess.CalledProcessError as e:
            logger.error(f"Failed to reload Nginx: {e}")


class DockerfileGenerator:
    """Generates Dockerfiles for different site types"""

    def __init__(self, templates_dir: str = "templates/docker"):
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(parents=True, exist_ok=True)

    async def generate_dockerfile(
        self, site_name: str, site_path: Path, environment: EnvironmentType
    ) -> Path:
        """Generate appropriate Dockerfile for a site"""
        framework = self._detect_framework(site_path)
        dockerfile_path = site_path / "Dockerfile"

        if framework == "nextjs":
            content = self._generate_nextjs_dockerfile(site_name, environment)
        elif framework == "react":
            content = self._generate_react_dockerfile(site_name, environment)
        elif framework == "static":
            content = self._generate_static_dockerfile(site_name, environment)
        else:
            content = self._generate_generic_dockerfile(site_name, environment)

        with open(dockerfile_path, "w") as f:
            f.write(content)

        return dockerfile_path

    def _detect_framework(self, site_path: Path) -> str:
        """Detect the framework used by the site"""
        if (site_path / "next.config.js").exists() or (
            site_path / "next.config.ts"
        ).exists():
            return "nextjs"
        elif (site_path / "package.json").exists():
            with open(site_path / "package.json", "r") as f:
                package_data = json.load(f)
                dependencies = package_data.get("dependencies", {})
                if "react" in dependencies and "next" not in dependencies:
                    return "react"
        elif (site_path / "index.html").exists():
            return "static"
        return "generic"

    def _detect_site_characteristics(self, site_path: Path) -> Dict[str, Any]:
        """Detect site type and optimize build strategy"""
        import json

        characteristics = {
            "framework": "static",
            "needs_build": False,
            "build_output_dir": None,
            "runtime_files": [],
            "dev_files": [],
            "package_manager": None,
            "has_dependencies": False
        }

        # Check for package.json (Node.js projects)
        if (site_path / "package.json").exists():
            characteristics["has_dependencies"] = True
            characteristics["package_manager"] = "npm"

            try:
                with open(site_path / "package.json", "r") as f:
                    package_data = json.load(f)

                dependencies = package_data.get("dependencies", {})
                dev_dependencies = package_data.get("devDependencies", {})
                scripts = package_data.get("scripts", {})

                # Detect React
                if "react" in dependencies:
                    characteristics["framework"] = "react"
                    characteristics["needs_build"] = "build" in scripts
                    characteristics["build_output_dir"] = "build"
                    characteristics["dev_files"].extend(["src/", "public/"])

                # Detect Next.js
                if "next" in dependencies:
                    characteristics["framework"] = "nextjs"
                    characteristics["needs_build"] = True
                    characteristics["build_output_dir"] = ".next"
                    characteristics["dev_files"].extend(["pages/", "components/", "styles/"])

                # Detect Vue
                if "vue" in dependencies:
                    characteristics["framework"] = "vue"
                    characteristics["needs_build"] = "build" in scripts
                    characteristics["build_output_dir"] = "dist"

            except (json.JSONDecodeError, FileNotFoundError):
                pass

        # Check for requirements.txt (Python projects)
        if (site_path / "requirements.txt").exists():
            characteristics["has_dependencies"] = True
            characteristics["package_manager"] = "pip"
            characteristics["framework"] = "python"

            # Check for common Python web frameworks
            try:
                with open(site_path / "requirements.txt", "r") as f:
                    requirements = f.read().lower()
                    if "flask" in requirements:
                        characteristics["framework"] = "flask"
                    elif "django" in requirements:
                        characteristics["framework"] = "django"
                    elif "fastapi" in requirements:
                        characteristics["framework"] = "fastapi"
            except FileNotFoundError:
                pass

        # Check for static files
        static_files = list(site_path.glob("*.html")) + list(site_path.glob("*.css")) + list(site_path.glob("*.js"))
        if static_files and not characteristics["has_dependencies"]:
            characteristics["framework"] = "static"
            characteristics["runtime_files"] = [f.name for f in static_files]

        # Add common dev files to ignore
        characteristics["dev_files"].extend([
            "node_modules/", "__pycache__/", ".git/", ".vscode/",
            "*.log", "*.tmp", ".env*", "!.env.example",
            "tests/", "__tests__/", "*.test.*", "coverage/",
            "docs/", "README.md", "*.md"
        ])

        return characteristics

    def _generate_nextjs_dockerfile(
        self, site_name: str, environment: EnvironmentType
    ) -> str:
        """Generate Dockerfile for Next.js applications"""
        if environment == EnvironmentType.PRODUCTION:
            return f"""
# Multi-stage build for Next.js production
FROM node:18-alpine@sha256:f77a1aef2da8d83e45ec990f45df906f7a8c4fe8b9fcb24135eba597d5e3dc6d AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM node:18-alpine@sha256:f77a1aef2da8d83e45ec990f45df906f7a8c4fe8b9fcb24135eba597d5e3dc6d AS runner
WORKDIR /app
ENV NODE_ENV=production
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static
EXPOSE 3000
CMD ["node", "server.js"]
"""
        else:
            return f"""
# Development Dockerfile for Next.js
FROM node:18-alpine
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "run", "dev"]
"""

    def _generate_react_dockerfile(
        self, site_name: str, environment: EnvironmentType
    ) -> str:
        """Generate Dockerfile for React applications"""
        if environment == EnvironmentType.PRODUCTION:
            return f"""
# Multi-stage build for React production
FROM node:18-alpine@sha256:f77a1aef2da8d83e45ec990f45df906f7a8c4fe8b9fcb24135eba597d5e3dc6d AS builder
WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production
COPY . .
RUN npm run build

FROM nginx:1.25-alpine@sha256:2d194184b067db3598771b4cf326cfe6ad5051937ba2cc83222d94d4cd9c81bd AS runner
COPY --from=builder /app/build /usr/share/nginx/html
COPY nginx.conf /etc/nginx/nginx.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
"""
        else:
            return f"""
# Development Dockerfile for React
FROM node:18-alpine@sha256:f77a1aef2da8d83e45ec990f45df906f7a8c4fe8b9fcb24135eba597d5e3dc6d
WORKDIR /app
COPY package*.json ./
RUN npm install
COPY . .
EXPOSE 3000
CMD ["npm", "start"]
"""

    def _generate_static_dockerfile(
        self, site_name: str, environment: EnvironmentType
    ) -> str:
        """Generate Dockerfile for static sites"""
        return f"""
# Static site Dockerfile
FROM nginx:alpine
COPY . /usr/share/nginx/html
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
"""

    def _generate_generic_dockerfile(
        self, site_name: str, environment: EnvironmentType
    ) -> str:
        """Generate generic Dockerfile"""
        return f"""
# Generic Dockerfile
FROM python:3.9-slim
WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
EXPOSE 8000
CMD ["python", "app.py"]
"""


class SiteContainerManager:
    """Manages individual Docker containers for each website"""

    def __init__(self, sites_dir: str = "sites", containers_dir: str = "containers"):
        """Initialize the SiteContainerManager"""
        self.sites_dir = Path(sites_dir)
        self.containers_dir = Path(containers_dir)
        self.port_manager = PortManager()
        self.nginx_manager = NginxManager()
        self.dockerfile_generator = DockerfileGenerator()
        self.persona_manager = PersonaManager()

        # Initialize Docker-First policy enforcer
        self.docker_first_enforcer = DockerFirstPolicyEnforcer()

        # Initialize Docker client only if Docker socket is available
        self.docker_client = None
        try:
            self.docker_client = docker.from_env()
            logger.info("Docker client initialized successfully")
        except Exception as e:
            logger.warning(
                f"Docker client not available: {e}. Container operations will be limited."
            )

        self.site_containers: Dict[str, SiteContainer] = {}

        # Ensure directories exist
        self.sites_dir.mkdir(exist_ok=True)
        self.containers_dir.mkdir(exist_ok=True)

        # Load existing container states
        self._load_container_states()

        logger.info("Enhanced SiteContainerManager initialized with Docker-First policy enforcement")

    def _load_container_states(self):
        """Load existing container states from disk"""
        state_file = self.containers_dir / "container_states.json"
        if state_file.exists():
            try:
                with open(state_file, "r") as f:
                    data = json.load(f)

                # Restore port allocations
                for site_name, port in data.get("port_assignments", {}).items():
                    self.port_manager.port_assignments[site_name] = port
                    self.port_manager.allocated_ports.add(port)

                # Restore container states
                for site_data in data.get("containers", []):
                    container = SiteContainer(**site_data)
                    self.site_containers[container.site_name] = container

                logger.info(f"Loaded {len(self.site_containers)} container states")
            except Exception as e:
                logger.error(f"Failed to load container states: {e}")

    def _save_container_states(self):
        """Save container states to disk"""
        state_file = self.containers_dir / "container_states.json"
        try:
            data = {
                "port_assignments": self.port_manager.port_assignments,
                "containers": [
                    asdict(container) for container in self.site_containers.values()
                ],
            }
            with open(state_file, "w") as f:
                json.dump(data, f, indent=2, default=str)
        except Exception as e:
            logger.error(f"Failed to save container states: {e}")

    async def create_site_container(
        self, site_name: str, site_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Create a Docker container for a website"""
        try:
            logger.info(f"Creating container for site: {site_name}")

            # Enforce Docker-First policy
            context = {
                "operation": "create_site_container",
                "site_name": site_name,
                "site_config": site_config,
                "uses_site_container_manager": True,
                "containerized": True
            }

            if not self.docker_first_enforcer.validate_container_operation("website_creation", context):
                return {
                    "success": False,
                    "error": "Docker-First policy violation: Website creation must use containerized deployment"
                }

            # Check if site exists
            site_path = self.sites_dir / site_name
            if not site_path.exists():
                return {
                    "success": False,
                    "error": f"Site {site_name} does not exist at {site_path}",
                }

            # Emit container creation event
            await monitor_container_lifecycle(site_name, "creation_started", {
                "site_path": str(site_path),
                "site_config": site_config
            })

            # Allocate port
            port = self.port_manager.allocate_port(site_name)

            # Detect site characteristics for optimization
            characteristics = self.dockerfile_generator._detect_site_characteristics(site_path)

            # Create container configuration
            container_name = f"site-{site_name}"
            image_name = f"ai-coding-site-{site_name}"

            # Create optimized .dockerignore
            dockerignore_path = await self._create_site_dockerignore(site_path, characteristics)

            # Create Dockerfile for the site
            dockerfile_path = await self._create_site_dockerfile(site_name, site_path)

            # Create site-specific environment file
            env_file_path = await self._create_site_env_file(site_name, port)

            # Create docker-compose for the site
            compose_path = await self._create_site_compose(site_name, port, image_name)

            # Build the container image
            build_result = await self._build_site_image(
                site_name, image_name, dockerfile_path
            )
            if not build_result["success"]:
                self.port_manager.release_port(site_name)
                return build_result

            # Validate container security configuration
            compose_config = self._load_compose_config(compose_path)
            security_validation = validate_container_security(compose_config.get("services", {}).get(site_name, {}))

            if not security_validation["compliant"]:
                logger.warning(f"Security validation warnings for {site_name}: {security_validation['violations']}")
                # Continue but log warnings

            # Create container record
            container = SiteContainer(
                site_name=site_name,
                container_name=container_name,
                port=port,
                status=ContainerStatus.STOPPED,
                image_name=image_name,
                created_at=datetime.now(),
                resource_usage={},
                logs=[],
            )

            self.site_containers[site_name] = container
            self._save_container_states()

            # Emit container creation success event
            await monitor_container_lifecycle(site_name, "created", {
                "container_name": container_name,
                "port": port,
                "image_name": image_name,
                "security_score": security_validation["security_score"]
            })

            logger.info(f"Container created for site {site_name} on port {port}")

            return {
                "success": True,
                "container": asdict(container),
                "port": port,
                "dockerfile": str(dockerfile_path),
                "compose": str(compose_path),
                "security_validation": security_validation
            }

        except Exception as e:
            logger.error(f"Failed to create container for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    def _load_compose_config(self, compose_path: Path) -> Dict[str, Any]:
        """Load docker-compose configuration"""
        try:
            import yaml
            with open(compose_path, 'r') as f:
                return yaml.safe_load(f)
        except Exception as e:
            logger.error(f"Error loading compose config from {compose_path}: {e}")
            return {}

    async def enforce_docker_first_policy(self) -> Dict[str, Any]:
        """Enforce Docker-First policy across all operations"""
        try:
            # Run policy enforcement
            policy_result = self.docker_first_enforcer.enforce_docker_first(strict=False)

            # Get violation report
            violation_report = self.docker_first_enforcer.get_violation_report()

            return {
                "success": True,
                "policy_result": policy_result,
                "violation_report": violation_report,
                "containers_managed": len(self.site_containers)
            }

        except Exception as e:
            logger.error(f"Error enforcing Docker-First policy: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    async def get_monitoring_status(self) -> Dict[str, Any]:
        """Get comprehensive monitoring status for all containers"""
        try:
            monitoring_data = {}

            for site_name, container in self.site_containers.items():
                # Collect performance metrics
                metrics = await monitor_container_performance(site_name)

                # Get container status
                status_result = await self.get_container_status(site_name)

                monitoring_data[site_name] = {
                    "container": asdict(container),
                    "metrics": self._metrics_to_dict(metrics) if metrics else None,
                    "status": status_result,
                    "monitoring_active": True
                }

            # Get global monitoring summary
            global_summary = container_monitor_hooks.get_monitoring_summary()

            return {
                "success": True,
                "containers": monitoring_data,
                "global_monitoring": global_summary,
                "total_containers": len(self.site_containers)
            }

        except Exception as e:
            logger.error(f"Error getting monitoring status: {e}")
            return {
                "success": False,
                "error": str(e)
            }

    def _metrics_to_dict(self, metrics) -> Dict[str, Any]:
        """Convert ContainerMetrics object to dictionary"""
        if hasattr(metrics, '__dict__'):
            return {
                "container_name": getattr(metrics, 'container_name', ''),
                "timestamp": getattr(metrics, 'timestamp', datetime.now()).isoformat(),
                "cpu_usage_percent": getattr(metrics, 'cpu_usage_percent', 0.0),
                "memory_usage_mb": getattr(metrics, 'memory_usage_mb', 0.0),
                "memory_limit_mb": getattr(metrics, 'memory_limit_mb', 0.0),
                "network_rx_bytes": getattr(metrics, 'network_rx_bytes', 0),
                "network_tx_bytes": getattr(metrics, 'network_tx_bytes', 0),
                "disk_usage_mb": getattr(metrics, 'disk_usage_mb', 0.0),
                "uptime_seconds": getattr(metrics, 'uptime_seconds', 0),
                "restart_count": getattr(metrics, 'restart_count', 0),
                "health_status": getattr(metrics, 'health_status', 'unknown'),
                "response_time_ms": getattr(metrics, 'response_time_ms', 0.0)
            }
        return {}

    async def _create_site_dockerfile(self, site_name: str, site_path: Path) -> Path:
        """Create a Dockerfile for a site"""
        dockerfile_path = self.containers_dir / f"Dockerfile.{site_name}"

        # Determine if it's a static site or needs a server
        has_server_files = any(site_path.glob("*.py")) or any(
            site_path.glob("package.json")
        )

        if has_server_files:
            # Dynamic site with server
            dockerfile_content = f"""
FROM python:3.11-slim@sha256:f2ee145f3bc4e061f8dfe7e6ebd427a410121495a0bd26e7622136db060b7e8e

# Create non-root user early
RUN groupadd --gid 1000 appuser && \\
    useradd --uid 1000 --gid appuser --shell /bin/bash --create-home appuser

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \\
    curl \\
    && rm -rf /var/lib/apt/lists/*

# Copy site files with proper ownership
COPY --chown=appuser:appuser {site_name}/ .

# Install Python dependencies if requirements.txt exists
RUN if [ -f requirements.txt ]; then pip install -r requirements.txt; fi

# Install Node.js dependencies if package.json exists
RUN if [ -f package.json ]; then npm install; fi

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:80/ || exit 1

# Start command
CMD ["python", "-m", "http.server", "80", "--bind", "0.0.0.0"]
"""
        else:
            # Static site
            dockerfile_content = f"""
FROM nginx:1.25-alpine@sha256:2d194184b067db3598771b4cf326cfe6ad5051937ba2cc83222d94d4cd9c81bd

# Install curl for health checks
RUN apk add --no-cache curl

# Create non-root user
RUN addgroup --system --gid 1000 appuser && \\
    adduser --system --uid 1000 --ingroup appuser appuser

# Copy site files with proper ownership
COPY --chown=appuser:appuser {site_name}/ /usr/share/nginx/html/

# Copy custom nginx config if exists
COPY {site_name}/nginx.conf /etc/nginx/conf.d/default.conf 2>/dev/null || true

# Configure nginx for non-root
RUN chown -R appuser:appuser /var/cache/nginx /var/log/nginx /etc/nginx/conf.d \\
    && touch /var/run/nginx.pid \\
    && chown appuser:appuser /var/run/nginx.pid

# Switch to non-root user
USER appuser

# Expose port
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \\
    CMD curl -f http://localhost:80/ || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
"""

        with open(dockerfile_path, "w") as f:
            f.write(dockerfile_content)

        return dockerfile_path

    async def _create_site_dockerignore(self, site_path: Path, characteristics: Dict[str, Any]) -> Path:
        """Generate optimized .dockerignore for the site"""
        dockerignore_path = site_path / ".dockerignore"

        ignore_patterns = [
            "# AI Coding Agent - Auto-generated .dockerignore",
            "node_modules/", "__pycache__/", ".git/", ".vscode/",
            "*.log", "*.tmp", ".env*", "!.env.example",
            "tests/", "__tests__/", "*.test.*", "coverage/",
            "docs/", "README.md", "*.md"
        ]

        # Add framework-specific ignores
        framework = characteristics.get("framework", "static")
        if framework == "nextjs":
            ignore_patterns.extend([".next/", "out/", "coverage/", ".vercel/"])
        elif framework == "react":
            ignore_patterns.extend(["build/", "dist/", "coverage/", ".cache/"])
        elif framework in ["python", "flask", "django", "fastapi"]:
            ignore_patterns.extend(["__pycache__/", "*.pyc", ".pytest_cache/", "venv/", ".venv/"])
        elif framework == "vue":
            ignore_patterns.extend(["dist/", "node_modules/", ".cache/"])

        # Add development files from characteristics
        dev_files = characteristics.get("dev_files", [])
        ignore_patterns.extend(dev_files)

        # Remove duplicates while preserving order
        seen = set()
        unique_patterns = []
        for pattern in ignore_patterns:
            if pattern not in seen:
                seen.add(pattern)
                unique_patterns.append(pattern)

        with open(dockerignore_path, "w") as f:
            f.write("\n".join(unique_patterns))

        logger.info(f"Generated .dockerignore for {site_path.name} with {len(unique_patterns)} patterns")
        return dockerignore_path

    async def _create_site_env_file(self, site_name: str, port: int) -> Path:
        """Create site-specific environment file"""
        env_file_path = Path(f".env.{site_name}")

        env_content = f"""# Site-specific environment for {site_name}
SITE_NAME={site_name}
CONTAINER_PORT=80
EXTERNAL_PORT={port}
ENVIRONMENT=production
BUILD_TIMESTAMP={datetime.now().isoformat()}
LOG_LEVEL=info
HEALTH_CHECK_INTERVAL=30s
"""

        with open(env_file_path, "w") as f:
            f.write(env_content)

        logger.info(f"Created environment file for {site_name}: {env_file_path}")
        return env_file_path

    async def _create_site_compose(
        self, site_name: str, port: int, image_name: str
    ) -> Path:
        """Create docker-compose file for a site"""
        compose_path = self.containers_dir / f"docker-compose.{site_name}.yml"

        compose_config = {
            "version": "3.8",
            "services": {
                site_name: {
                    "build": {
                        "context": str(self.sites_dir),
                        "dockerfile": f"../containers/Dockerfile.{site_name}",
                    },
                    "container_name": f"site-{site_name}",
                    "restart": "unless-stopped",
                    "ports": [f"{port}:80"],
                    "volumes": [
                        f"./{site_name}:/app/{site_name}:ro",
                        f"./logs:/app/logs",
                    ],
                    "networks": ["ai-coding-network"],
                    "env_file": [".env", f".env.{site_name}"],
                    "deploy": {
                        "resources": {
                            "limits": {
                                "cpus": "1.0",
                                "memory": "512M"
                            },
                            "reservations": {
                                "cpus": "0.25",
                                "memory": "128M"
                            }
                        }
                    },
                    "healthcheck": {
                        "test": ["CMD", "curl", "-f", "http://localhost:80/"],
                        "interval": "30s",
                        "timeout": "10s",
                        "retries": 3,
                        "start_period": "40s",
                    },
                    "logging": {
                        "driver": "json-file",
                        "options": {
                            "max-size": "10m",
                            "max-file": "3"
                        }
                    },
                }
            },
            "networks": {"ai-coding-network": {"external": True}},
        }

        with open(compose_path, "w") as f:
            yaml.dump(compose_config, f, default_flow_style=False)

        return compose_path

    async def _build_site_image(
        self, site_name: str, image_name: str, dockerfile_path: Path
    ) -> Dict[str, Any]:
        """Build Docker image for a site using the build script"""
        try:
            logger.info(f"Building image for site: {site_name}")

            # Determine the build script to use based on platform
            import platform

            if platform.system() == "Windows":
                build_script = "containers/build.bat"
            else:
                build_script = "containers/build.sh"

            # Make the script executable on Unix systems
            if platform.system() != "Windows":
                import os

                os.chmod(build_script, 0o755)

            # Build the image using the build script
            result = subprocess.run(
                [build_script, site_name, image_name, "latest"],
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                logger.info(f"Successfully built image {image_name}")
                return {"success": True}
            else:
                logger.error(f"Failed to build image: {result.stderr}")
                return {"success": False, "error": result.stderr}

        except Exception as e:
            logger.error(f"Error building image for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def start_site_container(self, site_name: str) -> Dict[str, Any]:
        """Start a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]
            compose_path = self.containers_dir / f"docker-compose.{site_name}.yml"

            if not compose_path.exists():
                return {
                    "success": False,
                    "error": f"Docker compose file not found for {site_name}",
                }

            # Start the container
            result = subprocess.run(
                ["docker-compose", "-f", str(compose_path), "up", "-d"],
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                container.status = ContainerStatus.RUNNING
                container.last_started = datetime.now()
                self._save_container_states()

                # Emit container start event
                await monitor_container_lifecycle(site_name, "started", {
                    "container_name": container.container_name,
                    "port": container.port,
                    "url": f"http://localhost:{container.port}"
                })

                logger.info(
                    f"Started container for site {site_name} on port {container.port}"
                )

                return {
                    "success": True,
                    "container": asdict(container),
                    "url": f"http://localhost:{container.port}",
                }
            else:
                container.status = ContainerStatus.ERROR
                self._save_container_states()

                # Emit container failure event
                await monitor_container_lifecycle(site_name, "start_failed", {
                    "error": result.stderr,
                    "container_name": container.container_name
                })

                return {"success": False, "error": result.stderr}

        except Exception as e:
            logger.error(f"Failed to start container for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def stop_site_container(self, site_name: str) -> Dict[str, Any]:
        """Stop a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]
            compose_path = self.containers_dir / f"docker-compose.{site_name}.yml"

            if not compose_path.exists():
                return {
                    "success": False,
                    "error": f"Docker compose file not found for {site_name}",
                }

            # Stop the container
            result = subprocess.run(
                ["docker-compose", "-f", str(compose_path), "down"],
                capture_output=True,
                text=True,
            )

            if result.returncode == 0:
                container.status = ContainerStatus.STOPPED
                self._save_container_states()

                # Emit container stop event
                await monitor_container_lifecycle(site_name, "stopped", {
                    "container_name": container.container_name,
                    "port": container.port
                })

                logger.info(f"Stopped container for site {site_name}")

                return {"success": True, "container": asdict(container)}
            else:
                # Emit container stop failure event
                await monitor_container_lifecycle(site_name, "stop_failed", {
                    "error": result.stderr,
                    "container_name": container.container_name
                })

                return {"success": False, "error": result.stderr}

        except Exception as e:
            logger.error(f"Failed to stop container for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def delete_site_container(self, site_name: str) -> Dict[str, Any]:
        """Delete a site container and its resources"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]
            compose_path = self.containers_dir / f"docker-compose.{site_name}.yml"

            # Stop and remove container
            if compose_path.exists():
                subprocess.run(
                    [
                        "docker-compose",
                        "-f",
                        str(compose_path),
                        "down",
                        "--rmi",
                        "all",
                        "--volumes",
                    ],
                    capture_output=True,
                )

            # Remove Docker image
            if self.docker_client:
                try:
                    self.docker_client.images.remove(container.image_name, force=True)
                except:
                    pass  # Image might not exist
            else:
                logger.warning("Docker client not available, skipping image removal")

            # Remove files
            dockerfile_path = self.containers_dir / f"Dockerfile.{site_name}"
            if dockerfile_path.exists():
                dockerfile_path.unlink()

            if compose_path.exists():
                compose_path.unlink()

            # Release port
            self.port_manager.release_port(site_name)

            # Remove container record
            del self.site_containers[site_name]
            self._save_container_states()

            logger.info(f"Deleted container for site {site_name}")

            return {
                "success": True,
                "message": f"Container for site {site_name} deleted successfully",
            }

        except Exception as e:
            logger.error(f"Failed to delete container for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def get_container_status(self, site_name: str) -> Dict[str, Any]:
        """Get the status of a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]

            # Get Docker container status
            if self.docker_client:
                try:
                    docker_container = self.docker_client.containers.get(
                        container.container_name
                    )
                    container.status = (
                        ContainerStatus.RUNNING
                        if docker_container.status == "running"
                        else ContainerStatus.STOPPED
                    )
                    container.health_status = (
                        docker_container.attrs.get("State", {})
                        .get("Health", {})
                        .get("Status", "unknown")
                    )

                    # Get resource usage
                    stats = docker_container.stats(stream=False)
                    container.resource_usage = {
                        "cpu_percent": stats.get("cpu_stats", {})
                        .get("cpu_usage", {})
                        .get("total_usage", 0),
                        "memory_usage": stats.get("memory_stats", {}).get("usage", 0),
                        "memory_limit": stats.get("memory_stats", {}).get("limit", 0),
                    }

                except docker_errors.NotFound:
                    container.status = ContainerStatus.STOPPED
                    container.health_status = "not_found"
                    container.resource_usage = {}
            else:
                # Docker client not available, use stored status
                logger.warning(
                    "Docker client not available, using stored container status"
                )
                container.health_status = "unknown"
                container.resource_usage = {}

            self._save_container_states()

            return {"success": True, "container": asdict(container)}

        except Exception as e:
            logger.error(f"Failed to get status for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def list_containers(self) -> Dict[str, Any]:
        """List all site containers"""
        try:
            containers = []
            for site_name, container in self.site_containers.items():
                # Update status
                status_result = await self.get_container_status(site_name)
                if status_result["success"]:
                    containers.append(status_result["container"])

            return {"success": True, "containers": containers, "total": len(containers)}

        except Exception as e:
            logger.error(f"Failed to list containers: {e}")
            return {"success": False, "error": str(e)}

    async def rebuild_site_container(self, site_name: str) -> Dict[str, Any]:
        """Rebuild a site container"""
        try:
            # Stop and delete existing container
            await self.stop_site_container(site_name)
            await self.delete_site_container(site_name)

            # Recreate container
            site_config = {"name": site_name}  # Basic config, can be enhanced
            create_result = await self.create_site_container(site_name, site_config)

            if create_result["success"]:
                # Start the new container
                start_result = await self.start_site_container(site_name)
                return start_result
            else:
                return create_result

        except Exception as e:
            logger.error(f"Failed to rebuild container for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def get_container_logs(
        self, site_name: str, lines: int = 100
    ) -> Dict[str, Any]:
        """Get logs from a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]

            if self.docker_client:
                try:
                    docker_container = self.docker_client.containers.get(
                        container.container_name
                    )
                    logs = docker_container.logs(tail=lines, timestamps=True).decode(
                        "utf-8"
                    )

                    return {
                        "success": True,
                        "logs": logs.split("\n"),
                        "container_name": container.container_name,
                    }

                except docker_errors.NotFound:
                    return {"success": False, "error": "Container not found in Docker"}
            else:
                logger.warning(
                    "Docker client not available, cannot retrieve container logs"
                )
                return {"success": False, "error": "Docker client not available"}

        except Exception as e:
            logger.error(f"Failed to get logs for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    # Enhanced methods
    async def backup_site_container(self, site_name: str) -> Dict[str, Any]:
        """Backup a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]
            backup_dir = Path("backups") / site_name
            backup_dir.mkdir(parents=True, exist_ok=True)

            # Create backup timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_path = backup_dir / f"backup_{timestamp}.tar"

            # Create Docker container backup
            if self.docker_client:
                try:
                    docker_container = self.docker_client.containers.get(
                        container.container_name
                    )
                    backup_stream = docker_container.export()

                    with open(backup_path, "wb") as f:
                        for chunk in backup_stream:
                            if isinstance(chunk, str):
                                f.write(chunk.encode('utf-8'))
                            else:
                                f.write(chunk)

                    # Update container backup info
                    container.last_backup = datetime.now()
                    self._save_container_states()

                    return {
                        "success": True,
                        "message": f"Backup created for {site_name}",
                        "backup_path": str(backup_path),
                        "backup_size": backup_path.stat().st_size,
                    }

                except docker_errors.NotFound:
                    return {"success": False, "error": "Container not found in Docker"}
            else:
                logger.warning(
                    "Docker client not available, cannot create container backup"
                )
                return {"success": False, "error": "Docker client not available"}

        except Exception as e:
            logger.error(f"Failed to backup site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def enable_hot_reload(self, site_name: str) -> Dict[str, Any]:
        """Enable hot reload for a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]
            container.hot_reload_enabled = True
            self._save_container_states()

            # Update docker-compose with volume mounts for hot reload
            compose_path = self.containers_dir / f"docker-compose.{site_name}.yml"
            if compose_path.exists():
                with open(compose_path, "r") as f:
                    compose_data = yaml.safe_load(f)

                # Add volume mounts for hot reload
                if "services" in compose_data and site_name in compose_data["services"]:
                    service = compose_data["services"][site_name]
                    service["volumes"] = [
                        f"./sites/{site_name}:/app",
                        "/app/node_modules",  # Preserve node_modules
                    ]

                with open(compose_path, "w") as f:
                    yaml.dump(compose_data, f)

            return {"success": True, "message": f"Hot reload enabled for {site_name}"}

        except Exception as e:
            logger.error(f"Failed to enable hot reload for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def configure_ssl(
        self, site_name: str, ssl_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Configure SSL for a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]
            container.ssl_enabled = True
            container.ssl_cert_path = ssl_config.get("cert_path")
            self._save_container_states()

            # Add Nginx SSL configuration
            nginx_result = await self.nginx_manager.add_site_config(
                site_name, container.port, ssl_enabled=True
            )

            if nginx_result["success"]:
                return {
                    "success": True,
                    "message": f"SSL configured for {site_name}",
                    "nginx_config": nginx_result,
                }
            else:
                return nginx_result

        except Exception as e:
            logger.error(f"Failed to configure SSL for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def execute_command_in_container(
        self, site_name: str, command: List[str], working_dir: str = "/app"
    ) -> Dict[str, Any]:
        """Execute a command inside a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            container = self.site_containers[site_name]
            container_name = f"site-{site_name}"

            # Check if container is running
            if container.status != ContainerStatus.RUNNING:
                return {
                    "success": False,
                    "error": f"Container {container_name} is not running. Status: {container.status}",
                }

            # Execute command using docker exec
            docker_command = [
                "docker", "exec",
                "-w", working_dir,  # Set working directory
                container_name
            ] + command

            logger.info(f"Executing command in {container_name}: {' '.join(command)}")

            result = subprocess.run(
                docker_command,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )

            return {
                "success": result.returncode == 0,
                "return_code": result.returncode,
                "stdout": result.stdout,
                "stderr": result.stderr,
                "command": command,
                "container_name": container_name
            }

        except subprocess.TimeoutExpired:
            logger.error(f"Command timed out in container {site_name}")
            return {
                "success": False,
                "error": "Command execution timed out (5 minutes)",
                "command": command
            }
        except Exception as e:
            logger.error(f"Failed to execute command in container {site_name}: {e}")
            return {"success": False, "error": str(e), "command": command}

    async def apply_database_migrations(
        self, site_name: str, migration_config: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Apply database migrations within a site container"""
        try:
            if site_name not in self.site_containers:
                return {
                    "success": False,
                    "error": f"Container for site {site_name} does not exist",
                }

            # Default migration configuration
            if migration_config is None:
                migration_config = {
                    "database_url": f"sqlite:///app/data/{site_name}.db",
                    "migrations_dir": f"/app/migrations",
                    "python_path": "/usr/local/bin/python"
                }

            container = self.site_containers[site_name]
            logger.info(f"Applying database migrations for site: {site_name}")

            # Step 1: Check if migration runner is available in container
            check_result = await self.execute_command_in_container(
                site_name,
                [migration_config.get("python_path", "python"), "-c", "from agent.core.db.migration_runner import MigrationRunner; print('Migration runner available')"],
                "/app"
            )

            if not check_result["success"]:
                logger.warning(f"Migration runner not available in container {site_name}, installing...")
                # Try to install the migration runner dependencies
                install_result = await self.execute_command_in_container(
                    site_name,
                    ["pip", "install", "alembic", "sqlalchemy"],
                    "/app"
                )
                if not install_result["success"]:
                    return {
                        "success": False,
                        "error": f"Failed to install migration dependencies: {install_result.get('stderr', 'Unknown error')}",
                        "details": install_result
                    }

            # Step 2: Create migrations directory if it doesn't exist
            mkdir_result = await self.execute_command_in_container(
                site_name,
                ["mkdir", "-p", migration_config["migrations_dir"]],
                "/app"
            )

            # Step 3: Initialize migrations if needed
            init_command = [
                migration_config.get("python_path", "python"), "-c",
                f"""
import sys
sys.path.insert(0, '/app')
from agent.core.db.migration_runner import MigrationRunner

runner = MigrationRunner(
    project_name='{site_name}',
    database_url='{migration_config["database_url"]}',
    projects_root='/app/projects'
)

result = runner.init_migrations()
print(f'Init result: {{result.success}} - {{result.message}}')
if not result.success and result.error_analysis:
    print(f'Error: {{result.error_analysis.error_message}}')
"""
            ]

            init_result = await self.execute_command_in_container(
                site_name, init_command, "/app"
            )

            if not init_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to initialize migrations: {init_result.get('stderr', 'Unknown error')}",
                    "details": init_result
                }

            # Step 4: Apply migrations
            apply_command = [
                migration_config.get("python_path", "python"), "-c",
                f"""
import sys
sys.path.insert(0, '/app')
from agent.core.db.migration_runner import MigrationRunner

runner = MigrationRunner(
    project_name='{site_name}',
    database_url='{migration_config["database_url"]}',
    projects_root='/app/projects'
)

result = runner.apply_migrations()
print(f'Apply result: {{result.success}} - {{result.message}}')
if result.migrations_applied:
    print(f'Applied migrations: {{", ".join(result.migrations_applied)}}')
if not result.success and result.error_analysis:
    print(f'Error: {{result.error_analysis.error_message}}')
"""
            ]

            apply_result = await self.execute_command_in_container(
                site_name, apply_command, "/app"
            )

            # Update container metadata
            container.last_migration_applied = datetime.now()
            self._save_container_states()

            return {
                "success": apply_result["success"],
                "message": f"Database migrations applied for {site_name}",
                "init_output": init_result.get("stdout", ""),
                "apply_output": apply_result.get("stdout", ""),
                "migration_config": migration_config,
                "container_name": container.container_name
            }

        except Exception as e:
            logger.error(f"Failed to apply migrations for site {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def setup_supabase_project(
        self, site_name: str, project_type: str = "blog", **kwargs
    ) -> Dict[str, Any]:
        """Automatically set up a complete Supabase project for the user"""
        try:
            logger.info(f"Setting up Supabase project for {site_name} (type: {project_type})")

            # Step 1: Create Supabase project via API
            from agent.core.supabase_manager import SupabaseManager
            supabase_manager = SupabaseManager()

            project_result = await supabase_manager.create_project(
                name=f"{site_name}-{project_type}",
                region="us-west-1"
            )

            if not project_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to create Supabase project: {project_result['error']}"
                }

            project_id = project_result["project_id"]
            database_url = project_result["database_url"]
            api_url = project_result["api_url"]
            anon_key = project_result["anon_key"]

            # Step 2: Set up database schema based on project type
            schema_result = await supabase_manager.setup_schema(
                project_id=project_id,
                schema_type=project_type
            )

            # Step 3: Configure authentication
            auth_result = await supabase_manager.configure_auth(
                project_id=project_id,
                enable_email=True,
                enable_social=True
            )

            # Step 4: Create site container with Supabase configuration
            container_config = {
                "database_url": database_url,
                "supabase_url": api_url,
                "supabase_anon_key": anon_key,
                "project_type": project_type
            }

            container_result = await self.create_site_container(
                site_name=site_name,
                site_config=container_config
            )

            # Step 5: Apply initial migrations
            migration_config = {
                "database_url": database_url,
                "migrations_dir": "/app/migrations",
                "python_path": "python"
            }

            migration_result = await self.apply_database_migrations(
                site_name=site_name,
                migration_config=migration_config
            )

            return {
                "success": True,
                "message": f"Supabase {project_type} project created successfully",
                "project_id": project_id,
                "database_url": database_url,
                "api_url": api_url,
                "site_url": f"http://localhost:{container_result.get('port', 8080)}",
                "container_name": f"site-{site_name}",
                "features_enabled": {
                    "authentication": auth_result["success"],
                    "database": schema_result["success"],
                    "migrations": migration_result["success"]
                }
            }

        except Exception as e:
            logger.error(f"Failed to setup Supabase project for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    async def handle_error_with_user_escalation(
        self, site_name: str, error_details: Dict[str, Any], attempted_fixes: Optional[List[Dict[str, Any]]] = None
    ) -> Dict[str, Any]:
        """Handle errors by trying automatic fixes first, then escalating to user if needed"""
        try:
            logger.info(f"Architect handling error for site {site_name}: {error_details.get('title', 'Unknown error')}")

            attempted_fixes = attempted_fixes or []

            # Step 1: Architect tries automatic fixes first
            if len(attempted_fixes) < 3:  # Limit to 3 automatic attempts
                auto_fix_result = await self._architect_coordinate_fix(site_name, error_details, attempted_fixes)

                if auto_fix_result["success"]:
                    return {
                        "success": True,
                        "resolution": "automatic",
                        "fix_applied": auto_fix_result["fix_description"],
                        "attempts": len(attempted_fixes) + 1,
                        "architect_message": auto_fix_result.get("architect_message", "I've resolved the issue successfully!")
                    }
                else:
                    # Add this attempt to the list and try again
                    attempted_fixes.append(auto_fix_result)

                    # If we still have attempts left, try again
                    if len(attempted_fixes) < 3:
                        return await self.handle_error_with_user_escalation(site_name, error_details, attempted_fixes)

            # Special case: For critical/high severity errors, escalate after first failure
            if error_details.get("severity") in ["critical", "high"] and len(attempted_fixes) >= 1:
                logger.info(f"Architect escalating {error_details.get('severity')} error after {len(attempted_fixes)} attempts")
                return await self._architect_escalate_to_user(site_name, error_details, attempted_fixes)

            # Step 2: All automatic attempts failed, architect escalates to user
            return await self._architect_escalate_to_user(site_name, error_details, attempted_fixes)

        except Exception as e:
            logger.error(f"Error in architect error handling for {site_name}: {e}")
            return {"success": False, "error": str(e)}

    def _map_error_to_task_category(self, error_category: str) -> TaskCategory:
        """Map error category to appropriate task category"""
        mapping = {
            "frontend": TaskCategory.FRONTEND_DEV,
            "backend": TaskCategory.BACKEND_DEV,
            "database": TaskCategory.DATABASE_OPS,
            "security": TaskCategory.SECURITY_REVIEW,
            "performance": TaskCategory.BACKEND_DEV,
            "infrastructure": TaskCategory.BACKEND_DEV
        }
        return mapping.get(error_category.lower(), TaskCategory.ERROR_ANALYSIS)

    async def _architect_coordinate_fix(
        self, site_name: str, error_details: Dict[str, Any], attempted_fixes: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Architect coordinates the fix by delegating to appropriate specialist"""
        try:
            logger.info(f"Architect coordinating fix for {site_name}: {error_details.get('title', 'Unknown')}")

            # Step 1: Architect analyzes the error and determines specialist needed
            error_category = error_details.get("category", "unknown")
            task_category = self._map_error_to_task_category(error_category)
            specialist_agent = self.persona_manager.get_agent_for_task(task_category, error_details)

            # Step 2: Architect creates clear, specific prompt for specialist
            specialist_prompt = await self._create_specialist_prompt(specialist_agent, error_details, site_name, attempted_fixes)

            # Step 3: Delegate to specialist agent
            specialist_result = await self._delegate_to_specialist(specialist_agent, specialist_prompt, error_details)

            # Step 4: Architect verifies the specialist's output
            verification_result = await self._architect_verify_output(specialist_result, error_details, site_name)

            if verification_result["verified"]:
                # Step 5: Architect applies the verified fix
                application_result = await self._architect_apply_fix(site_name, specialist_result, error_details)

                if application_result["success"]:
                    return {
                        "success": True,
                        "fix_type": f"architect_coordinated_{specialist_agent.value}",
                        "fix_description": application_result["fix_description"],
                        "architect_message": self._generate_architect_success_message(specialist_agent, application_result),
                        "specialist_used": specialist_agent.value,
                        "verification_passed": True
                    }
                else:
                    return {
                        "success": False,
                        "fix_type": f"architect_coordination_failed",
                        "error": f"Failed to apply verified fix: {application_result.get('error', 'Unknown error')}"
                    }
            else:
                return {
                    "success": False,
                    "fix_type": f"architect_verification_failed",
                    "error": f"Specialist output failed verification: {verification_result.get('reason', 'Unknown reason')}"
                }

        except Exception as e:
            return {
                "success": False,
                "fix_type": "architect_coordination_error",
                "error": f"Error in architect coordination: {str(e)}"
            }

    async def _create_specialist_prompt(
        self, specialist_agent: AgentType, error_details: Dict[str, Any], site_name: str, attempted_fixes: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Create clear, specific, context-rich prompt for specialist agent"""

        specialist_persona = self.persona_manager.personas.get(specialist_agent)
        if not specialist_persona:
            raise ValueError(f"No persona found for agent {specialist_agent}")

        # Build comprehensive context
        context = {
            "site_name": site_name,
            "error_details": error_details,
            "attempted_fixes": attempted_fixes,
            "specialist_expertise": specialist_persona.expertise_areas,
            "available_tools": await self._get_available_tools_for_agent(specialist_agent),
            "site_context": await self._get_site_context(site_name)
        }

        # Create specific, unambiguous prompt
        prompt = {
            "task_type": "error_resolution",
            "specialist_role": specialist_agent.value,
            "clear_objective": self._generate_clear_objective(specialist_agent, error_details),
            "specific_instructions": self._generate_specific_instructions(specialist_agent, error_details, attempted_fixes),
            "context": context,
            "success_criteria": self._define_success_criteria(specialist_agent, error_details),
            "constraints": self._define_constraints(specialist_agent, error_details),
            "expected_output_format": self._define_expected_output_format(specialist_agent),
            "validation_requirements": self._define_validation_requirements(specialist_agent, error_details)
        }

        return prompt

    def _generate_clear_objective(self, specialist_agent: AgentType, error_details: Dict[str, Any]) -> str:
        """Generate clear, unambiguous objective for specialist"""

        error_title = error_details.get("title", "Unknown error")
        error_description = error_details.get("description", "No description")

        objectives = {
            AgentType.FRONTEND: f"Resolve the frontend issue: '{error_title}'. Specifically: {error_description}. Ensure the user interface works perfectly and provides excellent user experience.",

            AgentType.BACKEND: f"Fix the backend problem: '{error_title}'. Specifically: {error_description}. Ensure server performance, reliability, and scalability are optimized.",

            AgentType.DATABASE: f"Resolve the database issue: '{error_title}'. Specifically: {error_description}. Ensure data operations are fast, reliable, and properly optimized.",

            AgentType.SECURITY: f"Address the security concern: '{error_title}'. Specifically: {error_description}. Ensure complete protection of user data and system security.",

            AgentType.DEVOPS: f"Fix the infrastructure issue: '{error_title}'. Specifically: {error_description}. Ensure deployment, scaling, and system reliability."
        }

        return objectives.get(specialist_agent, f"Resolve the issue: '{error_title}'. Specifically: {error_description}")

    def _generate_specific_instructions(
        self, specialist_agent: AgentType, error_details: Dict[str, Any], attempted_fixes: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate specific, actionable instructions for specialist"""

        base_instructions = [
            "1. Analyze the error details thoroughly and identify the root cause",
            "2. Review what has already been attempted to avoid repeating failed approaches",
            "3. Design a comprehensive solution that addresses the root cause",
            "4. Implement the solution using your specialized expertise and available tools",
            "5. Test the solution to ensure it works correctly",
            "6. Provide detailed documentation of what was changed and why"
        ]

        # Add specialist-specific instructions
        specialist_instructions = {
            AgentType.FRONTEND: [
                "7. Ensure cross-browser compatibility and responsive design",
                "8. Verify accessibility standards are maintained",
                "9. Test user interactions and interface responsiveness",
                "10. Optimize for performance and loading speed"
            ],

            AgentType.BACKEND: [
                "7. Verify API endpoints are functioning correctly",
                "8. Check server resource usage and optimize if needed",
                "9. Ensure proper error handling and logging",
                "10. Test scalability and performance under load"
            ],

            AgentType.DATABASE: [
                "7. Analyze query performance and optimize indexes",
                "8. Verify data integrity and consistency",
                "9. Check backup and recovery procedures",
                "10. Ensure proper connection pooling and resource management"
            ],

            AgentType.SECURITY: [
                "7. Conduct security vulnerability assessment",
                "8. Verify authentication and authorization mechanisms",
                "9. Check for data encryption and secure transmission",
                "10. Ensure compliance with security standards"
            ]
        }

        specific_instructions = specialist_instructions.get(specialist_agent, [])
        return base_instructions + specific_instructions

    async def _get_available_tools_for_agent(self, specialist_agent: AgentType) -> List[str]:
        """Get list of available tools for the specialist agent"""

        base_tools = [
            "file_system_access",
            "container_management",
            "log_analysis",
            "configuration_management",
            "backup_and_restore"
        ]

        specialist_tools = {
            AgentType.FRONTEND: [
                "browser_testing",
                "css_analysis",
                "javascript_debugging",
                "responsive_design_testing",
                "accessibility_checker",
                "performance_profiler"
            ],

            AgentType.BACKEND: [
                "api_testing",
                "database_connection",
                "server_monitoring",
                "load_testing",
                "memory_profiler",
                "process_manager"
            ],

            AgentType.DATABASE: [
                "query_analyzer",
                "index_optimizer",
                "backup_manager",
                "replication_monitor",
                "connection_pool_manager",
                "data_integrity_checker"
            ],

            AgentType.SECURITY: [
                "vulnerability_scanner",
                "ssl_certificate_manager",
                "access_control_auditor",
                "encryption_tools",
                "security_log_analyzer",
                "compliance_checker"
            ],

            AgentType.DEVOPS: [
                "deployment_manager",
                "scaling_controller",
                "health_checker",
                "resource_monitor",
                "network_analyzer",
                "service_orchestrator"
            ]
        }

        agent_tools = specialist_tools.get(specialist_agent, [])
        return base_tools + agent_tools

    async def _get_site_context(self, site_name: str) -> Dict[str, Any]:
        """Get comprehensive context about the site"""
        try:
            context = {
                "site_name": site_name,
                "container_status": "unknown",
                "port": "unknown",
                "technology_stack": [],
                "recent_changes": [],
                "performance_metrics": {},
                "error_history": []
            }

            # Get container information
            try:
                docker_client = docker.from_env()
                containers = docker_client.containers.list(all=True)
                site_container = None
                for container in containers:
                    if site_name in container.name:
                        site_container = container
                        break

                if site_container:
                    context["container_status"] = site_container.status
                    context["container_id"] = site_container.id[:12]

                    # Get port information
                    if site_container.ports:
                        for port_info in site_container.ports.values():
                            if port_info:
                                context["port"] = port_info[0]["HostPort"]
                                break

            except Exception as e:
                logger.warning(f"Could not get container info for {site_name}: {e}")

            # Get technology stack information
            site_path = Path("projects") / site_name
            if site_path.exists():
                # Check for common framework files
                if (site_path / "package.json").exists():
                    context["technology_stack"].append("Node.js/JavaScript")
                if (site_path / "requirements.txt").exists():
                    context["technology_stack"].append("Python")
                if (site_path / "Dockerfile").exists():
                    context["technology_stack"].append("Docker")
                if (site_path / "docker-compose.yml").exists():
                    context["technology_stack"].append("Docker Compose")

            return context

        except Exception as e:
            logger.error(f"Error getting site context for {site_name}: {e}")
            return {"site_name": site_name, "error": str(e)}

    def _define_success_criteria(self, specialist_agent: AgentType, error_details: Dict[str, Any]) -> List[str]:
        """Define clear success criteria for the specialist"""

        base_criteria = [
            "Error is completely resolved and no longer occurs",
            "Solution is tested and verified to work correctly",
            "No new errors or issues are introduced",
            "System performance is maintained or improved"
        ]

        specialist_criteria = {
            AgentType.FRONTEND: [
                "User interface displays correctly across all browsers",
                "All interactive elements function properly",
                "Page loading performance is optimal",
                "Accessibility standards are maintained"
            ],

            AgentType.BACKEND: [
                "All API endpoints respond correctly",
                "Server resource usage is within normal limits",
                "Database connections are stable",
                "Error handling and logging work properly"
            ],

            AgentType.DATABASE: [
                "All queries execute within acceptable time limits",
                "Data integrity is maintained",
                "Backup and recovery procedures work",
                "Connection pooling is optimized"
            ],

            AgentType.SECURITY: [
                "No security vulnerabilities remain",
                "Authentication and authorization work correctly",
                "Data encryption is properly implemented",
                "Security logs show no suspicious activity"
            ]
        }

        agent_criteria = specialist_criteria.get(specialist_agent, [])
        return base_criteria + agent_criteria

    def _define_constraints(self, specialist_agent: AgentType, error_details: Dict[str, Any]) -> List[str]:
        """Define constraints and limitations for the specialist"""

        base_constraints = [
            "Do not modify core system files without explicit permission",
            "Maintain backward compatibility with existing functionality",
            "Follow security best practices at all times",
            "Document all changes made for future reference",
            "Test changes in isolated environment before applying to production"
        ]

        specialist_constraints = {
            AgentType.FRONTEND: [
                "Maintain existing design system and branding",
                "Ensure mobile responsiveness is preserved",
                "Do not break existing user workflows",
                "Maintain accessibility compliance"
            ],

            AgentType.BACKEND: [
                "Do not modify database schema without approval",
                "Maintain API compatibility for existing clients",
                "Preserve existing authentication mechanisms",
                "Do not change core business logic without verification"
            ],

            AgentType.DATABASE: [
                "Do not delete or modify existing data",
                "Maintain referential integrity at all times",
                "Do not change primary keys or critical indexes",
                "Ensure backup is available before making changes"
            ],

            AgentType.SECURITY: [
                "Do not weaken existing security measures",
                "Maintain compliance with relevant regulations",
                "Do not expose sensitive information in logs",
                "Verify all security changes with multiple tests"
            ]
        }

        agent_constraints = specialist_constraints.get(specialist_agent, [])
        return base_constraints + agent_constraints

    def _define_expected_output_format(self, specialist_agent: AgentType) -> Dict[str, Any]:
        """Define the expected output format from specialist"""
        return {
            "success": "boolean - whether the fix was successful",
            "fix_description": "string - detailed description of what was fixed",
            "changes_made": "list - specific changes that were implemented",
            "testing_performed": "list - tests that were run to verify the fix",
            "performance_impact": "string - impact on system performance",
            "rollback_instructions": "string - how to undo changes if needed",
            "recommendations": "list - additional recommendations for improvement",
            "tools_used": "list - tools and resources that were utilized",
            "verification_steps": "list - steps to verify the fix is working"
        }

    def _define_validation_requirements(self, specialist_agent: AgentType, error_details: Dict[str, Any]) -> List[str]:
        """Define validation requirements for specialist output"""
        return [
            "All required output fields must be present and properly formatted",
            "Fix description must be specific and actionable",
            "Changes made must be clearly documented",
            "Testing performed must be comprehensive and relevant",
            "Rollback instructions must be complete and tested",
            "Recommendations must be practical and implementable"
        ]

    async def _delegate_to_specialist(
        self, specialist_agent: AgentType, specialist_prompt: Dict[str, Any], error_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Delegate task to specialist agent with comprehensive prompt"""
        try:
            logger.info(f"Architect delegating to {specialist_agent.value} specialist")

            # Get the appropriate model for this specialist
            model = self.persona_manager.get_model_for_agent_task(specialist_agent, "development")

            # For now, simulate specialist work (in production, this would call the actual LLM)
            # This is where you would integrate with your LLM system
            specialist_result = await self._simulate_specialist_work(specialist_agent, specialist_prompt, error_details)

            return specialist_result

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to delegate to {specialist_agent.value}: {str(e)}"
            }

    async def _simulate_specialist_work(
        self, specialist_agent: AgentType, prompt: Dict[str, Any], error_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Simulate specialist work (replace with actual LLM integration)"""

        # This is a simulation - in production, you would send the prompt to the appropriate LLM
        error_category = error_details.get("category", "unknown")

        if specialist_agent == AgentType.FRONTEND and error_category == "frontend":
            return {
                "success": True,
                "fix_description": "Fixed React component state initialization and added proper error boundaries",
                "changes_made": [
                    "Added null checks in ProductList component",
                    "Implemented proper error boundary",
                    "Updated component state initialization"
                ],
                "testing_performed": [
                    "Unit tests for component rendering",
                    "Integration tests for user interactions",
                    "Cross-browser compatibility testing"
                ],
                "performance_impact": "Minimal - improved error handling may slightly reduce rendering time",
                "rollback_instructions": "Revert to previous component version in git commit abc123",
                "recommendations": [
                    "Implement TypeScript for better type safety",
                    "Add more comprehensive error boundaries",
                    "Consider using React Query for data fetching"
                ],
                "tools_used": ["React DevTools", "Jest", "Cypress"],
                "verification_steps": [
                    "Load the page and verify component renders",
                    "Test with empty data arrays",
                    "Verify error messages display properly"
                ]
            }

        elif specialist_agent == AgentType.DATABASE and error_category == "database":
            return {
                "success": True,
                "fix_description": "Optimized product search queries and added proper indexing",
                "changes_made": [
                    "Added composite index on (category, price, created_at)",
                    "Optimized search query to use index hints",
                    "Implemented query result caching"
                ],
                "testing_performed": [
                    "Query performance analysis",
                    "Load testing with 1000 concurrent searches",
                    "Index usage verification"
                ],
                "performance_impact": "Significant improvement - query time reduced from 3s to 150ms",
                "rollback_instructions": "Drop the new index and restore previous query structure",
                "recommendations": [
                    "Consider implementing full-text search for better performance",
                    "Add query monitoring and alerting",
                    "Regular index maintenance schedule"
                ],
                "tools_used": ["Database profiler", "Query analyzer", "Performance monitor"],
                "verification_steps": [
                    "Run search queries and measure response time",
                    "Verify index is being used in query plan",
                    "Test with various search parameters"
                ]
            }

        elif specialist_agent == AgentType.SECURITY and error_category == "security":
            return {
                "success": True,
                "fix_description": "Implemented parameterized queries and input sanitization to prevent SQL injection",
                "changes_made": [
                    "Replaced string concatenation with parameterized queries",
                    "Added input validation and sanitization",
                    "Implemented prepared statements for all database operations"
                ],
                "testing_performed": [
                    "SQL injection vulnerability testing",
                    "Input validation testing with malicious payloads",
                    "Security audit of all database interactions"
                ],
                "performance_impact": "Minimal - prepared statements may slightly improve performance",
                "rollback_instructions": "Revert to previous database access layer implementation",
                "recommendations": [
                    "Implement Web Application Firewall (WAF)",
                    "Regular security audits and penetration testing",
                    "Add rate limiting to prevent abuse"
                ],
                "tools_used": ["Security scanner", "SQL injection tester", "Code analyzer"],
                "verification_steps": [
                    "Test search functionality with normal inputs",
                    "Attempt SQL injection attacks to verify protection",
                    "Review security logs for any suspicious activity"
                ]
            }

        else:
            # Generic fix for other cases
            return {
                "success": True,
                "fix_description": f"Applied {specialist_agent.value} specialist fix for {error_category} issue",
                "changes_made": ["Reset configuration to known working state"],
                "testing_performed": ["Basic functionality testing"],
                "performance_impact": "No significant impact expected",
                "rollback_instructions": "Restore from backup if needed",
                "recommendations": ["Monitor system for stability"],
                "tools_used": ["Standard diagnostic tools"],
                "verification_steps": ["Verify basic functionality works"]
            }

    async def _architect_verify_output(
        self, specialist_result: Dict[str, Any], error_details: Dict[str, Any], site_name: str
    ) -> Dict[str, Any]:
        """Architect verifies the specialist's output for accuracy and completeness"""
        verification_checks = []  # Initialize at the beginning
        try:
            logger.info(f"Architect verifying specialist output for {site_name}")

            # Check 1: Required fields present
            required_fields = ["success", "fix_description", "changes_made", "testing_performed"]
            missing_fields = [field for field in required_fields if field not in specialist_result]

            if missing_fields:
                return {
                    "verified": False,
                    "reason": f"Missing required fields: {', '.join(missing_fields)}",
                    "checks_performed": verification_checks
                }

            verification_checks.append("✅ All required fields present")

            # Check 2: Success status is boolean
            if not isinstance(specialist_result.get("success"), bool):
                return {
                    "verified": False,
                    "reason": "Success field must be boolean",
                    "checks_performed": verification_checks
                }

            verification_checks.append("✅ Success status is valid boolean")

            # Check 3: Fix description is meaningful
            fix_description = specialist_result.get("fix_description", "")
            if len(fix_description) < 20:
                return {
                    "verified": False,
                    "reason": "Fix description is too brief or missing",
                    "checks_performed": verification_checks
                }

            verification_checks.append("✅ Fix description is comprehensive")

            # Check 4: Changes are documented
            changes_made = specialist_result.get("changes_made", [])
            if not changes_made or len(changes_made) == 0:
                return {
                    "verified": False,
                    "reason": "No changes documented",
                    "checks_performed": verification_checks
                }

            verification_checks.append("✅ Changes are properly documented")

            # Check 5: Testing was performed
            testing_performed = specialist_result.get("testing_performed", [])
            if not testing_performed or len(testing_performed) == 0:
                return {
                    "verified": False,
                    "reason": "No testing documented",
                    "checks_performed": verification_checks
                }

            verification_checks.append("✅ Testing procedures documented")

            # Check 6: Rollback instructions provided
            rollback_instructions = specialist_result.get("rollback_instructions", "")
            if len(rollback_instructions) < 10:
                return {
                    "verified": False,
                    "reason": "Rollback instructions missing or insufficient",
                    "checks_performed": verification_checks
                }

            verification_checks.append("✅ Rollback instructions provided")

            # Check 7: Verification steps provided
            verification_steps = specialist_result.get("verification_steps", [])
            if not verification_steps or len(verification_steps) == 0:
                return {
                    "verified": False,
                    "reason": "No verification steps provided",
                    "checks_performed": verification_checks
                }

            verification_checks.append("✅ Verification steps documented")

            # All checks passed
            return {
                "verified": True,
                "reason": "All verification checks passed",
                "checks_performed": verification_checks,
                "quality_score": len(verification_checks) / 7 * 100  # Percentage of checks passed
            }

        except Exception as e:
            return {
                "verified": False,
                "reason": f"Error during verification: {str(e)}",
                "checks_performed": verification_checks if 'verification_checks' in locals() else []
            }

    async def _architect_apply_fix(
        self, site_name: str, specialist_result: Dict[str, Any], error_details: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Architect applies the verified fix from specialist"""
        try:
            logger.info(f"Architect applying verified fix for {site_name}")

            # For now, simulate applying the fix
            # In production, this would execute the actual changes

            fix_description = specialist_result.get("fix_description", "Unknown fix")
            changes_made = specialist_result.get("changes_made", [])

            # Simulate applying changes
            application_steps = []

            for change in changes_made:
                try:
                    # Simulate applying each change
                    application_steps.append(f"✅ Applied: {change}")
                    logger.info(f"Applied change: {change}")
                except Exception as e:
                    application_steps.append(f"❌ Failed: {change} - {str(e)}")
                    return {
                        "success": False,
                        "error": f"Failed to apply change: {change}",
                        "application_steps": application_steps
                    }

            # Run verification steps
            verification_steps = specialist_result.get("verification_steps", [])
            verification_results = []

            for step in verification_steps:
                try:
                    # Simulate running verification
                    verification_results.append(f"✅ Verified: {step}")
                    logger.info(f"Verification passed: {step}")
                except Exception as e:
                    verification_results.append(f"❌ Failed: {step} - {str(e)}")
                    # Continue with other verifications

            return {
                "success": True,
                "fix_description": fix_description,
                "application_steps": application_steps,
                "verification_results": verification_results,
                "changes_applied": len(changes_made),
                "verifications_passed": len([r for r in verification_results if "✅" in r])
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Error applying fix: {str(e)}"
            }

    def _generate_architect_success_message(self, specialist_agent: AgentType, application_result: Dict[str, Any]) -> str:
        """Generate architect's success message after coordinating specialist fix"""

        fix_description = application_result.get("fix_description", "Fixed the issue")
        specialist_name = self.persona_manager.personas[specialist_agent].name

        messages = {
            AgentType.FRONTEND: f"Excellent! I worked with our {specialist_name} to make your site beautiful and user-friendly again. {fix_description}",

            AgentType.BACKEND: f"Perfect! I coordinated with our {specialist_name} to optimize your server performance. {fix_description}",

            AgentType.DATABASE: f"Great! I collaborated with our {specialist_name} to get your data operations running smoothly. {fix_description}",

            AgentType.SECURITY: f"Wonderful! I worked with our {specialist_name} to ensure your site is completely secure. {fix_description}",

            AgentType.DEVOPS: f"Fantastic! I coordinated with our {specialist_name} to optimize your infrastructure. {fix_description}"
        }

        return messages.get(specialist_agent, f"Success! I worked with our {specialist_name} to resolve the issue. {fix_description}")

    async def _architect_escalate_to_user(
        self, site_name: str, error_details: Dict[str, Any], attempted_fixes: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Architect escalates to user with comprehensive information"""
        try:
            logger.info(f"Architect escalating {site_name} issue to user")

            error_title = error_details.get("title", "Unknown Issue")
            error_description = error_details.get("description", "An error occurred")

            # Create architect's user message
            message = f"🏗️ **Architect here!** I've been working hard to resolve an issue with your {site_name} site.\n\n"
            message += f"**The Challenge:** {error_title}\n"
            message += f"**What's Happening:** {error_description}\n\n"

            if attempted_fixes:
                message += f"**What I've Tried:**\n"
                for i, fix in enumerate(attempted_fixes, 1):
                    specialist = fix.get("specialist_used", "specialist")
                    fix_type = fix.get("fix_type", "unknown").replace("_", " ").title()
                    if fix.get("success"):
                        message += f"{i}. ✅ {fix_type} (with {specialist}): {fix.get('fix_description', 'Applied successfully')}\n"
                    else:
                        message += f"{i}. ❌ {fix_type} (with {specialist}): {fix.get('error', 'Failed')}\n"

            message += f"\n🤝 **I could really use your expertise here!** What would you like me to try next?"

            # Generate architect's suggestions
            suggestions = self._generate_architect_suggestions(error_details, attempted_fixes)

            return {
                "success": False,
                "requires_user_input": True,
                "architect_message": message,
                "suggested_actions": suggestions,
                "escalation_reason": "Automatic fixes unsuccessful, need user guidance",
                "attempted_fixes_count": len(attempted_fixes)
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to escalate to user: {str(e)}"
            }

    def _generate_architect_suggestions(
        self, error_details: Dict[str, Any], attempted_fixes: List[Dict[str, Any]]
    ) -> List[str]:
        """Generate architect's suggestions for user"""

        base_suggestions = [
            "🔍 Let me investigate the logs more thoroughly",
            "🔄 Try a different approach with a different specialist",
            "📋 Review recent changes that might have caused this",
            "🛠️ Apply a manual fix with your guidance",
            "⏪ Rollback to a previous working version",
            "🆘 Get additional expert consultation"
        ]

        # Add context-specific suggestions based on error category
        error_category = error_details.get("category", "unknown")

        if error_category == "frontend":
            base_suggestions.extend([
                "🎨 Check for recent design or code changes",
                "🌐 Test on different browsers or devices",
                "📱 Verify responsive design functionality"
            ])
        elif error_category == "database":
            base_suggestions.extend([
                "🗄️ Check database server status and connections",
                "📊 Analyze recent data changes or migrations",
                "⚡ Review query performance and optimization"
            ])
        elif error_category == "security":
            base_suggestions.extend([
                "🛡️ Review recent security policy changes",
                "🔐 Check authentication and authorization settings",
                "📋 Verify SSL certificates and encryption"
            ])

        return base_suggestions[:8]  # Limit to 8 suggestions

    def _generate_success_message(self, agent_type: AgentType, fix_result: Dict[str, Any]) -> str:
        """Generate a persona-appropriate success message"""
        conversation_style = self.persona_manager.get_conversation_style(agent_type, "success")
        fix_description = fix_result.get("fix_description", "Fixed the issue")

        if agent_type == AgentType.FRONTEND:
            return f"Perfect! I've made your site look and work beautifully again. {fix_description}"
        elif agent_type == AgentType.BACKEND:
            return f"Excellent! The backend is now running smoothly and efficiently. {fix_description}"
        elif agent_type == AgentType.SECURITY:
            return f"Great! Your site is now secure and protected. {fix_description}"
        elif agent_type == AgentType.DATABASE:
            return f"Fantastic! Your database is optimized and performing well. {fix_description}"
        else:
            return f"Wonderful! I've resolved the issue successfully. {fix_description}"

    async def _attempt_automatic_fix_with_persona(
        self, site_name: str, error_details: Dict[str, Any], previous_attempts: List[Dict[str, Any]], agent_type: AgentType
    ) -> Dict[str, Any]:
        """Attempt to automatically fix the error using persona-specific approach"""
        try:
            # Get the appropriate model for this agent and task
            model = self.persona_manager.get_model_for_agent_task(agent_type, "development")

            # Use the existing automatic fix logic but with persona context
            base_result = await self._attempt_automatic_fix(site_name, error_details, previous_attempts)

            # Enhance the result with persona-specific messaging
            if base_result.get("success"):
                base_result["agent_type"] = agent_type.value
                base_result["model_used"] = model
                base_result["fix_description"] = self._enhance_fix_description(agent_type, base_result.get("fix_description", ""))

            return base_result

        except Exception as e:
            return {
                "success": False,
                "fix_type": "persona_error",
                "error": f"Error in persona-based fix attempt: {str(e)}"
            }

    async def _escalate_to_user_with_persona(
        self, site_name: str, error_details: Dict[str, Any], attempted_fixes: List[Dict[str, Any]], agent_type: AgentType
    ) -> Dict[str, Any]:
        """Escalate the error to the user with persona-appropriate messaging"""
        try:
            # Use the existing escalation logic
            base_result = await self._escalate_to_user(site_name, error_details, attempted_fixes)

            # Enhance with persona-specific messaging
            if base_result.get("requires_user_input"):
                base_result["agent_type"] = agent_type.value
                base_result["agent_name"] = self.persona_manager.personas[agent_type].name
                base_result["user_message"] = self._create_persona_user_message(agent_type, error_details, attempted_fixes)
                base_result["suggested_actions"] = self._suggest_persona_actions(agent_type, error_details, attempted_fixes)

            return base_result

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to escalate with persona: {str(e)}"
            }

    def _enhance_fix_description(self, agent_type: AgentType, base_description: str) -> str:
        """Enhance fix description with persona-specific language"""
        if agent_type == AgentType.FRONTEND:
            return f"Made your user interface beautiful and responsive again. {base_description}"
        elif agent_type == AgentType.BACKEND:
            return f"Optimized your server performance and reliability. {base_description}"
        elif agent_type == AgentType.SECURITY:
            return f"Secured your application and protected user data. {base_description}"
        elif agent_type == AgentType.DATABASE:
            return f"Optimized your data operations for better performance. {base_description}"
        else:
            return base_description

    def _create_persona_user_message(
        self, agent_type: AgentType, error_details: Dict[str, Any], attempted_fixes: List[Dict[str, Any]]
    ) -> str:
        """Create a persona-appropriate user message"""

        persona = self.persona_manager.personas.get(agent_type)
        if not persona:
            return self._create_user_friendly_error_message(error_details, attempted_fixes)

        error_title = error_details.get("title", "Unknown Issue")
        error_description = error_details.get("description", "An error occurred")

        # Get persona-specific greeting
        greeting_pattern = persona.communication_patterns.get("problem_solving",
                                                            "Let me work through this challenge with you.")

        message = f"🤖 {persona.name}: {greeting_pattern}\n\n"
        message += f"**Issue I'm investigating:** {error_title}\n"
        message += f"**What's happening:** {error_description}\n\n"

        if attempted_fixes:
            message += f"**What I've tried so far:**\n"
            for i, fix in enumerate(attempted_fixes, 1):
                fix_type = fix.get("fix_type", "unknown").replace("_", " ").title()
                if fix.get("success"):
                    message += f"{i}. ✅ {fix_type}: {fix.get('fix_description', 'Applied successfully')}\n"
                else:
                    message += f"{i}. ❌ {fix_type}: {fix.get('error', 'Failed')}\n"

        # Add persona-specific request for help
        uncertainty_pattern = persona.communication_patterns.get("uncertainty",
                                                               "I could use your expertise here.")
        message += f"\n{uncertainty_pattern} What would you like me to try next?"

        return message

    def _suggest_persona_actions(
        self, agent_type: AgentType, error_details: Dict[str, Any], attempted_fixes: List[Dict[str, Any]]
    ) -> List[str]:
        """Suggest persona-specific actions"""

        base_suggestions = self._suggest_user_actions(error_details, attempted_fixes)

        # Add persona-specific suggestions
        if agent_type == AgentType.FRONTEND:
            persona_suggestions = [
                "Check if recent design changes caused the issue",
                "Test the interface on different devices/browsers",
                "Review user experience flow for problems"
            ]
        elif agent_type == AgentType.BACKEND:
            persona_suggestions = [
                "Check server logs for performance bottlenecks",
                "Review recent API changes or deployments",
                "Verify database connection and query performance"
            ]
        elif agent_type == AgentType.SECURITY:
            persona_suggestions = [
                "Review recent security policy changes",
                "Check for suspicious access patterns",
                "Verify SSL certificates and authentication"
            ]
        elif agent_type == AgentType.DATABASE:
            persona_suggestions = [
                "Check database server status and connections",
                "Review recent schema changes or migrations",
                "Analyze query performance and indexing"
            ]
        else:
            persona_suggestions = []

        # Combine and limit suggestions
        all_suggestions = persona_suggestions + base_suggestions
        return all_suggestions[:6]  # Limit to 6 suggestions

    async def _attempt_automatic_fix(
        self, site_name: str, error_details: Dict[str, Any], previous_attempts: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Attempt to automatically fix the error"""
        try:
            error_type = error_details.get("category", "unknown")
            error_description = error_details.get("description", "")

            # Determine what type of fix to try based on error and previous attempts
            attempted_types = [attempt.get("fix_type") for attempt in previous_attempts]

            # Try different fix strategies
            if "container_restart" not in attempted_types and "not responding" in error_description.lower():
                return await self._fix_container_restart(site_name)

            elif "resource_optimization" not in attempted_types and ("memory" in error_description.lower() or "cpu" in error_description.lower()):
                return await self._fix_resource_optimization(site_name)

            elif "dependency_update" not in attempted_types and ("module" in error_description.lower() or "import" in error_description.lower()):
                return await self._fix_dependency_update(site_name, error_description)

            elif "configuration_reset" not in attempted_types:
                return await self._fix_configuration_reset(site_name)

            else:
                return {
                    "success": False,
                    "fix_type": "exhausted",
                    "error": "All automatic fix strategies have been attempted"
                }

        except Exception as e:
            return {
                "success": False,
                "fix_type": "error",
                "error": f"Error during automatic fix attempt: {str(e)}"
            }

    async def _fix_container_restart(self, site_name: str) -> Dict[str, Any]:
        """Try fixing by restarting the container"""
        try:
            logger.info(f"Attempting container restart fix for {site_name}")

            # Stop and start the container
            stop_result = await self.stop_site_container(site_name)
            if not stop_result["success"]:
                return {
                    "success": False,
                    "fix_type": "container_restart",
                    "error": f"Failed to stop container: {stop_result['error']}"
                }

            start_result = await self.start_site_container(site_name)
            if start_result["success"]:
                return {
                    "success": True,
                    "fix_type": "container_restart",
                    "fix_description": "Restarted container to resolve connectivity issues"
                }
            else:
                return {
                    "success": False,
                    "fix_type": "container_restart",
                    "error": f"Failed to start container: {start_result['error']}"
                }

        except Exception as e:
            return {
                "success": False,
                "fix_type": "container_restart",
                "error": str(e)
            }

    async def _fix_resource_optimization(self, site_name: str) -> Dict[str, Any]:
        """Try fixing by optimizing resource usage"""
        try:
            logger.info(f"Attempting resource optimization fix for {site_name}")

            # This would implement resource optimization logic
            # For now, simulate the fix
            return {
                "success": True,
                "fix_type": "resource_optimization",
                "fix_description": "Optimized memory usage and CPU allocation"
            }

        except Exception as e:
            return {
                "success": False,
                "fix_type": "resource_optimization",
                "error": str(e)
            }

    async def _fix_dependency_update(self, site_name: str, error_description: str) -> Dict[str, Any]:
        """Try fixing by updating dependencies"""
        try:
            logger.info(f"Attempting dependency update fix for {site_name}")

            # This would implement dependency update logic
            # For now, simulate the fix
            return {
                "success": True,
                "fix_type": "dependency_update",
                "fix_description": "Updated missing dependencies and packages"
            }

        except Exception as e:
            return {
                "success": False,
                "fix_type": "dependency_update",
                "error": str(e)
            }

    async def _fix_configuration_reset(self, site_name: str) -> Dict[str, Any]:
        """Try fixing by resetting configuration to defaults"""
        try:
            logger.info(f"Attempting configuration reset fix for {site_name}")

            # This would implement configuration reset logic
            # For now, simulate the fix
            return {
                "success": True,
                "fix_type": "configuration_reset",
                "fix_description": "Reset configuration to known working state"
            }

        except Exception as e:
            return {
                "success": False,
                "fix_type": "configuration_reset",
                "error": str(e)
            }

    async def _escalate_to_user(
        self, site_name: str, error_details: Dict[str, Any], attempted_fixes: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """Escalate the error to the user for guidance"""
        try:
            logger.info(f"Escalating error to user for site {site_name}")

            # Prepare user-friendly error summary
            user_message = self._create_user_friendly_error_message(error_details, attempted_fixes)

            return {
                "success": False,
                "resolution": "user_escalation",
                "requires_user_input": True,
                "user_message": user_message,
                "error_details": error_details,
                "attempted_fixes": attempted_fixes,
                "suggested_actions": self._suggest_user_actions(error_details, attempted_fixes)
            }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to escalate to user: {str(e)}"
            }

    def _create_user_friendly_error_message(
        self, error_details: Dict[str, Any], attempted_fixes: List[Dict[str, Any]]
    ) -> str:
        """Create a user-friendly error message"""

        error_title = error_details.get("title", "Unknown Issue")
        error_description = error_details.get("description", "An error occurred")

        message = f"🚨 I need your help with an issue on your site:\n\n"
        message += f"**Problem:** {error_title}\n"
        message += f"**Details:** {error_description}\n\n"

        if attempted_fixes:
            message += f"**What I've tried:**\n"
            for i, fix in enumerate(attempted_fixes, 1):
                fix_type = fix.get("fix_type", "unknown").replace("_", " ").title()
                if fix.get("success"):
                    message += f"{i}. ✅ {fix_type}: {fix.get('fix_description', 'Applied successfully')}\n"
                else:
                    message += f"{i}. ❌ {fix_type}: {fix.get('error', 'Failed')}\n"

        message += f"\n**I've exhausted my automatic fix options. How would you like me to proceed?**"

        return message

    def _suggest_user_actions(
        self, error_details: Dict[str, Any], attempted_fixes: List[Dict[str, Any]]
    ) -> List[str]:
        """Suggest possible actions the user can take"""

        suggestions = [
            "Tell me to try a specific fix you have in mind",
            "Ask me to investigate a particular aspect of the issue",
            "Request a full system diagnostic",
            "Suggest rolling back to a previous working state",
            "Ask me to contact technical support",
            "Provide additional context about when the issue started"
        ]

        # Add specific suggestions based on error type
        error_category = error_details.get("category", "")

        if "database" in error_category.lower():
            suggestions.insert(0, "Check if database credentials need updating")
            suggestions.insert(1, "Verify database server is accessible")

        elif "frontend" in error_category.lower():
            suggestions.insert(0, "Check if recent code changes caused the issue")
            suggestions.insert(1, "Clear browser cache and try again")

        elif "performance" in error_category.lower():
            suggestions.insert(0, "Consider upgrading server resources")
            suggestions.insert(1, "Check for recent traffic spikes")

        return suggestions[:6]  # Limit to 6 suggestions

    async def apply_user_suggested_fix(
        self, site_name: str, user_suggestion: str, error_context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Apply a fix suggested by the user"""
        try:
            logger.info(f"Applying user-suggested fix for {site_name}: {user_suggestion}")

            # Parse user suggestion and attempt to implement it
            # This would be enhanced with NLP to understand user intent

            suggestion_lower = user_suggestion.lower()

            if "restart" in suggestion_lower:
                return await self._fix_container_restart(site_name)

            elif "rollback" in suggestion_lower or "previous" in suggestion_lower:
                return await self._apply_rollback(site_name)

            elif "diagnostic" in suggestion_lower or "check" in suggestion_lower:
                return await self._run_full_diagnostic(site_name)

            elif "update" in suggestion_lower or "upgrade" in suggestion_lower:
                return await self._fix_dependency_update(site_name, user_suggestion)

            else:
                # For complex suggestions, ask for clarification
                return {
                    "success": False,
                    "requires_clarification": True,
                    "message": f"I understand you want me to: '{user_suggestion}'\n\n"
                              f"Could you provide more specific steps? For example:\n"
                              f"- 'Restart the container'\n"
                              f"- 'Update the database configuration'\n"
                              f"- 'Check the application logs'\n"
                              f"- 'Rollback to previous version'"
                }

        except Exception as e:
            return {
                "success": False,
                "error": f"Failed to apply user suggestion: {str(e)}"
            }

    async def _apply_rollback(self, site_name: str) -> Dict[str, Any]:
        """Apply rollback to previous working state"""
        try:
            # This would implement rollback logic
            return {
                "success": True,
                "fix_type": "rollback",
                "fix_description": "Rolled back to previous working configuration"
            }
        except Exception as e:
            return {
                "success": False,
                "fix_type": "rollback",
                "error": str(e)
            }

    async def _run_full_diagnostic(self, site_name: str) -> Dict[str, Any]:
        """Run comprehensive diagnostic"""
        try:
            # This would implement full diagnostic logic
            return {
                "success": True,
                "fix_type": "diagnostic",
                "fix_description": "Completed full system diagnostic - detailed report available"
            }
        except Exception as e:
            return {
                "success": False,
                "fix_type": "diagnostic",
                "error": str(e)
            }

    async def _perform_health_check(self, site_name: str, container: SiteContainer):
        """Perform health check on a container"""
        try:
            logger.debug(f"Performing health check for {site_name}")
            # Check if container is responding
            async with aiohttp.ClientSession() as session:
                url = f"http://localhost:{container.port}/health"
                timeout = aiohttp.ClientTimeout(total=5)
                async with session.get(url, timeout=timeout) as response:
                    if response.status == 200:
                        container.health_status = "healthy"
                        container.status = ContainerStatus.HEALTHY
                    else:
                        container.health_status = "unhealthy"
                        container.status = ContainerStatus.UNHEALTHY
        except Exception:
            container.health_status = "unhealthy"
            container.status = ContainerStatus.UNHEALTHY

        container.last_health_check = datetime.now()
        self._save_container_states()

    def _calculate_cpu_percent(self, stats: Dict[str, Any]) -> float:
        """Calculate CPU usage percentage from Docker stats"""
        try:
            cpu_delta = (
                stats["cpu_stats"]["cpu_usage"]["total_usage"]
                - stats["precpu_stats"]["cpu_usage"]["total_usage"]
            )
            system_delta = (
                stats["cpu_stats"]["system_cpu_usage"]
                - stats["precpu_stats"]["system_cpu_usage"]
            )

            if system_delta > 0:
                return (
                    (cpu_delta / system_delta)
                    * len(stats["cpu_stats"]["cpu_usage"]["percpu_usage"])
                    * 100
                )
            return 0.0
        except (KeyError, ZeroDivisionError):
            return 0.0
