# AI Coding Agent

A comprehensive AI-powered coding assistant that helps developers write, review, and optimize code.

## 🐳 Quick Start with Docker Desktop

The easiest way to get started with AI Coding Agent is using Docker Desktop. This ensures a consistent environment across all platforms.

### Prerequisites

1. **Install Docker Desktop**
   - [Download Docker Desktop](https://www.docker.com/products/docker-desktop)
   - Install and start Docker Desktop
   - Ensure Docker is running (you should see the Docker icon in your system tray)

2. **System Requirements**
   - **RAM**: Minimum 8GB, Recommended 16GB+
   - **Storage**: At least 20GB free space
   - **CPU**: Multi-core processor recommended
   - **GPU**: Optional, for fine-tuning with CUDA support

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd AICodingAgent
   ```

2. **Copy environment configuration**
   ```bash
   cp config/env.example .env
   # Edit .env file with your settings
   ```

3. **Build and start services**
   ```bash
   # Build all Docker images
   python scripts/docker_management.py build

   # Start all services
   python scripts/docker_management.py start
   ```

4. **Access the application**
   - **Main API**: http://localhost:8000
   - **Health Check**: http://localhost:8000/health
   - **API Documentation**: http://localhost:8000/docs
   - **Ollama API**: http://localhost:11434

### Development Setup

For development with hot-reload:

```bash
# Start in development mode
docker-compose -f docker-compose.yml -f docker-compose.override.yml up -d

# View logs
docker-compose logs -f api
```

### Cursor Rules Monitor

The AI Coding Agent includes a **mandatory cursor rules monitoring system** that ensures all development follows the established coding standards and best practices.

#### Automatic Startup

The cursor rules monitor is **automatically started** when you run:

```bash
# Start all services (includes cursor monitor)
python scripts/docker_management.py start

# Or using Docker Compose directly
docker-compose up -d cursor_monitor api
```

#### Manual Control

You can also manually control the monitor:

```bash
# Run tests
python -m pytest

# Check code quality
python -m flake8 agent/
python -m mypy agent/
```

#### Development Features

- **Containerized development** - Docker-first approach for all services
- **Comprehensive testing** - Unit and integration tests
- **Type safety** - Full mypy type checking
- **Code quality** - Automated linting and formatting

#### Development Requirements

- **90%+ compliance score** required for development to proceed
- **100% test success rate** mandatory for all test runs
- **All TODOs must be completed** before ending work sessions
- **Docker-first policy** for all website projects
- **Virtual environment activation** required for all Python commands

#### CI Integration

The monitor is integrated into the CI pipeline:

```yaml
# .github/workflows/docker-ci.yml
- name: Check cursor rules monitor status
  run: |
    python scripts/check_monitor_status.py || { echo "Monitor not running – aborting"; exit 1; }
```

### Production Deployment

For production deployment:

```bash
# Set environment variables
export DB_PASSWORD=your-secure-password

# Deploy with production settings
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Management Commands

```bash
# Check service status
python scripts/docker_management.py status

# View logs
python scripts/docker_management.py logs api

# Scale services
python scripts/docker_management.py scale api 3

# Health check
python scripts/docker_management.py health

# Backup data
python scripts/docker_management.py backup

# Stop all services
python scripts/docker_management.py stop

# Clean up
python scripts/docker_management.py cleanup
```

### Architecture

The application runs as a microservices architecture:

- **API Service**: Main application API (port 8000)
- **Ollama**: LLM server for AI models (port 11434)
- **PostgreSQL**: Database (port 5432)
- **Redis**: Caching and sessions (port 6379)
- **Nginx**: Reverse proxy (port 80/443)

### Troubleshooting

**Port conflicts**: If ports are already in use, modify the port mappings in `docker-compose.yml`

**Memory issues**: Increase Docker Desktop memory limit in Settings → Resources

**Build failures**: Run `docker-compose build --no-cache` to rebuild from scratch

**Permission issues**: Ensure Docker has proper permissions to access the project directory

For more detailed Docker documentation, see [README_Docker.md](README_Docker.md).

## 🚀 Features

### Core Functionality
- **AI-Powered Code Generation**: Generate code using local Ollama models
- **Code Review & Analysis**: Automated code review with AI insights
- **Learning System**: Adaptive learning from user interactions
- **Fine-tuning Pipeline**: Custom model training and optimization
- **Robustness Features**: Recovery, validation, and monitoring systems

### Development Tools
- **IDE Integration**: Seamless integration with popular IDEs
- **CLI Interface**: Command-line tools for automation
- **API Access**: RESTful API for programmatic access
- **Real-time Monitoring**: Live system metrics and health checks

### Security & Reliability
- **Container Isolation**: Secure service isolation with Docker
- **Automated Backups**: Regular data backup and recovery
- **Health Monitoring**: Comprehensive system health checks
- **Security Scanning**: Automated vulnerability detection

## 🏗️ Architecture

### Microservices
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx Proxy   │    │   API Service   │    │   PostgreSQL    │
│   (80/443)      │◄──►│   (8000)        │◄──►│   (5432)        │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌────────┴────────┐              │
         │              │                 │              │
         │        ┌─────▼─────┐    ┌─────▼─────┐         │
         │        │   Redis   │    │ Monitoring│         │
         │        │  (6379)   │    │           │         │
         │        └───────────┘    └───────────┘         │
         │                                                   │
    ┌────▼────┐    ┌──────────┐    ┌──────────┐    ┌────────▼────────┐
    │ Recovery│    │Validation│    │Fine-tune │    │   Backup        │
    │         │    │          │    │          │    │   Security      │
    └─────────┘    └──────────┘    └──────────┘    └─────────────────┘
```

### Technology Stack
- **Backend**: Python 3.11, FastAPI, SQLAlchemy
- **AI Models**: Ollama (local LLMs)
- **Database**: PostgreSQL 15
- **Cache**: Redis 7
- **Frontend**: React, Next.js
- **Containerization**: Docker, Docker Compose
- **Reverse Proxy**: Nginx
- **Monitoring**: Custom monitoring system

## 📚 Documentation

- [Docker Setup Guide](README_Docker.md) - Comprehensive Docker documentation
- [API Documentation](docs/API.md) - API reference and examples
- [Development Guide](docs/DEVELOPMENT.md) - Development setup and guidelines
- [Deployment Guide](docs/DEPLOYMENT.md) - Production deployment instructions

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Issues**: [GitHub Issues](https://github.com/your-repo/issues)
- **Documentation**: [Project Wiki](https://github.com/your-repo/wiki)
- **Discussions**: [GitHub Discussions](https://github.com/your-repo/discussions)

---

**🎉 Welcome to AI Coding Agent! Get started with Docker Desktop for the best experience.**
