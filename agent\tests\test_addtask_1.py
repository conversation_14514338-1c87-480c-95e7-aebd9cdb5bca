import os
import sys
import time

import pytest

# Skip this test if not on Windows or if Windows modules are not available
if sys.platform != "win32":
    pytest.skip(
        "Windows-specific test - skipping on non-Windows platform",
        allow_module_level=True,
    )

try:
    import pythoncom
    import win32api
    from win32com.taskscheduler import taskscheduler
except ImportError:
    pytest.skip("Windows-specific modules not available", allow_module_level=True)


@pytest.mark.skipif(sys.platform != "win32", reason="Windows-specific test")
def test_addtask_1():
    """Test Windows task scheduler functionality - variant 1"""
    try:
        task_name = "test_addtask_1.job"
        ts = pythoncom.CoCreateInstance(
            taskscheduler.CLSID_CTaskScheduler,
            None,
            pythoncom.CLSCTX_INPROC_SERVER,
            taskscheduler.IID_ITaskScheduler,
        )

        # Create a new task
        new_task = pythoncom.CoCreateInstance(
            taskscheduler.CLSID_CTask,
            None,
            pythoncom.CLSCTX_INPROC_SERVER,
            taskscheduler.IID_ITask,
        )

        # Set task properties
        new_task.SetComment("Test task from test_addtask_1.py")
        new_task.SetApplicationName(sys.executable)
        new_task.SetPriority(taskscheduler.REALTIME_PRIORITY_CLASS)
        new_task.SetParameters(
            "-c\"import win32ui,time;win32ui.MessageBox('why ain't you doing no work ?');\""
        )
        new_task.SetWorkingDirectory(os.path.dirname(sys.executable))
        new_task.SetCreator("test_addtask_1.py")
        new_task.SetMaxRunTime(20000)  # milliseconds
        new_task.SetFlags(
            taskscheduler.TASK_FLAG_INTERACTIVE
            | taskscheduler.TASK_FLAG_RUN_ONLY_IF_LOGGED_ON
        )
        new_task.SetAccountInformation(win32api.GetUserName(), None)
        new_task.SetWorkItemData("some binary garbage")

        # Create trigger
        run_time = time.localtime(time.time() + 60)
        tr_ind, tr = new_task.CreateTrigger()
        tt = tr.GetTrigger()

        tt.Flags = taskscheduler.TASK_TRIGGER_FLAG_KILL_AT_DURATION_END
        tt.BeginYear = int(time.strftime("%Y", run_time))
        tt.BeginMonth = int(time.strftime("%m", run_time))
        tt.BeginDay = int(time.strftime("%d", run_time))
        tt.StartMinute = int(time.strftime("%M", run_time))
        tt.StartHour = int(time.strftime("%H", run_time))
        tt.MinutesInterval = 1
        tt.MinutesDuration = 5

        tt.TriggerType = taskscheduler.TASK_TIME_TRIGGER_MONTHLYDATE
        tt.MonthlyDate_Months = 1 << (int(time.strftime("%m", run_time)) - 1)
        tt.MonthlyDate_Days = 1 << (int(time.strftime("%d", run_time)) - 1)
        tr.SetTrigger(tt)
        print(new_task.GetTriggerString(tr_ind))

        pf = new_task.QueryInterface(pythoncom.IID_IPersistFile)
        pf.Save(None, 1)

        # Clean up
        if task_name in ts.Enum():
            ts.Delete(task_name)

    except Exception as e:
        pytest.skip(f"Windows task scheduler test failed: {e}")
