import asyncio
import time
from typing import List
from unittest.mock import AsyncMock, Mock, patch

import pytest

from agent.core.agents.agent_message_bus import (
    AgentMessage,
    AgentMessageBus,
    MessageFilter,
    MessagePriority,
    MessageStatus,
    MessageSubscription,
    MessageType,
)


class TestAgentMessage:
    """Test suite for AgentMessage class."""

    def test_message_creation(self):
        """Test basic message creation."""
        message = AgentMessage(
            id="test_id",
            message_type=MessageType.TASK_REQUEST,
            sender_id="agent1",
            recipient_id="agent2",
            subject="Test Subject",
            content={"test": "data"},
        )

        assert message.id == "test_id"
        assert message.message_type == MessageType.TASK_REQUEST
        assert message.sender_id == "agent1"
        assert message.recipient_id == "agent2"
        assert message.subject == "Test Subject"
        assert message.content == {"test": "data"}
        assert message.priority == MessagePriority.NORMAL
        assert message.status == MessageStatus.PENDING

    def test_message_serialization(self):
        """Test message to/from dict conversion."""
        original_message = AgentMessage(
            id="test_id",
            message_type=MessageType.TASK_REQUEST,
            sender_id="agent1",
            recipient_id="agent2",
            subject="Test Subject",
            content={"test": "data"},
            priority=MessagePriority.HIGH,
        )

        # Convert to dict
        message_dict = original_message.to_dict()
        assert message_dict["id"] == "test_id"
        assert message_dict["message_type"] == "task_request"
        assert message_dict["priority"] == 3

        # Convert back from dict
        restored_message = AgentMessage.from_dict(message_dict)
        assert restored_message.id == original_message.id
        assert restored_message.message_type == original_message.message_type
        assert restored_message.priority == original_message.priority
        assert restored_message.content == original_message.content

    def test_message_expiration(self):
        """Test message expiration logic."""
        # Non-expiring message
        message1 = AgentMessage(
            id="test1",
            message_type=MessageType.DIRECT_MESSAGE,
            sender_id="agent1",
            recipient_id="agent2",
            subject="Test",
            content={},
        )
        assert not message1.is_expired()

        # Expired message
        message2 = AgentMessage(
            id="test2",
            message_type=MessageType.DIRECT_MESSAGE,
            sender_id="agent1",
            recipient_id="agent2",
            subject="Test",
            content={},
            expires_at=time.time() - 10,  # Expired 10 seconds ago
        )
        assert message2.is_expired()

        # Future expiration
        message3 = AgentMessage(
            id="test3",
            message_type=MessageType.DIRECT_MESSAGE,
            sender_id="agent1",
            recipient_id="agent2",
            subject="Test",
            content={},
            expires_at=time.time() + 10,  # Expires in 10 seconds
        )
        assert not message3.is_expired()


class TestMessageFilter:
    """Test suite for MessageFilter class."""

    def test_filter_matching(self):
        """Test message filter matching logic."""
        # Create test message
        message = AgentMessage(
            id="test",
            message_type=MessageType.TASK_REQUEST,
            sender_id="agent1",
            recipient_id="agent2",
            subject="Test Task",
            content={},
            priority=MessagePriority.HIGH,
        )

        # Test message type filter
        type_filter = MessageFilter(message_types=[MessageType.TASK_REQUEST])
        assert type_filter.matches(message)

        wrong_type_filter = MessageFilter(message_types=[MessageType.TASK_RESPONSE])
        assert not wrong_type_filter.matches(message)

        # Test sender filter
        sender_filter = MessageFilter(sender_ids=["agent1"])
        assert sender_filter.matches(message)

        wrong_sender_filter = MessageFilter(sender_ids=["agent3"])
        assert not wrong_sender_filter.matches(message)

        # Test priority filter
        priority_filter = MessageFilter(priorities=[MessagePriority.HIGH])
        assert priority_filter.matches(message)

        wrong_priority_filter = MessageFilter(priorities=[MessagePriority.LOW])
        assert not wrong_priority_filter.matches(message)

        # Test combined filter
        combined_filter = MessageFilter(
            message_types=[MessageType.TASK_REQUEST],
            sender_ids=["agent1"],
            priorities=[MessagePriority.HIGH],
        )
        assert combined_filter.matches(message)

        # Test empty filter (should match everything)
        empty_filter = MessageFilter()
        assert empty_filter.matches(message)


class TestAgentMessageBus:
    """Test suite for AgentMessageBus class."""

    @pytest.fixture
    def message_bus(self):
        """Create a message bus for testing."""
        bus = AgentMessageBus(max_message_history=100)
        bus.start()
        yield bus
        bus.stop()

    @pytest.fixture
    def test_message(self):
        """Create a test message."""
        return AgentMessage(
            id="test_message",
            message_type=MessageType.DIRECT_MESSAGE,
            sender_id="agent1",
            recipient_id="agent2",
            subject="Test Message",
            content={"data": "test"},
        )

    def test_subscription_management(self, message_bus):
        """Test subscription creation and management."""
        received_messages = []

        def message_handler(message: AgentMessage):
            received_messages.append(message)

        # Subscribe
        subscription_id = message_bus.subscribe("agent1", message_handler)
        assert subscription_id in message_bus.subscriptions
        assert "agent1" in message_bus.agent_subscriptions
        assert subscription_id in message_bus.agent_subscriptions["agent1"]

        # Unsubscribe
        success = message_bus.unsubscribe(subscription_id)
        assert success
        assert subscription_id not in message_bus.subscriptions
        assert subscription_id not in message_bus.agent_subscriptions.get("agent1", [])

    @pytest.mark.asyncio
    async def test_direct_message_delivery(self, message_bus, test_message):
        """Test direct message delivery."""
        received_messages = []

        def message_handler(message: AgentMessage):
            received_messages.append(message)

        # Subscribe agent2 to receive messages
        message_bus.subscribe("agent2", message_handler)

        # Publish message
        success = await message_bus.publish(test_message)
        assert success

        # Wait a bit for delivery
        await asyncio.sleep(0.1)

        # Check message was delivered
        assert len(received_messages) == 1
        assert received_messages[0].id == test_message.id
        assert test_message.status == MessageStatus.DELIVERED

    @pytest.mark.asyncio
    async def test_broadcast_message_delivery(self, message_bus):
        """Test broadcast message delivery."""
        received_messages_1 = []
        received_messages_2 = []

        def handler_1(message: AgentMessage):
            received_messages_1.append(message)

        def handler_2(message: AgentMessage):
            received_messages_2.append(message)

        # Subscribe multiple agents
        message_bus.subscribe("agent1", handler_1)
        message_bus.subscribe("agent2", handler_2)

        # Create broadcast message (no recipient)
        broadcast_message = AgentMessage(
            id="broadcast_test",
            message_type=MessageType.BROADCAST,
            sender_id="system",
            recipient_id=None,  # Broadcast
            subject="System Announcement",
            content={"announcement": "Test broadcast"},
        )

        # Publish broadcast
        success = await message_bus.publish(broadcast_message)
        assert success

        # Wait for delivery
        await asyncio.sleep(0.1)

        # Both agents should receive the message
        assert len(received_messages_1) == 1
        assert len(received_messages_2) == 1
        assert received_messages_1[0].id == "broadcast_test"
        assert received_messages_2[0].id == "broadcast_test"

    @pytest.mark.asyncio
    async def test_message_filtering(self, message_bus):
        """Test message filtering in subscriptions."""
        received_messages = []

        def message_handler(message: AgentMessage):
            received_messages.append(message)

        # Subscribe with filter for only HIGH priority messages
        message_filter = MessageFilter(priorities=[MessagePriority.HIGH])
        message_bus.subscribe("agent1", message_handler, message_filter)

        # Send normal priority message (should not be received)
        normal_message = AgentMessage(
            id="normal",
            message_type=MessageType.DIRECT_MESSAGE,
            sender_id="agent2",
            recipient_id="agent1",
            subject="Normal Message",
            content={},
            priority=MessagePriority.NORMAL,
        )
        await message_bus.publish(normal_message)

        # Send high priority message (should be received)
        high_message = AgentMessage(
            id="high",
            message_type=MessageType.DIRECT_MESSAGE,
            sender_id="agent2",
            recipient_id="agent1",
            subject="High Priority Message",
            content={},
            priority=MessagePriority.HIGH,
        )
        await message_bus.publish(high_message)

        # Wait for delivery
        await asyncio.sleep(0.1)

        # Only high priority message should be received
        assert len(received_messages) == 1
        assert received_messages[0].id == "high"

    @pytest.mark.asyncio
    async def test_request_response_pattern(self, message_bus):
        """Test request-response messaging pattern."""

        # Set up responder
        def responder(message: AgentMessage):
            if message.message_type == MessageType.TASK_REQUEST:
                # Simulate async response
                async def send_response():
                    await message_bus.send_message(
                        sender_id="agent2",
                        recipient_id=message.sender_id,
                        message_type=MessageType.TASK_RESPONSE,
                        subject="Response to " + message.subject,
                        content={"response": "task completed"},
                        correlation_id=message.correlation_id,
                    )

                asyncio.create_task(send_response())

        message_bus.subscribe("agent2", responder)

        # Send request and wait for response
        response = await message_bus.send_request(
            sender_id="agent1",
            recipient_id="agent2",
            subject="Test Request",
            content={"request": "do something"},
            timeout_seconds=5,
        )

        assert response is not None
        assert response.message_type == MessageType.TASK_RESPONSE
        assert response.content["response"] == "task completed"

    @pytest.mark.asyncio
    async def test_message_acknowledgment(self, message_bus):
        """Test message acknowledgment system."""
        # Create message requiring acknowledgment
        ack_message = AgentMessage(
            id="ack_test",
            message_type=MessageType.DIRECT_MESSAGE,
            sender_id="agent1",
            recipient_id="agent2",
            subject="Acknowledge Me",
            content={},
            requires_acknowledgment=True,
        )

        # Set up receiver that acknowledges
        def ack_handler(message: AgentMessage):
            if message.requires_acknowledgment:
                asyncio.create_task(
                    message_bus.acknowledge_message(message.id, "agent2")
                )

        message_bus.subscribe("agent2", ack_handler)

        # Publish message
        await message_bus.publish(ack_message)

        # Wait for acknowledgment
        await asyncio.sleep(0.1)

        # Check acknowledgment was processed
        assert ack_message.id not in message_bus.pending_acknowledgments

    def test_message_history(self, message_bus, test_message):
        """Test message history functionality."""
        # Initially empty
        history = message_bus.get_message_history()
        initial_count = len(history)

        # Add message to history
        with message_bus._lock:
            message_bus.message_history.append(test_message)

        # Check history
        history = message_bus.get_message_history()
        assert len(history) == initial_count + 1
        assert history[-1].id == test_message.id

        # Test filtering by agent
        agent_history = message_bus.get_message_history(agent_id="agent1")
        assert len(agent_history) == 1
        assert agent_history[0].sender_id == "agent1"

        # Test filtering by message type
        type_history = message_bus.get_message_history(
            message_type=MessageType.DIRECT_MESSAGE
        )
        assert len(type_history) == 1
        assert type_history[0].message_type == MessageType.DIRECT_MESSAGE

    def test_queued_messages(self, message_bus, test_message):
        """Test message queueing for offline agents."""
        # Publish message to agent with no subscriptions
        with message_bus._lock:
            message_bus.message_queues["agent2"].append(test_message)

        # Get queued messages
        queued = message_bus.get_queued_messages("agent2")
        assert len(queued) == 1
        assert queued[0].id == test_message.id

        # Queue should be empty after retrieval
        queued_again = message_bus.get_queued_messages("agent2")
        assert len(queued_again) == 0

    def test_delivery_statistics(self, message_bus):
        """Test delivery statistics tracking."""
        # Get initial stats
        stats = message_bus.get_delivery_stats()
        initial_sent = stats.get("agent1", {}).get("sent", 0)

        # Simulate message sending
        with message_bus._lock:
            message_bus.delivery_stats["agent1"]["sent"] += 1
            message_bus.delivery_stats["agent1"]["delivered"] += 1

        # Check updated stats
        stats = message_bus.get_delivery_stats()
        assert stats["agent1"]["sent"] == initial_sent + 1
        assert stats["agent1"]["delivered"] == 1

    def test_system_statistics(self, message_bus):
        """Test system statistics."""
        stats = message_bus.get_system_stats()

        assert "message_history_size" in stats
        assert "pending_messages" in stats
        assert "total_subscriptions" in stats
        assert "active_agents" in stats
        assert "is_running" in stats
        assert stats["is_running"] is True

    @pytest.mark.asyncio
    async def test_expired_message_handling(self, message_bus):
        """Test handling of expired messages."""
        # Create expired message
        expired_message = AgentMessage(
            id="expired",
            message_type=MessageType.DIRECT_MESSAGE,
            sender_id="agent1",
            recipient_id="agent2",
            subject="Expired Message",
            content={},
            expires_at=time.time() - 10,  # Expired 10 seconds ago
        )

        # Try to publish expired message
        success = await message_bus.publish(expired_message)
        assert not success  # Should fail because message is expired

    @pytest.mark.asyncio
    async def test_async_message_handling(self, message_bus):
        """Test asynchronous message handling."""
        received_messages = []

        async def async_handler(message: AgentMessage):
            await asyncio.sleep(0.01)  # Simulate async work
            received_messages.append(message)

        # Subscribe with async handler
        message_bus.subscribe("agent1", async_handler, is_async=True)

        # Send message
        test_message = AgentMessage(
            id="async_test",
            message_type=MessageType.DIRECT_MESSAGE,
            sender_id="agent2",
            recipient_id="agent1",
            subject="Async Test",
            content={},
        )

        await message_bus.publish(test_message)

        # Wait for async processing
        await asyncio.sleep(0.1)

        assert len(received_messages) == 1
        assert received_messages[0].id == "async_test"


def run_message_bus_tests():
    """Run all message bus tests."""
    print("Running agent message bus tests...")

    import subprocess
    import sys

    try:
        result = subprocess.run(
            [
                sys.executable,
                "-m",
                "pytest",
                "tests/test_message_bus.py",
                "-v",
                "--tb=short",
            ],
            capture_output=True,
            text=True,
        )

        print("Test Output:")
        print(result.stdout)
        if result.stderr:
            print("Test Errors:")
            print(result.stderr)

        return result.returncode == 0
    except Exception as e:
        print(f"Error running tests: {e}")
        return False


if __name__ == "__main__":
    success = run_message_bus_tests()
    exit(0 if success else 1)
