#!/usr/bin/env python3
"""
Safe removal script for cursor rules monitoring and enforcement system.
Removes all cursor rules related files, Docker services, and references.
"""
from __future__ import annotations

import os
import re
import shutil
import subprocess
import sys
from pathlib import Path
from typing import List, Tuple

# Files to remove (relative to repo root)
FILES_TO_REMOVE = [
    # Configuration & Rules Files
    ".cursor/rules/cursorrules.mdc",
    ".cursor/rules/cursorrules.md", 
    ".cursor/rules/README.md",
    ".augment/rules/imported/cursorrules.md",
    
    # Core Enforcement Code
    "agent/core/cursor_rules_enforcer.py",
    "agent/core/utils/cursor_rules_validator.py",
    
    # Scripts
    "agent/scripts/cursor_rules_check.py",
    "agent/scripts/cursor_rules_monitor.py", 
    "agent/scripts/setup_cursor_rules_enforcement.py",
    "scripts/cursor_rules_monitor.py",
    "scripts/extended_health_check.py",
    "scripts/monitor_http_server.py",
    
    # Monitoring & Configuration
    "agent/monitoring/cursor_monitor_rules.yml",
    "agent/config/.pre-commit-config.yaml",
    
    # Documentation
    "agent/docs/CURSOR_RULES_MONITOR_GUIDE.md",
    "agent/docs/CURSOR_RULES_ENFORCEMENT_SYSTEM.md",
    "docs/reports/CURSOR_RULES_VIOLATIONS_REPORT.md",
    
    # Docker Infrastructure
    "infrastructure/Dockerfile.cursor-monitor",
    "infrastructure/supervisor/cursor-monitor.conf",
    
    # Data Files
    "data/cursor_compliance.json",
    "shared/cursor_compliance.json", 
    "logs/cursor_rules_monitor.log",
    "containers/extracted/docker-compose_cursor_monitor_ports.json",
]

# Directories to remove if empty after file removal
DIRS_TO_CLEANUP = [
    ".cursor/rules",
    ".cursor", 
    "infrastructure/supervisor",
    "containers/extracted",
]

# Files that need content modification (not removal)
FILES_TO_MODIFY = [
    "infrastructure/docker-compose.yml",  # Remove cursor_monitor service
    ".pre-commit-config.yaml",           # Remove cursor-rules-enforcement hook
    "tools/scripts/compose_blob_guard.py",  # Remove cursor rules references
    "tools/scripts/separation_guard.py",    # Remove cursor rules references
]


def run_git_command(cmd: List[str]) -> Tuple[bool, str]:
    """Run git command safely and return success status and output."""
    try:
        result = subprocess.run(
            cmd, capture_output=True, text=True, check=True, cwd=Path.cwd()
        )
        return True, result.stdout.strip()
    except subprocess.CalledProcessError as e:
        return False, e.stderr.strip()


def remove_file_safely(file_path: Path) -> bool:
    """Remove a file using git rm if tracked, otherwise regular removal."""
    if not file_path.exists():
        print(f"⚠️  File not found: {file_path}")
        return False
        
    # Check if file is tracked by git
    success, _ = run_git_command(["git", "ls-files", "--error-unmatch", str(file_path)])
    
    if success:
        # File is tracked, use git rm
        success, output = run_git_command(["git", "rm", str(file_path)])
        if success:
            print(f"✅ Git removed: {file_path}")
            return True
        else:
            print(f"❌ Git rm failed for {file_path}: {output}")
            return False
    else:
        # File is not tracked, remove directly
        try:
            file_path.unlink()
            print(f"✅ Removed: {file_path}")
            return True
        except Exception as e:
            print(f"❌ Failed to remove {file_path}: {e}")
            return False


def remove_cursor_monitor_from_compose(compose_path: Path) -> bool:
    """Remove cursor_monitor service from docker-compose.yml."""
    if not compose_path.exists():
        print(f"⚠️  Compose file not found: {compose_path}")
        return False
        
    try:
        content = compose_path.read_text(encoding="utf-8")
        
        # Remove cursor_monitor service block (lines 155-221 based on search)
        # Use regex to remove the entire service definition
        pattern = r'\s*cursor_monitor:\s*\n(?:(?:\s{2,}.*\n)*)'
        content = re.sub(pattern, '', content, flags=re.MULTILINE)
        
        # Also remove the volume reference
        content = re.sub(
            r'\s*- containers/extracted/docker-compose_cursor_monitor_ports\.json:/app/config/ports\.json:ro\n',
            '', content
        )
        
        compose_path.write_text(content, encoding="utf-8")
        print(f"✅ Removed cursor_monitor service from: {compose_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to modify {compose_path}: {e}")
        return False


def remove_cursor_rules_from_precommit(precommit_path: Path) -> bool:
    """Remove cursor-rules-enforcement hook from .pre-commit-config.yaml."""
    if not precommit_path.exists():
        print(f"⚠️  Pre-commit config not found: {precommit_path}")
        return False
        
    try:
        content = precommit_path.read_text(encoding="utf-8")
        
        # Remove cursor-rules-enforcement hook
        pattern = r'\s*- id: cursor-rules-enforcement\s*\n(?:(?:\s{2,}.*\n)*)'
        content = re.sub(pattern, '', content, flags=re.MULTILINE)
        
        precommit_path.write_text(content, encoding="utf-8")
        print(f"✅ Removed cursor-rules-enforcement from: {precommit_path}")
        return True
        
    except Exception as e:
        print(f"❌ Failed to modify {precommit_path}: {e}")
        return False


def cleanup_empty_directories(dirs: List[str]) -> None:
    """Remove directories if they are empty."""
    for dir_path in dirs:
        path = Path(dir_path)
        if path.exists() and path.is_dir():
            try:
                if not any(path.iterdir()):  # Directory is empty
                    path.rmdir()
                    print(f"✅ Removed empty directory: {path}")
                else:
                    print(f"⚠️  Directory not empty, keeping: {path}")
            except Exception as e:
                print(f"❌ Failed to remove directory {path}: {e}")


def main() -> int:
    """Main removal function."""
    print("🗑️  Cursor Rules System Removal Script")
    print("=" * 50)
    
    repo_root = Path.cwd()
    removed_count = 0
    failed_count = 0
    
    # Confirm with user
    print(f"\nThis will remove {len(FILES_TO_REMOVE)} cursor rules files and modify {len(FILES_TO_MODIFY)} files.")
    response = input("Continue? [y/N]: ").strip().lower()
    if response != 'y':
        print("❌ Aborted by user")
        return 1
    
    print("\n📁 Removing files...")
    
    # Remove individual files
    for file_rel in FILES_TO_REMOVE:
        file_path = repo_root / file_rel
        if remove_file_safely(file_path):
            removed_count += 1
        else:
            failed_count += 1
    
    print("\n📝 Modifying files...")
    
    # Modify docker-compose.yml
    compose_path = repo_root / "infrastructure/docker-compose.yml"
    if remove_cursor_monitor_from_compose(compose_path):
        print("✅ Modified docker-compose.yml")
    else:
        failed_count += 1
    
    # Modify .pre-commit-config.yaml
    precommit_path = repo_root / ".pre-commit-config.yaml"
    if remove_cursor_rules_from_precommit(precommit_path):
        print("✅ Modified .pre-commit-config.yaml")
    else:
        failed_count += 1
    
    print("\n🧹 Cleaning up empty directories...")
    cleanup_empty_directories(DIRS_TO_CLEANUP)
    
    print(f"\n📊 Summary:")
    print(f"✅ Files removed: {removed_count}")
    print(f"❌ Failed operations: {failed_count}")
    
    if failed_count == 0:
        print("\n🎉 Cursor rules system successfully removed!")
        print("\nNext steps:")
        print("1. Review changes: git status")
        print("2. Commit changes: git commit -m 'feat: remove cursor rules monitoring system'")
        print("3. Update any remaining references in documentation")
        return 0
    else:
        print(f"\n⚠️  Completed with {failed_count} failures. Please review and fix manually.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
