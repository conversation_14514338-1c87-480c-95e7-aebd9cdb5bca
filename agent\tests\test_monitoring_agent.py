#!/usr/bin/env python3
"""
Test script for the Monitoring Agent
Tests all monitoring functionality including metrics collection, alerting, and API endpoints.
"""

import asyncio
import json
import logging
import sys
import time
from pathlib import Path
from typing import Any, Dict

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agent.monitoring.monitoring_agent import (
    AlertConfig,
    MonitoringAgent,
    SystemMetrics,
    start_monitoring_agent,
    stop_monitoring_agent,
)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def print_test_header(test_name: str):
    """Print test header"""
    print(f"\n{'='*60}")
    print(f"🧪 TESTING: {test_name}")
    print(f"{'='*60}")


def print_test_result(test_name: str, success: bool, details: str = ""):
    """Print test result"""
    status = "✅ PASSED" if success else "❌ FAILED"
    print(f"{status}: {test_name}")
    if details:
        print(f"   Details: {details}")


async def test_monitoring_agent_initialization():
    """Test monitoring agent initialization"""
    print_test_header("Monitoring Agent Initialization")

    try:
        # Test with default config
        agent = MonitoringAgent()
        print_test_result("Default initialization", True)

        # Test with custom config
        custom_config = AlertConfig(
            cpu_threshold=70.0,
            memory_threshold=65.0,
            disk_threshold=80.0,
            check_interval=30,
        )
        agent_custom = MonitoringAgent(custom_config)
        print_test_result("Custom config initialization", True)

        return True
    except Exception as e:
        print_test_result("Initialization", False, str(e))
        return False


async def test_metrics_collection():
    """Test metrics collection functionality"""
    print_test_header("Metrics Collection")

    try:
        agent = MonitoringAgent()

        # Collect metrics
        metrics = await agent.collect_metrics()

        # Verify metrics structure
        assert hasattr(metrics, "timestamp"), "Missing timestamp"
        assert hasattr(metrics, "cpu_percent"), "Missing CPU percentage"
        assert hasattr(metrics, "memory_percent"), "Missing memory percentage"
        assert hasattr(metrics, "disk_percent"), "Missing disk percentage"
        assert hasattr(metrics, "network_io"), "Missing network I/O"
        assert hasattr(metrics, "process_count"), "Missing process count"
        assert hasattr(metrics, "uptime"), "Missing uptime"

        # Verify metric values are reasonable
        assert (
            0 <= metrics.cpu_percent <= 100
        ), f"CPU percentage out of range: {metrics.cpu_percent}"
        assert (
            0 <= metrics.memory_percent <= 100
        ), f"Memory percentage out of range: {metrics.memory_percent}"
        assert (
            0 <= metrics.disk_percent <= 100
        ), f"Disk percentage out of range: {metrics.disk_percent}"
        assert (
            metrics.process_count > 0
        ), f"Process count should be positive: {metrics.process_count}"
        assert metrics.uptime > 0, f"Uptime should be positive: {metrics.uptime}"

        print_test_result("Metrics collection", True)
        print(f"   CPU: {metrics.cpu_percent:.1f}%")
        print(f"   Memory: {metrics.memory_percent:.1f}%")
        print(f"   Disk: {metrics.disk_percent:.1f}%")
        print(f"   Processes: {metrics.process_count}")
        print(f"   Uptime: {metrics.uptime:.0f} seconds")

        return True
    except Exception as e:
        print_test_result("Metrics collection", False, str(e))
        return False


async def test_alert_detection():
    """Test alert detection functionality"""
    print_test_header("Alert Detection")

    try:
        # Create agent with low thresholds to trigger alerts
        config = AlertConfig(
            cpu_threshold=1.0,  # Very low threshold to trigger alert
            memory_threshold=1.0,
            disk_threshold=1.0,
            check_interval=1,
        )
        agent = MonitoringAgent(config)

        # Collect metrics
        metrics = await agent.collect_metrics()

        # Check for alerts
        alerts = await agent.check_alerts(metrics)

        # Should have alerts due to low thresholds
        assert len(alerts) > 0, "Expected alerts due to low thresholds"

        # Verify alert structure
        for alert in alerts:
            assert "type" in alert, "Alert missing type"
            assert "severity" in alert, "Alert missing severity"
            assert "message" in alert, "Alert missing message"
            assert "value" in alert, "Alert missing value"
            assert "threshold" in alert, "Alert missing threshold"
            assert "timestamp" in alert, "Alert missing timestamp"

        print_test_result("Alert detection", True, f"Found {len(alerts)} alerts")

        # Print alert details
        for alert in alerts:
            print(
                f"   {alert['type']}: {alert['message']} (Severity: {alert['severity']})"
            )

        return True
    except Exception as e:
        print_test_result("Alert detection", False, str(e))
        return False


async def test_metrics_history():
    """Test metrics history functionality"""
    print_test_header("Metrics History")

    try:
        agent = MonitoringAgent()

        # Collect multiple metrics
        for i in range(3):
            await agent.collect_metrics()
            await asyncio.sleep(0.1)  # Small delay

        # Check history
        history = agent.get_metrics_history()
        assert len(history) >= 3, f"Expected at least 3 metrics, got {len(history)}"

        # Check history limit
        limited_history = agent.get_metrics_history(limit=2)
        assert (
            len(limited_history) <= 2
        ), f"Expected max 2 metrics, got {len(limited_history)}"

        print_test_result("Metrics history", True, f"Collected {len(history)} metrics")

        return True
    except Exception as e:
        print_test_result("Metrics history", False, str(e))
        return False


async def test_health_endpoint():
    """Test health endpoint functionality"""
    print_test_header("Health Endpoint")

    try:
        agent = MonitoringAgent()

        # Collect some metrics first
        await agent.collect_metrics()

        # Get health data
        health_data = await agent.get_current_health()

        # Verify health response structure
        assert hasattr(health_data, "status"), "Health response missing status"
        assert hasattr(health_data, "timestamp"), "Health response missing timestamp"
        assert hasattr(health_data, "metrics"), "Health response missing metrics"
        assert hasattr(health_data, "alerts"), "Health response missing alerts"
        assert hasattr(health_data, "uptime"), "Health response missing uptime"

        # Verify status is valid
        assert health_data.status in [
            "healthy",
            "warning",
            "critical",
        ], f"Invalid status: {health_data.status}"

        print_test_result("Health endpoint", True, f"Status: {health_data.status}")
        print(f"   CPU: {health_data.metrics.cpu_percent:.1f}%")
        print(f"   Memory: {health_data.metrics.memory_percent:.1f}%")
        print(f"   Disk: {health_data.metrics.disk_percent:.1f}%")
        print(f"   Alerts: {len(health_data.alerts)}")

        return True
    except Exception as e:
        print_test_result("Health endpoint", False, str(e))
        return False


async def test_monitoring_lifecycle():
    """Test monitoring agent start/stop lifecycle"""
    print_test_header("Monitoring Lifecycle")

    try:
        # Test global start/stop functions
        await start_monitoring_agent()
        print_test_result("Start monitoring agent", True)

        # Wait a moment for metrics to be collected
        await asyncio.sleep(2)

        await stop_monitoring_agent()
        print_test_result("Stop monitoring agent", True)

        return True
    except Exception as e:
        print_test_result("Monitoring lifecycle", False, str(e))
        return False


async def test_configuration_loading():
    """Test configuration loading from environment"""
    print_test_header("Configuration Loading")

    try:
        # Test default configuration
        agent = MonitoringAgent()

        # Verify default values
        assert (
            agent.config.cpu_threshold == 80.0
        ), f"Expected CPU threshold 80.0, got {agent.config.cpu_threshold}"
        assert (
            agent.config.memory_threshold == 75.0
        ), f"Expected memory threshold 75.0, got {agent.config.memory_threshold}"
        assert (
            agent.config.disk_threshold == 85.0
        ), f"Expected disk threshold 85.0, got {agent.config.disk_threshold}"
        assert (
            agent.config.check_interval == 60
        ), f"Expected check interval 60, got {agent.config.check_interval}"

        print_test_result("Default configuration", True)

        # Test custom configuration
        custom_config = AlertConfig(
            cpu_threshold=90.0,
            memory_threshold=85.0,
            disk_threshold=90.0,
            check_interval=30,
            email_enabled=True,
            slack_enabled=True,
        )

        agent_custom = MonitoringAgent(custom_config)
        assert agent_custom.config.cpu_threshold == 90.0, "Custom config not applied"
        assert agent_custom.config.email_enabled == True, "Email not enabled"
        assert agent_custom.config.slack_enabled == True, "Slack not enabled"

        print_test_result("Custom configuration", True)

        return True
    except Exception as e:
        print_test_result("Configuration loading", False, str(e))
        return False


async def test_error_handling():
    """Test error handling in monitoring agent"""
    print_test_header("Error Handling")

    try:
        agent = MonitoringAgent()

        # Test with invalid configuration
        try:
            invalid_config = AlertConfig(cpu_threshold=-10.0)  # Invalid negative value
            agent_invalid = MonitoringAgent(invalid_config)
            # Should not raise exception, just use the invalid value
            print_test_result("Invalid config handling", True)
        except Exception as e:
            print_test_result("Invalid config handling", False, str(e))
            return False

        # Test metrics collection with potential errors
        try:
            metrics = await agent.collect_metrics()
            print_test_result("Metrics collection error handling", True)
        except Exception as e:
            print_test_result("Metrics collection error handling", False, str(e))
            return False

        return True
    except Exception as e:
        print_test_result("Error handling", False, str(e))
        return False


async def run_all_tests():
    """Run all monitoring agent tests"""
    print("🚀 TESTING MONITORING AGENT FUNCTIONALITY")
    print("=" * 60)

    tests = [
        ("Initialization", test_monitoring_agent_initialization),
        ("Metrics Collection", test_metrics_collection),
        ("Alert Detection", test_alert_detection),
        ("Metrics History", test_metrics_history),
        ("Health Endpoint", test_health_endpoint),
        ("Monitoring Lifecycle", test_monitoring_lifecycle),
        ("Configuration Loading", test_configuration_loading),
        ("Error Handling", test_error_handling),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ FAILED: {test_name} - Exception: {e}")
            results.append((test_name, False))

    # Print summary
    print(f"\n{'='*60}")
    print("📊 TEST RESULTS SUMMARY")
    print(f"{'='*60}")

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")

    print(f"\nTotal: {total} tests")
    print(f"Passed: {passed}")
    print(f"Failed: {total - passed}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")

    if passed == total:
        print("\n🎉 ALL TESTS PASSED! Monitoring agent is working correctly.")
        print("\n📋 Next Steps:")
        print(
            "1. Test the monitoring endpoint: curl http://localhost:8000/monitor/health"
        )
        print("2. Configure email/Slack alerts by setting environment variables")
        print("3. Monitor the logs/monitoring.log file for alerts")
        print("4. Adjust thresholds in the AlertConfig as needed")
    else:
        print(f"\n⚠️  {total - passed} test(s) failed. Please check the errors above.")

    return passed == total


if __name__ == "__main__":
    success = asyncio.run(run_all_tests())
    sys.exit(0 if success else 1)

"""
Unit tests for monitoring agent PID file functionality and status checking
"""

import os
import tempfile
import time
from pathlib import Path
from unittest.mock import MagicMock, mock_open, patch

import psutil
import pytest

from agent.monitoring.monitoring_agent import MonitoringAgent


class TestMonitoringAgentPIDFile:
    """Test PID file functionality"""

    @pytest.fixture
    def temp_logs_dir(self):
        """Create temporary logs directory"""
        with tempfile.TemporaryDirectory() as temp_dir:
            logs_dir = Path(temp_dir) / "logs"
            logs_dir.mkdir()
            yield logs_dir

    @pytest.fixture
    def agent(self, temp_logs_dir):
        """Create monitoring agent with temporary logs directory"""
        with patch("monitoring.monitoring_agent.Path") as mock_path:
            mock_path.return_value = temp_logs_dir / "monitor.pid"
            agent = MonitoringAgent()
            agent.pid_file_path = temp_logs_dir / "monitor.pid"
            yield agent

    def test_check_pid_file_not_exists(self, agent):
        """Test checking PID file when it doesn't exist"""
        result = agent._check_pid_file()
        assert result is None

    def test_check_pid_file_empty(self, agent):
        """Test checking empty PID file"""
        # Create empty PID file
        agent.pid_file_path.write_text("")

        result = agent._check_pid_file()
        assert result is None
        assert not agent.pid_file_path.exists()  # Should be removed

    def test_check_pid_file_invalid_content(self, agent):
        """Test checking PID file with invalid content"""
        # Create PID file with invalid content
        agent.pid_file_path.write_text("invalid")

        result = agent._check_pid_file()
        assert result is None
        assert not agent.pid_file_path.exists()  # Should be removed

    @patch("psutil.pid_exists")
    def test_check_pid_file_process_alive(self, mock_pid_exists, agent):
        """Test checking PID file when process is alive"""
        mock_pid_exists.return_value = True

        # Create PID file with valid PID
        test_pid = 12345
        agent.pid_file_path.write_text(str(test_pid))

        result = agent._check_pid_file()
        assert result == test_pid
        assert agent.pid_file_path.exists()  # Should not be removed

    @patch("psutil.pid_exists")
    def test_check_pid_file_process_dead(self, mock_pid_exists, agent):
        """Test checking PID file when process is dead"""
        mock_pid_exists.return_value = False

        # Create PID file with valid PID
        test_pid = 12345
        agent.pid_file_path.write_text(str(test_pid))

        result = agent._check_pid_file()
        assert result is None
        assert not agent.pid_file_path.exists()  # Should be removed

    def test_write_pid_file_success(self, agent):
        """Test writing PID file successfully"""
        result = agent._write_pid_file()
        assert result is True
        assert agent.pid_file_path.exists()
        assert agent.pid_file_path.read_text().strip() == str(agent.pid)

    def test_write_pid_file_failure(self, agent):
        """Test writing PID file when it fails"""
        # Mock the open to raise an exception
        with patch("builtins.open", side_effect=OSError("Permission denied")):
            result = agent._write_pid_file()
            assert result is False

    def test_remove_pid_file_exists(self, agent):
        """Test removing PID file when it exists"""
        # Create PID file
        agent.pid_file_path.write_text("12345")
        assert agent.pid_file_path.exists()

        agent._remove_pid_file()
        assert not agent.pid_file_path.exists()

    def test_remove_pid_file_not_exists(self, agent):
        """Test removing PID file when it doesn't exist"""
        assert not agent.pid_file_path.exists()

        # Should not raise an exception
        agent._remove_pid_file()
        assert not agent.pid_file_path.exists()


class TestMonitoringAgentStatus:
    """Test status checking functionality"""

    @pytest.fixture
    def temp_logs_dir(self):
        """Create temporary logs directory"""
        with tempfile.TemporaryDirectory() as temp_dir:
            logs_dir = Path(temp_dir) / "logs"
            logs_dir.mkdir()
            yield logs_dir

    @pytest.fixture
    def agent(self, temp_logs_dir):
        """Create monitoring agent with temporary logs directory"""
        with patch("monitoring.monitoring_agent.Path") as mock_path:
            mock_path.return_value = temp_logs_dir / "monitor.pid"
            agent = MonitoringAgent()
            agent.pid_file_path = temp_logs_dir / "monitor.pid"
            yield agent

    @patch("psutil.pid_exists")
    def test_get_status_running_pid_file(self, mock_pid_exists, agent):
        """Test getting status when process is running and PID file exists"""
        mock_pid_exists.return_value = True

        # Create PID file
        test_pid = 12345
        agent.pid_file_path.write_text(str(test_pid))

        status = agent.get_status()

        assert status["status"] == "running"
        assert status["pid"] == test_pid
        assert status["message"] == f"Monitoring running (PID {test_pid})"
        assert status["source"] == "pid_file"

    @patch("psutil.pid_exists")
    def test_get_status_not_running_pid_file(self, mock_pid_exists, agent):
        """Test getting status when PID file exists but process is dead"""
        mock_pid_exists.return_value = False

        # Create PID file
        test_pid = 12345
        agent.pid_file_path.write_text(str(test_pid))

        status = agent.get_status()

        assert status["status"] == "not_running"
        assert status["pid"] is None
        assert status["message"] == "Monitoring not running"
        assert status["source"] == "process_scan"

    @patch("psutil.process_iter")
    def test_get_status_running_process_scan_single(self, mock_process_iter, agent):
        """Test getting status when no PID file but single process found"""
        # Mock process_iter to return one monitoring process
        mock_process = MagicMock()
        mock_process.pid = 12345
        mock_process.info = {
            "pid": 12345,
            "name": "python",
            "cmdline": ["python", "-m", "monitoring.monitoring_agent"],
        }
        mock_process_iter.return_value = [mock_process]

        status = agent.get_status()

        assert status["status"] == "running"
        assert status["pid"] == 12345
        assert status["message"] == "Monitoring running (PID 12345) (recovered)"
        assert status["source"] == "process_scan_recovered"
        assert agent.pid_file_path.exists()  # PID file should be recreated

    @patch("psutil.process_iter")
    def test_get_status_running_process_scan_multiple(self, mock_process_iter, agent):
        """Test getting status when no PID file but multiple processes found"""
        # Mock process_iter to return multiple monitoring processes
        mock_process1 = MagicMock()
        mock_process1.pid = 12345
        mock_process1.info = {
            "pid": 12345,
            "name": "python",
            "cmdline": ["python", "-m", "monitoring.monitoring_agent"],
        }
        mock_process2 = MagicMock()
        mock_process2.pid = 67890
        mock_process2.info = {
            "pid": 67890,
            "name": "python",
            "cmdline": ["python", "-m", "monitoring.monitoring_agent"],
        }
        mock_process_iter.return_value = [mock_process1, mock_process2]

        status = agent.get_status()

        assert status["status"] == "multiple_processes"
        assert status["pid"] is None
        assert (
            status["message"]
            == "Multiple monitoring processes detected: PIDs [12345, 67890]"
        )
        assert status["source"] == "process_scan"
        assert status["pids"] == [12345, 67890]

    @patch("psutil.process_iter")
    def test_get_status_not_running_process_scan(self, mock_process_iter, agent):
        """Test getting status when no PID file and no processes found"""
        # Mock process_iter to return no processes
        mock_process_iter.return_value = []

        status = agent.get_status()

        assert status["status"] == "not_running"
        assert status["pid"] is None
        assert status["message"] == "Monitoring not running"
        assert status["source"] == "process_scan"

    def test_get_status_error(self, agent):
        """Test getting status when an error occurs"""
        # Mock _check_pid_file to raise an exception
        with patch.object(
            agent, "_check_pid_file", side_effect=Exception("Test error")
        ):
            status = agent.get_status()

            assert status["status"] == "error"
            assert status["pid"] is None
            assert "Test error" in status["message"]
            assert status["source"] == "error"


class TestMonitoringAgentStartup:
    """Test startup functionality"""

    @pytest.fixture
    def temp_logs_dir(self):
        """Create temporary logs directory"""
        with tempfile.TemporaryDirectory() as temp_dir:
            logs_dir = Path(temp_dir) / "logs"
            logs_dir.mkdir()
            yield logs_dir

    @pytest.fixture
    def agent(self, temp_logs_dir):
        """Create monitoring agent with temporary logs directory"""
        with patch("monitoring.monitoring_agent.Path") as mock_path:
            mock_path.return_value = temp_logs_dir / "monitor.pid"
            agent = MonitoringAgent()
            agent.pid_file_path = temp_logs_dir / "monitor.pid"
            yield agent

    @patch("psutil.pid_exists")
    async def test_start_already_running(self, mock_pid_exists, agent):
        """Test starting when another instance is already running"""
        mock_pid_exists.return_value = True

        # Create PID file for existing process
        test_pid = 12345
        agent.pid_file_path.write_text(str(test_pid))

        await agent.start()

        assert not agent.is_running  # Should not start
        assert not agent.monitoring_task  # Should not create monitoring task

    @patch("psutil.pid_exists")
    async def test_start_success(self, mock_pid_exists, agent):
        """Test starting successfully when no other instance is running"""
        mock_pid_exists.return_value = False

        await agent.start()

        assert agent.is_running
        assert agent.monitoring_task is not None
        assert agent.pid_file_path.exists()
        assert agent.pid_file_path.read_text().strip() == str(agent.pid)

    async def test_start_pid_file_write_failure(self, agent):
        """Test starting when PID file write fails"""
        # Mock _write_pid_file to return False
        with patch.object(agent, "_write_pid_file", return_value=False):
            await agent.start()

            assert not agent.is_running  # Should not start
            assert not agent.monitoring_task  # Should not create monitoring task

    async def test_stop_success(self, agent):
        """Test stopping successfully"""
        # Start the agent first
        agent.is_running = True
        agent.monitoring_task = MagicMock()

        await agent.stop()

        assert not agent.is_running
        assert not agent.pid_file_path.exists()  # PID file should be removed

    async def test_stop_not_running(self, agent):
        """Test stopping when not running"""
        agent.is_running = False

        # Should not raise an exception
        await agent.stop()

        assert not agent.is_running
