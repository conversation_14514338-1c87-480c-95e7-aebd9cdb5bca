"""
Test Enhanced Code Generator

Comprehensive tests for the enhanced code generation system.
Ensures 100% test success as required by cursor rules.

Phase 19 Implementation - Enhanced Code Generation
"""

import asyncio
import json
from pathlib import Path
from typing import Any, Dict

import pytest

from code_generation.bug_detector import BugDetector
from code_generation.code_analyzer import CodeAnalyzer
from code_generation.code_optimizer import CodeOptimizer
from code_generation.deepseek_integration import DeepseekIntegration
from code_generation.editor_integration import EditorIntegration
from code_generation.enhanced_code_generator import (
    CodeGenerationRequest,
    CodeGenerationResponse,
    EnhancedCodeGenerator,
)
from code_generation.pattern_recognizer import PatternRecognizer


class TestEnhancedCodeGenerator:
    """Test cases for the Enhanced Code Generator."""

    @pytest.fixture
    def config(self) -> Dict[str, Any]:
        """Load test configuration."""
        config_path = Path("config/enhanced_code_generation_config.json")
        if config_path.exists():
            with open(config_path, "r") as f:
                return json.load(f)
        return {
            "enabled": True,
            "version": "1.0.0",
            "model": {
                "name": "deepseek-coder:1.3b",
                "temperature": 0.3,
                "max_tokens": 2048,
                "top_p": 0.95,
            },
        }

    @pytest.fixture
    def generator(self, config) -> EnhancedCodeGenerator:
        """Create enhanced code generator instance."""
        return EnhancedCodeGenerator(config)

    @pytest.fixture
    def sample_python_code(self) -> str:
        """Sample Python code for testing."""
        return """
def calculate_factorial(n):
    if n <= 1:
        return 1
    return n * calculate_factorial(n - 1)

result = calculate_factorial(5)
print(result)
"""

    @pytest.fixture
    def sample_javascript_code(self) -> str:
        """Sample JavaScript code for testing."""
        return """
function calculateSum(numbers) {
    let sum = 0;
    for (let i = 0; i < numbers.length; i++) {
        sum += numbers[i];
    }
    return sum;
}

const result = calculateSum([1, 2, 3, 4, 5]);
console.log(result);
"""

    def test_enhanced_code_generator_initialization(self, generator):
        """Test enhanced code generator initialization."""
        assert generator is not None
        assert generator.config is not None
        assert generator.deepseek_integration is not None
        assert generator.code_analyzer is not None
        assert generator.pattern_recognizer is not None
        assert generator.bug_detector is not None
        assert generator.code_optimizer is not None
        assert generator.editor_integration is not None

    def test_default_config_loading(self):
        """Test default configuration loading."""
        generator = EnhancedCodeGenerator()
        config = generator.config

        assert config["enabled"] is True
        assert config["version"] == "1.0.0"
        assert "model" in config
        assert "features" in config
        assert "languages" in config
        assert "editor_integration" in config

    @pytest.mark.asyncio
    async def test_generate_code_completion_python(self, generator, sample_python_code):
        """Test code completion for Python."""
        request = CodeGenerationRequest(
            code=sample_python_code,
            language="python",
            context="Calculate factorial function",
            cursor_position=len(sample_python_code),
        )

        response = await generator.generate_code_completion(request)

        assert isinstance(response, CodeGenerationResponse)
        assert response.generated_code is not None
        assert isinstance(response.suggestions, list)
        assert isinstance(response.confidence, float)
        assert 0.0 <= response.confidence <= 1.0
        assert isinstance(response.analysis_results, dict)
        assert isinstance(response.optimization_suggestions, list)
        assert isinstance(response.pattern_insights, list)
        assert isinstance(response.execution_time, float)
        assert response.execution_time >= 0.0

    @pytest.mark.asyncio
    async def test_generate_code_completion_javascript(
        self, generator, sample_javascript_code
    ):
        """Test code completion for JavaScript."""
        request = CodeGenerationRequest(
            code=sample_javascript_code,
            language="javascript",
            context="Calculate sum of array",
            cursor_position=len(sample_javascript_code),
        )

        response = await generator.generate_code_completion(request)

        assert isinstance(response, CodeGenerationResponse)
        assert response.generated_code is not None
        assert isinstance(response.suggestions, list)
        assert isinstance(response.confidence, float)
        assert 0.0 <= response.confidence <= 1.0

    @pytest.mark.asyncio
    async def test_detect_and_fix_bugs_python(self, generator, sample_python_code):
        """Test bug detection and fixing for Python."""
        request = CodeGenerationRequest(code=sample_python_code, language="python")

        result = await generator.detect_and_fix_bugs(request)

        assert isinstance(result, dict)
        assert "bugs_detected" in result
        assert "bugs" in result
        assert "fixes" in result
        assert "auto_fix_applied" in result
        assert isinstance(result["bugs_detected"], int)
        assert isinstance(result["bugs"], list)
        assert isinstance(result["fixes"], list)
        assert isinstance(result["auto_fix_applied"], int)

    @pytest.mark.asyncio
    async def test_optimize_code_python(self, generator, sample_python_code):
        """Test code optimization for Python."""
        request = CodeGenerationRequest(code=sample_python_code, language="python")

        result = await generator.optimize_code(request)

        assert isinstance(result, dict)
        assert "original_code" in result
        assert "optimized_code" in result
        assert "optimization_suggestions" in result
        assert "performance_improvements" in result
        assert "confidence" in result
        assert isinstance(result["original_code"], str)
        assert isinstance(result["optimized_code"], str)
        assert isinstance(result["optimization_suggestions"], list)
        assert isinstance(result["performance_improvements"], dict)
        assert isinstance(result["confidence"], float)

    @pytest.mark.asyncio
    async def test_recognize_patterns_python(self, generator, sample_python_code):
        """Test pattern recognition for Python."""
        request = CodeGenerationRequest(
            code=sample_python_code,
            language="python",
            project_context={"project_id": "test_project"},
        )

        result = await generator.recognize_patterns(request)

        assert isinstance(result, dict)
        assert "patterns_found" in result
        assert "patterns" in result
        assert "insights" in result
        assert "best_practices" in result
        assert "anti_patterns" in result
        assert isinstance(result["patterns_found"], int)
        assert isinstance(result["patterns"], list)
        assert isinstance(result["insights"], list)
        assert isinstance(result["best_practices"], list)
        assert isinstance(result["anti_patterns"], list)

    @pytest.mark.asyncio
    async def test_get_editor_suggestions(self, generator, sample_python_code):
        """Test editor suggestions."""
        request = CodeGenerationRequest(
            code=sample_python_code,
            language="python",
            cursor_position=len(sample_python_code),
            file_path="test.py",
        )

        result = await generator.get_editor_suggestions(request)

        assert isinstance(result, dict)
        assert "suggestions" in result
        assert "quick_fixes" in result
        assert "refactoring_opportunities" in result
        assert "inline_completions" in result
        assert "diagnostics" in result

    def test_health_status(self, generator):
        """Test health status reporting."""
        health = generator.get_health_status()

        assert isinstance(health, dict)
        assert "status" in health
        assert "version" in health
        assert "components" in health
        assert "features_enabled" in health
        assert health["status"] == "healthy"
        assert health["version"] == "1.0.0"
        assert isinstance(health["components"], dict)
        assert isinstance(health["features_enabled"], dict)


class TestDeepseekIntegration:
    """Test cases for Deepseek Integration."""

    def test_deepseek_integration_initialization(self):
        """Test deepseek integration initialization."""
        integration = DeepseekIntegration()

        assert integration is not None
        assert integration.config is not None
        assert integration.model_loaded is False
        assert integration.health_status == "initializing"

    @pytest.mark.asyncio
    async def test_initialize_model(self):
        """Test model initialization."""
        integration = DeepseekIntegration()

        result = await integration.initialize_model()

        assert isinstance(result, bool)
        assert result is True
        assert integration.model_loaded is True
        assert integration.health_status == "healthy"

    @pytest.mark.asyncio
    async def test_generate_completion(self):
        """Test code completion generation."""
        integration = DeepseekIntegration()
        await integration.initialize_model()

        result = await integration.generate_completion(
            code="def hello_world():",
            language="python",
            context="Simple greeting function",
            cursor_position=20,
        )

        assert isinstance(result, dict)
        assert "generated_code" in result
        assert "suggestions" in result
        assert "confidence" in result
        assert "language" in result
        assert "context_used" in result
        assert isinstance(result["generated_code"], str)
        assert isinstance(result["suggestions"], list)
        assert isinstance(result["confidence"], float)
        assert result["language"] == "python"
        assert result["context_used"] is True

    def test_health_status(self):
        """Test health status reporting."""
        integration = DeepseekIntegration()
        health = integration.get_health_status()

        assert isinstance(health, dict)
        assert "status" in health
        assert "model_loaded" in health
        assert "model_name" in health
        assert "temperature" in health
        assert "max_tokens" in health
        assert "top_p" in health


class TestCodeAnalyzer:
    """Test cases for Code Analyzer."""

    def test_code_analyzer_initialization(self):
        """Test code analyzer initialization."""
        analyzer = CodeAnalyzer()

        assert analyzer is not None
        assert analyzer.config is not None
        assert analyzer.supported_languages is not None
        assert analyzer.health_status == "healthy"

    @pytest.mark.asyncio
    async def test_analyze_python_code(self):
        """Test Python code analysis."""
        analyzer = CodeAnalyzer()

        code = """
def factorial(n):
    if n <= 1:
        return 1
    return n * factorial(n - 1)
"""

        result = await analyzer.analyze_code(code, "python")

        assert isinstance(result, dict)
        assert "language" in result
        assert "metrics" in result
        assert "issues" in result
        assert "suggestions" in result
        assert "analysis_time" in result
        assert result["language"] == "python"
        assert isinstance(result["metrics"], dict)
        assert isinstance(result["issues"], list)
        assert isinstance(result["suggestions"], list)
        assert isinstance(result["analysis_time"], float)

    def test_health_status(self):
        """Test health status reporting."""
        analyzer = CodeAnalyzer()
        health = analyzer.get_health_status()

        assert isinstance(health, dict)
        assert "status" in health
        assert "supported_languages" in health
        assert "config" in health
        assert health["status"] == "healthy"


class TestPatternRecognizer:
    """Test cases for Pattern Recognizer."""

    def test_pattern_recognizer_initialization(self):
        """Test pattern recognizer initialization."""
        recognizer = PatternRecognizer()

        assert recognizer is not None
        assert recognizer.config is not None
        assert recognizer.supported_languages is not None
        assert recognizer.health_status == "healthy"

    @pytest.mark.asyncio
    async def test_recognize_patterns(self):
        """Test pattern recognition."""
        recognizer = PatternRecognizer()

        code = """
def process_data(data):
    result = []
    for item in data:
        if item > 0:
            result.append(item * 2)
    return result
"""

        result = await recognizer.recognize_patterns(code, "python")

        assert isinstance(result, dict)
        assert "patterns" in result
        assert "insights" in result
        assert "best_practices" in result
        assert "anti_patterns" in result
        assert "design_patterns" in result
        assert "custom_patterns" in result
        assert "pattern_count" in result
        assert "learning_data" in result

    def test_health_status(self):
        """Test health status reporting."""
        recognizer = PatternRecognizer()
        health = recognizer.get_health_status()

        assert isinstance(health, dict)
        assert "status" in health
        assert "supported_languages" in health
        assert "learned_patterns_count" in health
        assert "config" in health
        assert health["status"] == "healthy"


class TestBugDetector:
    """Test cases for Bug Detector."""

    def test_bug_detector_initialization(self):
        """Test bug detector initialization."""
        detector = BugDetector()

        assert detector is not None
        assert detector.config is not None
        assert detector.supported_languages is not None
        assert detector.health_status == "healthy"

    @pytest.mark.asyncio
    async def test_detect_bugs(self):
        """Test bug detection."""
        detector = BugDetector()

        code = """
def divide_numbers(a, b):
    return a / b

result = divide_numbers(10, 0)
"""

        result = await detector.detect_bugs(code, "python")

        assert isinstance(result, dict)
        assert "bugs" in result
        assert "total_bugs" in result
        assert "critical_count" in result
        assert "high_count" in result
        assert "medium_count" in result
        assert "low_count" in result
        assert "security_issues" in result
        assert "performance_issues" in result
        assert "code_quality_issues" in result
        assert "language" in result

    def test_health_status(self):
        """Test health status reporting."""
        detector = BugDetector()
        health = detector.get_health_status()

        assert isinstance(health, dict)
        assert "status" in health
        assert "supported_languages" in health
        assert "bug_patterns_count" in health
        assert "security_patterns_count" in health
        assert "performance_patterns_count" in health
        assert "config" in health
        assert health["status"] == "healthy"


class TestCodeOptimizer:
    """Test cases for Code Optimizer."""

    def test_code_optimizer_initialization(self):
        """Test code optimizer initialization."""
        optimizer = CodeOptimizer()

        assert optimizer is not None
        assert optimizer.config is not None
        assert optimizer.supported_languages is not None
        assert optimizer.health_status == "healthy"

    @pytest.mark.asyncio
    async def test_optimize_code(self):
        """Test code optimization."""
        optimizer = CodeOptimizer()

        code = """
def sum_numbers(numbers):
    total = 0
    for i in range(len(numbers)):
        total = total + numbers[i]
    return total
"""

        result = await optimizer.optimize_code(code, "python")

        assert isinstance(result, dict)
        assert "suggestions" in result
        assert "performance_score" in result
        assert "memory_score" in result
        assert "readability_score" in result
        assert "maintainability_score" in result
        assert "overall_score" in result
        assert "improvements" in result
        assert "total_suggestions" in result
        assert "high_priority_count" in result
        assert "medium_priority_count" in result
        assert "low_priority_count" in result
        assert "language" in result

    def test_health_status(self):
        """Test health status reporting."""
        optimizer = CodeOptimizer()
        health = optimizer.get_health_status()

        assert isinstance(health, dict)
        assert "status" in health
        assert "supported_languages" in health
        assert "performance_patterns_count" in health
        assert "memory_patterns_count" in health
        assert "readability_patterns_count" in health
        assert "config" in health
        assert health["status"] == "healthy"


class TestEditorIntegration:
    """Test cases for Editor Integration."""

    def test_editor_integration_initialization(self):
        """Test editor integration initialization."""
        integration = EditorIntegration()

        assert integration is not None
        assert integration.config is not None
        assert integration.supported_editors is not None
        assert integration.supported_languages is not None
        assert integration.health_status == "healthy"

    @pytest.mark.asyncio
    async def test_get_suggestions(self):
        """Test getting editor suggestions."""
        integration = EditorIntegration()

        code = """
def calculate_area(length, width):
    area = length * width
    return area
"""

        result = await integration.get_suggestions(
            code=code,
            language="python",
            cursor_position=len(code),
            file_path="test.py",
            editor_type="vscode",
        )

        assert isinstance(result, dict)
        assert "suggestions" in result
        assert "quick_fixes" in result
        assert "refactoring_opportunities" in result
        assert "inline_completions" in result
        assert "diagnostics" in result
        assert "total_suggestions" in result
        assert "total_quick_fixes" in result
        assert "total_refactoring_opportunities" in result
        assert "language" in result
        assert "editor_type" in result
        assert "file_path" in result

    def test_health_status(self):
        """Test health status reporting."""
        integration = EditorIntegration()
        health = integration.get_health_status()

        assert isinstance(health, dict)
        assert "status" in health
        assert "supported_editors" in health
        assert "supported_languages" in health
        assert "suggestion_cache_size" in health
        assert "config" in health
        assert health["status"] == "healthy"


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
