#!/usr/bin/env python3
"""
Testing Harness - Phase 2.5
Comprehensive testing framework for AI Coding Agent project.
"""

import asyncio
import json
import logging
import os
import shutil
import sqlite3
import time
from dataclasses import dataclass
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

import requests

try:
    import playwright.async_api

    playwright_available = True
except ImportError:
    logging.warning("Playwright not installed. Install with: pip install playwright")
    playwright_available = False

# Configure logging
logger = logging.getLogger(__name__)


# Adapt datetime for SQLite
def adapt_datetime(dt: datetime) -> str:
    """Adapt datetime for SQLite storage"""
    return dt.isoformat()


def convert_datetime(s: str) -> datetime:
    """Convert string back to datetime"""
    return datetime.fromisoformat(s)


@dataclass
class TestResult:
    """Test result data structure"""

    test_name: str
    test_type: str
    status: str  # 'passed', 'failed', 'skipped', 'error'
    duration: float
    timestamp: datetime
    details: Dict[str, Any]
    error_message: Optional[str] = None
    screenshots: Optional[List[str]] = None
    logs: Optional[List[str]] = None


@dataclass
class TestConfig:
    """Test configuration"""

    test_timeout: int = 300
    screenshot_on_failure: bool = True
    headless: bool = True
    slow_mo: int = 100
    retry_count: int = 3
    parallel_tests: int = 4
    validation_rules: Optional[Dict[str, Any]] = None
    rollback_conditions: Optional[Dict[str, Any]] = None


from agent.core.validators.css_validator import CSSValidator

# Import unified validators from core/validators
from agent.core.validators.html_validator import HTMLValidator


class PlaywrightTester:
    """Playwright-based browser testing"""

    def __init__(self, config: TestConfig):
        self.config = config
        self.page: Optional[Any] = None
        self.browser: Optional[Any] = None
        self.playwright: Optional[Any] = None

    async def setup(self) -> bool:
        """Setup Playwright resources"""
        try:
            if not playwright_available:
                return False
            # self.playwright = await playwright.async_api.async_playwright().start()
            # self.browser = await self.playwright.chromium.launch(headless=self.config.headless, slow_mo=self.config.slow_mo)
            # self.page = await self.browser.new_page()
            return True
        except ImportError:
            logging.warning(
                "Playwright not installed. Install with: pip install playwright"
            )
            return False

    async def teardown(self) -> None:
        """Cleanup Playwright resources"""
        if self.page:
            try:
                await self.page.close()
            except Exception:
                pass
        if self.browser:
            try:
                await self.browser.close()
            except Exception:
                pass
        return None

    async def test_page_load(self, url: str) -> TestResult:
        """Test page loading and basic functionality"""
        start_time = time.time()
        test_name = f"Page_Load_{url.replace('://', '_').replace('/', '_')}"

        try:
            if not self.page:
                return TestResult(
                    test_name=test_name,
                    test_type="playwright",
                    status="skipped",
                    duration=0,
                    timestamp=datetime.now(),
                    details={"url": url},
                    error_message="Playwright not available",
                )

            # Navigate to page
            await self.page.goto(url, timeout=self.config.test_timeout * 1000)

            # Wait for page to load
            await self.page.wait_for_load_state("networkidle")

            # Basic checks
            title = await self.page.title()
            if not title:
                raise Exception("Page has no title")

            # Check for console errors
            console_errors: List[str] = []
            # This would require setting up console event listeners

            # Take screenshot if configured
            screenshots = []
            if self.config.screenshot_on_failure:
                screenshot_path = f"test_screenshots/{test_name}_{int(time.time())}.png"
                os.makedirs("test_screenshots", exist_ok=True)
                await self.page.screenshot(path=screenshot_path)
                screenshots.append(screenshot_path)

            duration = time.time() - start_time

            return TestResult(
                test_name=test_name,
                test_type="playwright",
                status="passed",
                duration=duration,
                timestamp=datetime.now(),
                details={"url": url, "title": title, "console_errors": console_errors},
                screenshots=screenshots,
            )

        except Exception as e:
            duration = time.time() - start_time

            # Take error screenshot
            error_screenshots: List[str] = []
            if self.config.screenshot_on_failure and self.page:
                try:
                    screenshot_path = (
                        f"test_screenshots/{test_name}_error_{int(time.time())}.png"
                    )
                    os.makedirs("test_screenshots", exist_ok=True)
                    await self.page.screenshot(path=screenshot_path)
                    error_screenshots.append(screenshot_path)
                except Exception:
                    pass

            return TestResult(
                test_name=test_name,
                test_type="playwright",
                status="failed",
                duration=duration,
                timestamp=datetime.now(),
                details={"url": url},
                error_message=str(e),
                screenshots=error_screenshots,
            )


class DeploymentRollbackTester:
    """Deployment rollback testing and validation"""

    def __init__(self, config: TestConfig):
        self.config = config
        self.rollback_conditions = (
            config.rollback_conditions or self._default_rollback_conditions()
        )

    def _default_rollback_conditions(self) -> Dict[str, Any]:
        """Default rollback conditions"""
        return {
            "max_load_time": 5.0,  # seconds
            "max_error_rate": 0.05,  # 5%
            "required_status_codes": [200, 201, 202],
            "health_check_endpoints": ["/", "/health", "/api/status"],
            "rollback_threshold": 3,  # failed checks before rollback
            "monitoring_duration": 300,  # seconds to monitor after deployment
        }

    def test_deployment_health(self, deployment_url: str) -> TestResult:
        """Test deployment health and performance"""
        start_time = time.time()
        test_name = (
            f"Deployment_Health_{deployment_url.replace('://', '_').replace('/', '_')}"
        )

        try:
            health_checks = []
            error_count = 0

            # Test each health check endpoint
            for endpoint in self.rollback_conditions["health_check_endpoints"]:
                url = f"{deployment_url.rstrip('/')}{endpoint}"

                try:
                    response = requests.get(url, timeout=10)
                    load_time = response.elapsed.total_seconds()

                    health_checks.append(
                        {
                            "endpoint": endpoint,
                            "status_code": response.status_code,
                            "load_time": load_time,
                            "success": response.status_code
                            in self.rollback_conditions["required_status_codes"],
                        }
                    )

                    if (
                        response.status_code
                        not in self.rollback_conditions["required_status_codes"]
                    ):
                        error_count += 1

                    if load_time > self.rollback_conditions["max_load_time"]:
                        error_count += 1

                except Exception as e:
                    health_checks.append(
                        {
                            "endpoint": endpoint,
                            "status_code": None,
                            "load_time": None,
                            "success": False,
                            "error": str(e),
                        }
                    )
                    error_count += 1

            # Determine if rollback is needed
            total_checks = len(health_checks)
            error_rate = error_count / total_checks if total_checks > 0 else 1.0

            should_rollback = (
                error_rate > self.rollback_conditions["max_error_rate"]
                or error_count >= self.rollback_conditions["rollback_threshold"]
            )

            status = "failed" if should_rollback else "passed"
            duration = time.time() - start_time

            return TestResult(
                test_name=test_name,
                test_type="deployment_health",
                status=status,
                duration=duration,
                timestamp=datetime.now(),
                details={
                    "deployment_url": deployment_url,
                    "health_checks": health_checks,
                    "error_count": error_count,
                    "error_rate": error_rate,
                    "should_rollback": should_rollback,
                    "total_checks": total_checks,
                },
                error_message=(
                    f"Rollback recommended: {error_count}/{total_checks} checks failed"
                    if should_rollback
                    else None
                ),
            )

        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                test_name=test_name,
                test_type="deployment_health",
                status="error",
                duration=duration,
                timestamp=datetime.now(),
                details={"deployment_url": deployment_url},
                error_message=str(e),
            )

    def test_rollback_procedure(self, deployment_id: str) -> TestResult:
        """Test rollback procedure"""
        start_time = time.time()
        test_name = f"Rollback_Procedure_{deployment_id}"

        try:
            # This would integrate with your deployment system
            # For now, we'll simulate a rollback test

            # Simulate rollback steps
            rollback_steps = [
                "Stop current deployment",
                "Restore previous version",
                "Update DNS/load balancer",
                "Verify rollback success",
                "Notify stakeholders",
            ]

            # Simulate rollback execution
            successful_steps = []
            failed_steps = []

            for step in rollback_steps:
                # Simulate step execution
                if "Stop" in step or "Restore" in step:
                    # Simulate potential failure
                    if "current" in step.lower():
                        failed_steps.append(step)
                    else:
                        successful_steps.append(step)
                else:
                    successful_steps.append(step)

            status = "passed" if not failed_steps else "failed"
            duration = time.time() - start_time

            return TestResult(
                test_name=test_name,
                test_type="rollback_procedure",
                status=status,
                duration=duration,
                timestamp=datetime.now(),
                details={
                    "deployment_id": deployment_id,
                    "successful_steps": successful_steps,
                    "failed_steps": failed_steps,
                    "total_steps": len(rollback_steps),
                },
                error_message=(
                    f"Rollback failed: {', '.join(failed_steps)}"
                    if failed_steps
                    else None
                ),
            )

        except Exception as e:
            duration = time.time() - start_time
            return TestResult(
                test_name=test_name,
                test_type="rollback_procedure",
                status="error",
                duration=duration,
                timestamp=datetime.now(),
                details={"deployment_id": deployment_id},
                error_message=str(e),
            )


class TestDatabase:
    """Database for storing test results"""

    def __init__(self, db_path: str = "test_results.db"):
        self.db_path = db_path
        self._init_database()

    def _init_database(self):
        """Initialize test results database"""
        conn = sqlite3.connect(self.db_path, detect_types=sqlite3.PARSE_DECLTYPES)
        cursor = conn.cursor()

        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS test_results (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                test_name TEXT NOT NULL,
                test_type TEXT NOT NULL,
                status TEXT NOT NULL,
                duration REAL NOT NULL,
                timestamp TEXT NOT NULL,
                details TEXT,
                error_message TEXT,
                screenshots TEXT,
                logs TEXT
            )
        """
        )

        cursor.execute(
            """
            CREATE TABLE IF NOT EXISTS test_sessions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                session_name TEXT NOT NULL,
                start_time TEXT NOT NULL,
                end_time TEXT,
                total_tests INTEGER DEFAULT 0,
                passed_tests INTEGER DEFAULT 0,
                failed_tests INTEGER DEFAULT 0,
                skipped_tests INTEGER DEFAULT 0,
                error_tests INTEGER DEFAULT 0
            )
        """
        )

        conn.commit()
        conn.close()

    def save_test_result(self, result: TestResult):
        """Save test result to database"""
        conn = sqlite3.connect(self.db_path, detect_types=sqlite3.PARSE_DECLTYPES)
        cursor = conn.cursor()

        cursor.execute(
            """
            INSERT INTO test_results
            (test_name, test_type, status, duration, timestamp, details, error_message, screenshots, logs)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
        """,
            (
                result.test_name,
                result.test_type,
                result.status,
                result.duration,
                result.timestamp.isoformat(),
                json.dumps(result.details),
                result.error_message,
                json.dumps(result.screenshots) if result.screenshots else None,
                json.dumps(result.logs) if result.logs else None,
            ),
        )

        conn.commit()
        conn.close()

    def get_test_results(self, limit: int = 100) -> List[Dict]:
        """Get recent test results"""
        conn = sqlite3.connect(self.db_path, detect_types=sqlite3.PARSE_DECLTYPES)
        cursor = conn.cursor()

        cursor.execute(
            """
            SELECT * FROM test_results
            ORDER BY timestamp DESC
            LIMIT ?
        """,
            (limit,),
        )

        results = []
        for row in cursor.fetchall():
            results.append(
                {
                    "id": row[0],
                    "test_name": row[1],
                    "test_type": row[2],
                    "status": row[3],
                    "duration": row[4],
                    "timestamp": row[5],
                    "details": json.loads(row[6]) if row[6] else {},
                    "error_message": row[7],
                    "screenshots": json.loads(row[8]) if row[8] else [],
                    "logs": json.loads(row[9]) if row[9] else [],
                }
            )

        conn.close()
        return results


class TestingHarness:
    """Main testing harness orchestrator"""

    def __init__(self, config: Optional[TestConfig] = None):
        self.config = config or TestConfig()
        self.html_validator = HTMLValidator(self.config)
        self.css_validator = CSSValidator(self.config)
        self.playwright_tester = PlaywrightTester(self.config)
        self.rollback_tester = DeploymentRollbackTester(self.config)
        self.test_db = TestDatabase()
        self.results: List[Dict[str, Any]] = []

    async def run_html_validation(self, directory: str) -> List[TestResult]:
        """Run HTML validation on all HTML files in directory"""
        results = []
        html_files = list(Path(directory).rglob("*.html"))

        for html_file in html_files:
            result = self.html_validator.validate_html_file(str(html_file))
            results.append(result)
            self.test_db.save_test_result(result)

        return results

    async def run_css_validation(self, directory: str) -> List[TestResult]:
        """Run CSS validation on all CSS files in directory"""
        results = []
        css_files = list(Path(directory).rglob("*.css"))

        for css_file in css_files:
            result = self.css_validator.validate_css_file(str(css_file))
            results.append(result)
            self.test_db.save_test_result(result)

        return results

    async def run_playwright_tests(self, urls: List[str]) -> List[TestResult]:
        """Run Playwright tests on URLs"""
        results = []

        # Setup Playwright
        await self.playwright_tester.setup()

        try:
            for url in urls:
                result = await self.playwright_tester.test_page_load(url)
                results.append(result)
                self.test_db.save_test_result(result)
        finally:
            await self.playwright_tester.teardown()

        return results

    async def run_deployment_tests(
        self, deployment_url: str, deployment_id: Optional[str] = None
    ) -> List[TestResult]:
        """Run deployment health and rollback tests"""
        results = []

        # Test deployment health
        health_result = self.rollback_tester.test_deployment_health(deployment_url)
        results.append(health_result)
        self.test_db.save_test_result(health_result)

        # Test rollback procedure if deployment ID provided
        if deployment_id:
            rollback_result = self.rollback_tester.test_rollback_procedure(
                deployment_id
            )
            results.append(rollback_result)
            self.test_db.save_test_result(rollback_result)

        return results

    async def run_comprehensive_test_suite(
        self, test_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Run comprehensive test suite"""
        start_time = time.time()
        all_results = []

        # HTML validation
        if test_config.get("html_validation", {}).get("enabled", True):
            html_dir = test_config["html_validation"].get("directory", "sites")
            html_results = await self.run_html_validation(html_dir)
            all_results.extend(html_results)

        # CSS validation
        if test_config.get("css_validation", {}).get("enabled", True):
            css_dir = test_config["css_validation"].get("directory", "sites")
            css_results = await self.run_css_validation(css_dir)
            all_results.extend(css_results)

        # Playwright tests
        if test_config.get("playwright_tests", {}).get("enabled", True):
            urls = test_config["playwright_tests"].get("urls", [])
            playwright_results = await self.run_playwright_tests(urls)
            all_results.extend(playwright_results)

        # Deployment tests
        if test_config.get("deployment_tests", {}).get("enabled", True):
            deployment_url = test_config["deployment_tests"].get("url")
            deployment_id = test_config["deployment_tests"].get("deployment_id")
            if deployment_url:
                deployment_results = await self.run_deployment_tests(
                    deployment_url, deployment_id
                )
                all_results.extend(deployment_results)

        # Generate summary
        total_duration = time.time() - start_time
        summary = self._generate_test_summary(all_results, total_duration)

        return {
            "summary": summary,
            "results": all_results,
            "timestamp": datetime.now().isoformat(),
        }

    def _generate_test_summary(
        self, results: List[TestResult], total_duration: float
    ) -> Dict[str, Any]:
        """Generate test summary statistics"""
        total_tests = len(results)
        passed_tests = len([r for r in results if r.status == "passed"])
        failed_tests = len([r for r in results if r.status == "failed"])
        skipped_tests = len([r for r in results if r.status == "skipped"])
        error_tests = len([r for r in results if r.status == "error"])

        avg_duration = (
            sum(r.duration for r in results) / total_tests if total_tests > 0 else 0
        )

        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "skipped_tests": skipped_tests,
            "error_tests": error_tests,
            "success_rate": (
                (passed_tests / total_tests * 100) if total_tests > 0 else 0
            ),
            "total_duration": total_duration,
            "average_duration": avg_duration,
        }

    def generate_test_report(
        self, results: List[TestResult], output_file: str = "test_report.html"
    ):
        """Generate HTML test report"""
        summary = self._generate_test_summary(results, 0)

        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>AI Coding Agent - Test Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; }}
                .summary {{ background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
                .test-result {{ margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }}
                .passed {{ border-left-color: #4CAF50; background: #f1f8e9; }}
                .failed {{ border-left-color: #f44336; background: #ffebee; }}
                .skipped {{ border-left-color: #ff9800; background: #fff3e0; }}
                .error {{ border-left-color: #9c27b0; background: #f3e5f5; }}
                .screenshot {{ max-width: 300px; margin: 10px 0; }}
            </style>
        </head>
        <body>
            <h1>AI Coding Agent - Test Report</h1>
            <div class="summary">
                <h2>Summary</h2>
                <p><strong>Total Tests:</strong> {summary['total_tests']}</p>
                <p><strong>Passed:</strong> {summary['passed_tests']}</p>
                <p><strong>Failed:</strong> {summary['failed_tests']}</p>
                <p><strong>Skipped:</strong> {summary['skipped_tests']}</p>
                <p><strong>Errors:</strong> {summary['error_tests']}</p>
                <p><strong>Success Rate:</strong> {summary['success_rate']:.1f}%</p>
                <p><strong>Average Duration:</strong> {summary['average_duration']:.2f}s</p>
            </div>

            <h2>Test Results</h2>
        """

        for result in results:
            status_class = result.status
            html_content += f"""
            <div class="test-result {status_class}">
                <h3>{result.test_name}</h3>
                <p><strong>Type:</strong> {result.test_type}</p>
                <p><strong>Status:</strong> {result.status}</p>
                <p><strong>Duration:</strong> {result.duration:.2f}s</p>
                <p><strong>Timestamp:</strong> {result.timestamp}</p>
            """

            if result.error_message:
                html_content += f"<p><strong>Error:</strong> {result.error_message}</p>"

            if result.screenshots:
                html_content += "<p><strong>Screenshots:</strong></p>"
                for screenshot in result.screenshots:
                    html_content += f'<img src="{screenshot}" class="screenshot" alt="Test Screenshot">'

            html_content += "</div>"

        html_content += """
        </body>
        </html>
        """

        with open(output_file, "w", encoding="utf-8") as f:
            f.write(html_content)

        return output_file


async def main():
    """Main function for testing harness"""
    # Example usage
    config = TestConfig(
        test_timeout=300, screenshot_on_failure=True, headless=True, retry_count=3
    )

    harness = TestingHarness(config)

    # Example test configuration
    test_config = {
        "html_validation": {"enabled": True, "directory": "sites"},
        "css_validation": {"enabled": True, "directory": "sites"},
        "playwright_tests": {
            "enabled": False,  # Disabled if Playwright not installed
            "urls": ["http://localhost:5000", "http://localhost:8000"],
        },
        "deployment_tests": {
            "enabled": True,
            "url": "http://localhost:5000",
            "deployment_id": "test-deployment-123",
        },
    }

    # Run comprehensive test suite
    results = await harness.run_comprehensive_test_suite(test_config)

    # Generate report
    harness.generate_test_report(results["results"])

    print("Testing completed!")
    print(f"Total tests: {results['summary']['total_tests']}")
    print(f"Passed: {results['summary']['passed_tests']}")
    print(f"Failed: {results['summary']['failed_tests']}")
    print(f"Success rate: {results['summary']['success_rate']:.1f}%")


if __name__ == "__main__":
    asyncio.run(main())
