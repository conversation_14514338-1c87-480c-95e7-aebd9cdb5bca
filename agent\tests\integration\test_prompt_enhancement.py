#!/usr/bin/env python3
"""
Test script to verify prompt enhancement logic in ChatPanel
"""

import json
import time

import requests


def test_prompt_enhancement():
    """Test the prompt enhancement functionality"""

    print("🧪 TESTING PROMPT ENHANCEMENT LOGIC")
    print("=" * 60)

    # Test cases with different enhancement modes
    test_cases = [
        {
            "name": "Code Generation",
            "prompt": "create a website",
            "expected_mode": "code_generation",
            "expected_enhancement": True,
        },
        {
            "name": "Code Modification",
            "prompt": "modify the header",
            "expected_mode": "code_modification",
            "expected_enhancement": True,
        },
        {
            "name": "Content Creation",
            "prompt": "write content for homepage",
            "expected_mode": "content_creation",
            "expected_enhancement": True,
        },
        {
            "name": "Deployment",
            "prompt": "deploy my website",
            "expected_mode": "deployment",
            "expected_enhancement": True,
        },
        {
            "name": "Debugging",
            "prompt": "fix the error",
            "expected_mode": "debugging",
            "expected_enhancement": True,
        },
        {
            "name": "Simple Query",
            "prompt": "hello",
            "expected_mode": "code_generation",  # Default
            "expected_enhancement": False,  # Low confidence expected
        },
    ]

    results = []

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {test_case['name']}")
        print(f"   Input: '{test_case['prompt']}'")
        print(f"   Expected Mode: {test_case['expected_mode']}")

        try:
            # Test the chat endpoint with the prompt
            response = requests.post(
                "http://127.0.0.1:8000/api/v1/chat/test",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": test_case["prompt"],
                    "context": {
                        "currentProject": "Test Project",
                        "activeFiles": ["index.html"],
                        "recentActions": ["Created project"],
                        "conversationHistory": ["Previous message"],
                    },
                    "intent": {
                        "type": "create",
                        "confidence": 0.8,
                        "action": "test",
                        "target": "website",
                    },
                    "history": [
                        {"role": "user", "content": "Previous message"},
                        {"role": "assistant", "content": "Previous response"},
                    ],
                },
                timeout=10,
            )

            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Response received")
                print(f"   📝 Response: {data.get('response', 'No response')}")

                # Check if response shows enhancement
                response_text = data.get("response", "")
                if isinstance(response_text, dict):
                    response_text = response_text.get("content", "")
                response_text = str(response_text).lower()

                enhanced_indicators = [
                    "enhanced",
                    "specific",
                    "detailed",
                    "technical",
                    "html",
                    "css",
                    "javascript",
                    "component",
                    "function",
                ]

                is_enhanced = any(
                    indicator in response_text for indicator in enhanced_indicators
                )
                print(f"   🔍 Enhancement detected: {is_enhanced}")

                results.append(
                    {
                        "test": test_case["name"],
                        "status": "PASS",
                        "enhanced": is_enhanced,
                        "expected_enhanced": test_case["expected_enhancement"],
                    }
                )

            else:
                print(f"   ❌ HTTP {response.status_code}: {response.text}")
                results.append(
                    {
                        "test": test_case["name"],
                        "status": "FAIL",
                        "error": f"HTTP {response.status_code}",
                    }
                )

        except requests.exceptions.Timeout:
            print(f"   ⏰ Timeout - server not responding")
            results.append(
                {
                    "test": test_case["name"],
                    "status": "TIMEOUT",
                    "error": "Request timeout",
                }
            )
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            results.append(
                {"test": test_case["name"], "status": "ERROR", "error": str(e)}
            )

    # Summary
    print(f"\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)

    passed = 0
    failed = 0
    enhanced_correctly = 0

    for result in results:
        if result["status"] == "PASS":
            passed += 1
            if result.get("enhanced") == result.get("expected_enhanced"):
                enhanced_correctly += 1
        else:
            failed += 1

        status_icon = "✅" if result["status"] == "PASS" else "❌"
        print(f"{status_icon} {result['test']}: {result['status']}")
        if result.get("enhanced") is not None:
            print(
                f"   Enhancement: {result['enhanced']} (expected: {result['expected_enhanced']})"
            )

    print(f"\n📈 SUMMARY:")
    print(f"   ✅ Passed: {passed}/{len(results)}")
    print(f"   ❌ Failed: {failed}/{len(results)}")
    print(
        f"   🎯 Enhancement Accuracy: {enhanced_correctly}/{passed}"
        if passed > 0
        else "   🎯 Enhancement Accuracy: N/A"
    )

    if passed == len(results):
        print(f"\n🎉 ALL TESTS PASSED! Prompt enhancement is working correctly.")
    else:
        print(f"\n⚠️  Some tests failed. Check the implementation.")

    return passed == len(results)


def test_auto_enhance_toggle():
    """Test that auto-enhance toggle affects behavior"""

    print(f"\n🔧 TESTING AUTO-ENHANCE TOGGLE BEHAVIOR")
    print("=" * 60)

    # Test with different prompts that should trigger enhancement
    test_prompts = [
        "create a portfolio website",
        "add a contact form",
        "fix the navigation menu",
    ]

    for prompt in test_prompts:
        print(f"\nTesting prompt: '{prompt}'")

        try:
            response = requests.post(
                "http://127.0.0.1:8000/api/v1/chat/test",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": prompt,
                    "context": {"currentProject": "Test"},
                    "intent": {"type": "create"},
                    "history": [],
                },
                timeout=10,
            )

            if response.status_code == 200:
                data = response.json()
                response_text = data.get("response", "")
                if isinstance(response_text, dict):
                    response_text = response_text.get("content", "")
                response_text = str(response_text)

                # Check for enhancement indicators
                enhancement_indicators = [
                    "html",
                    "css",
                    "javascript",
                    "component",
                    "function",
                    "specific",
                    "detailed",
                    "technical",
                    "implementation",
                ]

                enhanced_count = sum(
                    1
                    for indicator in enhancement_indicators
                    if indicator in response_text.lower()
                )

                print(f"   📝 Response length: {len(response_text)} chars")
                print(f"   🔍 Enhancement indicators: {enhanced_count}")
                print(
                    f"   📊 Enhancement level: {'High' if enhanced_count > 3 else 'Medium' if enhanced_count > 1 else 'Low'}"
                )

            else:
                print(f"   ❌ HTTP {response.status_code}")

        except Exception as e:
            print(f"   ❌ Error: {str(e)}")


if __name__ == "__main__":
    print("🚀 Starting Prompt Enhancement Verification")
    print("=" * 60)

    # Check if server is running
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running")
        else:
            print("❌ Backend server not responding properly")
            exit(1)
    except Exception as e:
        print(f"❌ Cannot connect to backend server: {e}")
        print("Please start the backend server first:")
        print(
            "  .\\.venv\\Scripts\\Activate.ps1 && python src/dashboard/minimal_api.py"
        )
        exit(1)

    # Run tests
    success = test_prompt_enhancement()
    test_auto_enhance_toggle()

    print(f"\n" + "=" * 60)
    if success:
        print("🎉 PROMPT ENHANCEMENT VERIFICATION COMPLETE - ALL TESTS PASSED!")
    else:
        print("⚠️  PROMPT ENHANCEMENT VERIFICATION COMPLETE - SOME ISSUES FOUND")
    print("=" * 60)
