#!/usr/bin/env python3
"""
Simple API Integration Test for Advanced Learning System
Tests the API endpoints without starting a full server
"""

import asyncio
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from fastapi.testclient import TestClient

from agent.api.main import app


def test_api_integration():
    """Test API integration using FastAPI TestClient"""
    print("🚀 Simple API Integration Test")
    print("=" * 50)

    try:
        # Create test client
        client = TestClient(app)

        # Test root endpoint
        print("📋 Testing root endpoint...")
        response = client.get("/")
        if response.status_code == 200:
            print("✅ Root endpoint working")
        else:
            print(f"❌ Root endpoint failed: {response.status_code}")
            return False

        # Test health endpoint
        print("📋 Testing health endpoint...")
        response = client.get("/health")
        if response.status_code == 200:
            print("✅ Health endpoint working")
        else:
            print(f"❌ Health endpoint failed: {response.status_code}")
            return False

        # Test advanced learning endpoints
        print("📋 Testing advanced learning endpoints...")

        # Test learning summary endpoint
        response = client.get("/api/advanced-learning/learning/summary")
        if response.status_code == 200:
            print("✅ Learning summary endpoint working")
        else:
            print(f"❌ Learning summary endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")

        # Test learning recommendations endpoint
        response = client.get("/api/advanced-learning/learning/recommendations")
        if response.status_code == 200:
            print("✅ Learning recommendations endpoint working")
        else:
            print(
                f"❌ Learning recommendations endpoint failed: {response.status_code}"
            )
            print(f"   Response: {response.text}")

        # Test learning event recording endpoint
        response = client.post(
            "/api/advanced-learning/learning/event",
            params={
                "event_type": "api_test",
                "user_id": "test_user",
                "outcome": "success",
            },
        )
        if response.status_code == 200:
            print("✅ Learning event recording endpoint working")
        else:
            print(
                f"❌ Learning event recording endpoint failed: {response.status_code}"
            )
            print(f"   Response: {response.text}")

        # Test advanced learning enhancements endpoints
        print("📋 Testing advanced learning enhancements endpoints...")

        # Test meta learning endpoint
        response = client.get(
            "/api/advanced-learning-enhancements/meta-learning/insights"
        )
        if response.status_code == 200:
            print("✅ Meta learning insights endpoint working")
        else:
            print(f"❌ Meta learning insights endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")

        # Test Pareto optimization endpoint
        response = client.get("/api/advanced-learning-enhancements/pareto/frontier")
        if response.status_code == 200:
            print("✅ Pareto frontier endpoint working")
        else:
            print(f"❌ Pareto frontier endpoint failed: {response.status_code}")
            print(f"   Response: {response.text}")

        print("\n🎯 API Integration Test Complete")
        return True

    except Exception as e:
        print(f"❌ API integration test failed: {e}")
        return False


if __name__ == "__main__":
    success = test_api_integration()
    if success:
        print("✅ API Integration Test PASSED")
    else:
        print("❌ API Integration Test FAILED")
        sys.exit(1)
