"""
Tests for AI Model Optimization System (Rebuilt)

This test suite covers the AI model optimizer, performance monitor, and
optimization manager to ensure they function correctly and efficiently.
"""

import os
import sys
from pathlib import Path

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import asyncio
import json
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch

import pytest

# Module imports
from ai_optimization.model_optimizer import (
    ModelOptimizer,
    ModelPerformanceMetrics,
    OptimizationConfig,
)
from ai_optimization.optimization_manager import AIOptimizationManager
from ai_optimization.performance_monitor import PerformanceAlert, PerformanceMonitor

# --- Fixtures ---


@pytest.fixture
def optimization_config():
    """Provides a default OptimizationConfig for tests."""
    return OptimizationConfig(
        target_response_time_ms=2000.0,
        max_memory_usage_mb=4096.0,
        max_cpu_usage_percent=80.0,
        auto_optimize=False,  # Disable auto-optimize to prevent hanging
        metrics_retention_days=1,
    )


@pytest.fixture
def model_optimizer(optimization_config):
    """Provides a ModelOptimizer instance."""
    return ModelOptimizer(optimization_config)


@pytest.fixture
def performance_monitor():
    """Provides a PerformanceMonitor instance."""
    config = {
        "resource_monitor_interval": 60,  # Increase interval to prevent hanging
        "alert_check_interval": 60,
        "health_check_interval": 60,
        "auto_start_monitoring": False,  # Disable auto-start to prevent hanging
    }
    return PerformanceMonitor(config)


@pytest.fixture
def optimization_manager(tmp_path):
    """Provides an AIOptimizationManager instance."""
    config = {
        "optimization": {
            "target_response_time_ms": 100.0,
            "auto_optimize": False,  # Disable auto-optimize to prevent hanging
        },
        "monitoring": {
            "resource_monitor_interval": 60,
            "alert_check_interval": 60,
            "auto_start_monitoring": False,
        },
        "data_dir": str(tmp_path),
    }
    return AIOptimizationManager(config)


# --- Mocks ---


@pytest.fixture
def mock_psutil():
    """Mocks psutil functions for resource monitoring."""
    with patch("ai_optimization.performance_monitor.psutil") as mock_psutil:
        mock_psutil.cpu_percent.return_value = 50.0
        mock_psutil.virtual_memory.return_value.percent = 60.0
        mock_psutil.disk_usage.return_value.percent = 70.0
        yield mock_psutil


# --- Test Classes ---


class TestModelOptimizer:
    """Tests for the ModelOptimizer."""

    def test_add_and_get_metrics(self, model_optimizer):
        """Test adding and retrieving performance metrics."""
        metrics = ModelPerformanceMetrics(
            model_name="test-model",
            response_time_ms=1500.0,
            memory_usage_mb=2048.0,
            cpu_usage_percent=60.0,
        )
        model_optimizer.add_performance_metrics(metrics)
        assert len(model_optimizer.get_model_performance("test-model")) == 1

    def test_average_performance(self, model_optimizer):
        """Test calculating average performance metrics."""
        for i in range(3):
            model_optimizer.add_performance_metrics(
                ModelPerformanceMetrics(
                    model_name="test-model",
                    response_time_ms=1000.0 + i * 100,
                    memory_usage_mb=2000.0,
                    cpu_usage_percent=50.0,
                )
            )
        avg = model_optimizer.get_average_performance("test-model")
        assert avg.response_time_ms == 1100.0

    def test_optimization_trigger(self, model_optimizer):
        """Test optimization trigger conditions."""
        # Add metrics that should trigger optimization
        metrics = ModelPerformanceMetrics(
            model_name="test-model",
            response_time_ms=5000.0,  # Above threshold
            memory_usage_mb=6000.0,  # Above threshold
            cpu_usage_percent=90.0,  # Above threshold
        )
        model_optimizer.add_performance_metrics(metrics)

        # Test optimization recommendations
        recommendations = model_optimizer.get_optimization_recommendations("test-model")
        assert isinstance(recommendations, list)


class TestPerformanceMonitor:
    """Tests for the PerformanceMonitor."""

    def test_record_and_summarize_performance(self, performance_monitor):
        """Test recording and summarizing performance data."""
        performance_monitor.record_model_performance(
            model_name="test-model",
            response_time_ms=1500.0,
            success=True,
            memory_usage_mb=2048.0,
            cpu_usage_percent=60.0,
        )

        summary = performance_monitor.get_model_performance_summary("test-model")
        assert summary is not None
        assert summary["model_name"] == "test-model"

    def test_alert_creation_and_resolution(self, performance_monitor):
        """Test creating and resolving performance alerts."""
        # Create an alert by recording poor performance
        performance_monitor.record_model_performance(
            model_name="test-model",
            response_time_ms=10000.0,  # Very slow
            success=False,
        )

        alerts = performance_monitor.get_active_alerts()
        assert len(alerts) >= 0  # May or may not create alert based on thresholds

    def test_resource_monitoring(self, performance_monitor, mock_psutil):
        """Test resource monitoring functionality."""
        resource_usage = performance_monitor.get_resource_usage()
        assert resource_usage.cpu_percent == 50.0
        assert resource_usage.memory_percent == 60.0


class TestAIOptimizationManager:
    """Tests for the AIOptimizationManager."""

    def test_model_registration(self, optimization_manager):
        """Test model registration functionality."""
        optimization_manager.register_model("test-model", {"type": "test"})
        assert "test-model" in optimization_manager.active_models

    def test_performance_recording(self, optimization_manager):
        """Test performance recording through the manager."""
        optimization_manager.record_model_performance(
            model_name="test-model",
            response_time_ms=1500.0,
            success=True,
            memory_usage_mb=2048.0,
            cpu_usage_percent=60.0,
        )

        summary = optimization_manager.get_model_performance_summary("test-model")
        assert summary is not None

    @pytest.mark.asyncio
    async def test_full_integration(self, optimization_manager, mock_psutil):
        """Test full integration of the optimization system."""
        # Register a model
        optimization_manager.register_model("test-model", {"type": "test"})

        # Record performance
        optimization_manager.record_model_performance(
            model_name="test-model",
            response_time_ms=1500.0,
            success=True,
            memory_usage_mb=2048.0,
            cpu_usage_percent=60.0,
        )

        # Get optimization recommendations
        recommendations = optimization_manager.get_optimization_recommendations(
            "test-model"
        )
        assert isinstance(recommendations, list)

        # Get performance summary
        summary = optimization_manager.get_model_performance_summary("test-model")
        assert summary is not None

        # Get optimization stats
        stats = optimization_manager.get_optimization_stats()
        assert isinstance(stats, dict)
