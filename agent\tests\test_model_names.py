#!/usr/bin/env python3
"""
Test script to check exact model names that Ollama expects
"""

import asyncio
import aiohttp
import json

async def test_model_names():
    """Test different model names to see which ones work"""

    # Get the list of models from Ollama
    import subprocess
    result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
    print("Available models:")
    print(result.stdout)

    # Test a few model names
    test_models = [
        "starcoder2:3b",
        "deepseek-coder:6.7b-instruct",
        "yi-coder:1.5b",
        "qwen2.5:3b",
        "mistral:7b-instruct-q4_0"
    ]

    base_url = "http://localhost:11434"

    for model_name in test_models:
        print(f"\nTesting model: {model_name}")

        payload = {
            "model": model_name,
            "prompt": "Hello",
            "stream": False
        }

        try:
            async with aiohttp.ClientSession() as session:
                async with session.post(f"{base_url}/api/generate", json=payload, timeout=10) as response:
                    if response.status == 200:
                        data = await response.json()
                        print(f"✅ {model_name} works!")
                        print(f"   Response: {data.get('response', '')[:100]}...")
                    else:
                        error_text = await response.text()
                        print(f"❌ {model_name} failed: {response.status} - {error_text}")
        except Exception as e:
            print(f"❌ {model_name} error: {e}")

if __name__ == "__main__":
    asyncio.run(test_model_names())
