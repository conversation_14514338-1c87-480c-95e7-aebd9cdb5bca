/**
 * Setup test file to verify Jest and React Testing Library installation
 */

import '@testing-library/jest-dom';

describe('Jest and React Testing Library Setup', () => {
  it('should have Jest working', () => {
    expect(1 + 1).toBe(2);
  });

  it('should have jest-dom matchers available', () => {
    const element = document.createElement('div');
    element.className = 'test-class';
    expect(element).toHaveClass('test-class');
  });

  it('should be able to import React Testing Library', () => {
    // This test verifies that the imports work
    expect(true).toBe(true);
  });
});
