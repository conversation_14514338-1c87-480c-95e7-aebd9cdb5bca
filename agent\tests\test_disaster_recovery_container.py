#!/usr/bin/env python3
"""
Test Disaster Recovery Container
Comprehensive test suite for disaster recovery containerization
"""

import asyncio
import json
import logging
import subprocess
import time
from pathlib import Path
from typing import Any, Dict, List

import aiohttp

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DisasterRecoveryContainerTester:
    """Test suite for disaster recovery container"""

    def __init__(self):
        self.base_url = "http://localhost:8086"
        self.timeout = 30
        self.test_results = []
        self.container_name = "ai-coding-disaster-recovery"

    def log_test(
        self, test_name: str, passed: bool, message: str, details: Dict[str, Any] = None
    ):
        """Log test result"""
        status = "✅ PASSED" if passed else "❌ FAILED"
        logger.info(f"{status} - {test_name}: {message}")

        self.test_results.append(
            {
                "test": test_name,
                "passed": passed,
                "message": message,
                "details": details or {},
            }
        )

    def test_dockerfile_exists(self) -> bool:
        """Test if Dockerfile.disaster-recovery exists"""
        try:
            dockerfile_path = Path("containers/Dockerfile.disaster-recovery")
            if dockerfile_path.exists():
                content = dockerfile_path.read_text()

                # Check for required elements
                has_from = "FROM python:3.11-slim" in content
                has_user = "disasterrecovery" in content
                has_healthcheck = "HEALTHCHECK" in content
                has_expose = "EXPOSE 8086" in content
                has_entrypoint = "ENTRYPOINT" in content

                self.log_test(
                    "Dockerfile.disaster-recovery Exists",
                    has_from
                    and has_user
                    and has_healthcheck
                    and has_expose
                    and has_entrypoint,
                    "Multi-stage build with security, health checks, and proper resource management",
                    {
                        "has_from": has_from,
                        "has_user": has_user,
                        "has_healthcheck": has_healthcheck,
                        "has_expose": has_expose,
                        "has_entrypoint": has_entrypoint,
                    },
                )
                return (
                    has_from
                    and has_user
                    and has_healthcheck
                    and has_expose
                    and has_entrypoint
                )
            else:
                self.log_test(
                    "Dockerfile.disaster-recovery Exists", False, "Dockerfile not found"
                )
                return False
        except Exception as e:
            self.log_test(
                "Dockerfile.disaster-recovery Exists", False, f"Error: {str(e)}"
            )
            return False

    def test_docker_compose_integration(self) -> bool:
        """Test docker-compose.yml integration"""
        try:
            compose_path = Path("docker-compose.yml")
            if compose_path.exists():
                content = compose_path.read_text()

                # Find disaster recovery service section
                service_section_start = content.find("  # Disaster Recovery Manager")
                if service_section_start != -1:
                    # Find the end of the service section (next service or end of file)
                    next_service_start = content.find("  # ", service_section_start + 1)
                    if next_service_start == -1:
                        service_section = content[service_section_start:]
                    else:
                        service_section = content[
                            service_section_start:next_service_start
                        ]

                    # Look for required elements
                    has_build = "build:" in service_section
                    has_ports = "8086:8086" in service_section
                    has_healthcheck = "healthcheck:" in service_section
                    has_restart = "restart: unless-stopped" in service_section
                    has_volumes = "volumes:" in service_section
                    has_deploy = "deploy:" in service_section

                    self.log_test(
                        "Docker Compose Integration",
                        has_build
                        and has_ports
                        and has_healthcheck
                        and has_restart
                        and has_volumes
                        and has_deploy,
                        "Service, port mapping, health check, resource limits",
                        {
                            "has_build": has_build,
                            "has_ports": has_ports,
                            "has_healthcheck": has_healthcheck,
                            "has_restart": has_restart,
                            "has_volumes": has_volumes,
                            "has_deploy": has_deploy,
                        },
                    )
                    return (
                        has_build
                        and has_ports
                        and has_healthcheck
                        and has_restart
                        and has_volumes
                        and has_deploy
                    )
                else:
                    self.log_test(
                        "Docker Compose Integration",
                        False,
                        "Disaster recovery service not found",
                    )
                    return False
            else:
                self.log_test(
                    "Docker Compose Integration", False, "docker-compose.yml not found"
                )
                return False
        except Exception as e:
            self.log_test("Docker Compose Integration", False, f"Error: {str(e)}")
            return False

    def test_configuration_file(self) -> bool:
        """Test disaster recovery configuration file"""
        try:
            config_path = Path("config/disaster_recovery_docker_config.json")
            if config_path.exists():
                with open(config_path, "r") as f:
                    config = json.load(f)

                # Check for required sections
                has_service = "disaster_recovery_service" in config
                has_container = "container" in config
                has_components = "disaster_recovery_components" in config
                has_monitoring = "monitoring" in config
                has_api = "api_endpoints" in config

                self.log_test(
                    "Disaster Recovery Configuration File",
                    has_service
                    and has_container
                    and has_components
                    and has_monitoring
                    and has_api,
                    "Complete configuration structure",
                    {
                        "has_service": has_service,
                        "has_container": has_container,
                        "has_components": has_components,
                        "has_monitoring": has_monitoring,
                        "has_api": has_api,
                    },
                )
                return (
                    has_service
                    and has_container
                    and has_components
                    and has_monitoring
                    and has_api
                )
            else:
                self.log_test(
                    "Disaster Recovery Configuration File",
                    False,
                    "Configuration file not found",
                )
                return False
        except Exception as e:
            self.log_test(
                "Disaster Recovery Configuration File", False, f"Error: {str(e)}"
            )
            return False

    def test_cli_commands(self) -> bool:
        """Test CLI commands implementation"""
        try:
            cli_path = Path("cli/disaster_recovery_commands.py")
            if cli_path.exists():
                content = cli_path.read_text()

                # Check for required methods
                required_methods = [
                    "check_disaster_recovery_status",
                    "get_backups_summary",
                    "get_backups_list",
                    "get_recovery_components",
                    "test_disaster_recovery_components",
                    "create_backup",
                    "perform_recovery",
                    "run_recovery_drill",
                    "validate_backup",
                    "get_recovery_metrics",
                    "export_recovery_data",
                ]

                implemented_methods = sum(
                    1 for method in required_methods if method in content
                )

                self.log_test(
                    "CLI Commands",
                    implemented_methods == len(required_methods),
                    f"{implemented_methods}/{len(required_methods)} methods implemented",
                    {
                        "implemented": implemented_methods,
                        "total": len(required_methods),
                        "methods": required_methods,
                    },
                )
                return implemented_methods == len(required_methods)
            else:
                self.log_test("CLI Commands", False, "CLI commands file not found")
                return False
        except Exception as e:
            self.log_test("CLI Commands", False, f"Error: {str(e)}")
            return False

    def test_api_routes(self) -> bool:
        """Test API routes implementation"""
        try:
            api_path = Path("api/disaster_recovery_routes.py")
            if api_path.exists():
                content = api_path.read_text()

                # Check for required endpoints
                required_endpoints = [
                    "/status",
                    "/backups/summary",
                    "/backups/list",
                    "/recovery/components",
                    "/backups/create",
                    "/recovery/perform",
                    "/drills/run",
                    "/backups/validate",
                    "/metrics",
                    "/export",
                    "/health",
                ]

                implemented_endpoints = sum(
                    1 for endpoint in required_endpoints if endpoint in content
                )

                self.log_test(
                    "API Routes",
                    implemented_endpoints == len(required_endpoints),
                    f"{implemented_endpoints}/{len(required_endpoints)} endpoints implemented",
                    {
                        "implemented": implemented_endpoints,
                        "total": len(required_endpoints),
                        "endpoints": required_endpoints,
                    },
                )
                return implemented_endpoints == len(required_endpoints)
            else:
                self.log_test("API Routes", False, "API routes file not found")
                return False
        except Exception as e:
            self.log_test("API Routes", False, f"Error: {str(e)}")
            return False

    def test_resource_limits(self) -> bool:
        """Test resource limits configuration"""
        try:
            compose_path = Path("docker-compose.yml")
            if compose_path.exists():
                content = compose_path.read_text()

                # Find disaster recovery service section
                service_section_start = content.find("  # Disaster Recovery Manager")
                if service_section_start != -1:
                    # Find the end of the service section
                    next_service_start = content.find("  # ", service_section_start + 1)
                    if next_service_start == -1:
                        service_section = content[service_section_start:]
                    else:
                        service_section = content[
                            service_section_start:next_service_start
                        ]

                    # Look for resource limits
                    has_cpu_limit = "cpus: '0.5'" in service_section
                    has_memory_limit = "memory: 1G" in service_section
                    has_deploy_section = "deploy:" in service_section

                    self.log_test(
                        "Resource Limits",
                        has_cpu_limit and has_memory_limit and has_deploy_section,
                        "CPU: 0.5, Memory: 1G configured",
                        {
                            "has_cpu_limit": has_cpu_limit,
                            "has_memory_limit": has_memory_limit,
                            "has_deploy_section": has_deploy_section,
                        },
                    )
                    return has_cpu_limit and has_memory_limit and has_deploy_section
                else:
                    self.log_test(
                        "Resource Limits", False, "Disaster recovery service not found"
                    )
                    return False
            else:
                self.log_test("Resource Limits", False, "docker-compose.yml not found")
                return False
        except Exception as e:
            self.log_test("Resource Limits", False, f"Error: {str(e)}")
            return False

    def test_service_discovery(self) -> bool:
        """Test service discovery configuration"""
        try:
            compose_path = Path("docker-compose.yml")
            if compose_path.exists():
                content = compose_path.read_text()

                # Find disaster recovery service section
                service_section_start = content.find("  # Disaster Recovery Manager")
                if service_section_start != -1:
                    # Find the end of the service section
                    next_service_start = content.find("  # ", service_section_start + 1)
                    if next_service_start == -1:
                        service_section = content[service_section_start:]
                    else:
                        service_section = content[
                            service_section_start:next_service_start
                        ]

                    # Look for network and dependencies
                    has_network = "ai-coding-network" in service_section
                    has_depends_on = "depends_on:" in service_section

                    self.log_test(
                        "Service Discovery",
                        has_network and has_depends_on,
                        "Network and dependencies configured",
                        {"has_network": has_network, "has_depends_on": has_depends_on},
                    )
                    return has_network and has_depends_on
                else:
                    self.log_test(
                        "Service Discovery",
                        False,
                        "Disaster recovery service not found",
                    )
                    return False
            else:
                self.log_test(
                    "Service Discovery", False, "docker-compose.yml not found"
                )
                return False
        except Exception as e:
            self.log_test("Service Discovery", False, f"Error: {str(e)}")
            return False

    async def test_container_build(self) -> bool:
        """Test container build process"""
        try:
            # Check if Docker is running
            result = subprocess.run(["docker", "info"], capture_output=True, text=True)
            if result.returncode != 0:
                self.log_test(
                    "Container Build", False, "Docker not running or accessible"
                )
                return False

            # Try to build the container
            build_result = subprocess.run(
                [
                    "docker",
                    "build",
                    "-f",
                    "containers/Dockerfile.disaster-recovery",
                    "-t",
                    "ai-coding-disaster-recovery:test",
                    ".",
                ],
                capture_output=True,
                text=True,
                timeout=300,
            )

            success = build_result.returncode == 0
            self.log_test(
                "Container Build",
                success,
                (
                    "Container builds successfully"
                    if success
                    else f"Build failed: {build_result.stderr[:200]}"
                ),
            )
            return success
        except subprocess.TimeoutExpired:
            self.log_test("Container Build", False, "Build timed out")
            return False
        except Exception as e:
            self.log_test("Container Build", False, f"Error: {str(e)}")
            return False

    async def test_container_startup(self) -> bool:
        """Test container startup and health check"""
        try:
            container_name = f"{self.container_name}-test"

            # Clean up any existing test container
            subprocess.run(["docker", "stop", container_name], capture_output=True)
            subprocess.run(["docker", "rm", container_name], capture_output=True)

            # Start the container
            start_result = subprocess.run(
                [
                    "docker",
                    "run",
                    "-d",
                    "--name",
                    container_name,
                    "-p",
                    "8086:8086",
                    "ai-coding-disaster-recovery:test",
                ],
                capture_output=True,
                text=True,
            )

            if start_result.returncode != 0:
                self.log_test(
                    "Container Startup",
                    False,
                    f"Failed to start container: {start_result.stderr}",
                )
                return False

            # Wait longer for container to fully start
            await asyncio.sleep(15)

            # Test health check with retries
            max_retries = 3
            for attempt in range(max_retries):
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(
                            f"{self.base_url}/health", timeout=self.timeout
                        ) as response:
                            if response.status == 200:
                                data = await response.json()
                                if data.get("status") == "healthy":
                                    self.log_test(
                                        "Container Startup",
                                        True,
                                        "Container starts and health check passes",
                                    )

                                    # Clean up
                                    subprocess.run(
                                        ["docker", "stop", container_name],
                                        capture_output=True,
                                    )
                                    subprocess.run(
                                        ["docker", "rm", container_name],
                                        capture_output=True,
                                    )
                                    return True
                                else:
                                    logger.warning(
                                        f"Health check returned unhealthy status on attempt {attempt + 1}"
                                    )
                            else:
                                logger.warning(
                                    f"Health check failed with status {response.status} on attempt {attempt + 1}"
                                )
                except Exception as e:
                    logger.warning(
                        f"Health check error on attempt {attempt + 1}: {str(e)}"
                    )

                if attempt < max_retries - 1:
                    await asyncio.sleep(5)

            # Clean up on failure
            subprocess.run(["docker", "stop", container_name], capture_output=True)
            subprocess.run(["docker", "rm", container_name], capture_output=True)

            self.log_test(
                "Container Startup", False, "Health check failed after all retries"
            )
            return False
        except Exception as e:
            self.log_test("Container Startup", False, f"Error: {str(e)}")
            return False

    async def test_api_endpoints(self) -> bool:
        """Test API endpoints"""
        try:
            # Start a fresh container for API testing
            container_name = f"{self.container_name}-api-test"

            # Clean up any existing test container
            subprocess.run(["docker", "stop", container_name], capture_output=True)
            subprocess.run(["docker", "rm", container_name], capture_output=True)

            # Start the container
            start_result = subprocess.run(
                [
                    "docker",
                    "run",
                    "-d",
                    "--name",
                    container_name,
                    "-p",
                    "8086:8086",
                    "ai-coding-disaster-recovery:test",
                ],
                capture_output=True,
                text=True,
            )

            if start_result.returncode != 0:
                self.log_test(
                    "API Endpoints",
                    False,
                    f"Failed to start container for API testing: {start_result.stderr}",
                )
                return False

            # Wait longer for container to fully start
            await asyncio.sleep(15)

            endpoints_to_test = [
                "/health",
                "/status",
                "/backups/summary",
                "/backups/list",
                "/recovery/components",
            ]

            successful_endpoints = 0
            async with aiohttp.ClientSession() as session:
                for endpoint in endpoints_to_test:
                    try:
                        async with session.get(
                            f"{self.base_url}{endpoint}", timeout=self.timeout
                        ) as response:
                            if response.status == 200:
                                successful_endpoints += 1
                            else:
                                logger.warning(
                                    f"Endpoint {endpoint} returned status {response.status}"
                                )
                    except Exception as e:
                        logger.warning(f"Endpoint {endpoint} error: {str(e)}")

            # Clean up test container
            subprocess.run(["docker", "stop", container_name], capture_output=True)
            subprocess.run(["docker", "rm", container_name], capture_output=True)

            success = successful_endpoints == len(endpoints_to_test)
            self.log_test(
                "API Endpoints",
                success,
                f"{successful_endpoints}/{len(endpoints_to_test)} endpoints respond correctly",
            )
            return success
        except Exception as e:
            self.log_test("API Endpoints", False, f"Error: {str(e)}")
            return False

    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all tests"""
        logger.info("🧪 Starting Disaster Recovery Container Tests...")

        # Run synchronous tests
        self.test_dockerfile_exists()
        self.test_docker_compose_integration()
        self.test_configuration_file()
        self.test_cli_commands()
        self.test_api_routes()
        self.test_resource_limits()
        self.test_service_discovery()

        # Run asynchronous tests
        await self.test_container_build()
        await self.test_container_startup()
        await self.test_api_endpoints()

        # Calculate results
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["passed"])
        success_rate = (passed_tests / total_tests) * 100 if total_tests > 0 else 0

        logger.info(f"\n📊 Test Results Summary:")
        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests}")
        logger.info(f"Failed: {total_tests - passed_tests}")
        logger.info(f"Success Rate: {success_rate:.1f}%")

        return {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": success_rate,
            "results": self.test_results,
        }


async def main():
    """Main test function"""
    tester = DisasterRecoveryContainerTester()
    results = await tester.run_all_tests()

    print(f"\n🎯 Disaster Recovery Container Test Results:")
    print(
        f"Success Rate: {results['success_rate']:.1f}% ({results['passed_tests']}/{results['total_tests']})"
    )

    if results["success_rate"] == 100:
        print("✅ All tests passed! Disaster recovery container is ready.")
    else:
        print("❌ Some tests failed. Please review the implementation.")


if __name__ == "__main__":
    asyncio.run(main())
