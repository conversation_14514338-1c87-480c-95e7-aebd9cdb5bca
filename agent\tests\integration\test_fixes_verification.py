#!/usr/bin/env python3
"""
Comprehensive test script to verify all fixes implemented
"""

import json
import time
from datetime import datetime

import requests

# Import unified test functions to eliminate duplication
from agent.tests.utils.common_test_functions import (
    test_api_proxy,
    test_authentication_endpoints,
    test_backend_health,
    test_chat_endpoint,
    test_frontend_access,
    test_ide_page,
    test_model_health,
)


def test_chat_authentication():
    """Test chat endpoint authentication"""
    print("\n🔐 Testing Chat Authentication...")
    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/chat",
            json={"prompt": "Hello, can you help me create a website?"},
            timeout=10,
        )
        if response.status_code == 401:
            print(
                "✅ Chat Authentication Working (Authentication required as expected)"
            )
            return True
        elif response.status_code == 200:
            data = response.json()
            print("✅ Chat Endpoint Working (No auth required)")
            print(f"Response: {data.get('response', {}).get('content', 'No content')}")
            return True
        else:
            print(f"❌ Chat Endpoint Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Chat Endpoint Error: {e}")
        return False


def test_frontend_access():
    """Test frontend accessibility"""
    print("\n🌐 Testing Frontend Access...")
    try:
        response = requests.get("http://localhost:3000/", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend Homepage Accessible")
            return True
        else:
            print(f"❌ Frontend Access Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend Access Error: {e}")
        return False


def test_ide_page():
    """Test IDE page accessibility"""
    print("\n💻 Testing IDE Page...")
    try:
        response = requests.get("http://localhost:3000/ide", timeout=5)
        if response.status_code == 200:
            print("✅ IDE Page Accessible")
            return True
        else:
            print(f"❌ IDE Page Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ IDE Page Error: {e}")
        return False


def test_api_proxy():
    """Test API proxy configuration"""
    print("\n🔗 Testing API Proxy...")
    try:
        response = requests.get(
            "http://localhost:3000/api/v1/ai/models/health", timeout=10
        )
        if response.status_code == 200:
            print("✅ API Proxy Working (Frontend to Backend)")
            return True
        else:
            print(f"❌ API Proxy Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API Proxy Error: {e}")
        return False


def test_tailwind_css():
    """Test Tailwind CSS compilation"""
    print("\n🎨 Testing Tailwind CSS...")
    try:
        response = requests.get("http://localhost:3000/", timeout=5)
        if response.status_code == 200:
            # Check if Tailwind CSS is loaded
            if "tailwind" in response.text.lower():
                print("✅ Tailwind CSS Loaded")
                return True
            else:
                print("⚠️  Tailwind CSS not detected in response")
                return False
        else:
            print(f"❌ Tailwind CSS Test Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Tailwind CSS Error: {e}")
        return False


def test_authentication_endpoints():
    """Test authentication endpoints"""
    print("\n🔐 Testing Authentication Endpoints...")
    try:
        # Test login endpoint
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/auth/login",
            json={"email": "<EMAIL>", "password": "testpass"},
            timeout=10,
        )
        if response.status_code in [200, 401, 422]:  # Accept various expected responses
            print("✅ Login Endpoint Accessible")
            return True
        else:
            print(f"❌ Login Endpoint Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Authentication Endpoints Error: {e}")
        return False


def main():
    """Main test execution function"""
    print("🚀 Starting Comprehensive Fix Verification Tests...")
    print(f"⏰ Test started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

    # Track test results
    test_results = []

    # Run all tests
    tests = [
        ("Backend Health", test_backend_health),
        ("Model Health", test_model_health),
        ("Chat Authentication", test_chat_authentication),
        ("Frontend Access", test_frontend_access),
        ("IDE Page", test_ide_page),
        ("API Proxy", test_api_proxy),
        ("Tailwind CSS", test_tailwind_css),
        ("Authentication Endpoints", test_authentication_endpoints),
    ]

    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} Error: {e}")
            test_results.append((test_name, False))

    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1

    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 ALL TESTS PASSED! All fixes are working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
