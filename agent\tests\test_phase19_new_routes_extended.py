# tests/test_phase19_new_routes_extended.py
"""Additional tests to fully validate the manual testing checklist in API_ACTION_PLAN.md.
Covers endpoints not covered in the initial focused test module.
"""

from unittest.mock import AsyncMock, Mock, patch

import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

from agent.api.ai_container_routes import router as ai_router
from agent.api.monitoring_dashboard_routes import router as monitoring_router
from agent.api.port_management_routes import router as ports_router
from agent.api.health_check_routes import router as health_router
from agent.api.alert_management_routes import router as alerts_router


@pytest.fixture
def app():
    app = FastAPI()
    app.include_router(ai_router)
    app.include_router(monitoring_router)
    app.include_router(ports_router)
    app.include_router(health_router)
    app.include_router(alerts_router)
    return app


@pytest.fixture
def client(app):
    return TestClient(app)


def test_ai_generate_dockerfile(client):
    with patch("core.ai_container_manager.AIEnhancedContainerManager._check_ollama_availability", return_value=True), \
         patch("core.ai_container_manager.AIEnhancedContainerManager.generate_intelligent_dockerfile", new_callable=AsyncMock) as mock_gen:
        mock_gen.return_value = {
            "success": True,
            "dockerfile_content": "FROM node:18-alpine",
            "dockerignore_content": "node_modules\n",
            "strategy_used": "auto",
            "optimizations_applied": ["cache"],
            "estimated_build_time": "fast",
            "ai_confidence": 0.9
        }
        resp = client.post("/api/ai-containers/generate-dockerfile/test-site", json={"strategy": "auto", "include_security_scan": True})
        assert resp.status_code == 200
        body = resp.json()
        assert body["success"] is True
        assert "dockerfile_content" in body["data"]


def test_ports_release(client):
    with patch("api.port_management_routes.SiteContainerManager") as MockMgr:
        mgr = Mock()
        MockMgr.return_value = mgr
        r = client.post("/api/ports/release/site-a")
        assert r.status_code == 200
        assert r.json()["success"] is True


def test_health_summary(client):
    with patch("monitoring.health_check_system.HealthCheckSystem.check_all_containers", new_callable=AsyncMock) as mock_check:
        from types import SimpleNamespace
        from agent.monitoring.health_check_system import HealthStatus
        result = SimpleNamespace(
            container_name="site-a",
            container_id="id1",
            status=HealthStatus.HEALTHY,
            response_time_ms=12.3,
            endpoint="/",
            timestamp=__import__("datetime").datetime.now(),
            error_message=None,
        )
        mock_check.return_value = {"site-a": result}
        r = client.get("/api/health/summary")
        assert r.status_code == 200
        data = r.json()["data"]
        assert data["healthy"] == 1


def test_monitoring_dashboard_data(client):
    with patch("core.container_monitor.ContainerMonitor.get_container_status", new_callable=AsyncMock) as mock_status, \
         patch("monitoring.container_metrics_collector.ContainerMetricsCollector.collect_all_metrics", new_callable=AsyncMock) as mock_metrics, \
         patch("monitoring.health_check_system.HealthCheckSystem.check_all_containers", new_callable=AsyncMock) as mock_health, \
         patch("monitoring.alerting_system.AlertingSystem.get_active_alerts", return_value=[]):
        mock_status.return_value = {"success": True, "containers": []}
        mock_metrics.return_value = {}
        mock_health.return_value = {}
        r = client.get("/api/monitoring/dashboard/data")
        assert r.status_code == 200
        body = r.json()
        assert body["success"] is True
        assert "containers" in body["data"]


def test_monitoring_container_metrics(client):
    with patch("monitoring.container_metrics_collector.ContainerMetricsCollector.get_performance_summary", return_value={
        "average_cpu_percent": 5.5,
        "average_memory_percent": 10.0,
        "average_memory_mb": 100.0,
        "metric_points": []
    }):
        r = client.get("/api/monitoring/containers/site-a/metrics?minutes=30")
        assert r.status_code == 200
        assert "average_cpu_percent" in r.json()["data"]


def test_alert_details_and_ack(client):
    from types import SimpleNamespace
    dummy = SimpleNamespace(
        id="a1",
        alert_type=type("T", (), {"value": "high_cpu"})(),
        severity=type("S", (), {"value": "warning"})(),
        container_name="site-x",
        message="High CPU",
        timestamp=__import__("datetime").datetime.now(),
        acknowledged=False,
        details={}
    )

    with patch("monitoring.alerting_system.AlertingSystem.get_alert_details", return_value=dummy), \
         patch("monitoring.alerting_system.AlertingSystem.acknowledge_alert", return_value=True):
        d = client.get("/api/alerts/a1")
        assert d.status_code == 200
        assert d.json()["data"]["id"] == "a1"
        a = client.post("/api/alerts/a1/acknowledge", json={"acknowledge": True})
        assert a.status_code == 200
        assert a.json()["success"] is True

