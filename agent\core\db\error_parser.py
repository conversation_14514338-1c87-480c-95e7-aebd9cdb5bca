#!/usr/bin/env python3
"""
Database Error Parser & Remediation System

This module provides comprehensive database error classification and remediation
suggestions for common SQLAlchemy, psycopg2, and other database errors.
"""

import re
import logging
from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from pathlib import Path

logger = logging.getLogger(__name__)


class ErrorCategory(Enum):
    """Database error categories"""
    SCHEMA_MISSING = "schema_missing"
    COLUMN_MISSING = "column_missing"
    TABLE_MISSING = "table_missing"
    CONSTRAINT_VIOLATION = "constraint_violation"
    CONNECTION_ERROR = "connection_error"
    PERMISSION_ERROR = "permission_error"
    DATA_TYPE_ERROR = "data_type_error"
    FOREIGN_KEY_ERROR = "foreign_key_error"
    UNIQUE_CONSTRAINT = "unique_constraint"
    NOT_NULL_CONSTRAINT = "not_null_constraint"
    CHECK_CONSTRAINT = "check_constraint"
    SYNTAX_ERROR = "syntax_error"
    UNKNOWN = "unknown"


class RemediationAction(Enum):
    """Types of remediation actions"""
    CREATE_TABLE = "create_table"
    ADD_COLUMN = "add_column"
    DROP_COLUMN = "drop_column"
    MODIFY_COLUMN = "modify_column"
    ADD_CONSTRAINT = "add_constraint"
    DROP_CONSTRAINT = "drop_constraint"
    CREATE_INDEX = "create_index"
    DROP_INDEX = "drop_index"
    UPDATE_DATA = "update_data"
    DELETE_DATA = "delete_data"
    RESTART_CONNECTION = "restart_connection"
    CHECK_PERMISSIONS = "check_permissions"
    MANUAL_INTERVENTION = "manual_intervention"


@dataclass
class RemediationStep:
    """Individual remediation step"""
    action: RemediationAction
    description: str
    sql_template: Optional[str] = None
    parameters: Optional[Dict[str, Any]] = None
    priority: int = 1  # 1=high, 2=medium, 3=low
    risk_level: str = "low"  # low, medium, high
    rollback_sql: Optional[str] = None


@dataclass
class ErrorAnalysis:
    """Complete error analysis and remediation plan"""
    category: ErrorCategory
    confidence: float  # 0.0 to 1.0
    error_message: str
    extracted_info: Dict[str, Any]
    remediation_steps: List[RemediationStep]
    description: str
    auto_fixable: bool = False


class DatabaseErrorParser:
    """Main database error parser and remediation planner"""

    def __init__(self):
        self.error_patterns = self._load_error_patterns()
        self.remediation_templates = self._load_remediation_templates()

    def _load_error_patterns(self) -> Dict[ErrorCategory, List[Dict[str, Any]]]:
        """Load error patterns for different database errors"""
        return {
            # Column missing patterns should come first (more specific)
            ErrorCategory.COLUMN_MISSING: [
                {
                    "pattern": r"column \"([^\"]+)\" of relation \"([^\"]+)\" does not exist",
                    "engine": "postgresql",
                    "confidence": 0.95,
                    "extract": ["column_name", "table_name"]
                },
                {
                    "pattern": r"no such column: (\w+)\.(\w+)",
                    "engine": "sqlite",
                    "confidence": 0.95,
                    "extract": ["table_name", "column_name"]
                },
                {
                    "pattern": r"Unknown column '([^']+)' in '([^']+)'",
                    "engine": "mysql",
                    "confidence": 0.95,
                    "extract": ["column_name", "context"]
                }
            ],
            ErrorCategory.TABLE_MISSING: [
                {
                    "pattern": r"relation \"([^\"]+)\" does not exist",
                    "engine": "postgresql",
                    "confidence": 0.95,
                    "extract": ["table_name"]
                },
                {
                    "pattern": r"no such table: (\w+)",
                    "engine": "sqlite",
                    "confidence": 0.95,
                    "extract": ["table_name"]
                },
                {
                    "pattern": r"Table '([^']+)' doesn't exist",
                    "engine": "mysql",
                    "confidence": 0.95,
                    "extract": ["table_name"]
                }
            ],
            ErrorCategory.CONSTRAINT_VIOLATION: [
                {
                    "pattern": r"duplicate key value violates unique constraint \"([^\"]+)\"",
                    "engine": "postgresql",
                    "confidence": 0.90,
                    "extract": ["constraint_name"]
                },
                {
                    "pattern": r"UNIQUE constraint failed: ([^,\n]+)",
                    "engine": "sqlite",
                    "confidence": 0.90,
                    "extract": ["column_info"]
                },
                {
                    "pattern": r"Duplicate entry '([^']+)' for key '([^']+)'",
                    "engine": "mysql",
                    "confidence": 0.90,
                    "extract": ["value", "key_name"]
                }
            ],
            ErrorCategory.NOT_NULL_CONSTRAINT: [
                {
                    "pattern": r"null value in column \"([^\"]+)\" violates not-null constraint",
                    "engine": "postgresql",
                    "confidence": 0.95,
                    "extract": ["column_name"]
                },
                {
                    "pattern": r"NOT NULL constraint failed: ([^,\n]+)",
                    "engine": "sqlite",
                    "confidence": 0.95,
                    "extract": ["column_info"]
                }
            ],
            ErrorCategory.FOREIGN_KEY_ERROR: [
                {
                    "pattern": r"insert or update on table \"([^\"]+)\" violates foreign key constraint \"([^\"]+)\"",
                    "engine": "postgresql",
                    "confidence": 0.90,
                    "extract": ["table_name", "constraint_name"]
                },
                {
                    "pattern": r"FOREIGN KEY constraint failed",
                    "engine": "sqlite",
                    "confidence": 0.85,
                    "extract": []
                }
            ],
            ErrorCategory.CONNECTION_ERROR: [
                {
                    "pattern": r"could not connect to server|connection refused|connection timed out",
                    "engine": "any",
                    "confidence": 0.90,
                    "extract": []
                },
                {
                    "pattern": r"database is locked",
                    "engine": "sqlite",
                    "confidence": 0.95,
                    "extract": []
                }
            ],
            ErrorCategory.PERMISSION_ERROR: [
                {
                    "pattern": r"permission denied for relation \"([^\"]+)\"",
                    "engine": "postgresql",
                    "confidence": 0.95,
                    "extract": ["table_name"]
                },
                {
                    "pattern": r"access denied for user '([^']+)'",
                    "engine": "mysql",
                    "confidence": 0.90,
                    "extract": ["username"]
                }
            ],
            ErrorCategory.DATA_TYPE_ERROR: [
                {
                    "pattern": r"invalid input syntax for type (\w+): \"([^\"]+)\"",
                    "engine": "postgresql",
                    "confidence": 0.85,
                    "extract": ["data_type", "value"]
                },
                {
                    "pattern": r"datatype mismatch",
                    "engine": "sqlite",
                    "confidence": 0.80,
                    "extract": []
                }
            ]
        }

    def _load_remediation_templates(self) -> Dict[ErrorCategory, List[RemediationStep]]:
        """Load remediation templates for different error categories"""
        return {
            ErrorCategory.TABLE_MISSING: [
                RemediationStep(
                    action=RemediationAction.CREATE_TABLE,
                    description="Create missing table with basic structure",
                    sql_template="CREATE TABLE {table_name} (id SERIAL PRIMARY KEY, created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP);",
                    priority=1,
                    risk_level="medium",
                    rollback_sql="DROP TABLE IF EXISTS {table_name};"
                )
            ],
            ErrorCategory.COLUMN_MISSING: [
                RemediationStep(
                    action=RemediationAction.ADD_COLUMN,
                    description="Add missing column '{column_name}' to table '{table_name}'",
                    sql_template="ALTER TABLE {table_name} ADD COLUMN {column_name} VARCHAR(255);",
                    priority=1,
                    risk_level="low",
                    rollback_sql="ALTER TABLE {table_name} DROP COLUMN {column_name};"
                )
            ],
            ErrorCategory.CONSTRAINT_VIOLATION: [
                RemediationStep(
                    action=RemediationAction.UPDATE_DATA,
                    description="Update conflicting data to resolve constraint violation",
                    sql_template="-- Manual data cleanup required for constraint: {constraint_name}",
                    priority=2,
                    risk_level="high"
                ),
                RemediationStep(
                    action=RemediationAction.DROP_CONSTRAINT,
                    description="Temporarily drop constraint (use with caution)",
                    sql_template="ALTER TABLE {table_name} DROP CONSTRAINT {constraint_name};",
                    priority=3,
                    risk_level="high"
                )
            ],
            ErrorCategory.NOT_NULL_CONSTRAINT: [
                RemediationStep(
                    action=RemediationAction.UPDATE_DATA,
                    description="Update NULL values in column '{column_name}'",
                    sql_template="UPDATE {table_name} SET {column_name} = 'default_value' WHERE {column_name} IS NULL;",
                    priority=1,
                    risk_level="medium"
                ),
                RemediationStep(
                    action=RemediationAction.MODIFY_COLUMN,
                    description="Make column '{column_name}' nullable (if appropriate)",
                    sql_template="ALTER TABLE {table_name} ALTER COLUMN {column_name} DROP NOT NULL;",
                    priority=2,
                    risk_level="medium"
                )
            ],
            ErrorCategory.CONNECTION_ERROR: [
                RemediationStep(
                    action=RemediationAction.RESTART_CONNECTION,
                    description="Restart database connection",
                    priority=1,
                    risk_level="low"
                ),
                RemediationStep(
                    action=RemediationAction.MANUAL_INTERVENTION,
                    description="Check database server status and network connectivity",
                    priority=2,
                    risk_level="low"
                )
            ],
            ErrorCategory.PERMISSION_ERROR: [
                RemediationStep(
                    action=RemediationAction.CHECK_PERMISSIONS,
                    description="Grant necessary permissions to database user for table '{table_name}'",
                    sql_template="GRANT SELECT, INSERT, UPDATE, DELETE ON {table_name} TO current_user;",
                    priority=1,
                    risk_level="medium"
                )
            ]
        }

    def parse_error(self, error: Union[Exception, str], context: Optional[Dict[str, Any]] = None) -> ErrorAnalysis:
        """
        Parse database error and generate remediation plan

        Args:
            error: Exception object or error message string
            context: Additional context (table_name, operation, etc.)

        Returns:
            ErrorAnalysis with categorization and remediation steps
        """
        error_message = self._extract_error_message(error)
        context = context or {}

        # Try to match error patterns
        best_match = None
        best_confidence = 0.0

        for category, patterns in self.error_patterns.items():
            for pattern_info in patterns:
                pattern = pattern_info["pattern"]
                confidence = pattern_info["confidence"]

                match = re.search(pattern, error_message, re.IGNORECASE)
                if match and confidence > best_confidence:
                    best_match = {
                        "category": category,
                        "confidence": confidence,
                        "match": match,
                        "pattern_info": pattern_info
                    }
                    best_confidence = confidence

        if best_match:
            return self._create_analysis_from_match(error_message, best_match, context)
        else:
            return self._create_unknown_analysis(error_message, context)

    def _create_analysis_from_match(self, error_message: str, match_info: Dict[str, Any], context: Dict[str, Any]) -> ErrorAnalysis:
        """Create error analysis from pattern match"""
        category = match_info["category"]
        confidence = match_info["confidence"]
        match = match_info["match"]
        pattern_info = match_info["pattern_info"]

        # Extract information from regex groups
        extracted_info = {}
        if "extract" in pattern_info:
            for i, field_name in enumerate(pattern_info["extract"]):
                if i + 1 <= len(match.groups()):
                    extracted_info[field_name] = match.group(i + 1)

        # Add context information
        extracted_info.update(context)

        # Try to infer missing information
        self._infer_missing_info(extracted_info, category)

        # Get remediation steps for this category
        remediation_steps = self.remediation_templates.get(category, [])

        # Customize remediation steps with extracted info
        customized_steps = []
        for step in remediation_steps:
            # Safe formatting - only format if all required keys are present
            try:
                description = step.description.format(**extracted_info) if extracted_info else step.description
            except KeyError:
                description = step.description

            try:
                sql_template = step.sql_template.format(**extracted_info) if step.sql_template and extracted_info else step.sql_template
            except KeyError:
                sql_template = step.sql_template

            try:
                rollback_sql = step.rollback_sql.format(**extracted_info) if step.rollback_sql and extracted_info else step.rollback_sql
            except KeyError:
                rollback_sql = step.rollback_sql

            customized_step = RemediationStep(
                action=step.action,
                description=description,
                sql_template=sql_template,
                parameters=extracted_info,
                priority=step.priority,
                risk_level=step.risk_level,
                rollback_sql=rollback_sql
            )
            customized_steps.append(customized_step)

        # Determine if auto-fixable
        auto_fixable = (
            category in [ErrorCategory.TABLE_MISSING, ErrorCategory.COLUMN_MISSING] and
            confidence > 0.9 and
            all(step.risk_level in ["low", "medium"] for step in customized_steps)
        )

        return ErrorAnalysis(
            category=category,
            confidence=confidence,
            error_message=error_message,
            extracted_info=extracted_info,
            remediation_steps=customized_steps,
            description=f"Detected {category.value} with {confidence*100:.1f}% confidence",
            auto_fixable=auto_fixable
        )

    def _create_unknown_analysis(self, error_message: str, context: Dict[str, Any]) -> ErrorAnalysis:
        """Create analysis for unknown error"""
        return ErrorAnalysis(
            category=ErrorCategory.UNKNOWN,
            confidence=0.0,
            error_message=error_message,
            extracted_info=context,
            remediation_steps=[
                RemediationStep(
                    action=RemediationAction.MANUAL_INTERVENTION,
                    description="Manual investigation required for unknown error",
                    priority=1,
                    risk_level="high"
                )
            ],
            description="Unknown database error - manual investigation required",
            auto_fixable=False
        )

    def suggest_migration_sql(self, analysis: ErrorAnalysis) -> Optional[str]:
        """Generate migration SQL for auto-fixable errors"""
        if not analysis.auto_fixable or not analysis.remediation_steps:
            return None

        sql_statements = []
        for step in analysis.remediation_steps:
            if step.sql_template and step.priority == 1:
                sql_statements.append(step.sql_template)

        return ";\n".join(sql_statements) + ";" if sql_statements else None

    def _infer_missing_info(self, extracted_info: Dict[str, Any], category: ErrorCategory) -> None:
        """Infer missing information from available data"""

        # Try to extract table name from constraint name
        if "constraint_name" in extracted_info and "table_name" not in extracted_info:
            constraint_name = extracted_info["constraint_name"]
            # Common pattern: table_column_key or table_constraint_type
            if "_" in constraint_name:
                parts = constraint_name.split("_")
                if len(parts) >= 2:
                    extracted_info["table_name"] = parts[0]

        # Try to extract table and column from column_info (e.g., "users.email")
        if "column_info" in extracted_info:
            column_info = extracted_info["column_info"]
            if "." in column_info:
                table_name, column_name = column_info.split(".", 1)
                extracted_info["table_name"] = table_name
                extracted_info["column_name"] = column_name

        # Provide default values for missing required fields
        if category == ErrorCategory.NOT_NULL_CONSTRAINT:
            if "table_name" not in extracted_info:
                extracted_info["table_name"] = "unknown_table"

        if category == ErrorCategory.CONSTRAINT_VIOLATION:
            if "table_name" not in extracted_info:
                extracted_info["table_name"] = "unknown_table"

    def _extract_error_message(self, error: Union[Exception, str]) -> str:
        """Extract the actual error message from various exception types"""
        if isinstance(error, str):
            return error

        error_str = str(error)

        # Handle SQLAlchemy exceptions
        if hasattr(error, 'orig') and error.orig:
            # Try to get the original database error
            orig_error = error.orig
            try:
                if hasattr(orig_error, 'args') and orig_error.args:
                    return str(orig_error.args[0])
            except (TypeError, IndexError):
                pass
            return str(orig_error)

        # Handle psycopg2 and other database exceptions
        try:
            if hasattr(error, 'args') and error.args:
                return str(error.args[0])
        except (TypeError, IndexError):
            pass

        return error_str
