# tests/test_site_container_cli.py
"""
Tests for SiteContainerCommands CLI interface
"""

import asyncio
from pathlib import Path
from unittest.mock import AsyncMock, <PERSON>Mock, Mock, patch

import pytest

from agent.cli.site_container_commands import SiteContainerCommands
from agent.core.site_container_manager import Con<PERSON>er<PERSON><PERSON><PERSON>, SiteContainerManager


class TestSiteContainerCommands:
    """Test cases for SiteContainerCommands CLI interface"""

    @pytest.fixture
    def mock_agent(self):
        """Create a mock agent for testing"""
        agent = Mock()
        return agent

    @pytest.fixture
    def site_commands(self, mock_agent):
        """Create SiteContainerCommands instance for testing"""
        with patch("core.site_container_manager.docker.from_env"):
            commands = SiteContainerCommands(mock_agent)
            # Replace the container manager with a mock
            commands.container_manager = Mock()
            return commands

    @pytest.mark.asyncio
    async def test_create_site_container_success(self, site_commands):
        """Test successful site container creation via CLI"""
        site_name = "test-site"

        # Mock the container manager create method
        mock_result = {
            "success": True,
            "port": 8080,
            "container": {
                "site_name": "test-site",
                "container_name": "site-test-site",
                "port": 8080,
                "status": "stopped",
            },
            "dockerfile": "/path/to/Dockerfile",
            "compose": "/path/to/docker-compose.yml",
        }
        site_commands.container_manager.create_site_container = AsyncMock(
            return_value=mock_result
        )

        result = await site_commands.create_site_container(site_name)

        assert result["success"] is True
        assert result["port"] == 8080
        assert "container" in result
        site_commands.container_manager.create_site_container.assert_called_once()

    @pytest.mark.asyncio
    async def test_create_site_container_failure(self, site_commands):
        """Test site container creation failure via CLI"""
        site_name = "non-existent-site"

        # Mock the container manager create method to return failure
        mock_result = {
            "success": False,
            "error": "Site non-existent-site does not exist",
        }
        site_commands.container_manager.create_site_container = AsyncMock(
            return_value=mock_result
        )

        result = await site_commands.create_site_container(site_name)

        assert result["success"] is False
        assert "error" in result

    @pytest.mark.asyncio
    async def test_start_site_container_success(self, site_commands):
        """Test successful site container start via CLI"""
        site_name = "test-site"

        mock_result = {
            "success": True,
            "url": "http://localhost:8080",
            "status": "running",
        }
        site_commands.container_manager.start_site_container = AsyncMock(
            return_value=mock_result
        )

        result = await site_commands.start_site_container(site_name)

        assert result["success"] is True
        assert result["url"] == "http://localhost:8080"

    @pytest.mark.asyncio
    async def test_stop_site_container_success(self, site_commands):
        """Test successful site container stop via CLI"""
        site_name = "test-site"

        mock_result = {"success": True, "status": "stopped"}
        site_commands.container_manager.stop_site_container = AsyncMock(
            return_value=mock_result
        )

        result = await site_commands.stop_site_container(site_name)

        assert result["success"] is True
        assert "message" in result

    @pytest.mark.asyncio
    async def test_delete_site_container_success(self, site_commands):
        """Test successful site container deletion via CLI"""
        site_name = "test-site"

        mock_result = {"success": True, "message": "Container deleted successfully"}
        site_commands.container_manager.delete_site_container = AsyncMock(
            return_value=mock_result
        )

        result = await site_commands.delete_site_container(site_name)

        assert result["success"] is True
        assert "message" in result

    @pytest.mark.asyncio
    async def test_list_site_containers_success(self, site_commands):
        """Test successful site containers listing via CLI"""
        mock_result = {
            "success": True,
            "containers": [
                {
                    "site_name": "site1",
                    "port": 8080,
                    "status": "running",
                    "health_status": "healthy",
                },
                {
                    "site_name": "site2",
                    "port": 8081,
                    "status": "stopped",
                    "health_status": "healthy",
                },
            ],
            "total": 2,
        }
        site_commands.container_manager.list_containers = AsyncMock(
            return_value=mock_result
        )

        result = await site_commands.list_site_containers()

        assert result["success"] is True
        assert result["total"] == 2
        assert len(result["containers"]) == 2

    @pytest.mark.asyncio
    async def test_get_container_status_success(self, site_commands):
        """Test successful container status retrieval via CLI"""
        site_name = "test-site"

        mock_result = {
            "success": True,
            "container": {
                "site_name": "test-site",
                "port": 8080,
                "status": "running",
                "health_status": "healthy",
                "resource_usage": {"cpu": 10, "memory": 1024},
            },
        }
        site_commands.container_manager.get_container_status = AsyncMock(
            return_value=mock_result
        )

        result = await site_commands.get_container_status(site_name)

        assert result["success"] is True
        assert result["container"]["site_name"] == site_name
        assert result["container"]["status"] == "running"

    @pytest.mark.asyncio
    async def test_rebuild_site_container_success(self, site_commands):
        """Test successful site container rebuild via CLI"""
        site_name = "test-site"

        mock_result = {
            "success": True,
            "message": "Container rebuilt successfully",
            "port": 8080,
        }
        site_commands.container_manager.rebuild_site_container = AsyncMock(
            return_value=mock_result
        )

        result = await site_commands.rebuild_site_container(site_name)

        assert result["success"] is True
        assert "message" in result

    @pytest.mark.asyncio
    async def test_get_container_logs_success(self, site_commands):
        """Test successful container logs retrieval via CLI"""
        site_name = "test-site"

        mock_result = {
            "success": True,
            "logs": [
                "2024-01-01 12:00:00 - Container started",
                "2024-01-01 12:01:00 - HTTP server listening on port 8080",
            ],
            "container_name": "site-test-site",
        }
        site_commands.container_manager.get_container_logs = AsyncMock(
            return_value=mock_result
        )

        result = await site_commands.get_container_logs(site_name, lines=50)

        assert result["success"] is True
        assert len(result["logs"]) == 2

    @pytest.mark.asyncio
    async def test_cli_error_handling(self, site_commands):
        """Test CLI error handling"""
        site_name = "test-site"

        # Mock an exception in the container manager
        site_commands.container_manager.create_site_container = AsyncMock(
            side_effect=Exception("Test error")
        )

        result = await site_commands.create_site_container(site_name)

        assert result["success"] is False
        assert "error" in result
        assert "Test error" in result["error"]
