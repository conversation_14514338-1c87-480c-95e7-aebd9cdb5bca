import React, { useState, useCallback } from 'react';
import { useDropzone } from 'react-dropzone';

interface UploadZoneProps {
  onUpload: (files: File[]) => void;
  onUploadComplete: (result: any) => void;
}

export const UploadZone: React.FC<UploadZoneProps> = ({ onUpload, onUploadComplete }) => {
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    setUploading(true);
    setUploadProgress(0);

    try {
      // Create FormData for file upload
      const formData = new FormData();
      acceptedFiles.forEach(file => {
        formData.append('files', file);
      });

      // Simulate upload progress
      const progressInterval = setInterval(() => {
        setUploadProgress(prev => Math.min(prev + 10, 90));
      }, 200);

      const response = await fetch('/api/upload-site', {
        method: 'POST',
        body: formData,
      });

      clearInterval(progressInterval);
      setUploadProgress(100);

      const result = await response.json();
      onUploadComplete(result);
    } catch (error) {
      console.error('Upload failed:', error);
    } finally {
      setTimeout(() => {
        setUploading(false);
        setUploadProgress(0);
      }, 1000);
    }
  }, [onUploadComplete]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    multiple: true,
  });

  return (
    <div className="upload-zone-container">
      <div
        {...getRootProps()}
        className={`upload-zone ${isDragActive ? 'drag-active' : ''} ${uploading ? 'uploading' : ''}`}
      >
        <input {...getInputProps()} />

        {uploading ? (
          <div className="upload-progress">
            <div className="progress-bar">
              <div
                className="progress-fill"
                style={{ width: `${uploadProgress}%` }}
              ></div>
            </div>
            <p>Uploading... {uploadProgress}%</p>
          </div>
        ) : (
          <div className="upload-content">
            <div className="upload-icon">📁</div>
            <h3>Import Web Project</h3>
            <p>Drag & drop a web project folder here, or click to select</p>
            <p className="supported-frameworks">
              <strong>Supported:</strong> React, Next.js, Flask, Django, FastAPI, Vue, Angular, static sites
            </p>
            <div className="upload-tips">
              <p>💡 <strong>Tip:</strong> You can upload entire project folders</p>
              <p>🔒 <strong>Security:</strong> All uploads are scanned for safety</p>
            </div>
          </div>
        )}
      </div>

      <style jsx>{`
        .upload-zone-container {
          width: 100%;
          max-width: 600px;
          margin: 0 auto;
        }

        .upload-zone {
          border: 2px dashed #ccc;
          border-radius: 8px;
          padding: 40px 20px;
          text-align: center;
          cursor: pointer;
          transition: all 0.3s ease;
          background: #fafafa;
        }

        .upload-zone:hover {
          border-color: #007bff;
          background: #f0f8ff;
        }

        .upload-zone.drag-active {
          border-color: #28a745;
          background: #f0fff0;
          transform: scale(1.02);
        }

        .upload-zone.uploading {
          border-color: #ffc107;
          background: #fffbf0;
          cursor: not-allowed;
        }

        .upload-icon {
          font-size: 48px;
          margin-bottom: 16px;
        }

        .upload-content h3 {
          margin: 0 0 16px 0;
          color: #333;
        }

        .upload-content p {
          margin: 8px 0;
          color: #666;
        }

        .supported-frameworks {
          font-size: 14px;
          color: #007bff;
          margin: 16px 0;
        }

        .upload-tips {
          margin-top: 20px;
          text-align: left;
          background: #f8f9fa;
          padding: 16px;
          border-radius: 6px;
        }

        .upload-tips p {
          margin: 4px 0;
          font-size: 14px;
        }

        .upload-progress {
          width: 100%;
        }

        .progress-bar {
          width: 100%;
          height: 8px;
          background: #e9ecef;
          border-radius: 4px;
          overflow: hidden;
          margin-bottom: 16px;
        }

        .progress-fill {
          height: 100%;
          background: linear-gradient(90deg, #007bff, #28a745);
          transition: width 0.3s ease;
        }
      `}</style>
    </div>
  );
};
