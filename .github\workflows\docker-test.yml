name: docker-tests

on:
  push:
    branches: [ main, feature/** ]
  pull_request:
    branches: [ main ]

jobs:
  tests:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Compose up test-runner
        run: |
          docker compose -f containers/docker-compose.test.yml up --build --abort-on-container-exit --exit-code-from test-runner

      - name: Cleanup
        if: always()
        run: |
          docker compose -f containers/docker-compose.test.yml down -v || true

