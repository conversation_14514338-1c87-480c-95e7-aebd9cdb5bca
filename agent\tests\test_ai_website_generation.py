#!/usr/bin/env python3
"""
AI-Powered Website Generation Test
Tests website generation using local Ollama models configured for the AI Coding Agent.
"""

import asyncio
import json
import tempfile
from pathlib import Path
from typing import Any, Dict, List

from agent.core.website_generator import WebsiteGenerator
from agent.models.model_manager import ModelProvider

# Import the AI model integration components
from agent.models.model_router import ModelRouter
from tools.templates.template_manager import TemplateManager


class AIWebsiteGenerator:
    """AI-powered website generator using local Ollama models"""

    def __init__(self):
        self.model_router = ModelRouter()
        self.template_manager = TemplateManager()
        self.website_generator = WebsiteGenerator(self.template_manager)

        # Configure models for different tasks
        self.models = {
            "template_generation": "deepseek-coder:6.7b-instruct",  # Use available model
            "content_creation": "qwen2.5:3b",  # Use available model
            "styling": "yi-coder:1.5b",
            "complex_layout": "deepseek-coder:6.7b-instruct",
            "general": "qwen2.5:3b",
        }

    async def generate_template_with_ai(
        self, site_config: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate a website template using AI models"""

        # Generate HTML structure
        html_prompt = f"""
        Create a modern HTML template for a website with the following specifications:
        - Site name: {site_config.get('name', 'My Website')}
        - Title: {site_config.get('title', 'Welcome')}
        - Description: {site_config.get('description', 'A modern website')}
        - Author: {site_config.get('author', 'Unknown')}

        Requirements:
        - Use semantic HTML5 elements
        - Include proper meta tags
        - Add placeholder sections for content
        - Use modern CSS classes for styling
        - Include responsive design considerations
        - Add comments explaining the structure

        Return only the HTML code, no explanations.
        """

        try:
            html_response = await self.model_router.generate_response(
                self.models["template_generation"], html_prompt
            )

            # Generate CSS styling
            css_prompt = f"""
            Create modern CSS styles for a website with the following theme:
            - Site name: {site_config.get('name', 'My Website')}
            - Primary color: {site_config.get('primary_color', '#007bff')}
            - Secondary color: {site_config.get('secondary_color', '#6c757d')}

            Requirements:
            - Use CSS Grid and Flexbox for layout
            - Include responsive breakpoints
            - Use modern CSS features (custom properties, etc.)
            - Create a clean, professional design
            - Include hover effects and transitions
            - Use a readable font stack

            Return only the CSS code, no explanations.
            """

            css_response = await self.model_router.generate_response(
                self.models["styling"], css_prompt
            )

            # Generate JavaScript functionality
            js_prompt = f"""
            Create JavaScript functionality for a modern website:
            - Site name: {site_config.get('name', 'My Website')}

            Requirements:
            - Add smooth scrolling navigation
            - Include form validation
            - Add interactive elements
            - Use modern ES6+ syntax
            - Include error handling
            - Add comments explaining functionality

            Return only the JavaScript code, no explanations.
            """

            js_response = await self.model_router.generate_response(
                self.models["template_generation"], js_prompt
            )

            return {
                "status": "success",
                "html": html_response,
                "css": css_response,
                "javascript": js_response,
            }

        except Exception as e:
            return {
                "status": "error",
                "message": f"AI template generation failed: {str(e)}",
            }

    async def create_ai_website(self, site_config: Dict[str, Any]) -> Dict[str, Any]:
        """Create a complete website using AI-generated templates"""

        print(f"🤖 Generating AI website: {site_config.get('name', 'Unknown')}")

        # Generate AI template
        template_result = await self.generate_template_with_ai(site_config)

        if template_result["status"] != "success":
            return template_result

        # Create temporary template directory
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir)

            # Create template files
            (temp_path / "index.html").write_text(template_result["html"])
            (temp_path / "style.css").write_text(template_result["css"])
            (temp_path / "script.js").write_text(template_result["javascript"])

            # Create a temporary template manager for this generation
            temp_template_manager = TemplateManager(str(temp_path))

            # Create website using the generated template
            website_generator = WebsiteGenerator(temp_template_manager)

            # Override the template manager's get_template method
            def mock_get_template(name):
                return {"path": str(temp_path)}

            temp_template_manager.get_template = mock_get_template

            # Generate the website
            result = website_generator.create_website(site_config)

            return {
                "status": "success",
                "website_result": result,
                "ai_template": template_result,
            }


async def test_ai_website_generation():
    """Test AI-powered website generation with different models"""

    print("🚀 Testing AI-Powered Website Generation")
    print("=" * 50)

    # Test configurations
    test_configs = [
        {
            "name": "ai-business-site",
            "title": "AI Business Solutions",
            "description": "Modern business website generated by AI",
            "author": "AI Coding Agent",
            "primary_color": "#2563eb",
            "secondary_color": "#64748b",
        },
        {
            "name": "ai-portfolio-site",
            "title": "Creative Portfolio",
            "description": "Showcase of creative work and projects",
            "author": "Creative Developer",
            "primary_color": "#7c3aed",
            "secondary_color": "#f59e0b",
        },
        {
            "name": "ai-blog-site",
            "title": "Tech Blog",
            "description": "Sharing insights about technology and development",
            "author": "Tech Blogger",
            "primary_color": "#059669",
            "secondary_color": "#dc2626",
        },
    ]

    ai_generator = AIWebsiteGenerator()

    for i, config in enumerate(test_configs, 1):
        print(f"\n📝 Test {i}: Generating '{config['name']}'")
        print(f"   Title: {config['title']}")
        print(f"   Description: {config['description']}")

        try:
            result = await ai_generator.create_ai_website(config)

            if result["status"] == "success":
                print(f"   ✅ Success! Website generated successfully")
                print(
                    f"   📁 Site path: {result['website_result'].get('site_path', 'N/A')}"
                )

                # Show some stats about the generated content
                ai_template = result["ai_template"]
                html_length = len(ai_template["html"])
                css_length = len(ai_template["css"])
                js_length = len(ai_template["javascript"])

                print(f"   📊 Generated content:")
                print(f"      HTML: {html_length} characters")
                print(f"      CSS: {css_length} characters")
                print(f"      JS: {js_length} characters")

            else:
                print(f"   ❌ Failed: {result.get('message', 'Unknown error')}")

        except Exception as e:
            print(f"   ❌ Error: {str(e)}")

    print(f"\n🎉 AI Website Generation Test Complete!")


async def test_model_specific_generation():
    """Test website generation with specific models"""

    print("\n🔬 Testing Model-Specific Generation")
    print("=" * 40)

    ai_generator = AIWebsiteGenerator()

    # Test each model for a specific task
    test_config = {
        "name": "model-test-site",
        "title": "Model Comparison Test",
        "description": "Testing different AI models for website generation",
        "author": "AI Tester",
    }

    for task, model in ai_generator.models.items():
        print(f"\n🧠 Testing {task} with {model}")

        try:
            # Test the model with a specific prompt
            prompt = f"""
            Create a simple HTML structure for a {task} test page.
            Include basic styling and functionality.
            Site name: {test_config['name']}
            """

            response = await ai_generator.model_router.generate_response(model, prompt)

            print(f"   ✅ {model} responded successfully")
            print(f"   📝 Response length: {len(response)} characters")

        except Exception as e:
            print(f"   ❌ {model} failed: {str(e)}")


async def main():
    """Main test function"""

    print("🤖 AI-Powered Website Generation Test Suite")
    print("Using local Ollama models configured for AI Coding Agent")
    print("=" * 60)

    # Check if Ollama is running
    try:
        import subprocess

        result = subprocess.run(["ollama", "list"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ Ollama is running")
            print("📋 Available models:")
            for line in result.stdout.strip().split("\n")[1:]:  # Skip header
                if line.strip():
                    model_name = line.split()[0]
                    print(f"   - {model_name}")
        else:
            print("❌ Ollama is not running or not accessible")
            return
    except Exception as e:
        print(f"❌ Error checking Ollama: {str(e)}")
        return

    # Run tests
    await test_ai_website_generation()
    await test_model_specific_generation()

    print(f"\n🎯 Test Summary:")
    print(f"   - Tested AI-powered website generation")
    print(f"   - Used local Ollama models")
    print(f"   - Generated multiple website configurations")
    print(f"   - Verified model-specific capabilities")


if __name__ == "__main__":
    asyncio.run(main())
