# Multi-stage build for Disaster Recovery Manager
FROM python:3.11-slim AS builder
ENV PIP_NO_CACHE_DIR=1
# Set working directory
WORKDIR /app

# Install system dependencies for building
RUN apt-get update && apt-get install -y --no-install-recommends curl \
 && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
RUN python -m venv /opt/venv \
 && . /opt/venv/bin/activate \
 && pip install --upgrade pip \
 && pip install --no-cache-dir fastapi uvicorn aiohttp

# Production stage
FROM python:3.11-slim AS runtime
ENV PATH="/opt/venv/bin:$PATH" PYTHONUNBUFFERED=1 PYTHONPATH=/app
# Create non-root user for security
RUN addgroup -r disasterrecovery && adduser -r -g disasterrecovery disasterrecovery

# Set working directory
WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends curl \
 && rm -rf /var/lib/apt/lists/* && apt-get clean

# Copy Python packages from builder stage
COPY --from=builder /opt/venv /opt/venv

# Create necessary directories
RUN mkdir -p /app/data/disaster-recovery \
    /app/logs/disaster-recovery \
    /app/backups/disaster-recovery \
    /app/temp/disaster-recovery \
    /app/config

# Set ownership to non-root user
RUN chown -R disasterrecovery:disasterrecovery /app

# Switch to non-root user
USER disasterrecovery

# Set environment variables
ENV DISASTER_RECOVERY_ENABLED=true
ENV ENVIRONMENT=production
ENV LOG_LEVEL=INFO
ENV PORT=8086

# Expose port
EXPOSE 8086

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8086/health || exit 1

# Create disaster recovery service entry point
RUN echo '#!/usr/bin/env python3\nfrom fastapi import FastAPI\nimport uvicorn\napp=FastAPI()\<EMAIL>("/health")\nasync def h():\n    return {"status":"healthy","service":"disaster_recovery_manager"}\nif __name__=="__main__":\n uvicorn.run("disaster_recovery_service:app",host="0.0.0.0",port=8086,log_level="info")' > /app/disaster_recovery_service.py

# Make the script executable
RUN chmod +x /app/disaster_recovery_service.py

# Set the entry point
ENTRYPOINT ["python", "/app/disaster_recovery_service.py"]
