# Static Analysis & Type Safety Rules

## Overview
Comprehensive guidelines for maintaining type safety, static analysis compliance, and preventing common issues across the codebase.

## 🚨 CRITICAL: Dependency Management

### Requirements
- **ALWAYS update requirements.txt and requirements-dev.txt** when adding new imports or dependencies
- **NEVER use imports without ensuring the package is listed in requirements files**
- **MANDATORY: Pin dependency versions with exact versions (==)** - never use ranges
- **CRITICAL: Test with clean environment** after adding new dependencies
- **ALWAYS verify imports resolve correctly** before completing any task

### Examples
```python
# ✅ CORRECT: Add to requirements-dev.txt first
# config/requirements-dev.txt
astor==0.8.1

# Then use in code
import astor
```

## 🚨 CRITICAL: Type Annotations & Signatures

### Requirements
- **ALWAYS match function parameters and class attributes to actual usage**
- **USE Optional[...] or broaden unions if None is ever assigned**
- **ALLOW float if you ever store a float in an int field, etc.**
- **NEVER use concrete types when the code sometimes passes None or other types**

### Examples
```python
# ✅ CORRECT: Accept both string and enum
def analyze_code(source: str, language: Union[str, CodeLanguage]) -> List[CodePattern]:
    if isinstance(language, CodeLanguage):
        language = language.value
    # ... rest of function

# ❌ INCORRECT: Only accepts string
def analyze_code(source: str, language: str) -> List[CodePattern]:
```

## 🚨 CRITICAL: Method Declarations

### Requirements
- **IF you call any method on a class, that method MUST be declared in its class definition**
- **ALWAYS declare .close(), .get(), .shutdown() methods** even if they just forward to internal attributes
- **NEVER assume methods exist without declaring them**

### Examples
```python
# ✅ CORRECT: Declare the method
@dataclass
class CodePattern:
    name: str
    # ... other fields

    def get(self, key: str, default: Any = None) -> Any:
        """Get attribute value with default fallback"""
        return getattr(self, key, default)

# ❌ INCORRECT: Using .get() without declaring it
pattern.get('name')  # This will cause Pylance errors
```

## 🚨 CRITICAL: Enum Handling

### Requirements
- **IF passing an Enum into a parameter typed as a primitive, either cast it or accept both types in the signature**
- **ALWAYS handle enum conversion explicitly** when needed
- **USE Union[str, EnumType] for parameters that accept both string and enum values**

### Examples
```python
# ✅ CORRECT: Accept both types and convert
def find_patterns(language: Union[str, CodeLanguage]) -> List[Pattern]:
    if isinstance(language, CodeLanguage):
        language = language.value
    # ... rest of function

# ❌ INCORRECT: Only accepts string
def find_patterns(language: str) -> List[Pattern]:
    # This will fail when CodeLanguage enum is passed
```

## 🚨 CRITICAL: Config Argument Discipline

### Requirements
- **ENFORCE a single pattern for config parameters**: either require them everywhere or default them everywhere—never mix
- **DECIDE whether to require every caller to pass config (no default) or to provide a default**
- **APPLY that choice consistently to every __init__ signature in the project**

### Examples
```python
# ✅ CORRECT: Consistent required config pattern
class AIAgent:
    def __init__(self, config: str):  # Required everywhere
        assert config, "A config file path is required"
        self.config = get_config(config)

# ✅ CORRECT: Consistent optional config pattern
class DatabaseManager:
    def __init__(self, config: Dict[str, Any] = None):  # Optional everywhere
        self.config = config or {}
```

## 🚨 CRITICAL: Import Management

### Requirements
- **ALWAYS verify imports work in the target environment**
- **NEVER use imports without ensuring the package is available**
- **ALWAYS handle circular imports gracefully** with try-except blocks
- **ALWAYS test imports after making changes**

### Examples
```python
# ✅ CORRECT: Handle circular imports gracefully
try:
    from .ast_code_generator import CodeLanguage
except ImportError:
    # Fallback if circular import
    class CodeLanguage(Enum):
        """Supported programming languages"""
        PYTHON = "python"
        TYPESCRIPT = "typescript"
```

## 🚨 CRITICAL: Type Safety Best Practices

### Requirements
- **USE type hints everywhere** - especially for function parameters and return values
- **VALIDATE types at runtime** when necessary
- **DOCUMENT complex types** with clear examples
- **TEST type safety** with static analysis tools

### Examples
```python
# ✅ CORRECT: Comprehensive type hints
from typing import Union, List, Optional, Any
from dataclasses import dataclass

@dataclass
class CodePattern:
    name: str
    language: str
    description: str

    def get(self, key: str, default: Any = None) -> Any:
        """Get attribute value with default fallback"""
        return getattr(self, key, default)

async def find_patterns(
    requirements: List[str],
    language: Union[str, CodeLanguage]
) -> List[CodePattern]:
    """Find patterns matching requirements and language."""
    if isinstance(language, CodeLanguage):
        language = language.value
    # ... implementation
```

## 🚨 CRITICAL: Error Prevention Checklist

### Before completing any task, verify:
- [ ] **All imports resolve correctly** - no ModuleNotFoundError
- [ ] **All type annotations match usage** - no reportArgumentType errors
- [ ] **All methods are declared** - no reportAttributeAccessIssue errors
- [ ] **All enums are handled properly** - no type conversion errors
- [ ] **All config parameters are consistent** - no missing argument errors
- [ ] **Static analysis passes** - no mypy/pyright errors
- [ ] **Dependencies are properly managed** - all packages in requirements files

## 🔧 Static Analysis Tools

### Required Tools
- **mypy**: Type checking with strict mode
- **pyright**: Alternative type checker
- **flake8**: Linting and style checking
- **black**: Code formatting
- **isort**: Import sorting

### Configuration
```bash
# Run static analysis
mypy --strict core/
flake8 core/
black --check core/
isort --check-only core/
```

### Pre-commit Hooks
```yaml
# .pre-commit-config.yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
```

## 🎯 Common Issues & Solutions

### ModuleNotFoundError
**Problem**: Import fails because package not in requirements
**Solution**: Add package to requirements.txt with exact version

### reportArgumentType
**Problem**: Function expects different type than provided
**Solution**: Update type annotation to match actual usage

### reportAttributeAccessIssue
**Problem**: Method called but not declared
**Solution**: Add method declaration to class

### Type Conversion Errors
**Problem**: Enum passed to function expecting string
**Solution**: Use Union[str, EnumType] and handle conversion

### Missing Argument Errors
**Problem**: Config parameter missing or inconsistent
**Solution**: Decide on required vs optional pattern and apply consistently

## 📚 Additional Resources

- [Pylance Documentation](https://github.com/microsoft/pylance-release)
- [mypy Documentation](https://mypy.readthedocs.io/)
- [Python Type Hints](https://docs.python.org/3/library/typing.html)
- [Static Analysis Best Practices](https://realpython.com/python-type-checking/)
