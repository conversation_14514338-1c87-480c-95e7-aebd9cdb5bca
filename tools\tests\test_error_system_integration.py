#!/usr/bin/env python3
"""
Comprehensive Error System Integration Test
Tests the complete error detection and user escalation system
"""

import asyncio
import sys
import tempfile
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from agent.core.site_container_manager import SiteContainerManager
from agent.core.error_detection_system import ErrorDetectionSystem, DetectedError, ErrorCategory, ErrorSeverity


async def test_complete_error_workflow():
    """Test the complete error detection and escalation workflow"""
    
    print("🧪 Testing Complete Error System Integration")
    print("=" * 60)
    
    # Initialize components
    container_manager = SiteContainerManager()
    error_detector = ErrorDetectionSystem()
    
    test_results = {
        "imports": False,
        "instantiation": False,
        "error_creation": False,
        "auto_fix_attempt": False,
        "user_escalation": False,
        "user_suggestion": False,
        "integration": False
    }
    
    try:
        # Test 1: Component Imports and Instantiation
        print("📋 Test 1: Component Imports and Instantiation")
        print("-" * 50)
        
        print("✅ SiteContainerManager imported and instantiated")
        print("✅ ErrorDetectionSystem imported and instantiated")
        test_results["imports"] = True
        test_results["instantiation"] = True
        
        # Test 2: Error Creation and Detection
        print(f"\n📋 Test 2: Error Creation and Detection")
        print("-" * 50)
        
        test_error = DetectedError(
            id="test_001",
            timestamp=datetime.now(),
            category=ErrorCategory.DATABASE,
            severity=ErrorSeverity.HIGH,
            title="Test Database Connection Error",
            description="Cannot connect to database server",
            source="test_system",
            context={"site_name": "test-site"},
            auto_fixable=True
        )
        
        print(f"✅ Created test error: {test_error.title}")
        print(f"   Category: {test_error.category.value}")
        print(f"   Severity: {test_error.severity.value}")
        print(f"   Auto-fixable: {test_error.auto_fixable}")
        test_results["error_creation"] = True
        
        # Test 3: Automatic Fix Attempts
        print(f"\n📋 Test 3: Automatic Fix Attempts")
        print("-" * 50)
        
        error_details = {
            "title": test_error.title,
            "description": test_error.description,
            "category": test_error.category.value,
            "severity": test_error.severity.value
        }
        
        escalation_result = await container_manager.handle_error_with_user_escalation(
            site_name="test-site",
            error_details=error_details
        )
        
        print(f"✅ Error handling completed")
        print(f"   Resolution: {escalation_result.get('resolution', 'unknown')}")
        
        if escalation_result.get("success"):
            print(f"   Fix applied: {escalation_result.get('fix_applied', 'none')}")
            print(f"   Attempts: {escalation_result.get('attempts', 0)}")
            test_results["auto_fix_attempt"] = True
        elif escalation_result.get("requires_user_input"):
            print(f"   Escalated to user: {escalation_result.get('requires_user_input')}")
            print(f"   User message available: {bool(escalation_result.get('user_message'))}")
            test_results["user_escalation"] = True
        
        # Test 4: User Suggestion Handling
        print(f"\n📋 Test 4: User Suggestion Handling")
        print("-" * 50)
        
        user_suggestions = [
            "restart the container",
            "run a diagnostic",
            "rollback to previous version",
            "something vague and unclear"
        ]
        
        for suggestion in user_suggestions:
            print(f"\n   Testing suggestion: '{suggestion}'")
            
            suggestion_result = await container_manager.apply_user_suggested_fix(
                site_name="test-site",
                user_suggestion=suggestion,
                error_context=error_details
            )
            
            if suggestion_result.get("success"):
                print(f"   ✅ Applied: {suggestion_result.get('fix_description', 'unknown')}")
            elif suggestion_result.get("requires_clarification"):
                print(f"   🤔 Needs clarification: {bool(suggestion_result.get('message'))}")
            else:
                print(f"   ❌ Failed: {suggestion_result.get('error', 'unknown')}")
        
        test_results["user_suggestion"] = True
        
        # Test 5: Integration Points
        print(f"\n📋 Test 5: Integration Points")
        print("-" * 50)
        
        # Check method availability
        required_methods = [
            "handle_error_with_user_escalation",
            "apply_user_suggested_fix",
            "_escalate_to_user",
            "_create_user_friendly_error_message",
            "_suggest_user_actions"
        ]
        
        missing_methods = []
        for method in required_methods:
            if hasattr(container_manager, method):
                print(f"   ✅ {method}")
            else:
                print(f"   ❌ {method} - MISSING")
                missing_methods.append(method)
        
        if not missing_methods:
            test_results["integration"] = True
            print(f"   ✅ All required methods available")
        else:
            print(f"   ❌ Missing methods: {missing_methods}")
        
        # Test 6: Error Detection System Integration
        print(f"\n📋 Test 6: Error Detection System Integration")
        print("-" * 50)
        
        # Check if error detection can escalate to container manager
        try:
            await error_detector._escalate_to_user(test_error)
            print(f"   ✅ Error detection can escalate to container manager")
        except Exception as e:
            print(f"   ⚠️ Error detection escalation issue: {e}")
        
        # Summary
        print(f"\n📊 Test Results Summary")
        print("=" * 30)
        
        passed_tests = sum(test_results.values())
        total_tests = len(test_results)
        
        for test_name, result in test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"   {test_name}: {status}")
        
        print(f"\n🎯 Overall Result: {passed_tests}/{total_tests} tests passed")
        
        if passed_tests == total_tests:
            print(f"🎉 All tests passed! Error system is fully integrated.")
            return True
        else:
            print(f"⚠️ Some tests failed. System needs attention.")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_real_world_scenarios():
    """Test real-world error scenarios"""
    
    print(f"\n🌍 Real-World Scenario Testing")
    print("=" * 50)
    
    container_manager = SiteContainerManager()
    
    scenarios = [
        {
            "name": "Frontend JavaScript Error",
            "error": {
                "title": "Uncaught TypeError",
                "description": "Cannot read property 'innerHTML' of null",
                "category": "frontend",
                "severity": "medium"
            }
        },
        {
            "name": "Database Connection Timeout",
            "error": {
                "title": "Database Connection Failed",
                "description": "Connection timeout after 30 seconds",
                "category": "database",
                "severity": "critical"
            }
        },
        {
            "name": "High Memory Usage",
            "error": {
                "title": "Memory Usage Critical",
                "description": "Container using 95% of allocated memory",
                "category": "performance",
                "severity": "high"
            }
        }
    ]
    
    for i, scenario in enumerate(scenarios, 1):
        print(f"\n   Scenario {i}: {scenario['name']}")
        print(f"   " + "-" * 40)
        
        try:
            result = await container_manager.handle_error_with_user_escalation(
                site_name=f"test-site-{i}",
                error_details=scenario["error"]
            )
            
            if result.get("success"):
                print(f"   ✅ Auto-resolved: {result.get('fix_applied', 'unknown')}")
            elif result.get("requires_user_input"):
                print(f"   👤 Escalated to user")
                print(f"   📝 Message length: {len(result.get('user_message', ''))}")
                print(f"   💡 Suggestions: {len(result.get('suggested_actions', []))}")
            else:
                print(f"   ❌ Unhandled: {result.get('error', 'unknown')}")
                
        except Exception as e:
            print(f"   ❌ Exception: {e}")
    
    return True


async def main():
    """Run all integration tests"""
    
    print("🚀 Error System Integration Test Suite")
    print("=" * 60)
    print("Testing the complete error detection and user escalation system\n")
    
    # Run main integration test
    integration_success = await test_complete_error_workflow()
    
    # Run real-world scenarios
    scenarios_success = await test_real_world_scenarios()
    
    # Final summary
    print(f"\n🏁 Final Test Summary")
    print("=" * 30)
    
    if integration_success and scenarios_success:
        print(f"🎉 ALL TESTS PASSED!")
        print(f"   ✅ Error detection system working")
        print(f"   ✅ User escalation system working")
        print(f"   ✅ Integration points verified")
        print(f"   ✅ Real-world scenarios handled")
        print(f"\n💡 The error handling system is ready for production use!")
        return True
    else:
        print(f"❌ SOME TESTS FAILED!")
        print(f"   Integration test: {'✅' if integration_success else '❌'}")
        print(f"   Scenario test: {'✅' if scenarios_success else '❌'}")
        print(f"\n⚠️ System needs fixes before production use.")
        return False


if __name__ == "__main__":
    # Run the integration tests
    result = asyncio.run(main())
    
    print(f"\n{'🎉 Integration tests completed successfully!' if result else '❌ Integration tests failed'}")
    exit(0 if result else 1)
