#!/usr/bin/env python3
"""
Test Automated Security System

This script tests the complete automated security monitoring and notification system,
including threat detection, vulnerability scanning, automated patching, and user notifications.
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime, timedelta
from pathlib import Path
from typing import Any, Dict

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


async def test_automated_security_monitor():
    """Test the automated security monitor"""
    print("\n🔍 Testing Automated Security Monitor...")

    try:
        from agent.security.automated_security_monitor import AutomatedSecurityMonitor

        config = {
            "automated_security": {
                "enabled": True,
                "llm_enabled": True,
                "auto_patch_enabled": True,
                "auto_block_enabled": True,
                "notification_enabled": True,
                "monitoring_interval": 30,
                "threat_threshold": 0.7,
                "vulnerability_threshold": 0.6,
            }
        }

        monitor = AutomatedSecurityMonitor(config)

        # Test 1: Monitor initialization
        print("✅ Monitor initialized successfully")

        # Test 2: Start monitoring
        monitor.start_monitoring()
        print("✅ Monitoring started")

        # Test 3: Get status
        status = monitor.get_status()
        print(f"✅ Monitor status: {status}")

        # Test 4: Let it run for a bit
        print("⏳ Running monitoring for 60 seconds...")
        await asyncio.sleep(60)

        # Test 5: Check for alerts
        alerts = monitor.get_alerts(10)
        print(f"✅ Found {len(alerts)} alerts")

        # Test 6: Check for actions
        actions = monitor.get_actions(10)
        print(f"✅ Found {len(actions)} automated actions")

        # Test 7: Stop monitoring
        monitor.stop_monitoring()
        print("✅ Monitoring stopped")

        return True

    except Exception as e:
        print(f"❌ Automated Security Monitor test failed: {e}")
        return False


async def test_notification_system():
    """Test the notification system"""
    print("\n📧 Testing Notification System...")

    try:
        from agent.security.notification_system import (
            NotificationChannel,
            NotificationPreference,
            SecurityNotificationSystem,
        )

        config = {
            "notifications": {
                "enabled": True,
                "email": {
                    "smtp_server": "smtp.gmail.com",
                    "smtp_port": 587,
                    "username": "<EMAIL>",
                    "password": "test-password",
                    "from_email": "<EMAIL>",
                },
            }
        }

        notification_system = SecurityNotificationSystem(config)

        # Test 1: System initialization
        print("✅ Notification system initialized")

        # Test 2: Set user preferences
        user_preferences = NotificationPreference(
            user_id="test_user",
            email="<EMAIL>",
            enabled_channels=[
                NotificationChannel.CONSOLE,
                NotificationChannel.LOG_FILE,
            ],
            digest_enabled=True,
            digest_interval=3600,
        )

        notification_system.set_user_preferences("test_user", user_preferences)
        print("✅ User preferences set")

        # Test 3: Start notification processor
        notification_system.start_notification_processor()
        print("✅ Notification processor started")

        # Test 4: Send test notifications
        test_notifications = [
            {
                "template_id": "threat_detected",
                "variables": {
                    "threat_type": "DDoS Attack",
                    "severity": "HIGH",
                    "description": "Test DDoS attack from IP *************",
                    "affected_components": ["API endpoints", "Web server"],
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                    "immediate_actions": ["Block IP address", "Enable rate limiting"],
                    "recommendations": [
                        "Monitor traffic patterns",
                        "Review firewall rules",
                    ],
                    "automated_actions": ["IP blocked", "Rate limiting enabled"],
                },
            },
            {
                "template_id": "vulnerability_found",
                "variables": {
                    "vulnerability_type": "SQL Injection",
                    "severity": "HIGH",
                    "location": "api/user_handler.py:45",
                    "description": "Test SQL injection vulnerability",
                    "affected_components": ["User authentication", "Database queries"],
                    "recommendations": [
                        "Use parameterized queries",
                        "Input validation",
                    ],
                    "automated_actions": [
                        "Vulnerability logged",
                        "Code review scheduled",
                    ],
                },
            },
            {
                "template_id": "system_anomaly",
                "variables": {
                    "anomaly_type": "High CPU Usage",
                    "severity": "MEDIUM",
                    "description": "Test high CPU usage anomaly",
                    "affected_components": ["System resources"],
                    "evidence": {"cpu_usage": "95%", "duration": "10 minutes"},
                    "analysis": "Unusual CPU spike detected",
                    "recommendations": ["Monitor processes", "Check for malware"],
                },
            },
        ]

        for i, notification_data in enumerate(test_notifications):
            notification_id = await notification_system.send_notification(
                user_id="test_user",
                template_id=notification_data["template_id"],
                variables=notification_data["variables"],
            )
            print(f"✅ Test notification {i+1} sent: {notification_id}")

        # Test 5: Wait for processing
        await asyncio.sleep(10)

        # Test 6: Check notification history
        history = notification_system.get_notification_history("test_user", 10)
        print(f"✅ Notification history: {len(history)} notifications")

        # Test 7: Get system status
        status = notification_system.get_status()
        print(f"✅ Notification system status: {status}")

        # Test 8: Stop processor
        notification_system.stop_notification_processor()
        print("✅ Notification processor stopped")

        return True

    except Exception as e:
        print(f"❌ Notification system test failed: {e}")
        return False


async def test_integrated_security_system():
    """Test the integrated security system with all components"""
    print("\n🛡️ Testing Integrated Security System...")

    try:
        from agent.security.security_manager import SecurityManager

        config = {
            "security": {
                "enabled": True,
                "mfa": {"enabled": True},
                "oauth2": {"enabled": True},
                "audit": {"enabled": True},
                "compliance": {"enabled": True},
                "threat_detection": {"enabled": True},
            },
            "llm_security": {
                "enabled": True,
                "models": {
                    "threat_analysis": "deepseek-coder:1.3b",
                    "code_review": "deepseek-coder:1.3b",
                    "compliance": "mistral:7b-instruct-q4_0",
                    "general": "qwen2.5-coder:3b",
                },
            },
            "automated_security": {
                "enabled": True,
                "llm_enabled": True,
                "auto_patch_enabled": True,
                "auto_block_enabled": True,
                "notification_enabled": True,
                "monitoring_interval": 30,
            },
            "notifications": {
                "enabled": True,
                "channels": {"console": True, "dashboard": True},
            },
        }

        security_manager = SecurityManager(config)

        # Test 1: Security manager initialization
        print("✅ Security manager initialized")

        # Test 2: Start automated monitoring
        result = await security_manager.start_automated_monitoring()
        print(f"✅ Automated monitoring: {result}")

        # Test 3: Get monitoring status
        status = security_manager.get_automated_monitoring_status()
        print(f"✅ Monitoring status: {status}")

        # Test 4: Test LLM threat analysis
        threat_data = {
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0 (compatible; Bot/1.0)",
            "request_count": 150,
            "time_window": "5 minutes",
            "suspicious_patterns": ["rapid_requests", "bot_signature"],
        }

        llm_result = await security_manager.analyze_threat_with_llm(threat_data)
        print(f"✅ LLM threat analysis: {llm_result.get('success', False)}")

        # Test 5: Send security notification
        notification_result = await security_manager.send_security_notification(
            user_id="test_user",
            template_id="threat_detected",
            variables={
                "threat_type": "Test Threat",
                "severity": "HIGH",
                "description": "Test threat for integrated system",
                "affected_components": ["Test component"],
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "immediate_actions": ["Test action"],
                "recommendations": ["Test recommendation"],
                "automated_actions": ["Test automated action"],
            },
        )
        print(f"✅ Security notification: {notification_result}")

        # Test 6: Let system run for a bit
        print("⏳ Running integrated system for 30 seconds...")
        await asyncio.sleep(30)

        # Test 7: Get security alerts
        alerts = security_manager.get_security_alerts(10)
        print(f"✅ Security alerts: {alerts.get('count', 0)} found")

        # Test 8: Get automated actions
        actions = security_manager.get_automated_actions(10)
        print(f"✅ Automated actions: {actions.get('count', 0)} found")

        # Test 9: Get notification history
        notifications = security_manager.get_notification_history("test_user", 10)
        print(f"✅ Notification history: {notifications.get('count', 0)} notifications")

        # Test 10: Stop automated monitoring
        stop_result = await security_manager.stop_automated_monitoring()
        print(f"✅ Stop monitoring: {stop_result}")

        return True

    except Exception as e:
        print(f"❌ Integrated security system test failed: {e}")
        return False


async def test_automated_patching():
    """Test automated vulnerability patching"""
    print("\n🔧 Testing Automated Patching...")

    try:
        # Create a test vulnerability file
        test_file = Path("test_vulnerability.py")
        test_file.write_text(
            """
# Test file with potential vulnerabilities
import os
import subprocess

def vulnerable_function(user_input):
    # SQL injection vulnerability
    query = f"SELECT * FROM users WHERE id = {user_input}"

    # Command injection vulnerability
    os.system(f"echo {user_input}")
    subprocess.run(f"ls {user_input}", shell=True)

    # Hardcoded secret
    password=os.getenv("PASSWORD")
    api_key=os.getenv("API_KEY")

    return query

# More vulnerabilities
eval("print('Hello')")
exec("print('World')")
        """
        )

        print("✅ Test vulnerability file created")

        # Test vulnerability scanning
        from agent.security.automated_security_monitor import AutomatedSecurityMonitor

        config = {
            "automated_security": {
                "enabled": True,
                "auto_patch_enabled": True,
                "vulnerability_threshold": 0.6,
            }
        }

        monitor = AutomatedSecurityMonitor(config)

        # Run vulnerability scan
        vulnerabilities = await monitor._scan_code_vulnerabilities()
        print(f"✅ Found {len(vulnerabilities)} vulnerabilities")

        for vuln in vulnerabilities:
            print(f"   - {vuln['type']}: {vuln['description']}")

        # Test dependency scanning
        dependencies = await monitor._scan_dependencies()
        print(f"✅ Found {len(dependencies)} outdated dependencies")

        # Clean up
        test_file.unlink()
        print("✅ Test file cleaned up")

        return True

    except Exception as e:
        print(f"❌ Automated patching test failed: {e}")
        return False


async def test_notification_templates():
    """Test notification template rendering"""
    print("\n📝 Testing Notification Templates...")

    try:
        from agent.security.notification_system import SecurityNotificationSystem

        config = {"notifications": {"enabled": True}}
        notification_system = SecurityNotificationSystem(config)

        # Test template rendering
        test_variables = {
            "threat_type": "Malware",
            "severity": "CRITICAL",
            "description": "Test malware threat",
            "affected_components": ["System files", "User data"],
            "timestamp": "2025-01-15 14:30:00",
            "immediate_actions": ["Isolate system", "Scan for malware"],
            "recommendations": ["Update antivirus", "Review logs"],
            "automated_actions": ["System isolated", "Scan initiated"],
        }

        template = notification_system.templates["threat_detected"]
        rendered_subject = notification_system._render_template(
            template.subject, test_variables
        )
        rendered_body = notification_system._render_template(
            template.body, test_variables
        )

        print("✅ Template rendering successful")
        print(f"   Subject: {rendered_subject}")
        print(f"   Body length: {len(rendered_body)} characters")

        # Test all templates
        for template_id, template in notification_system.templates.items():
            print(f"✅ Template '{template_id}' available")

        return True

    except Exception as e:
        print(f"❌ Notification templates test failed: {e}")
        return False


async def test_security_metrics():
    """Test security metrics collection"""
    print("\n📊 Testing Security Metrics...")

    try:
        from agent.security.security_manager import SecurityManager

        config = {
            "security": {"enabled": True},
            "llm_security": {"enabled": True},
            "automated_security": {"enabled": True},
            "notifications": {"enabled": True},
        }

        security_manager = SecurityManager(config)

        # Test LLM metrics
        llm_metrics = security_manager.get_llm_security_metrics()
        print(f"✅ LLM metrics: {llm_metrics}")

        # Test monitoring status
        monitoring_status = security_manager.get_automated_monitoring_status()
        print(f"✅ Monitoring status: {monitoring_status}")

        # Test notification status
        notification_status = security_manager.get_notification_status()
        print(f"✅ Notification status: {notification_status}")

        return True

    except Exception as e:
        print(f"❌ Security metrics test failed: {e}")
        return False


async def main():
    """Run all automated security tests"""
    print("🚀 Starting Automated Security System Tests")
    print("=" * 60)

    test_results = {}

    # Test 1: Automated Security Monitor
    test_results["automated_monitor"] = await test_automated_security_monitor()

    # Test 2: Notification System
    test_results["notification_system"] = await test_notification_system()

    # Test 3: Integrated Security System
    test_results["integrated_system"] = await test_integrated_security_system()

    # Test 4: Automated Patching
    test_results["automated_patching"] = await test_automated_patching()

    # Test 5: Notification Templates
    test_results["notification_templates"] = await test_notification_templates()

    # Test 6: Security Metrics
    test_results["security_metrics"] = await test_security_metrics()

    # Summary
    print("\n" + "=" * 60)
    print("📋 Test Results Summary")
    print("=" * 60)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:.<30} {status}")
        if result:
            passed += 1

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All automated security tests passed!")
        return True
    else:
        print("⚠️ Some tests failed. Please review the output above.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)
