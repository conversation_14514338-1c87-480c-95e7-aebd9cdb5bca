#!/usr/bin/env python3
"""
Advanced Learning Enhancements Test Suite

Tests the new CLI commands and API routes for advanced learning enhancements:
- Meta Learning Optimizer
- Pareto Optimizer
- Workload Predictor
- Cascade Predictor
- Federated Learning Manager
- Capability Discovery
- Adversarial Detector
- Degradation Manager
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path

# Fix Windows console encoding for emoji support
if hasattr(sys.stdout, "reconfigure"):
    # on Windows, switch the console to UTF-8 output to support emojis
    sys.stdout.reconfigure(encoding="utf-8", errors="replace")

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_cli_commands_import():
    """Test that CLI commands can be imported"""
    print("🧪 Testing CLI Commands Import")
    print("=" * 50)

    try:
        from agent.cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        print("✅ Advanced Learning Enhancements CLI commands imported successfully")
        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False


def test_api_routes_import():
    """Test that API routes can be imported"""
    print("\n🧪 Testing API Routes Import")
    print("=" * 50)

    try:
        from agent.api.advanced_learning_enhancements_routes import router

        print("✅ Advanced Learning Enhancements API routes imported successfully")

        # Check that router has routes
        routes = [route.path for route in router.routes]
        print(f"✅ Router has {len(routes)} routes defined")

        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False


def test_cli_commands_structure():
    """Test CLI commands structure and methods"""
    print("\n🧪 Testing CLI Commands Structure")
    print("=" * 50)

    try:
        from agent.cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        # Create a mock agent for testing
        class MockAgent:
            def __init__(self):
                self.meta_learning_optimizer = None
                self.pareto_optimizer = None
                self.workload_predictor = None
                self.cascade_predictor = None
                self.federated_learning_manager = None
                self.capability_discovery = None
                self.adversarial_detector = None
                self.degradation_manager = None

        mock_agent = MockAgent()
        commands = AdvancedLearningEnhancementsCommands(mock_agent)

        # Test that all expected methods exist
        expected_methods = [
            # Meta Learning Optimizer
            "optimize_learning_rates",
            "get_meta_learning_insights",
            # Pareto Optimizer
            "find_pareto_solutions",
            "get_pareto_frontier",
            # Workload Predictor
            "predict_demand",
            "get_workload_insights",
            # Cascade Predictor
            "detect_cascade_risk",
            "get_cascade_insights",
            # Federated Learning Manager
            "start_federated_learning",
            "get_federated_status",
            # Capability Discovery
            "discover_capabilities",
            "get_capability_insights",
            # Adversarial Detector
            "detect_adversarial_activity",
            "get_adversarial_insights",
            # Degradation Manager
            "assess_degradation_risk",
            "get_degradation_insights",
            # System-wide
            "get_all_enhancements_status",
            "run_enhancement_cycle",
        ]

        for method_name in expected_methods:
            if hasattr(commands, method_name):
                print(f"✅ {method_name} method available")
            else:
                print(f"❌ {method_name} method missing")
                return False

        return True
    except Exception as e:
        print(f"❌ CLI commands structure test failed: {e}")
        return False


def test_api_routes_structure():
    """Test API routes structure"""
    print("\n🧪 Testing API Routes Structure")
    print("=" * 50)

    try:
        from agent.api.advanced_learning_enhancements_routes import router

        # Check that all expected routes are defined
        expected_routes = [
            "/api/advanced-learning-enhancements/meta-learning/optimize",
            "/api/advanced-learning-enhancements/meta-learning/insights",
            "/api/advanced-learning-enhancements/pareto/solutions",
            "/api/advanced-learning-enhancements/pareto/frontier",
            "/api/advanced-learning-enhancements/workload/predict",
            "/api/advanced-learning-enhancements/workload/insights",
            "/api/advanced-learning-enhancements/cascade/detect",
            "/api/advanced-learning-enhancements/cascade/insights",
            "/api/advanced-learning-enhancements/federated/start",
            "/api/advanced-learning-enhancements/federated/status",
            "/api/advanced-learning-enhancements/capability/discover",
            "/api/advanced-learning-enhancements/capability/insights",
            "/api/advanced-learning-enhancements/adversarial/detect",
            "/api/advanced-learning-enhancements/adversarial/insights",
            "/api/advanced-learning-enhancements/degradation/assess",
            "/api/advanced-learning-enhancements/degradation/insights",
            "/api/advanced-learning-enhancements/status",
            "/api/advanced-learning-enhancements/cycle",
            "/api/advanced-learning-enhancements/health",
        ]

        routes = [route.path for route in router.routes]

        for expected_route in expected_routes:
            if any(expected_route in route for route in routes):
                print(f"✅ {expected_route} route available")
            else:
                print(f"⚠️ {expected_route} route not found")

        return True
    except Exception as e:
        print(f"❌ API routes structure test failed: {e}")
        return False


def test_pydantic_models():
    """Test Pydantic models for API requests"""
    print("\n🧪 Testing Pydantic Models")
    print("=" * 50)

    try:
        from agent.api.advanced_learning_enhancements_routes import (
            AdversarialDetectionRequest,
            CapabilityDiscoveryRequest,
            CascadeRiskRequest,
            DegradationAssessmentRequest,
            FederatedLearningRequest,
            ModelPerformanceRequest,
            ParetoOptimizationRequest,
            WorkloadPredictionRequest,
        )

        # Test ModelPerformanceRequest
        model_perf_data = {
            "model_performance": {
                "model1": [0.8, 0.85, 0.9],
                "model2": [0.7, 0.75, 0.8],
            }
        }
        model_perf_request = ModelPerformanceRequest(**model_perf_data)
        print("✅ ModelPerformanceRequest model works")

        # Test ParetoOptimizationRequest
        pareto_data = {
            "objectives": ["speed", "quality"],
            "current_performance": {"speed": 0.8, "quality": 0.9},
            "constraints": {"max_memory": 4096},
        }
        pareto_request = ParetoOptimizationRequest(**pareto_data)
        print("✅ ParetoOptimizationRequest model works")

        # Test WorkloadPredictionRequest
        workload_data = {
            "historical_patterns": {
                "requests_per_minute": [10, 15, 20, 25, 30],
                "response_times": [100, 120, 150, 180, 200],
            },
            "external_factors": {"time_of_day": 0.8, "day_of_week": 0.6},
        }
        workload_request = WorkloadPredictionRequest(**workload_data)
        print("✅ WorkloadPredictionRequest model works")

        # Test CascadeRiskRequest
        cascade_data = {
            "current_loads": {"model1": 0.8, "model2": 0.7},
            "failure_patterns": {
                "model1": [0.1, 0.15, 0.2],
                "model2": [0.05, 0.1, 0.15],
            },
        }
        cascade_request = CascadeRiskRequest(**cascade_data)
        print("✅ CascadeRiskRequest model works")

        # Test FederatedLearningRequest
        federated_data = {
            "participants": ["node1", "node2", "node3"],
            "model_configuration": {"learning_rate": 0.01, "batch_size": 32},
        }
        federated_request = FederatedLearningRequest(**federated_data)
        print("✅ FederatedLearningRequest model works")

        # Test CapabilityDiscoveryRequest
        capability_data = {
            "model_id": "test_model_001",
            "test_scenarios": [{"scenario": "code_generation", "complexity": "high"}],
        }
        capability_request = CapabilityDiscoveryRequest(**capability_data)
        print("✅ CapabilityDiscoveryRequest model works")

        # Test AdversarialDetectionRequest
        adversarial_data = {
            "user_behavior": {"request_pattern": "suspicious", "frequency": "high"},
            "system_metrics": {"cpu_usage": 0.9, "memory_usage": 0.8},
        }
        adversarial_request = AdversarialDetectionRequest(**adversarial_data)
        print("✅ AdversarialDetectionRequest model works")

        # Test DegradationAssessmentRequest
        degradation_data = {
            "system_metrics": {
                "cpu_usage": 0.9,
                "memory_usage": 0.8,
                "response_time": 2000,
            },
            "performance_thresholds": {
                "max_cpu": 0.8,
                "max_memory": 0.8,
                "max_response_time": 1500,
            },
        }
        degradation_request = DegradationAssessmentRequest(**degradation_data)
        print("✅ DegradationAssessmentRequest model works")

        return True
    except Exception as e:
        print(f"❌ Pydantic models test failed: {e}")
        return False


async def test_cli_commands_functionality():
    """Test CLI commands functionality with mock data"""
    print("\n🧪 Testing CLI Commands Functionality")
    print("=" * 50)

    try:
        from agent.cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        # Create a mock agent with mock components
        class MockMetaLearningOptimizer:
            def optimize_learning_rates(self, model_performance):
                return {"model1": 0.001, "model2": 0.002}

        class MockParetoOptimizer:
            def find_pareto_optimal_routing(
                self, objectives, current_performance, constraints
            ):
                return [{"solution": 1}, {"solution": 2}, {"solution": 3}]

        class MockWorkloadPredictor:
            def predict_demand_spikes(self, historical_patterns, external_factors):
                class MockForecast:
                    def __init__(self):
                        self.predicted_demand = 1.0
                        self.confidence = 0.85
                        self.time_horizon = 3600
                        self.risk_level = "low"
                        self.recommendations = ["scale_up"]

                return MockForecast()

        class MockCascadePredictor:
            def detect_cascade_risk(self, current_loads, failure_patterns):
                class MockRiskAssessment:
                    def __init__(self):
                        self.risk_level = "medium"
                        self.risk_score = 0.6
                        self.vulnerable_components = ["model1"]
                        self.mitigation_strategies = ["load_balancing"]

                return MockRiskAssessment()

        class MockAgent:
            def __init__(self):
                self.meta_learning_optimizer = MockMetaLearningOptimizer()
                self.pareto_optimizer = MockParetoOptimizer()
                self.workload_predictor = MockWorkloadPredictor()
                self.cascade_predictor = MockCascadePredictor()

        mock_agent = MockAgent()
        commands = AdvancedLearningEnhancementsCommands(mock_agent)

        # Test meta learning optimization
        result = await commands.optimize_learning_rates(
            {"model1": [0.8, 0.85, 0.9], "model2": [0.7, 0.75, 0.8]}
        )
        if result["success"]:
            print("✅ Meta learning optimization works")
        else:
            print(f"❌ Meta learning optimization failed: {result['error']}")
            return False

        # Test Pareto optimization
        result = await commands.find_pareto_solutions(
            ["speed", "quality"], {"speed": 0.8, "quality": 0.9}, {"max_memory": 4096}
        )
        if result["success"]:
            print("✅ Pareto optimization works")
        else:
            print(f"❌ Pareto optimization failed: {result['error']}")
            return False

        # Test workload prediction
        result = await commands.predict_demand(
            {"requests_per_minute": [10, 15, 20, 25, 30]}, {"time_of_day": 0.8}
        )
        if result["success"]:
            print("✅ Workload prediction works")
        else:
            print(f"❌ Workload prediction failed: {result['error']}")
            return False

        # Test cascade risk detection
        result = await commands.detect_cascade_risk(
            {"model1": 0.8, "model2": 0.7}, {"model1": [0.1, 0.15, 0.2]}
        )
        if result["success"]:
            print("✅ Cascade risk detection works")
        else:
            print(f"❌ Cascade risk detection failed: {result['error']}")
            return False

        return True
    except Exception as e:
        print(f"❌ CLI commands functionality test failed: {e}")
        return False


def test_api_integration():
    """Test API integration with main app"""
    print("\n🧪 Testing API Integration")
    print("=" * 50)

    try:
        # Test that the router is properly included in main app
        from agent.api.main import app

        # Check if the router is included
        routes = [route.path for route in app.routes]

        # Look for advanced learning enhancements routes
        enhancement_routes = [
            route for route in routes if "advanced-learning-enhancements" in route
        ]

        if enhancement_routes:
            print(
                f"✅ Found {len(enhancement_routes)} advanced learning enhancements routes in main app"
            )
            for route in enhancement_routes[:5]:  # Show first 5
                print(f"   - {route}")
        else:
            print("❌ No advanced learning enhancements routes found in main app")
            return False

        return True
    except Exception as e:
        print(f"❌ API integration test failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 Advanced Learning Enhancements Test Suite")
    print("=" * 60)

    tests = [
        ("CLI Commands Import", test_cli_commands_import),
        ("API Routes Import", test_api_routes_import),
        ("CLI Commands Structure", test_cli_commands_structure),
        ("API Routes Structure", test_api_routes_structure),
        ("Pydantic Models", test_pydantic_models),
        ("CLI Commands Functionality", test_cli_commands_functionality),
        ("API Integration", test_api_integration),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 40)

        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()

            results.append((test_name, result))

            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")

        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))

    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 40)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")

    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")

    if passed == total:
        print(
            "🎉 All tests passed! Advanced Learning Enhancements are fully integrated."
        )
        print("\n📋 Integration Summary:")
        print("✅ CLI commands created and functional")
        print("✅ API routes created and integrated")
        print("✅ Pydantic models validated")
        print("✅ All components accessible via CLI and API")
        print("✅ Main API app includes enhancement routes")
        return True
    else:
        print("⚠️  Some tests failed. Please check the integration.")
        return False


if __name__ == "__main__":
    asyncio.run(main())
