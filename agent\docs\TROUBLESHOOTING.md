# AI Coding Agent - Troubleshooting Guide

This guide provides solutions for common issues encountered while using the AI Coding Agent project.

## Table of Contents

1. [Installation Issues](#installation-issues)
2. [SSL and Security Issues](#ssl-and-security-issues)
3. [Database Issues](#database-issues)
4. [Model Integration Issues](#model-integration-issues)
5. [Server Issues](#server-issues)
6. [Code Quality Issues](#code-quality-issues)
7. [Performance Issues](#performance-issues)
8. [Development Environment Issues](#development-environment-issues)
9. [General Debugging](#general-debugging)

## Installation Issues

### Python Version Problems

**Problem**: `Python 3.11+ is required`

**Solution**:
```bash
# Check current Python version
python --version

# Install Python 3.11+ if needed
# Windows: Download from python.org
# macOS: brew install python@3.11
# Linux: sudo apt install python3.11
```

### Virtual Environment Issues

**Problem**: Virtual environment not activating

**Solution**:
```bash
# Windows
.venv\Scripts\activate

# macOS/Linux
source .venv/bin/activate

# Verify activation
which python  # Should point to .venv directory
```

**Problem**: Virtual environment not found

**Solution**:
```bash
# Recreate virtual environment
rm -rf .venv
python -m venv .venv
source .venv/bin/activate  # or .venv\Scripts\activate on Windows
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

### Dependency Installation Issues

**Problem**: `pip install` fails with compilation errors

**Solution**:
```bash
# Update pip first
pip install --upgrade pip

# Install build tools (Windows)
pip install wheel setuptools

# Try installing with verbose output
pip install -r requirements.txt -v

# If specific package fails, install individually
pip install package-name --no-cache-dir
```

**Problem**: Rust compilation errors (memory-profiler, line-profiler)

**Solution**:
```bash
# Install Rust toolchain
curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh

# Or skip these packages for now
pip install -r requirements.txt
# Install profiling tools separately if needed
```

## SSL and Security Issues

### SSL Certificate Problems

**Problem**: SSL certificate not found

**Solution**:
```python
# Check SSL configuration
from src.ssl_manager import SSLManager
ssl_manager = SSLManager(config)
status = ssl_manager.get_certificate_status("example.com")
print(status)

# Generate new certificate
result = ssl_manager.create_self_signed_cert("example.com")
print(result)
```

**Problem**: Let's Encrypt certificate renewal fails

**Solution**:
```bash
# Check certificate expiry
openssl x509 -in ssl/cert.pem -text -noout | grep "Not After"

# Manual renewal
python -m src setup-ssl -d example.com -p lets-encrypt

# Check Let's Encrypt rate limits
# Wait if rate limit exceeded
```

**Problem**: Permission denied for SSL files

**Solution**:
```bash
# Check file permissions
ls -la ssl/

# Fix permissions
chmod 600 ssl/*.pem
chmod 700 ssl/

# Check ownership
sudo chown $USER:$USER ssl/
```

### Security Manager Issues

**Problem**: Rate limiting not working

**Solution**:
```python
# Check security configuration
from src.security_manager import SecurityManager
security = SecurityManager(config)

# Test rate limiting
result = security.check_rate_limit("127.0.0.1")
print(result)

# Check blocked IPs
blocked = security.get_blocked_ips()
print(blocked)
```

**Problem**: IP blocking not working

**Solution**:
```python
# Check security database
import sqlite3
conn = sqlite3.connect("security.db")
cursor = conn.cursor()
cursor.execute("SELECT * FROM blocked_ips")
print(cursor.fetchall())
conn.close()

# Clear blocked IPs if needed
security.clear_blocked_ips()
```

## Database Issues

### Connection Problems

**Problem**: Database connection failed

**Solution**:
```python
# Check database path
from src.db.database_manager import DatabaseManager
import os

db_path = "data/app.db"
print(f"Database exists: {os.path.exists(db_path)}")

# Test connection
try:
    db = DatabaseManager(db_path)
    db.test_connection()
    print("Database connection successful")
except Exception as e:
    print(f"Database error: {e}")
```

**Problem**: Database locked

**Solution**:
```bash
# Check for other processes using the database
lsof data/app.db  # Linux/macOS
# or
tasklist | findstr python  # Windows

# Kill processes if needed
kill -9 <process_id>

# Or delete and recreate database
rm data/app.db
python -c "from src.db.database_manager import DatabaseManager; DatabaseManager('data/app.db').create_tables()"
```

### Schema Issues

**Problem**: Database schema mismatch

**Solution**:
```python
# Recreate database tables
from src.db.database_manager import DatabaseManager

db = DatabaseManager("data/app.db")
db.create_tables()
print("Tables created successfully")
```

**Problem**: Migration errors

**Solution**:
```bash
# Backup current database
cp data/app.db data/app.db.backup

# Run migrations
alembic upgrade head

# If migration fails, restore backup and recreate
cp data/app.db.backup data/app.db
python -c "from src.db.database_manager import DatabaseManager; DatabaseManager('data/app.db').create_tables()"
```

## Model Integration Issues

### Ollama Connection Problems

**Problem**: Ollama not responding

**Solution**:
```bash
# Check if Ollama is running
curl http://localhost:11434/api/tags

# Start Ollama if not running
ollama serve

# Check Ollama logs
ollama logs
```

**Problem**: Model not found

**Solution**:
```bash
# List available models
ollama list

# Pull required model
ollama pull deepseek-coder:1.3b
ollama pull yi-coder:1.5b
ollama pull qwen2.5-coder:3b
ollama pull starcoder2:3b
```

**Problem**: Model response errors

**Solution**:
```python
# Test model directly
import requests

response = requests.post("http://localhost:11434/api/generate", json={
    "model": "deepseek-coder:1.3b",
    "prompt": "Hello, world!"
})
print(response.json())

# Check model status
from src.model_router import ModelRouter
router = ModelRouter(config)
status = router.get_model_status("deepseek-coder:1.3b")
print(status)
```

### Model Configuration Issues

**Problem**: Wrong model endpoint

**Solution**:
```python
# Check model configuration
config = {
    "models": {
        "endpoints": {
            "deepseek-coder:1.3b": "http://localhost:11434",
            "yi-coder:1.5b": "http://localhost:11434"
        }
    }
}

# Test endpoint connectivity
import requests
for model, endpoint in config["models"]["endpoints"].items():
    try:
        response = requests.get(f"{endpoint}/api/tags")
        print(f"{model}: {response.status_code}")
    except Exception as e:
        print(f"{model}: {e}")
```

## Server Issues

### Server Startup Problems

**Problem**: Port already in use

**Solution**:
```bash
# Find process using port
lsof -i :5000  # Linux/macOS
netstat -ano | findstr :5000  # Windows

# Kill process
kill -9 <process_id>  # Linux/macOS
taskkill /PID <process_id> /F  # Windows

# Or use different port
python -m src --port 5001
```

**Problem**: Server not starting

**Solution**:
```python
# Check server configuration
from src.server_launcher import ServerLauncher

config = {
    "port": 5000,
    "host": "localhost",
    "ssl": {"enabled": False}
}

server = ServerLauncher(config)
status = server.get_server_status()
print(status)

# Start server with debug
server.start_server(debug=True)
```

### Hot Reload Issues

**Problem**: Hot reload not working

**Solution**:
```python
# Check watchdog installation
pip install watchdog

# Enable debug logging
import logging
logging.basicConfig(level=logging.DEBUG)

# Test file watching
from watchdog.observers import Observer
from watchdog.events import FileSystemEventHandler

class TestHandler(FileSystemEventHandler):
    def on_modified(self, event):
        print(f"File modified: {event.src_path}")

observer = Observer()
observer.schedule(TestHandler(), path='.', recursive=True)
observer.start()
```

## Code Quality Issues

### Black Formatting Issues

**Problem**: Code not formatted correctly

**Solution**:
```bash
# Format code manually
black src tests scripts

# Check what would be changed
black --check --diff src tests scripts

# Format specific file
black src/main.py
```

**Problem**: Black configuration issues

**Solution**:
```bash
# Check Black configuration
cat pyproject.toml | grep -A 10 "\[tool.black\]"

# Use default configuration
black --line-length 88 src tests scripts
```

### isort Import Issues

**Problem**: Imports not sorted correctly

**Solution**:
```bash
# Sort imports
isort src tests scripts

# Check import order
isort --check-only --diff src tests scripts

# Use Black profile
isort --profile black src tests scripts
```

### flake8 Linting Issues

**Problem**: Too many linting errors

**Solution**:
```bash
# Run flake8 with specific rules
flake8 src tests scripts --max-line-length=88 --extend-ignore=E203,W503,W504

# Fix common issues
# E501: Line too long - break long lines
# E203: Whitespace before ':' - ignore (Black handles this)
# W503: Line break before binary operator - ignore (Black handles this)
```

### mypy Type Issues

**Problem**: Type checking errors

**Solution**:
```bash
# Run mypy with specific configuration
mypy src --ignore-missing-imports

# Add type hints to functions
def example_function(param: str) -> int:
    return len(param)

# Use type ignores for third-party libraries
import some_library  # type: ignore
```

## Performance Issues

### Memory Problems

**Problem**: High memory usage

**Solution**:
```python
# Monitor memory usage
import psutil
import os

process = psutil.Process(os.getpid())
print(f"Memory usage: {process.memory_info().rss / 1024 / 1024:.2f} MB")

# Use memory profiler
from memory_profiler import profile

@profile
def memory_intensive_function():
    # Your code here
    pass
```

**Problem**: Memory leaks

**Solution**:
```python
# Check for circular references
import gc

# Force garbage collection
gc.collect()

# Check object counts
print(gc.get_count())
print(gc.get_stats())
```

### Slow Performance

**Problem**: Slow model responses

**Solution**:
```python
# Profile model calls
import time
from src.model_router import ModelRouter

router = ModelRouter(config)

start_time = time.time()
response = router.route_request("Test prompt")
end_time = time.time()

print(f"Response time: {end_time - start_time:.2f} seconds")

# Check model performance
status = router.get_model_status("deepseek-coder:1.3b")
print(f"Model status: {status}")
```

**Problem**: Slow database queries

**Solution**:
```python
# Profile database queries
import time
from src.db.database_manager import DatabaseManager

db = DatabaseManager("data/app.db")

start_time = time.time()
result = db.get_user(1)
end_time = time.time()

print(f"Query time: {end_time - start_time:.4f} seconds")

# Add database indexes if needed
db.create_indexes()
```

## Development Environment Issues

### Pre-commit Hook Problems

**Problem**: Pre-commit hooks failing

**Solution**:
```bash
# Reinstall pre-commit hooks
pre-commit uninstall
pre-commit install

# Run hooks manually
pre-commit run --all-files

# Skip hooks for this commit
git commit --no-verify -m "Emergency fix"
```

**Problem**: Pre-commit hooks not found

**Solution**:
```bash
# Install pre-commit
pip install pre-commit

# Install hooks
pre-commit install

# Update hooks
pre-commit autoupdate
```

### VS Code Configuration Issues

**Problem**: VS Code not using correct Python interpreter

**Solution**:
```json
// .vscode/settings.json
{
    "python.defaultInterpreterPath": "./.venv/bin/python",
    "python.linting.enabled": true,
    "python.formatting.provider": "black",
    "editor.formatOnSave": true
}
```

**Problem**: VS Code extensions not working

**Solution**:
```bash
# Install recommended extensions
code --install-extension ms-python.python
code --install-extension ms-python.black-formatter
code --install-extension ms-python.isort
code --install-extension ms-python.flake8
```

### Makefile Issues

**Problem**: Make commands not working

**Solution**:
```bash
# Check if Make is installed
make --version

# Windows: Install Make
# Using Chocolatey: choco install make
# Using WSL: Use Linux commands

# Use Python directly instead
python scripts/quality_check.py
python -m black src tests scripts
python -m isort src tests scripts
```

## General Debugging

### Enable Debug Logging

```python
import logging

# Set up debug logging
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug.log'),
        logging.StreamHandler()
    ]
)

# Use in specific modules
logger = logging.getLogger(__name__)
logger.debug("Debug information")
```

### Run Health Check

```python
# Create health check script
def health_check():
    issues = []

    # Check Python version
    import sys
    if sys.version_info < (3, 11):
        issues.append("Python 3.11+ required")

    # Check virtual environment
    import os
    if not os.getenv('VIRTUAL_ENV'):
        issues.append("Virtual environment not activated")

    # Check dependencies
    try:
        import flask
        import requests
        import sqlite3
    except ImportError as e:
        issues.append(f"Missing dependency: {e}")

    # Check database
    try:
        from src.db.database_manager import DatabaseManager
        db = DatabaseManager("data/app.db")
        db.test_connection()
    except Exception as e:
        issues.append(f"Database issue: {e}")

    # Check Ollama
    try:
        import requests
        response = requests.get("http://localhost:11434/api/tags", timeout=5)
        if response.status_code != 200:
            issues.append("Ollama not responding")
    except Exception as e:
        issues.append(f"Ollama issue: {e}")

    return issues

# Run health check
issues = health_check()
if issues:
    print("Health check failed:")
    for issue in issues:
        print(f"  - {issue}")
else:
    print("Health check passed!")
```

### Common Error Messages

**Problem**: `ModuleNotFoundError: No module named 'src'`

**Solution**:
```bash
# Add project root to Python path
export PYTHONPATH="${PYTHONPATH}:$(pwd)"

# Or run from project root
cd /path/to/AICodingAgent
python -m src
```

**Problem**: `PermissionError: [Errno 13] Permission denied`

**Solution**:
```bash
# Check file permissions
ls -la

# Fix permissions
chmod +x scripts/*.py
chmod 600 config/*.json
chmod 700 data/

# Check ownership
sudo chown -R $USER:$USER .
```

**Problem**: `ConnectionRefusedError: [Errno 61] Connection refused`

**Solution**:
```bash
# Check if service is running
ps aux | grep ollama
ps aux | grep python

# Start required services
ollama serve
python -m src
```

### Getting Help

1. **Check Logs**: Look for error messages in log files
2. **Run Quality Check**: `python scripts/quality_check.py`
3. **Check Configuration**: Verify all config files are correct
4. **Test Components**: Test each component individually
5. **Search Issues**: Check for similar issues in project documentation
6. **Ask for Help**: Create an issue with detailed error information

### Emergency Recovery

If the system is completely broken:

```bash
# Backup current state
cp -r . ../AICodingAgent_backup

# Clean slate approach
rm -rf .venv
rm -rf __pycache__
rm -rf .pytest_cache
rm -rf .mypy_cache
rm -f *.log

# Reinstall everything
python -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
pip install -r requirements-dev.txt
python scripts/setup_dev_environment.py
```

### Performance Monitoring

```python
# Monitor system resources
import psutil
import time

def monitor_resources():
    while True:
        cpu_percent = psutil.cpu_percent()
        memory_percent = psutil.virtual_memory().percent
        disk_percent = psutil.disk_usage('/').percent

        print(f"CPU: {cpu_percent}%, Memory: {memory_percent}%, Disk: {disk_percent}%")
        time.sleep(5)

# Run monitoring
monitor_resources()
```

This troubleshooting guide should help resolve most common issues. If you encounter a problem not covered here, please check the project documentation or create an issue with detailed information about the problem.
