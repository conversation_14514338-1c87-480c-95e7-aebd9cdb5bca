#!/usr/bin/env python3
"""
API Routes for Project Orchestration

This module provides REST API endpoints for:
1. Project roadmap management
2. Task delegation and execution
3. Quality validation and gates
4. Context management
5. Cleanup automation
6. Monitoring and status
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect, status
from pydantic import BaseModel, Field

# Add project root to path for imports
import sys
sys.path.insert(0, str(Path(__file__).parent.parent))

from agent.core.agents.architect_agent import ArchitectAgent
from agent.core.context.shared_context_manager import SharedContextManager
from agent.core.cursor_rules_enforcer import CursorRulesEnforcer
from agent.core.project_models import Roadmap
from agent.core.project_store import ProjectStore
from agent.core.quality.validation_pipeline import ValidationPipeline

logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/v1/orchestration", tags=["orchestration"])


# Pydantic models for request/response
class ProjectCreateRequest(BaseModel):
    project_id: str = Field(..., description="Unique project identifier")
    title: str = Field(..., description="Project title")
    description: Optional[str] = Field(None, description="Project description")
    config_path: Optional[str] = Field("config/agent_config.json", description="Agent configuration file")


class ProjectStartRequest(BaseModel):
    project_id: str = Field(..., description="Project identifier")
    roadmap_data: Optional[Dict[str, Any]] = Field(None, description="Roadmap data for roadmap-first start")
    config_path: Optional[str] = Field("config/agent_config.json", description="Agent configuration file")


class ValidationRequest(BaseModel):
    suite: Optional[str] = Field("default", description="Validation suite name")
    config_path: Optional[str] = Field("config/validation_config.json", description="Validation configuration")


class ContextSetRequest(BaseModel):
    session_id: str = Field(..., description="Context session ID")
    key: str = Field(..., description="Context key")
    value: Any = Field(..., description="Context value")
    agent_id: Optional[str] = Field("api", description="Agent ID setting the context")
    tags: Optional[List[str]] = Field(None, description="Context tags")
    ttl_seconds: Optional[int] = Field(None, description="Time to live in seconds")
    global_scope: Optional[bool] = Field(False, description="Set in global scope")


class ContextSearchRequest(BaseModel):
    session_id: str = Field(..., description="Context session ID")
    tags: Optional[List[str]] = Field(None, description="Tags to search for")
    agent_id: Optional[str] = Field(None, description="Filter by agent ID")
    key_pattern: Optional[str] = Field(None, description="Key pattern (regex)")


class CleanupRequest(BaseModel):
    dry_run: Optional[bool] = Field(True, description="Show what would be cleaned without doing it")


class APIResponse(BaseModel):
    success: bool = Field(..., description="Operation success status")
    message: Optional[str] = Field(None, description="Response message")
    data: Optional[Dict[str, Any]] = Field(None, description="Response data")
    error: Optional[str] = Field(None, description="Error message if failed")


# Dependency to get project store
def get_project_store() -> ProjectStore:
    return ProjectStore()


# Dependency to get context manager
def get_context_manager() -> SharedContextManager:
    return SharedContextManager()


# Dependency to get cursor rules enforcer
def get_enforcer() -> CursorRulesEnforcer:
    return CursorRulesEnforcer()


# Project Management Endpoints
@router.post("/projects", response_model=APIResponse)
async def create_project(request: ProjectCreateRequest, store: ProjectStore = Depends(get_project_store)):
    """Create a new project"""
    try:
        # Create basic roadmap
        roadmap_data = {
            "id": f"roadmap_{request.project_id}",
            "title": request.title,
            "description": request.description or f"Roadmap for {request.title}",
            "phases": []
        }

        roadmap = Roadmap.from_dict(roadmap_data)
        store.save_roadmap(request.project_id, roadmap)

        # Initialize project state
        from agent.core.project_models import ProjectState
        state = ProjectState(
            project_id=request.project_id,
            roadmap_id=f"roadmap_{request.project_id}",
            current_phase="planning",
            current_step="",
            current_task=""
        )
        store.save_state(request.project_id, state)

        return APIResponse(
            success=True,
            message=f"Created project: {request.project_id}",
            data={
                "project_id": request.project_id,
                "title": request.title,
                "description": request.description
            }
        )

    except Exception as e:
        logger.error(f"Error creating project: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create project: {str(e)}"
        )


@router.post("/projects/{project_id}/start-with-roadmap", response_model=APIResponse)
async def start_project_with_roadmap(project_id: str, request: ProjectStartRequest):
    """Start project orchestration with a roadmap"""
    try:
        if not request.roadmap_data:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Roadmap data is required for roadmap-first start"
            )

        # Initialize architect agent
        config_path = request.config_path if request.config_path is not None else "config/agent_config.json"
        agent = ArchitectAgent(config_path)

        # Start orchestration
        result = await agent.start_with_roadmap(project_id, request.roadmap_data)

        if result["success"]:
            return APIResponse(
                success=True,
                message=f"Started project orchestration: {project_id}",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Unknown error")
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting orchestration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start orchestration: {str(e)}"
        )


@router.post("/projects/{project_id}/start-with-project", response_model=APIResponse)
async def start_project_with_inference(project_id: str, request: ProjectStartRequest):
    """Start project orchestration with heuristic roadmap inference"""
    try:
        # Initialize architect agent
        config_path = request.config_path if request.config_path is not None else "config/agent_config.json"
        agent = ArchitectAgent(config_path)

        # Start orchestration
        project_root = str(Path.cwd())  # Use current working directory as project root
        result = await agent.start_with_project(project_id, project_root)

        if result["success"]:
            return APIResponse(
                success=True,
                message=f"Started project orchestration: {project_id}",
                data=result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get("error", "Unknown error")
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error starting orchestration: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to start orchestration: {str(e)}"
        )


@router.get("/projects/{project_id}/status", response_model=APIResponse)
async def get_project_status(project_id: str, store: ProjectStore = Depends(get_project_store)):
    """Get project status and progress"""
    try:
        # Load project state
        state = store.load_state(project_id)
        if not state:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Project not found: {project_id}"
            )

        # Load roadmap
        roadmap = store.load_roadmap(project_id)

        status_data = {
            "project_id": project_id,
            "current_phase": state.current_phase,
            "current_step": state.current_step,
            "current_task": state.current_task,
            "last_updated": state.last_updated,
            "progress_percentage": state.progress_percentage
        }

        if roadmap:
            total_tasks = sum(len(step.tasks) for phase in roadmap.phases for step in phase.steps)
            completed_tasks = sum(
                1 for phase in roadmap.phases
                for step in phase.steps
                for task in step.tasks
                if task.status.value == "completed"
            )

            status_data.update({
                "total_tasks": total_tasks,
                "completed_tasks": completed_tasks,
                "completion_percentage": (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0,
                "total_phases": len(roadmap.phases),
                "roadmap_title": roadmap.title
            })

        return APIResponse(
            success=True,
            message=f"Project status for {project_id}",
            data=status_data
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting project status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get project status: {str(e)}"
        )


@router.get("/projects", response_model=APIResponse)
async def list_projects(store: ProjectStore = Depends(get_project_store)):
    """List all projects"""
    try:
        projects_dir = Path("data/projects")
        projects = []

        if projects_dir.exists():
            for project_dir in projects_dir.iterdir():
                if project_dir.is_dir():
                    project_id = project_dir.name
                    state = store.load_state(project_id)
                    roadmap = store.load_roadmap(project_id)

                    if state and roadmap:
                        projects.append({
                            "project_id": project_id,
                            "title": roadmap.title,
                            "description": roadmap.description,
                            "current_phase": state.current_phase,
                            "last_updated": state.last_updated,
                            "progress_percentage": state.progress_percentage
                        })

        return APIResponse(
            success=True,
            message=f"Found {len(projects)} projects",
            data={"projects": projects}
        )

    except Exception as e:
        logger.error(f"Error listing projects: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list projects: {str(e)}"
        )


# Validation Endpoints
@router.post("/validation/run", response_model=APIResponse)
async def run_validation(request: ValidationRequest):
    """Run quality validation pipeline"""
    try:
        config_path = request.config_path if request.config_path is not None else "config/validation_config.json"
        pipeline = ValidationPipeline(
            project_root=str(Path.cwd()),
            config_path=config_path
        )

        suite_name = request.suite if request.suite is not None else "default"
        result = await pipeline.run_validation_suite(suite_name)

        validation_data = {
            "suite": request.suite,
            "overall_status": result.overall_status.value,
            "passed_count": result.passed_count,
            "failed_count": result.failed_count,
            "duration": result.duration,
            "results": [
                {
                    "check_name": check.check_name,
                    "status": check.status.value,
                    "message": check.message,
                    "duration": check.duration
                }
                for check in result.results
            ]
        }

        # Check for blocking failures
        pipeline_summary = pipeline.get_validation_summary()
        if pipeline_summary.get("blocking_failure", False):
            return APIResponse(
                success=False,
                message="Validation completed with blocking failures",
                data=validation_data,
                error="Blocking failures detected - fix before proceeding"
            )

        return APIResponse(
            success=True,
            message=f"Validation completed: {result.overall_status.value}",
            data=validation_data
        )

    except Exception as e:
        logger.error(f"Error running validation: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to run validation: {str(e)}"
        )


@router.get("/validation/summary", response_model=APIResponse)
async def get_validation_summary():
    """Get validation summary"""
    try:
        pipeline = ValidationPipeline(project_root=str(Path.cwd()))
        summary = pipeline.get_validation_summary()

        if summary.get("status") == "no_validation_run":
            return APIResponse(
                success=True,
                message="No validation has been run yet",
                data={"status": "no_validation_run"}
            )

        return APIResponse(
            success=True,
            message="Validation summary retrieved",
            data=summary
        )

    except Exception as e:
        logger.error(f"Error getting validation summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get validation summary: {str(e)}"
        )


# Context Management Endpoints
@router.post("/context/sessions", response_model=APIResponse)
async def create_context_session(
    session_id: str,
    metadata: Optional[Dict[str, Any]] = None,
    context_manager: SharedContextManager = Depends(get_context_manager)
):
    """Create a new context session"""
    try:
        session = context_manager.create_session(session_id, metadata or {})

        return APIResponse(
            success=True,
            message=f"Created context session: {session_id}",
            data={
                "session_id": session_id,
                "created_at": session.created_at,
                "metadata": session.metadata
            }
        )

    except Exception as e:
        logger.error(f"Error creating context session: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create context session: {str(e)}"
        )


@router.post("/context/set", response_model=APIResponse)
async def set_context(
    request: ContextSetRequest,
    context_manager: SharedContextManager = Depends(get_context_manager)
):
    """Set context value"""
    try:
        tag_set = set(request.tags) if request.tags else set()

        global_scope = request.global_scope if request.global_scope is not None else False
        agent_id = request.agent_id if request.agent_id is not None else "api"
        success = context_manager.set_context(
            session_id=request.session_id,
            key=request.key,
            value=request.value,
            agent_id=agent_id,
            ttl_seconds=request.ttl_seconds,
            tags=tag_set,
            global_scope=global_scope
        )

        if success:
            scope = "global" if request.global_scope else "session"
            return APIResponse(
                success=True,
                message=f"Set context ({scope}): {request.key}",
                data={
                    "session_id": request.session_id,
                    "key": request.key,
                    "scope": scope,
                    "agent_id": request.agent_id
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Failed to set context: {request.key}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error setting context: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to set context: {str(e)}"
        )


@router.get("/context/{session_id}/{key}", response_model=APIResponse)
async def get_context(
    session_id: str,
    key: str,
    default: Optional[str] = None,
    check_global: bool = True,
    context_manager: SharedContextManager = Depends(get_context_manager)
):
    """Get context value"""
    try:
        value = context_manager.get_context(
            session_id=session_id,
            key=key,
            default=default,
            check_global=check_global
        )

        if value is not None:
            return APIResponse(
                success=True,
                message=f"Retrieved context: {key}",
                data={
                    "session_id": session_id,
                    "key": key,
                    "value": value
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Context key not found: {key}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting context: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get context: {str(e)}"
        )


@router.post("/context/search", response_model=APIResponse)
async def search_context(
    request: ContextSearchRequest,
    context_manager: SharedContextManager = Depends(get_context_manager)
):
    """Search context entries"""
    try:
        tag_set = set(request.tags) if request.tags else None

        entries = context_manager.search_context(
            session_id=request.session_id,
            tags=tag_set,
            agent_id=request.agent_id,
            key_pattern=request.key_pattern
        )

        search_results = [
            {
                "key": entry.key,
                "value": entry.value,
                "agent_id": entry.agent_id,
                "tags": list(entry.tags),
                "timestamp": entry.timestamp,
                "access_count": entry.access_count
            }
            for entry in entries
        ]

        return APIResponse(
            success=True,
            message=f"Found {len(search_results)} context entries",
            data={
                "session_id": request.session_id,
                "results": search_results,
                "total_found": len(search_results)
            }
        )

    except Exception as e:
        logger.error(f"Error searching context: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to search context: {str(e)}"
        )


@router.get("/context/{session_id}/summary", response_model=APIResponse)
async def get_context_summary(
    session_id: str,
    context_manager: SharedContextManager = Depends(get_context_manager)
):
    """Get context session summary"""
    try:
        summary = context_manager.get_session_summary(session_id)

        if "error" in summary:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=summary["error"]
            )

        return APIResponse(
            success=True,
            message=f"Context session summary for {session_id}",
            data=summary
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting context summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get context summary: {str(e)}"
        )


# Smoke Test Models
class SmokeTestRequest(BaseModel):
    backend: str = Field("http", description="Test backend: 'http' or 'playwright'")
    base_url: Optional[str] = Field(None, description="Base URL for testing")
    timeout: Optional[int] = Field(30, description="Timeout in seconds")


class SmokeTestResponse(BaseModel):
    success: bool
    data: Dict[str, Any]
    message: str


# Smoke Test Endpoints
@router.post("/smoke/run", response_model=SmokeTestResponse)
async def run_smoke_tests(request: SmokeTestRequest):
    """
    Run smoke tests and return complete results

    This endpoint triggers a full smoke test run using the specified backend
    and returns the complete results when finished.
    """
    try:
        # Import here to avoid circular imports
        import sys
        sys.path.insert(0, str(Path(__file__).parent.parent.parent))
        from tools.tests.smoke_runner import run_smoke_tests

        config = {
            "base_url": request.base_url or "http://localhost:8000",
            "timeout": request.timeout
        }

        logger.info(f"Starting smoke tests with backend: {request.backend}")
        result = await run_smoke_tests(backend=request.backend, config=config)

        return SmokeTestResponse(
            success=True,
            data=result,
            message=f"Smoke tests completed. Success rate: {result.get('success_rate', 0):.1f}%"
        )

    except Exception as e:
        logger.error(f"Error running smoke tests: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to run smoke tests: {str(e)}"
        )


@router.get("/smoke/status")
async def get_smoke_test_status():
    """
    Get current smoke test runner status

    Returns information about available backends and configuration.
    """
    try:
        return {
            "success": True,
            "data": {
                "available_backends": ["http", "playwright"],
                "default_backend": "http",
                "default_timeout": 30,
                "scenarios": [
                    {
                        "id": "health_check",
                        "name": "Health Check",
                        "description": "Verify basic application health"
                    },
                    {
                        "id": "authentication",
                        "name": "Authentication Flow",
                        "description": "Test user authentication"
                    }
                ]
            },
            "message": "Smoke test runner is available"
        }

    except Exception as e:
        logger.error(f"Error getting smoke test status: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get smoke test status: {str(e)}"
        )


# WebSocket endpoint for streaming smoke test results
@router.websocket("/smoke/stream")
async def smoke_test_websocket(websocket: WebSocket, backend: str = "http", base_url: str = "http://localhost:8000", timeout: int = 30):
    """
    WebSocket endpoint for streaming smoke test results in real-time

    This endpoint accepts WebSocket connections and streams smoke test
    progress and results as they execute, providing real-time feedback
    to the IDE or other clients.

    Query Parameters:
    - backend: Test backend ('http' or 'playwright')
    - base_url: Base URL for testing
    - timeout: Timeout in seconds
    """
    await websocket.accept()

    try:
        # Import here to avoid circular imports
        import sys
        sys.path.insert(0, str(Path(__file__).parent.parent.parent))
        from tools.tests.smoke_runner import stream_smoke_tests

        config = {
            "base_url": base_url,
            "timeout": timeout
        }

        logger.info(f"Starting smoke test stream with backend: {backend}")

        # Send initial connection confirmation
        await websocket.send_json({
            "type": "connection_established",
            "backend": backend,
            "config": config,
            "timestamp": datetime.now().isoformat()
        })

        # Stream test results
        async for event in stream_smoke_tests(backend=backend, config=config):
            await websocket.send_json(event)

        # Send final completion message
        await websocket.send_json({
            "type": "stream_complete",
            "timestamp": datetime.now().isoformat()
        })

    except WebSocketDisconnect:
        logger.info("Smoke test WebSocket client disconnected")
    except Exception as e:
        logger.error(f"Error in smoke test WebSocket: {e}")
        try:
            await websocket.send_json({
                "type": "error",
                "error": str(e),
                "timestamp": datetime.now().isoformat()
            })
        except:
            pass  # Connection might be closed
    finally:
        try:
            await websocket.close()
        except:
            pass
