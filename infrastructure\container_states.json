{"port_assignments": {"site1": 8080, "site2": 8081, "site3": 8082, "another-site": 8084, "test-site": 8080, "integration-test-site": 8087, "test-site-1": 8085, "test-site-2": 8086}, "containers": [{"site_name": "test-site", "container_name": "site-test-site", "port": 8080, "status": "ContainerStatus.STOPPED", "image_name": "ai-coding-site-test-site", "created_at": "2025-08-07 19:44:25.718313", "environment": "EnvironmentType.DEVELOPMENT", "last_started": null, "last_health_check": null, "health_status": "<MagicMock name='from_env().containers.get().attrs.get().get().get()' id='2822654363024'>", "resource_usage": {"cpu_percent": "<MagicMock name='from_env().containers.get().stats().get().get().get()' id='2822654365712'>", "memory_usage": "<MagicMock name='from_env().containers.get().stats().get().get()' id='2822654365040'>", "memory_limit": "<MagicMock name='from_env().containers.get().stats().get().get()' id='2822654365040'>"}, "logs": [], "ssl_enabled": false, "ssl_cert_path": null, "backup_enabled": true, "last_backup": null, "hot_reload_enabled": false, "monitoring_enabled": true}, {"site_name": "integration-test-site", "container_name": "site-integration-test-site", "port": 8087, "status": "ContainerStatus.STOPPED", "image_name": "ai-coding-site-integration-test-site", "created_at": "2025-08-07 19:28:30.003770", "environment": "EnvironmentType.DEVELOPMENT", "last_started": null, "last_health_check": null, "health_status": "unknown", "resource_usage": {}, "logs": [], "ssl_enabled": false, "ssl_cert_path": null, "backup_enabled": true, "last_backup": null, "hot_reload_enabled": false, "monitoring_enabled": true}]}