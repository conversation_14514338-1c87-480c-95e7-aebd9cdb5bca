"""
Container Monitor
Monitors container health and automatically restarts failed containers.
"""

import asyncio
import json
import logging
import time
from dataclasses import asdict, dataclass
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional

import docker

logger = logging.getLogger(__name__)


class ContainerHealth(Enum):
    """Container health status"""

    HEALTHY = "healthy"
    UNHEALTHY = "unhealthy"
    STARTING = "starting"
    UNKNOWN = "unknown"
    FAILED = "failed"


@dataclass
class ContainerStatus:
    """Container status information"""

    container_id: str
    name: str
    status: str
    health: ContainerHealth
    restart_count: int
    last_restart: Optional[datetime] = None
    uptime: Optional[timedelta] = None
    memory_usage: Optional[str] = None
    cpu_usage: Optional[float] = None
    port_mappings: Optional[List[str]] = None
    created: Optional[datetime] = None
    last_check: Optional[datetime] = None


@dataclass
class MonitorConfig:
    """Monitor configuration"""

    check_interval: int = 30  # seconds
    restart_threshold: int = 3  # max restarts before giving up
    restart_cooldown: int = 300  # seconds between restarts
    health_check_timeout: int = 10  # seconds
    memory_threshold: float = 90.0  # percentage
    cpu_threshold: float = 90.0  # percentage
    enable_auto_restart: bool = True
    enable_notifications: bool = True
    log_restarts: bool = True


class ContainerMonitor:
    """Monitors and manages container health"""

    def __init__(self, config: Optional[MonitorConfig] = None):
        self.config = config or MonitorConfig()
        self.docker_client = docker.from_env()
        self.monitored_containers: Dict[str, ContainerStatus] = {}
        self.restart_history: Dict[str, List[datetime]] = {}
        self.is_monitoring = False
        self.monitor_task: Optional[asyncio.Task] = None

        # Load restart history
        self._load_restart_history()

        logger.info("ContainerMonitor initialized")

    def _load_restart_history(self) -> None:
        """Load restart history from disk"""
        history_file = Path("logs/container_restart_history.json")
        if history_file.exists():
            try:
                with open(history_file, "r") as f:
                    data = json.load(f)

                for container_id, timestamps in data.items():
                    self.restart_history[container_id] = [
                        datetime.fromisoformat(ts) for ts in timestamps
                    ]

                logger.info(
                    f"Loaded restart history for {len(self.restart_history)} containers"
                )
            except Exception as e:
                logger.error(f"Failed to load restart history: {e}")

    def _save_restart_history(self) -> None:
        """Save restart history to disk"""
        history_file = Path("logs/container_restart_history.json")
        history_file.parent.mkdir(exist_ok=True)

        try:
            data = {
                container_id: [ts.isoformat() for ts in timestamps]
                for container_id, timestamps in self.restart_history.items()
            }

            with open(history_file, "w") as f:
                json.dump(data, f, indent=2)
        except Exception as e:
            logger.error(f"Failed to save restart history: {e}")

    async def start_monitoring(self) -> None:
        """Start the monitoring loop"""
        if self.is_monitoring:
            logger.warning("Monitoring is already running")
            return

        self.is_monitoring = True
        self.monitor_task = asyncio.create_task(self._monitoring_loop())
        logger.info("Container monitoring started")

    async def stop_monitoring(self) -> None:
        """Stop the monitoring loop"""
        if not self.is_monitoring:
            return

        self.is_monitoring = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass

        logger.info("Container monitoring stopped")

    async def _monitoring_loop(self):
        """Main monitoring loop"""
        while self.is_monitoring:
            try:
                await self._check_all_containers()
                await asyncio.sleep(self.config.check_interval)
            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(60)  # Wait before retrying

    async def _check_all_containers(self):
        """Check health of all monitored containers"""
        try:
            containers = self.docker_client.containers.list(all=True)

            for container in containers:
                if self._should_monitor_container(container):
                    await self._check_container_health(container)

        except Exception as e:
            logger.error(f"Error checking containers: {e}")

    def _should_monitor_container(self, container) -> bool:
        """Determine if a container should be monitored"""
        # Monitor containers with specific labels or names
        labels = container.labels
        name = container.name

        # Monitor AI coding agent containers
        if name.startswith("site-") or name.startswith("ai-coding-"):
            return True

        # Monitor containers with specific labels
        if "ai.coding.agent.monitor" in labels:
            return True

        return False

    async def _check_container_health(self, container):
        """Check health of a specific container"""
        try:
            container_id = container.id
            container_name = container.name

            # Get container status
            container.reload()
            status = container.status
            health = self._get_container_health(container)

            # Get resource usage
            stats = container.stats(stream=False)
            memory_usage = self._calculate_memory_usage(stats)
            cpu_usage = self._calculate_cpu_usage(stats)

            # Get port mappings
            port_mappings = []
            for port in container.ports.values():
                if port:
                    for mapping in port:
                        port_mappings.append(
                            f"{mapping['HostPort']}:{mapping['HostIp']}"
                        )

            # Update container status
            container_status = ContainerStatus(
                container_id=container_id,
                name=container_name,
                status=status,
                health=health,
                restart_count=container.attrs["RestartCount"],
                last_restart=self._get_last_restart(container_id),
                uptime=self._calculate_uptime(container),
                memory_usage=memory_usage,
                cpu_usage=cpu_usage,
                port_mappings=port_mappings,
                created=datetime.fromisoformat(
                    container.attrs["Created"].replace("Z", "+00:00")
                ),
                last_check=datetime.now(),
            )

            self.monitored_containers[container_id] = container_status

            # Check if container needs restart
            if self.config.enable_auto_restart:
                await self._check_restart_needed(container, container_status)

        except Exception as e:
            logger.error(f"Error checking container {container.name}: {e}")

    def _get_container_health(self, container) -> ContainerHealth:
        """Get container health status"""
        try:
            health = container.attrs.get("State", {}).get("Health", {})
            if health:
                status = health.get("Status", "unknown")
                if status == "healthy":
                    return ContainerHealth.HEALTHY
                elif status == "unhealthy":
                    return ContainerHealth.UNHEALTHY
                elif status == "starting":
                    return ContainerHealth.STARTING
                else:
                    return ContainerHealth.UNKNOWN
            else:
                # No health check configured, check if container is running
                if container.status == "running":
                    return ContainerHealth.HEALTHY
                else:
                    return ContainerHealth.UNKNOWN
        except Exception as e:
            logger.error(f"Error getting container health: {e}")
            return ContainerHealth.UNKNOWN

    def _calculate_memory_usage(self, stats) -> Optional[str]:
        """Calculate memory usage percentage"""
        try:
            if stats and "memory_stats" in stats:
                memory_stats = stats["memory_stats"]
                if "usage" in memory_stats and "limit" in memory_stats:
                    usage = memory_stats["usage"]
                    limit = memory_stats["limit"]
                    if limit > 0:
                        percentage = (usage / limit) * 100
                        return f"{percentage:.1f}%"
            return None
        except Exception as e:
            logger.error(f"Error calculating memory usage: {e}")
            return None

    def _calculate_cpu_usage(self, stats) -> Optional[float]:
        """Calculate CPU usage percentage"""
        try:
            if stats and "cpu_stats" in stats:
                cpu_stats = stats["cpu_stats"]
                if "cpu_usage" in cpu_stats and "total_usage" in cpu_stats["cpu_usage"]:
                    total_usage = cpu_stats["cpu_usage"]["total_usage"]
                    system_cpu_usage = cpu_stats.get("system_cpu_usage", 0)

                    # Calculate CPU percentage (simplified)
                    if system_cpu_usage > 0:
                        cpu_percent = (total_usage / system_cpu_usage) * 100
                        return min(cpu_percent, 100.0)
            return None
        except Exception as e:
            logger.error(f"Error calculating CPU usage: {e}")
            return None

    def _get_last_restart(self, container_id: str) -> Optional[datetime]:
        """Get last restart time for container"""
        if container_id in self.restart_history and self.restart_history[container_id]:
            return self.restart_history[container_id][-1]
        return None

    def _calculate_uptime(self, container) -> Optional[timedelta]:
        """Calculate container uptime"""
        try:
            state = container.attrs.get("State", {})
            started_at = state.get("StartedAt")
            if started_at:
                start_time = datetime.fromisoformat(started_at.replace("Z", "+00:00"))
                return datetime.now().replace(tzinfo=start_time.tzinfo) - start_time
            return None
        except Exception as e:
            logger.error(f"Error calculating uptime: {e}")
            return None

    async def _check_restart_needed(self, container, container_status: ContainerStatus):
        """Check if container needs to be restarted"""
        try:
            container_id = container.id
            container_name = container.name

            # Check if container is unhealthy or failed
            needs_restart = False
            restart_reason = ""

            if container_status.health == ContainerHealth.UNHEALTHY:
                needs_restart = True
                restart_reason = "unhealthy"
            elif container_status.status == "exited":
                needs_restart = True
                restart_reason = "exited"
            elif container_status.status == "dead":
                needs_restart = True
                restart_reason = "dead"

            # Check resource thresholds
            if container_status.memory_usage:
                memory_percent = float(container_status.memory_usage.rstrip("%"))
                if memory_percent > self.config.memory_threshold:
                    needs_restart = True
                    restart_reason = f"memory usage {memory_percent:.1f}%"

            if (
                container_status.cpu_usage
                and container_status.cpu_usage > self.config.cpu_threshold
            ):
                needs_restart = True
                restart_reason = f"CPU usage {container_status.cpu_usage:.1f}%"

            if needs_restart:
                await self._restart_container(
                    container, container_status, restart_reason
                )

        except Exception as e:
            logger.error(f"Error checking restart for {container.name}: {e}")

    async def _restart_container(
        self, container, container_status: ContainerStatus, reason: str
    ):
        """Restart a container"""
        try:
            container_id = container.id
            container_name = container.name

            # Check restart cooldown
            last_restart = self._get_last_restart(container_id)
            if last_restart:
                time_since_restart = datetime.now() - last_restart
                if time_since_restart.total_seconds() < self.config.restart_cooldown:
                    logger.info(
                        f"Container {container_name} restart skipped (cooldown)"
                    )
                    return

            # Check restart threshold
            restart_count = len(self.restart_history.get(container_id, []))
            if restart_count >= self.config.restart_threshold:
                logger.warning(
                    f"Container {container_name} exceeded restart threshold ({restart_count})"
                )
                await self._handle_excessive_restarts(container, container_status)
                return

            # Restart the container
            logger.info(f"Restarting container {container_name} (reason: {reason})")

            try:
                container.restart(timeout=30)

                # Record restart
                if container_id not in self.restart_history:
                    self.restart_history[container_id] = []
                self.restart_history[container_id].append(datetime.now())
                self._save_restart_history()

                # Log restart
                if self.config.log_restarts:
                    await self._log_restart(container_name, reason, restart_count + 1)

                # Send notification
                if self.config.enable_notifications:
                    await self._send_restart_notification(
                        container_name, reason, restart_count + 1
                    )

                logger.info(f"Container {container_name} restarted successfully")

            except Exception as e:
                logger.error(f"Failed to restart container {container_name}: {e}")

        except Exception as e:
            logger.error(f"Error in restart process for {container.name}: {e}")

    async def _handle_excessive_restarts(
        self, container, container_status: ContainerStatus
    ):
        """Handle containers that exceed restart threshold"""
        try:
            container_name = container.name

            # Log the issue
            logger.error(f"Container {container_name} has exceeded restart threshold")

            # Send critical notification
            if self.config.enable_notifications:
                await self._send_critical_notification(container_name)

            # Stop the container to prevent resource waste
            try:
                container.stop(timeout=10)
                logger.info(
                    f"Stopped container {container_name} due to excessive restarts"
                )
            except Exception as e:
                logger.error(f"Failed to stop container {container_name}: {e}")

        except Exception as e:
            logger.error(f"Error handling excessive restarts for {container.name}: {e}")

    async def _log_restart(self, container_name: str, reason: str, restart_count: int):
        """Log container restart"""
        try:
            log_entry = {
                "timestamp": datetime.now().isoformat(),
                "container": container_name,
                "reason": reason,
                "restart_count": restart_count,
                "action": "restart",
            }

            log_file = Path("logs/container_restarts.log")
            log_file.parent.mkdir(exist_ok=True)

            with open(log_file, "a") as f:
                f.write(json.dumps(log_entry) + "\n")

        except Exception as e:
            logger.error(f"Error logging restart: {e}")

    async def _send_restart_notification(
        self, container_name: str, reason: str, restart_count: int
    ):
        """Send restart notification"""
        try:
            # This would integrate with the notification system
            # For now, just log the notification
            logger.info(
                f"NOTIFICATION: Container {container_name} restarted (reason: {reason}, count: {restart_count})"
            )

        except Exception as e:
            logger.error(f"Error sending restart notification: {e}")

    async def _send_critical_notification(self, container_name: str):
        """Send critical notification for excessive restarts"""
        try:
            # This would integrate with the notification system
            # For now, just log the notification
            logger.error(
                f"CRITICAL NOTIFICATION: Container {container_name} exceeded restart threshold"
            )

        except Exception as e:
            logger.error(f"Error sending critical notification: {e}")

    async def get_container_status(
        self, container_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get status of monitored containers"""
        try:
            if container_name:
                # Return status for specific container
                for status in self.monitored_containers.values():
                    if status.name == container_name:
                        return {"success": True, "container": asdict(status)}
                return {
                    "success": False,
                    "error": f"Container {container_name} not found",
                }
            else:
                # Return status for all containers
                return {
                    "success": True,
                    "containers": [
                        asdict(status) for status in self.monitored_containers.values()
                    ],
                    "count": len(self.monitored_containers),
                }

        except Exception as e:
            logger.error(f"Error getting container status: {e}")
            return {"success": False, "error": str(e)}

    async def get_restart_history(
        self, container_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Get restart history for containers"""
        try:
            if container_name:
                # Find container ID by name
                container_id = None
                for status in self.monitored_containers.values():
                    if status.name == container_name:
                        container_id = status.container_id
                        break

                if container_id and container_id in self.restart_history:
                    return {
                        "success": True,
                        "container": container_name,
                        "restarts": [
                            ts.isoformat() for ts in self.restart_history[container_id]
                        ],
                        "count": len(self.restart_history[container_id]),
                    }
                else:
                    return {
                        "success": False,
                        "error": f"No restart history for {container_name}",
                    }
            else:
                # Return restart history for all containers
                history = {}
                for container_id, timestamps in self.restart_history.items():
                    # Find container name
                    container_name = "unknown"
                    for status in self.monitored_containers.values():
                        if status.container_id == container_id:
                            container_name = status.name
                            break

                    history[container_name] = {
                        "restarts": [ts.isoformat() for ts in timestamps],
                        "count": len(timestamps),
                    }

                return {
                    "success": True,
                    "history": history,
                    "total_containers": len(history),
                }

        except Exception as e:
            logger.error(f"Error getting restart history: {e}")
            return {"success": False, "error": str(e)}

    async def force_restart(self, container_name: str) -> Dict[str, Any]:
        """Force restart a container"""
        try:
            # Find container by name
            container = None
            for c in self.docker_client.containers.list(all=True):
                if c.name == container_name:
                    container = c
                    break

            if not container:
                return {
                    "success": False,
                    "error": f"Container {container_name} not found",
                }

            # Force restart
            logger.info(f"Force restarting container {container_name}")
            container.restart(timeout=30)

            return {
                "success": True,
                "message": f"Container {container_name} force restarted",
            }

        except Exception as e:
            logger.error(f"Error force restarting container {container_name}: {e}")
            return {"success": False, "error": str(e)}

    async def update_config(self, new_config: MonitorConfig) -> Dict[str, Any]:
        """Update monitor configuration"""
        try:
            self.config = new_config
            logger.info("Monitor configuration updated")

            return {"success": True, "message": "Configuration updated successfully"}

        except Exception as e:
            logger.error(f"Error updating configuration: {e}")
            return {"success": False, "error": str(e)}
