import React from 'react';

interface SecurityReportProps {
  report: {
    status: string;
    issues: string[];
    warnings: string[];
    recommendations: string[];
  };
  onProceed?: () => void;
  onCancel?: () => void;
}

export const SecurityReport: React.FC<SecurityReportProps> = ({
  report,
  onProceed,
  onCancel
}) => {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'safe': return '✅';
      case 'warning': return '⚠️';
      case 'needs_review': return '❌';
      default: return '❓';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'safe': return '#28a745';
      case 'warning': return '#ffc107';
      case 'needs_review': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case 'safe': return 'Safe to Import';
      case 'warning': return 'Warnings Found';
      case 'needs_review': return 'Review Required';
      default: return 'Unknown Status';
    }
  };

  return (
    <div className="security-report-container">
      <div className="security-report">
        <div className="report-header">
          <h3>Security Analysis Report</h3>
          <div
            className="status-badge"
            style={{ backgroundColor: getStatusColor(report.status) }}
          >
            {getStatusIcon(report.status)} {getStatusText(report.status)}
          </div>
        </div>

        <div className="report-content">
          {report.issues.length > 0 && (
            <div className="report-section issues">
              <h4>🚨 Issues Found</h4>
              <ul>
                {report.issues.map((issue, index) => (
                  <li key={index} className="issue-item">
                    {issue}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {report.warnings.length > 0 && (
            <div className="report-section warnings">
              <h4>⚠️ Warnings</h4>
              <ul>
                {report.warnings.map((warning, index) => (
                  <li key={index} className="warning-item">
                    {warning}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {report.recommendations.length > 0 && (
            <div className="report-section recommendations">
              <h4>💡 Recommendations</h4>
              <ul>
                {report.recommendations.map((rec, index) => (
                  <li key={index} className="recommendation-item">
                    {rec}
                  </li>
                ))}
              </ul>
            </div>
          )}

          {report.issues.length === 0 && report.warnings.length === 0 && (
            <div className="report-section safe">
              <h4>✅ No Issues Found</h4>
              <p>This project appears to be safe for import.</p>
            </div>
          )}
        </div>

        {(onProceed || onCancel) && (
          <div className="report-actions">
            {onProceed && (
              <button
                onClick={onProceed}
                className="action-button proceed"
                disabled={report.status === 'needs_review' && report.issues.length > 0}
              >
                ✅ Proceed with Import
              </button>
            )}
            {onCancel && (
              <button onClick={onCancel} className="action-button cancel">
                ❌ Cancel Import
              </button>
            )}
          </div>
        )}
      </div>

      <style jsx>{`
        .security-report-container {
          width: 100%;
          max-width: 700px;
          margin: 20px auto;
        }

        .security-report {
          background: white;
          border-radius: 8px;
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
          overflow: hidden;
        }

        .report-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px 24px;
          background: #f8f9fa;
          border-bottom: 1px solid #e1e5e9;
        }

        .report-header h3 {
          margin: 0;
          color: #333;
          font-size: 20px;
        }

        .status-badge {
          padding: 8px 16px;
          border-radius: 20px;
          color: white;
          font-weight: 500;
          font-size: 14px;
        }

        .report-content {
          padding: 24px;
        }

        .report-section {
          margin-bottom: 24px;
        }

        .report-section:last-child {
          margin-bottom: 0;
        }

        .report-section h4 {
          margin: 0 0 12px 0;
          font-size: 16px;
          font-weight: 600;
        }

        .report-section.issues h4 {
          color: #dc3545;
        }

        .report-section.warnings h4 {
          color: #ffc107;
        }

        .report-section.recommendations h4 {
          color: #007bff;
        }

        .report-section.safe h4 {
          color: #28a745;
        }

        .report-section ul {
          margin: 0;
          padding-left: 20px;
        }

        .report-section li {
          margin-bottom: 8px;
          line-height: 1.5;
        }

        .issue-item {
          color: #dc3545;
          font-weight: 500;
        }

        .warning-item {
          color: #856404;
        }

        .recommendation-item {
          color: #004085;
        }

        .report-actions {
          display: flex;
          gap: 12px;
          padding: 20px 24px;
          background: #f8f9fa;
          border-top: 1px solid #e1e5e9;
        }

        .action-button {
          flex: 1;
          padding: 12px 24px;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .action-button.proceed {
          background: #28a745;
          color: white;
        }

        .action-button.proceed:hover:not(:disabled) {
          background: #218838;
        }

        .action-button.proceed:disabled {
          background: #6c757d;
          cursor: not-allowed;
        }

        .action-button.cancel {
          background: #6c757d;
          color: white;
        }

        .action-button.cancel:hover {
          background: #5a6268;
        }
      `}</style>
    </div>
  );
};
