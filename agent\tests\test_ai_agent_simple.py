#!/usr/bin/env python3
"""
Simple AI Agent Test
"""

import sys
from pathlib import Path

# Fix Windows console encoding for emoji support
if hasattr(sys.stdout, "reconfigure"):
    # on Windows, switch the console to UTF-8 output to support emojis
    sys.stdout.reconfigure(encoding="utf-8", errors="replace")

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_agent_init():
    """Test basic AI Agent initialization"""
    try:
        from agent.core.agent import AIAgent

        # Test agent initialization
        agent = AIAgent("config/smart_routing_config.json")
        print("✅ AI Agent initialized successfully")

        # Test security manager is available
        if hasattr(agent, "security"):
            print("✅ Security manager is available")
        else:
            print("❌ Security manager not found")

        return True

    except Exception as e:
        print(f"❌ AI Agent initialization failed: {e}")
        return False


if __name__ == "__main__":
    test_agent_init()
