# AI Coding Agent - User Guide

## 📋 Table of Contents

1. [Getting Started](#getting-started)
2. [Basic Operations](#basic-operations)
3. [Website Generation](#website-generation)
4. [Content Management](#content-management)
5. [Local Hosting](#local-hosting)
6. [Deployment](#deployment)
7. [Maintenance](#maintenance)
8. [Testing](#testing)
9. [Advanced Features](#advanced-features)
10. [Troubleshooting](#troubleshooting)
11. [Best Practices](#best-practices)
12. [FAQ](#faq)

---

## 🚀 Getting Started

### Installation

#### For End Users (Quick Start)
1. **Download the latest release**:
   ```bash
   # Download from GitHub releases
   # Extract to your desired location
   ```

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Start using**:
   ```bash
   ai-agent --help
   ```

#### For Developers (Full Setup)
1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-org/ai-coding-agent.git
   cd ai-coding-agent
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv .venv
   .venv\Scripts\activate  # Windows
   source .venv/bin/activate  # macOS/Linux
   ```

3. **Install development dependencies**:
   ```bash
   pip install -r requirements.txt
   pip install -r requirements-dev.txt
   pip install -e .
   ```

4. **Run development setup**:
   ```bash
   python scripts/setup_dev_environment.py
   ```

5. **Install pre-commit hooks**:
   ```bash
   pre-commit install
   pre-commit install --hook-type pre-push
   ```

### Quick Start

Create your first website in under 5 minutes:

```bash
# Create and host a website
ai-agent create-and-host my-first-site

# Access your site
# Open browser to http://localhost:8000
```

---

## 🔧 Basic Operations

### Command Structure

All AI Coding Agent commands follow this pattern:
```bash
ai-agent <command> [options] [arguments]
```

### Available Commands

| Command | Description | Example |
|---------|-------------|---------|
| `create` | Create a new website | `ai-agent create my-site` |
| `host` | Host a website locally | `ai-agent host my-site` |
| `deploy` | Deploy to production | `ai-agent deploy my-site` |
| `create-content` | Create content | `ai-agent create-content article` |
| `test` | Run tests | `ai-agent test my-site` |
| `maintenance` | Run maintenance | `ai-agent maintenance` |

### Getting Help

```bash
# General help
ai-agent --help

# Command-specific help
ai-agent create --help
ai-agent host --help
```

### Configuration

The AI Coding Agent uses JSON configuration files in the `config/` directory:

- `config.json` - Main configuration
- `cli_web_config.json` - CLI and web settings
- `cms_config.json` - Content management
- `home_server_config.json` - Hosting settings

---

## 🌐 Website Generation

### Creating a Website

#### Basic Website Creation
```bash
# Create a simple website
ai-agent create my-website

# Create with specific template
ai-agent create my-website --template modern

# Create with custom theme
ai-agent create my-website --template modern --theme modern-blue
```

#### Advanced Website Creation
```bash
# Create with multiple features
ai-agent create my-website \
  --template modern \
  --theme modern-blue \
  --features blog,contact,gallery,portfolio

# Create with custom configuration
ai-agent create my-website \
  --config custom-config.json \
  --output-dir /path/to/output
```

### Available Templates

| Template | Description | Features |
|----------|-------------|----------|
| `modern` | Clean, responsive design | Blog, contact, gallery |
| `minimal` | Simple, focused design | Blog, about |
| `business` | Professional business site | Services, team, contact |
| `portfolio` | Creative portfolio | Gallery, projects, about |
| `ecommerce` | Online store template | Products, cart, checkout |

### Available Themes

| Theme | Description | Colors |
|-------|-------------|--------|
| `modern-blue` | Professional blue theme | Blue, white, gray |
| `modern-green` | Fresh green theme | Green, white, gray |
| `dark-mode` | Dark theme | Dark, light accents |
| `colorful` | Vibrant theme | Multiple colors |
| `monochrome` | Black and white | Black, white, gray |

### Customizing Your Website

#### Modifying Templates
```bash
# Edit template files
cd sites/my-website
# Edit HTML, CSS, and JavaScript files
```

#### Adding Custom Content
```bash
# Add custom pages
ai-agent create-content page --title "About Us" --content "Our story..."

# Add blog posts
ai-agent create-content article --title "My First Post" --content "Hello world!"
```

---

## 📝 Content Management

### Creating Content

#### Articles
```bash
# Create a simple article
ai-agent create-content article \
  --title "Getting Started with AI Coding Agent" \
  --content "This is my first article..."

# Create with metadata
ai-agent create-content article \
  --title "Advanced Features" \
  --content "Learn about advanced features..." \
  --author "John Doe" \
  --tags "tutorial,advanced,ai" \
  --publish
```

#### Pages
```bash
# Create a static page
ai-agent create-content page \
  --title "About Us" \
  --content "We are a team of developers..." \
  --slug "about"
```

#### Media Content
```bash
# Upload images
ai-agent create-content media \
  --type image \
  --file path/to/image.jpg \
  --alt "Description of image"

# Upload videos
ai-agent create-content media \
  --type video \
  --file path/to/video.mp4 \
  --title "Product Demo"
```

### Managing Content

#### Listing Content
```bash
# List all content
ai-agent list-content

# List by type
ai-agent list-content --type article
ai-agent list-content --type page
ai-agent list-content --type media

# List with filters
ai-agent list-content --author "John Doe"
ai-agent list-content --tags "tutorial"
```

#### Updating Content
```bash
# Update content by ID
ai-agent update-content <content-id> \
  --title "Updated Title" \
  --content "Updated content..."

# Update content by slug
ai-agent update-content --slug "about" \
  --content "New about page content"
```

#### Deleting Content
```bash
# Delete by ID
ai-agent delete-content <content-id>

# Delete by slug
ai-agent delete-content --slug "old-page"
```

### AI-Powered Content Generation

#### Generate Articles
```bash
# Generate article with AI
ai-agent generate-content article \
  --topic "The Future of Web Development" \
  --length medium \
  --style professional

# Generate with specific requirements
ai-agent generate-content article \
  --topic "AI in Healthcare" \
  --length long \
  --style academic \
  --include-seo \
  --keywords "ai,healthcare,technology"
```

#### Generate Pages
```bash
# Generate landing page
ai-agent generate-content page \
  --type landing \
  --business "Tech Startup" \
  --services "AI, Web Development, Consulting"

# Generate about page
ai-agent generate-content page \
  --type about \
  --company "AI Coding Agent" \
  --team-size 10
```

---

## 🏠 Local Hosting

### Starting Local Server

#### Basic Hosting
```bash
# Host a website
ai-agent host my-website

# Host with specific port
ai-agent host my-website --port 8080

# Host with hot-reload
ai-agent host my-website --hot-reload
```

#### Advanced Hosting
```bash
# Host with SSL
ai-agent host my-website --ssl

# Host with custom domain
ai-agent host my-website --domain my-site.local

# Host with monitoring
ai-agent host my-website --monitor --log-level debug
```

### Server Management

#### Checking Server Status
```bash
# Check if server is running
ai-agent status

# Get detailed status
ai-agent status --detailed

# Check specific site
ai-agent status --site my-website
```

#### Stopping Server
```bash
# Stop all servers
ai-agent stop-hosting

# Stop specific site
ai-agent stop-hosting --site my-website
```

#### Server Configuration

The server can be configured through `config/home_server_config.json`:

```json
{
  "server": {
    "default_port": 8000,
    "host": "0.0.0.0",
    "workers": 4,
    "timeout": 30
  },
  "ssl": {
    "enabled": false,
    "cert_path": "",
    "key_path": ""
  },
  "monitoring": {
    "enabled": true,
    "log_level": "INFO",
    "metrics": true
  }
}
```

---

## 🚀 Deployment

### Deploying to Production

#### Basic Deployment
```bash
# Deploy a website
ai-agent deploy my-website

# Deploy with backup
ai-agent deploy my-website --backup

# Deploy with validation
ai-agent deploy my-website --validate
```

#### Advanced Deployment
```bash
# Deploy to specific environment
ai-agent deploy my-website --environment production

# Deploy with custom configuration
ai-agent deploy my-website --config production-config.json

# Deploy with rollback protection
ai-agent deploy my-website --rollback-protection
```

### Deployment Management

#### Checking Deployment Status
```bash
# List deployments
ai-agent list-deployments

# Check specific deployment
ai-agent deployment-status <deployment-id>

# Get deployment history
ai-agent deployment-history my-website
```

#### Rolling Back Deployments
```bash
# Rollback to previous version
ai-agent rollback my-website

# Rollback to specific version
ai-agent rollback my-website --version <version-id>

# Rollback with confirmation
ai-agent rollback my-website --confirm
```

### Deployment Configuration

Configure deployment settings in `config/pipeline_config.json`:

```json
{
  "deployment": {
    "backup_enabled": true,
    "validation_enabled": true,
    "rollback_enabled": true,
    "max_rollback_versions": 5
  },
  "environments": {
    "development": {
      "path": "/var/www/dev",
      "url": "http://dev.example.com"
    },
    "production": {
      "path": "/var/www/prod",
      "url": "https://example.com"
    }
  }
}
```

---

## 🔧 Maintenance

### Running Maintenance

#### Basic Maintenance
```bash
# Run all maintenance tasks
ai-agent maintenance

# Run specific maintenance
ai-agent maintenance --check-links
ai-agent maintenance --update-dependencies
ai-agent maintenance --security-scan
```

#### Scheduled Maintenance
```bash
# Schedule daily maintenance
ai-agent maintenance --schedule daily

# Schedule weekly maintenance
ai-agent maintenance --schedule weekly

# Schedule custom maintenance
ai-agent maintenance --schedule "0 2 * * *"  # 2 AM daily
```

### Maintenance Tasks

#### Link Checking
```bash
# Check all links
ai-agent check-links

# Check specific site
ai-agent check-links --site my-website

# Check with detailed report
ai-agent check-links --detailed --output report.html
```

#### Dependency Updates
```bash
# Check for updates
ai-agent check-updates

# Update dependencies
ai-agent update-dependencies

# Update with approval
ai-agent update-dependencies --approve
```

#### Security Scanning
```bash
# Run security scan
ai-agent security-scan

# Scan specific components
ai-agent security-scan --components dependencies,ssl,permissions

# Generate security report
ai-agent security-scan --report security-report.html
```

### Maintenance Configuration

Configure maintenance in `config/maintenance_config.json`:

```json
{
  "schedule": {
    "daily": "0 2 * * *",
    "weekly": "0 2 * * 0",
    "monthly": "0 2 1 * *"
  },
  "tasks": {
    "link_checking": {
      "enabled": true,
      "frequency": "daily",
      "timeout": 30
    },
    "dependency_updates": {
      "enabled": true,
      "frequency": "weekly",
      "auto_approve": false
    },
    "security_scanning": {
      "enabled": true,
      "frequency": "daily",
      "components": ["dependencies", "ssl", "permissions"]
    }
  }
}
```

---

## 🧪 Testing

### Running Tests

#### Basic Testing
```bash
# Run all tests
ai-agent test

# Test specific site
ai-agent test my-website

# Test specific components
ai-agent test --components html,css,accessibility
```

#### Advanced Testing
```bash
# Run with coverage
ai-agent test --coverage

# Run performance tests
ai-agent test --performance

# Run browser tests
ai-agent test --browser chrome,firefox
```

### Test Types

#### HTML Validation
```bash
# Validate HTML
ai-agent test-html

# Validate with specific rules
ai-agent test-html --rules strict

# Generate validation report
ai-agent test-html --report html-report.html
```

#### CSS Validation
```bash
# Validate CSS
ai-agent test-css

# Validate with specific standards
ai-agent test-css --standard css3

# Check for browser compatibility
ai-agent test-css --browsers chrome,firefox,safari
```

#### Accessibility Testing
```bash
# Test accessibility
ai-agent test-accessibility

# Test with WCAG guidelines
ai-agent test-accessibility --wcag 2.1

# Generate accessibility report
ai-agent test-accessibility --report a11y-report.html
```

#### Performance Testing
```bash
# Test performance
ai-agent test-performance

# Test with specific metrics
ai-agent test-performance --metrics load-time,first-paint,largest-contentful-paint

# Compare with baseline
ai-agent test-performance --baseline baseline.json
```

### Test Configuration

Configure testing in `config/testing_config.json`:

```json
{
  "test_suites": {
    "html": {
      "enabled": true,
      "rules": "strict",
      "timeout": 30
    },
    "css": {
      "enabled": true,
      "standards": ["css3"],
      "browsers": ["chrome", "firefox", "safari"]
    },
    "accessibility": {
      "enabled": true,
      "wcag_version": "2.1",
      "level": "AA"
    },
    "performance": {
      "enabled": true,
      "metrics": ["load-time", "first-paint", "largest-contentful-paint"],
      "thresholds": {
        "load-time": 3000,
        "first-paint": 1000
      }
    }
  }
}
```

---

## 🚀 Advanced Features

### AI-Powered Features

#### Content Generation
```bash
# Generate blog post
ai-agent generate-content blog-post \
  --topic "Machine Learning Basics" \
  --length long \
  --style educational

# Generate product descriptions
ai-agent generate-content product-description \
  --product "AI Coding Assistant" \
  --features "automation,testing,deployment" \
  --target-audience "developers"
```

#### Code Generation
```bash
# Generate JavaScript component
ai-agent generate-code javascript \
  --component "ContactForm" \
  --framework "react" \
  --features "validation,submission,error-handling"

# Generate CSS styles
ai-agent generate-code css \
  --component "Button" \
  --style "modern" \
  --variants "primary,secondary,danger"
```

#### SEO Optimization
```bash
# Optimize content for SEO
ai-agent optimize-seo \
  --content-id <content-id> \
  --keywords "ai,coding,automation" \
  --target-page "homepage"

# Generate meta tags
ai-agent generate-meta-tags \
  --title "AI Coding Agent" \
  --description "Automated website generation and deployment" \
  --keywords "ai,web development,automation"
```

### Automation Features

#### Automated Workflows
```bash
# Create automated workflow
ai-agent create-workflow \
  --name "content-pipeline" \
  --steps "create-content,optimize-seo,deploy,test"

# Run workflow
ai-agent run-workflow content-pipeline

# Schedule workflow
ai-agent schedule-workflow content-pipeline --schedule "0 9 * * *"
```

#### Continuous Integration
```bash
# Setup CI pipeline
ai-agent setup-ci \
  --repository "my-website" \
  --triggers "push,pull-request" \
  --actions "test,deploy"

# Check CI status
ai-agent ci-status

# View CI logs
ai-agent ci-logs --workflow "test-and-deploy"
```

### Analytics and Monitoring

#### Performance Monitoring
```bash
# Start monitoring
ai-agent start-monitoring

# View performance metrics
ai-agent metrics --type performance

# Set up alerts
ai-agent setup-alerts \
  --metric "response-time" \
  --threshold 2000 \
  --action "email"
```

#### Usage Analytics
```bash
# View usage statistics
ai-agent analytics

# Generate analytics report
ai-agent analytics --report --period "last-30-days"

# Export analytics data
ai-agent analytics --export --format csv
```

---

## 🔧 Troubleshooting

### Common Issues

#### Installation Issues
```bash
# Problem: Virtual environment not activating
# Solution:
rm -rf .venv
python -m venv .venv
source .venv/bin/activate  # or .venv\Scripts\activate on Windows
pip install -r requirements.txt

# Problem: Package installation fails
# Solution:
pip install --upgrade pip setuptools wheel
pip install -r requirements.txt
```

#### Database Issues
```bash
# Problem: Database connection fails
# Solution:
python -c "from src.db.database_manager import DatabaseManager; db = DatabaseManager(); db.initialize_database()"

# Problem: Database locked
# Solution:
pkill -f "python.*ai-coding-agent"
python -c "from src.db.database_manager import DatabaseManager; db = DatabaseManager(); db.close()"
```

#### Network Issues
```bash
# Problem: Port already in use
# Solution:
sudo lsof -i :8000
sudo kill -9 <PID>

# Problem: Firewall blocking connections
# Solution:
sudo ufw allow 8000
sudo ufw allow 80
sudo ufw allow 443
```

### Diagnostic Tools

#### System Diagnostics
```bash
# Run comprehensive diagnostics
python scripts/diagnostic.py

# Check system requirements
python scripts/quality_check.py

# Test network connectivity
python scripts/get_network_info.py
```

#### Log Analysis
```bash
# View application logs
tail -f logs/agent.log

# Search for errors
grep "ERROR" logs/agent.log

# View recent activity
tail -n 100 logs/agent.log
```

### Getting Help

#### Documentation
- [Architecture Overview](ARCHITECTURE_OVERVIEW.md)
- [API Reference](API_REFERENCE.md)
- [Development Guidelines](DEVELOPMENT_GUIDELINES.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)

#### Support Channels
- **GitHub Issues**: [Report bugs and feature requests](https://github.com/your-org/ai-coding-agent/issues)
- **Documentation**: [Complete documentation](https://github.com/your-org/ai-coding-agent/docs)
- **Community**: [Discord/Slack channel](https://discord.gg/ai-coding-agent)

---

## 📚 Best Practices

### Website Development

#### Content Organization
- Use descriptive file names
- Organize content by type and date
- Implement proper SEO structure
- Use consistent naming conventions

#### Performance Optimization
- Optimize images and media files
- Minimize CSS and JavaScript
- Use caching strategies
- Implement lazy loading

#### Security Best Practices
- Keep dependencies updated
- Use HTTPS for all connections
- Implement proper access controls
- Regular security scans

### Workflow Management

#### Version Control
- Use Git for all projects
- Create feature branches
- Write descriptive commit messages
- Regular backups

#### Testing Strategy
- Write tests for all features
- Run tests before deployment
- Monitor test coverage
- Automated testing in CI/CD

#### Deployment Strategy
- Use staging environments
- Implement rollback procedures
- Monitor deployment health
- Document deployment procedures

### Content Management

#### Content Creation
- Plan content structure
- Use consistent formatting
- Optimize for search engines
- Regular content updates

#### Media Management
- Optimize file sizes
- Use appropriate formats
- Implement proper alt text
- Organize media files

---

## ❓ FAQ

### General Questions

**Q: What is AI Coding Agent?**
A: AI Coding Agent is a powerful tool for automated website generation, content management, and deployment with AI-powered features.

**Q: What are the system requirements?**
A: Python 3.8+, 4GB RAM minimum, 2GB free disk space, and a modern web browser.

**Q: Is it free to use?**
A: The core functionality is free and open-source. Some advanced features may require additional setup.

### Technical Questions

**Q: Can I use my own templates?**
A: Yes, you can create custom templates and themes. See the template documentation for details.

**Q: How do I deploy to my own server?**
A: Use the `ai-agent deploy` command with your server configuration. See the deployment guide for details.

**Q: Can I integrate with existing websites?**
A: Yes, AI Coding Agent can work with existing websites and content management systems.

### Feature Questions

**Q: What AI models are supported?**
A: The system exclusively uses local Ollama models for AI-powered features. No cloud-based APIs are currently supported. Supported models include deepseek-coder, yi-coder, and other Ollama-compatible models.

**Q: Can I customize the AI behavior?**
A: Yes, you can configure AI models, prompts, and behavior through configuration files.

**Q: Is there a web interface?**
A: Yes! The system includes a complete IDE-style web interface accessible at http://localhost:3000 when running the development server. This interface provides a no-code visual editor for website creation and management.

### Support Questions

**Q: How do I report a bug?**
A: Use the GitHub Issues page to report bugs with detailed information about the problem.

**Q: Where can I get help?**
A: Check the documentation, community forums, or contact support through the provided channels.

**Q: Can I contribute to the project?**
A: Yes! We welcome contributions. See the contributing guidelines for details.

---

## 📞 Support and Contact

### Getting Help
- **Documentation**: [Complete guides and references](https://github.com/your-org/ai-coding-agent/docs)
- **Community**: [Discord/Slack for discussions](https://discord.gg/ai-coding-agent)
- **Issues**: [Bug reports and feature requests](https://github.com/your-org/ai-coding-agent/issues)

### Contributing
- **Code**: [Development guidelines](DEVELOPMENT_GUIDELINES.md)
- **Documentation**: [Documentation standards](ARCHITECTURE_OVERVIEW.md)
- **Testing**: [Testing procedures](TESTING_GUIDE.md)

### Feedback
We value your feedback! Please share your experience, suggestions, and ideas to help improve AI Coding Agent.

---

*This user guide is part of the AI Coding Agent documentation. For the latest updates, visit our [documentation repository](https://github.com/your-org/ai-coding-agent/docs).*
