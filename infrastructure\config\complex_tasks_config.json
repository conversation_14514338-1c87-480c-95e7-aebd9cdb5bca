{"complex_tasks": {"general": {"max_concurrent_tasks": 4, "default_timeout_hours": 24, "max_retry_attempts": 3, "enable_auto_cleanup": true, "cleanup_days_old": 30}, "starcoder2": {"model_name": "starcoder2:3b", "temperature": 0.1, "max_tokens": 4096, "top_p": 0.95, "frequency_penalty": 0.0, "presence_penalty": 0.0, "timeout_seconds": 300}, "task_complexity": {"simple": {"estimated_duration_hours": 1.0, "cpu_cores": 1, "memory_gb": 2, "gpu_required": false, "max_concurrent": 8}, "moderate": {"estimated_duration_hours": 4.0, "cpu_cores": 2, "memory_gb": 4, "gpu_required": false, "max_concurrent": 4}, "complex": {"estimated_duration_hours": 12.0, "cpu_cores": 4, "memory_gb": 8, "gpu_required": false, "max_concurrent": 2}, "very_complex": {"estimated_duration_hours": 24.0, "cpu_cores": 8, "memory_gb": 16, "gpu_required": true, "gpu_memory_gb": 8, "max_concurrent": 1}, "extreme": {"estimated_duration_hours": 72.0, "cpu_cores": 16, "memory_gb": 32, "gpu_required": true, "gpu_memory_gb": 16, "max_concurrent": 1}}, "resource_allocation": {"default_cpu_cores": 2, "default_memory_gb": 4, "default_storage_gb": 10, "default_network_bandwidth_mbps": 100, "gpu_models": ["RTX 4090", "RTX 4080", "RTX 3090", "RTX 3080"], "resource_check_interval_seconds": 30, "resource_alert_thresholds": {"cpu_usage": 90.0, "memory_usage": 85.0, "disk_usage": 90.0, "gpu_usage": 95.0}}, "time_limits": {"architecture_design": {"simple": 2.0, "moderate": 6.0, "complex": 18.0, "very_complex": 36.0, "extreme": 72.0}, "system_integration": {"simple": 3.0, "moderate": 8.0, "complex": 24.0, "very_complex": 48.0, "extreme": 96.0}, "performance_optimization": {"simple": 2.0, "moderate": 6.0, "complex": 18.0, "very_complex": 36.0, "extreme": 72.0}, "complex_problem_solving": {"simple": 4.0, "moderate": 12.0, "complex": 36.0, "very_complex": 72.0, "extreme": 144.0}}, "quality_metrics": {"thresholds": {"code_quality_score": 80.0, "performance_improvement": 85.0, "test_coverage": 90.0, "complexity_reduction": 75.0, "maintainability_score": 85.0, "security_score": 90.0, "documentation_quality": 85.0, "user_satisfaction": 80.0}, "weights": {"code_quality": 0.25, "performance": 0.2, "test_coverage": 0.15, "maintainability": 0.15, "security": 0.15, "documentation": 0.1}}, "monitoring": {"tracking_interval_seconds": 60, "history_size": 1000, "alert_thresholds": {"progress_stalled_hours": 2.0, "resource_usage_high": 90.0, "error_rate_high": 5.0, "timeline_slippage_hours": 4.0}, "reporting": {"auto_generate_reports": true, "report_interval_hours": 6, "export_format": "json", "include_charts": true}}, "task_management": {"auto_assign": true, "priority_levels": 10, "dependency_checking": true, "circular_dependency_detection": true, "batch_processing": {"enabled": true, "max_batch_size": 5, "batch_timeout_hours": 48}}, "architecture_design": {"patterns": {"microservices": {"complexity": "high", "scalability": "excellent", "use_cases": ["large_scale", "team_autonomy", "technology_diversity"]}, "layered_architecture": {"complexity": "medium", "scalability": "good", "use_cases": ["traditional_apps", "clear_separation"]}, "event_driven": {"complexity": "high", "scalability": "excellent", "use_cases": ["real_time", "loose_coupling", "asynchronous"]}, "domain_driven_design": {"complexity": "high", "scalability": "good", "use_cases": ["complex_business_logic", "domain_expertise"]}, "clean_architecture": {"complexity": "medium", "scalability": "good", "use_cases": ["maintainable_code", "testability", "framework_independence"]}}, "technology_stacks": {"web_development": {"frontend": ["React", "Vue.js", "Angular", "Svelte"], "backend": ["Node.js", "Python", "Java", "Go", "C#"], "database": ["PostgreSQL", "MongoDB", "Redis", "MySQL"], "cloud": ["AWS", "Azure", "GCP", "DigitalOcean"]}, "mobile_development": {"native": ["Swift", "<PERSON><PERSON><PERSON>", "Java"], "cross_platform": ["React Native", "Flutter", "<PERSON><PERSON><PERSON>"], "backend": ["Node.js", "Python", "Firebase"], "cloud": ["AWS", "Firebase", "Heroku"]}, "data_science": {"languages": ["Python", "R", "<PERSON>"], "frameworks": ["TensorFlow", "PyTorch", "Scikit-learn"], "databases": ["PostgreSQL", "MongoDB", "InfluxDB"], "cloud": ["AWS", "GCP", "Azure"]}}}, "system_integration": {"patterns": {"api_gateway": {"complexity": "medium", "benefits": ["centralized_control", "security", "monitoring"]}, "event_driven": {"complexity": "high", "benefits": ["scalability", "decoupling", "reliability"]}, "message_queue": {"complexity": "medium", "benefits": ["reliability", "scalability", "decoupling"]}, "service_mesh": {"complexity": "high", "benefits": ["observability", "security", "traffic_management"]}, "data_pipeline": {"complexity": "medium", "benefits": ["automation", "reliability", "scalability"]}}, "api_standards": {"rest": {"methods": ["GET", "POST", "PUT", "DELETE", "PATCH"], "status_codes": ["200", "201", "400", "401", "404", "500"], "best_practices": ["stateless", "cacheable", "uniform_interface"]}, "graphql": {"features": ["single_endpoint", "flexible_queries", "real_time_subscriptions"], "best_practices": ["schema_design", "resolver_optimization", "caching"]}, "grpc": {"features": ["protocol_buffers", "streaming", "code_generation"], "best_practices": ["service_definition", "error_handling", "streaming_patterns"]}}}, "performance_optimization": {"patterns": {"caching": {"types": ["memory_cache", "redis_cache", "cdn_cache", "database_cache"], "benefits": ["reduced_latency", "reduced_load", "improved_user_experience"]}, "load_balancing": {"types": ["round_robin", "least_connections", "weighted", "geographic"], "benefits": ["improved_availability", "better_performance", "scalability"]}, "database_optimization": {"types": ["query_optimization", "indexing", "partitioning", "connection_pooling"], "benefits": ["faster_queries", "reduced_load", "better_scalability"]}, "algorithm_optimization": {"types": ["time_complexity", "space_complexity", "parallelization", "approximation"], "benefits": ["faster_execution", "reduced_memory_usage", "better_scalability"]}, "memory_optimization": {"types": ["memory_pooling", "object_reuse", "garbage_collection_tuning", "memory_mapping"], "benefits": ["reduced_memory_usage", "better_performance", "stability"]}}, "performance_metrics": {"response_time": {"unit": "milliseconds", "targets": {"excellent": "< 100ms", "good": "< 500ms", "acceptable": "< 1000ms"}}, "throughput": {"unit": "requests/second", "targets": {"excellent": "> 1000", "good": "> 500", "acceptable": "> 100"}}, "cpu_usage": {"unit": "percentage", "targets": {"excellent": "< 50%", "good": "< 70%", "acceptable": "< 90%"}}, "memory_usage": {"unit": "percentage", "targets": {"excellent": "< 60%", "good": "< 80%", "acceptable": "< 95%"}}, "error_rate": {"unit": "percentage", "targets": {"excellent": "< 0.1%", "good": "< 1%", "acceptable": "< 5%"}}}}, "complex_problem_solving": {"patterns": {"optimization": {"types": ["linear_programming", "dynamic_programming", "genetic_algorithms", "simulated_annealing"], "use_cases": ["resource_allocation", "scheduling", "routing", "inventory_management"]}, "search": {"types": ["binary_search", "depth_first", "breadth_first", "a_star", "genetic_search"], "use_cases": ["data_mining", "pathfinding", "pattern_recognition", "recommendation_systems"]}, "classification": {"types": ["decision_trees", "neural_networks", "support_vector_machines", "naive_bayes"], "use_cases": ["spam_detection", "image_recognition", "fraud_detection", "medical_diagnosis"]}, "clustering": {"types": ["k_means", "hierarchical", "dbscan", "spectral_clustering"], "use_cases": ["customer_segmentation", "anomaly_detection", "data_compression", "market_analysis"]}, "prediction": {"types": ["linear_regression", "time_series", "neural_networks", "ensemble_methods"], "use_cases": ["sales_forecasting", "weather_prediction", "stock_prediction", "demand_forecasting"]}}, "solution_strategies": {"divide_and_conquer": {"applicability": ["sorting", "searching", "optimization"], "complexity": "medium"}, "dynamic_programming": {"applicability": ["optimization", "pathfinding", "sequence_alignment"], "complexity": "high"}, "greedy_algorithm": {"applicability": ["scheduling", "routing", "compression"], "complexity": "low"}, "backtracking": {"applicability": ["constraint_satisfaction", "combinatorial_optimization"], "complexity": "high"}, "heuristic_search": {"applicability": ["pathfinding", "scheduling", "optimization"], "complexity": "medium"}}}, "quality_assurance": {"code_quality": {"tools": ["pylint", "flake8", "black", "mypy"], "checks": ["code_complexity", "code_duplication", "naming_conventions", "code_structure", "error_handling", "logging_quality"]}, "performance": {"tools": ["profiler", "benchmark", "load_tester"], "checks": ["response_time", "throughput", "resource_usage", "scalability", "memory_efficiency", "algorithm_complexity"]}, "security": {"tools": ["bandit", "safety", "semgrep"], "checks": ["input_validation", "authentication", "authorization", "data_encryption", "sql_injection", "xss_protection"]}, "test_coverage": {"tools": ["pytest", "coverage", "tox"], "checks": ["unit_test_coverage", "integration_test_coverage", "test_quality", "test_maintainability", "test_performance"]}, "documentation": {"tools": ["sphinx", "pydoc", "markdown_lint"], "checks": ["api_documentation", "code_comments", "user_guides", "technical_docs", "readme_quality"]}}, "progress_tracking": {"milestone_tracking": true, "risk_assessment": true, "resource_monitoring": true, "timeline_management": true, "progress_visualization": true, "auto_reporting": {"enabled": true, "interval_hours": 6, "format": "markdown"}}, "logging": {"level": "INFO", "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "file_rotation": {"enabled": true, "max_size_mb": 100, "backup_count": 5}, "structured_logging": true}, "error_handling": {"retry_strategy": "exponential_backoff", "max_retries": 3, "retry_delay_seconds": 5, "circuit_breaker": {"enabled": true, "failure_threshold": 5, "recovery_timeout_seconds": 60}}}}