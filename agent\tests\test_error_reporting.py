#!/usr/bin/env python3
"""
Test error reporting functionality
"""

import json

import requests


def test_error_reporting():
    """Test the error reporting endpoint"""
    url = "http://127.0.0.1:8000/api/v1/errors/report"
    data = {
        "type": "test_error",
        "message": "Test error message",
        "stack": "Test stack trace",
    }

    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")

        if response.status_code == 200:
            print("✅ Error reporting successful!")
            return True
        else:
            print("❌ Error reporting failed!")
            return False

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


if __name__ == "__main__":
    print("🧪 Testing Error Reporting...")
    print("=" * 40)
    test_error_reporting()
