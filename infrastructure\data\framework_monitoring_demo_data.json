{"frameworks": [{"name": "CoreFramework0", "category": "backend", "current_version": "1.0.0", "latest_version": "1.1.0", "last_checked": "2025-07-24T20:48:08.466989", "update_available": true, "update_type": "minor", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://coreframework0.org/changelog", "documentation_url": null}, {"name": "alabaster", "category": "python", "current_version": "0.7.16", "latest_version": "1.0.0", "last_checked": "2025-07-24T20:48:08.796712", "update_available": true, "update_type": "major", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/alabaster/#history", "documentation_url": null}, {"name": "annotated-types", "category": "python", "current_version": "0.7.0", "latest_version": "0.7.0", "last_checked": "2025-07-24T20:48:08.466165", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/annotated-types/#history", "documentation_url": null}, {"name": "anyio", "category": "python", "current_version": "4.9.0", "latest_version": "4.9.0", "last_checked": "2025-07-24T20:48:08.466171", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/anyio/#history", "documentation_url": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "python", "current_version": "1.6.0", "latest_version": "1.6.1", "last_checked": "2025-07-24T20:48:10.132548", "update_available": true, "update_type": "patch", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/Authlib/#history", "documentation_url": null}, {"name": "babel", "category": "python", "current_version": "2.17.0", "latest_version": "2.17.0", "last_checked": "2025-07-24T20:48:08.466179", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/babel/#history", "documentation_url": null}, {"name": "bandit", "category": "python", "current_version": "1.7.5", "latest_version": "1.8.6", "last_checked": "2025-07-24T20:48:10.858759", "update_available": true, "update_type": "minor", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/bandit/#history", "documentation_url": null}, {"name": "bcrypt", "category": "python", "current_version": "4.3.0", "latest_version": "4.3.0", "last_checked": "2025-07-24T20:48:08.466187", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/bcrypt/#history", "documentation_url": null}, {"name": "bidict", "category": "python", "current_version": "0.23.1", "latest_version": "0.23.1", "last_checked": "2025-07-24T20:48:08.466191", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/bidict/#history", "documentation_url": null}, {"name": "black", "category": "python", "current_version": "24.3.0", "latest_version": "25.1.0", "last_checked": "2025-07-24T20:48:12.513312", "update_available": true, "update_type": "major", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/black/#history", "documentation_url": null}, {"name": "blinker", "category": "python", "current_version": "1.9.0", "latest_version": "1.9.0", "last_checked": "2025-07-24T20:48:08.466200", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/blinker/#history", "documentation_url": null}, {"name": "boolean.py", "category": "python", "current_version": "5.0", "latest_version": "5.0", "last_checked": "2025-07-24T20:48:08.466216", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/boolean.py/#history", "documentation_url": null}, {"name": "CacheControl", "category": "python", "current_version": "0.14.3", "latest_version": "0.14.3", "last_checked": "2025-07-24T20:48:08.466220", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/CacheControl/#history", "documentation_url": null}, {"name": "certifi", "category": "python", "current_version": "2025.7.14", "latest_version": "2025.7.14", "last_checked": "2025-07-24T20:48:08.466224", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/certifi/#history", "documentation_url": null}, {"name": "cffi", "category": "python", "current_version": "1.17.1", "latest_version": "1.17.1", "last_checked": "2025-07-24T20:48:08.466227", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/cffi/#history", "documentation_url": null}, {"name": "cfgv", "category": "python", "current_version": "3.4.0", "latest_version": "3.4.0", "last_checked": "2025-07-24T20:48:08.466231", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/cfgv/#history", "documentation_url": null}, {"name": "charset-normalizer", "category": "python", "current_version": "3.4.2", "latest_version": "3.4.2", "last_checked": "2025-07-24T20:48:08.466235", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/charset-normalizer/#history", "documentation_url": null}, {"name": "click", "category": "python", "current_version": "8.2.1", "latest_version": "8.2.1", "last_checked": "2025-07-24T20:48:08.466239", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/click/#history", "documentation_url": null}, {"name": "colorama", "category": "python", "current_version": "0.4.6", "latest_version": "0.4.6", "last_checked": "2025-07-24T20:48:08.466244", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/colorama/#history", "documentation_url": null}, {"name": "coverage", "category": "python", "current_version": "7.9.2", "latest_version": "7.9.2", "last_checked": "2025-07-24T20:48:08.466248", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/coverage/#history", "documentation_url": null}, {"name": "cryptography", "category": "python", "current_version": "45.0.5", "latest_version": "45.0.5", "last_checked": "2025-07-24T20:48:08.466252", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/cryptography/#history", "documentation_url": null}, {"name": "cyclonedx-python-lib", "category": "python", "current_version": "9.1.0", "latest_version": "9.1.0", "last_checked": "2025-07-24T20:48:08.466256", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/cyclonedx-python-lib/#history", "documentation_url": null}, {"name": "defusedxml", "category": "python", "current_version": "0.7.1", "latest_version": "0.7.1", "last_checked": "2025-07-24T20:48:08.466259", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/defusedxml/#history", "documentation_url": null}, {"name": "deprecation", "category": "python", "current_version": "2.1.0", "latest_version": "2.1.0", "last_checked": "2025-07-24T20:48:08.466263", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/deprecation/#history", "documentation_url": null}, {"name": "distlib", "category": "python", "current_version": "0.4.0", "latest_version": "0.4.0", "last_checked": "2025-07-24T20:48:08.466267", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/distlib/#history", "documentation_url": null}, {"name": "docutils", "category": "python", "current_version": "0.20.1", "latest_version": "0.20.1", "last_checked": "2025-07-24T20:48:08.466271", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/docutils/#history", "documentation_url": null}, {"name": "dparse", "category": "python", "current_version": "0.6.4", "latest_version": "0.6.4", "last_checked": "2025-07-24T20:48:08.466275", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/dparse/#history", "documentation_url": null}, {"name": "ecdsa", "category": "python", "current_version": "0.19.1", "latest_version": "0.19.1", "last_checked": "2025-07-24T20:48:08.466279", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/ecdsa/#history", "documentation_url": null}, {"name": "<PERSON><PERSON><PERSON>", "category": "python", "current_version": "0.116.1", "latest_version": "0.116.1", "last_checked": "2025-07-24T20:48:08.466282", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/fastapi/#history", "documentation_url": null}, {"name": "filelock", "category": "python", "current_version": "3.16.1", "latest_version": "3.16.1", "last_checked": "2025-07-24T20:48:08.466286", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/filelock/#history", "documentation_url": null}, {"name": "flake8", "category": "python", "current_version": "7.0.0", "latest_version": "7.0.0", "last_checked": "2025-07-24T20:48:08.466289", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/flake8/#history", "documentation_url": null}, {"name": "Flask", "category": "python", "current_version": "3.1.1", "latest_version": "3.1.1", "last_checked": "2025-07-24T20:48:08.466293", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/Flask/#history", "documentation_url": null}, {"name": "flask-cors", "category": "python", "current_version": "6.0.1", "latest_version": "6.0.1", "last_checked": "2025-07-24T20:48:08.466297", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/flask-cors/#history", "documentation_url": null}, {"name": "Flask-SocketIO", "category": "python", "current_version": "5.5.1", "latest_version": "5.5.1", "last_checked": "2025-07-24T20:48:08.466301", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/Flask-SocketIO/#history", "documentation_url": null}, {"name": "gitdb", "category": "python", "current_version": "4.0.12", "latest_version": "4.0.12", "last_checked": "2025-07-24T20:48:08.466305", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/gitdb/#history", "documentation_url": null}, {"name": "GitPython", "category": "python", "current_version": "3.1.44", "latest_version": "3.1.44", "last_checked": "2025-07-24T20:48:08.466309", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/GitPython/#history", "documentation_url": null}, {"name": "gotrue", "category": "python", "current_version": "2.12.3", "latest_version": "2.12.3", "last_checked": "2025-07-24T20:48:08.466312", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/gotrue/#history", "documentation_url": null}, {"name": "greenlet", "category": "python", "current_version": "3.2.3", "latest_version": "3.2.3", "last_checked": "2025-07-24T20:48:08.466316", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/greenlet/#history", "documentation_url": null}, {"name": "h11", "category": "python", "current_version": "0.16.0", "latest_version": "0.16.0", "last_checked": "2025-07-24T20:48:08.466319", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/h11/#history", "documentation_url": null}, {"name": "h2", "category": "python", "current_version": "4.2.0", "latest_version": "4.2.0", "last_checked": "2025-07-24T20:48:08.466323", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/h2/#history", "documentation_url": null}, {"name": "hpack", "category": "python", "current_version": "4.1.0", "latest_version": "4.1.0", "last_checked": "2025-07-24T20:48:08.466327", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/hpack/#history", "documentation_url": null}, {"name": "httpcore", "category": "python", "current_version": "1.0.9", "latest_version": "1.0.9", "last_checked": "2025-07-24T20:48:08.466332", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/httpcore/#history", "documentation_url": null}, {"name": "httptools", "category": "python", "current_version": "0.6.4", "latest_version": "0.6.4", "last_checked": "2025-07-24T20:48:08.466336", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/httptools/#history", "documentation_url": null}, {"name": "httpx", "category": "python", "current_version": "0.28.1", "latest_version": "0.28.1", "last_checked": "2025-07-24T20:48:08.466340", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/httpx/#history", "documentation_url": null}, {"name": "hyperframe", "category": "python", "current_version": "6.1.0", "latest_version": "6.1.0", "last_checked": "2025-07-24T20:48:08.466343", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/hyperframe/#history", "documentation_url": null}, {"name": "identify", "category": "python", "current_version": "2.6.12", "latest_version": "2.6.12", "last_checked": "2025-07-24T20:48:08.466348", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/identify/#history", "documentation_url": null}, {"name": "idna", "category": "python", "current_version": "3.10", "latest_version": "3.10", "last_checked": "2025-07-24T20:48:08.466352", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/idna/#history", "documentation_url": null}, {"name": "imagesize", "category": "python", "current_version": "1.4.1", "latest_version": "1.4.1", "last_checked": "2025-07-24T20:48:08.466356", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/imagesize/#history", "documentation_url": null}, {"name": "iniconfig", "category": "python", "current_version": "2.1.0", "latest_version": "2.1.0", "last_checked": "2025-07-24T20:48:08.466360", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/iniconfig/#history", "documentation_url": null}, {"name": "isort", "category": "python", "current_version": "5.13.2", "latest_version": "5.13.2", "last_checked": "2025-07-24T20:48:08.466364", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/isort/#history", "documentation_url": null}, {"name": "itsdangerous", "category": "python", "current_version": "2.2.0", "latest_version": "2.2.0", "last_checked": "2025-07-24T20:48:08.466368", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/itsdangerous/#history", "documentation_url": null}, {"name": "Jinja2", "category": "python", "current_version": "3.1.6", "latest_version": "3.1.6", "last_checked": "2025-07-24T20:48:08.466374", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/Jinja2/#history", "documentation_url": null}, {"name": "joblib", "category": "python", "current_version": "1.5.1", "latest_version": "1.5.1", "last_checked": "2025-07-24T20:48:08.466378", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/joblib/#history", "documentation_url": null}, {"name": "license-expression", "category": "python", "current_version": "30.4.4", "latest_version": "30.4.4", "last_checked": "2025-07-24T20:48:08.466382", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/license-expression/#history", "documentation_url": null}, {"name": "markdown-it-py", "category": "python", "current_version": "3.0.0", "latest_version": "3.0.0", "last_checked": "2025-07-24T20:48:08.466387", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/markdown-it-py/#history", "documentation_url": null}, {"name": "MarkupSafe", "category": "python", "current_version": "3.0.2", "latest_version": "3.0.2", "last_checked": "2025-07-24T20:48:08.466391", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/MarkupSafe/#history", "documentation_url": null}, {"name": "marshmallow", "category": "python", "current_version": "4.0.0", "latest_version": "4.0.0", "last_checked": "2025-07-24T20:48:08.466395", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/marshmallow/#history", "documentation_url": null}, {"name": "mccabe", "category": "python", "current_version": "0.7.0", "latest_version": "0.7.0", "last_checked": "2025-07-24T20:48:08.466399", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/mccabe/#history", "documentation_url": null}, {"name": "mdurl", "category": "python", "current_version": "0.1.2", "latest_version": "0.1.2", "last_checked": "2025-07-24T20:48:08.466403", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/mdurl/#history", "documentation_url": null}, {"name": "msgpack", "category": "python", "current_version": "1.1.1", "latest_version": "1.1.1", "last_checked": "2025-07-24T20:48:08.466407", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/msgpack/#history", "documentation_url": null}, {"name": "mypy", "category": "python", "current_version": "1.8.0", "latest_version": "1.8.0", "last_checked": "2025-07-24T20:48:08.466411", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/mypy/#history", "documentation_url": null}, {"name": "mypy_extensions", "category": "python", "current_version": "1.1.0", "latest_version": "1.1.0", "last_checked": "2025-07-24T20:48:08.466415", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/mypy_extensions/#history", "documentation_url": null}, {"name": "nltk", "category": "python", "current_version": "3.9.1", "latest_version": "3.9.1", "last_checked": "2025-07-24T20:48:08.466419", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/nltk/#history", "documentation_url": null}, {"name": "nodeenv", "category": "python", "current_version": "1.9.1", "latest_version": "1.9.1", "last_checked": "2025-07-24T20:48:08.466423", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/nodeenv/#history", "documentation_url": null}, {"name": "packageurl-python", "category": "python", "current_version": "0.17.1", "latest_version": "0.17.1", "last_checked": "2025-07-24T20:48:08.466427", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/packageurl-python/#history", "documentation_url": null}, {"name": "packaging", "category": "python", "current_version": "25.0", "latest_version": "25.0", "last_checked": "2025-07-24T20:48:08.466430", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/packaging/#history", "documentation_url": null}, {"name": "passlib", "category": "python", "current_version": "1.7.4", "latest_version": "1.7.4", "last_checked": "2025-07-24T20:48:08.466435", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/passlib/#history", "documentation_url": null}, {"name": "pathspec", "category": "python", "current_version": "0.12.1", "latest_version": "0.12.1", "last_checked": "2025-07-24T20:48:08.466440", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pathspec/#history", "documentation_url": null}, {"name": "pbr", "category": "python", "current_version": "6.1.1", "latest_version": "6.1.1", "last_checked": "2025-07-24T20:48:08.466443", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pbr/#history", "documentation_url": null}, {"name": "pillow", "category": "python", "current_version": "11.3.0", "latest_version": "11.3.0", "last_checked": "2025-07-24T20:48:08.466447", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pillow/#history", "documentation_url": null}, {"name": "pip-api", "category": "python", "current_version": "0.0.34", "latest_version": "0.0.34", "last_checked": "2025-07-24T20:48:08.466451", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pip-api/#history", "documentation_url": null}, {"name": "pip-requirements-parser", "category": "python", "current_version": "32.0.1", "latest_version": "32.0.1", "last_checked": "2025-07-24T20:48:08.466455", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pip-requirements-parser/#history", "documentation_url": null}, {"name": "pip_audit", "category": "python", "current_version": "2.9.0", "latest_version": "2.9.0", "last_checked": "2025-07-24T20:48:08.466459", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pip_audit/#history", "documentation_url": null}, {"name": "platformdirs", "category": "python", "current_version": "4.3.8", "latest_version": "4.3.8", "last_checked": "2025-07-24T20:48:08.466463", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/platformdirs/#history", "documentation_url": null}, {"name": "pluggy", "category": "python", "current_version": "1.6.0", "latest_version": "1.6.0", "last_checked": "2025-07-24T20:48:08.466467", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pluggy/#history", "documentation_url": null}, {"name": "postgrest", "category": "python", "current_version": "1.1.1", "latest_version": "1.1.1", "last_checked": "2025-07-24T20:48:08.466471", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/postgrest/#history", "documentation_url": null}, {"name": "pre-commit", "category": "python", "current_version": "3.6.0", "latest_version": "3.6.0", "last_checked": "2025-07-24T20:48:08.466475", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pre-commit/#history", "documentation_url": null}, {"name": "psutil", "category": "python", "current_version": "6.1.1", "latest_version": "6.1.1", "last_checked": "2025-07-24T20:48:08.466478", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/psutil/#history", "documentation_url": null}, {"name": "py-serializable", "category": "python", "current_version": "2.1.0", "latest_version": "2.1.0", "last_checked": "2025-07-24T20:48:08.466484", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/py-serializable/#history", "documentation_url": null}, {"name": "pyasn1", "category": "python", "current_version": "0.4.8", "latest_version": "0.4.8", "last_checked": "2025-07-24T20:48:08.466487", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pyasn1/#history", "documentation_url": null}, {"name": "pycodestyle", "category": "python", "current_version": "2.11.1", "latest_version": "2.11.1", "last_checked": "2025-07-24T20:48:08.466491", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pycodestyle/#history", "documentation_url": null}, {"name": "pyc<PERSON><PERSON>", "category": "python", "current_version": "2.22", "latest_version": "2.22", "last_checked": "2025-07-24T20:48:08.466495", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pycparser/#history", "documentation_url": null}, {"name": "pydantic", "category": "python", "current_version": "2.11.7", "latest_version": "2.11.7", "last_checked": "2025-07-24T20:48:08.466499", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pydantic/#history", "documentation_url": null}, {"name": "pydantic-settings", "category": "python", "current_version": "2.10.1", "latest_version": "2.10.1", "last_checked": "2025-07-24T20:48:08.466503", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pydantic-settings/#history", "documentation_url": null}, {"name": "pydantic_core", "category": "python", "current_version": "2.33.2", "latest_version": "2.33.2", "last_checked": "2025-07-24T20:48:08.466507", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pydantic_core/#history", "documentation_url": null}, {"name": "pyflakes", "category": "python", "current_version": "3.2.0", "latest_version": "3.2.0", "last_checked": "2025-07-24T20:48:08.466511", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pyflakes/#history", "documentation_url": null}, {"name": "Pygments", "category": "python", "current_version": "2.19.2", "latest_version": "2.19.2", "last_checked": "2025-07-24T20:48:08.466514", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/Pygments/#history", "documentation_url": null}, {"name": "PyJWT", "category": "python", "current_version": "2.10.1", "latest_version": "2.10.1", "last_checked": "2025-07-24T20:48:08.466518", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/PyJWT/#history", "documentation_url": null}, {"name": "pyparsing", "category": "python", "current_version": "3.2.3", "latest_version": "3.2.3", "last_checked": "2025-07-24T20:48:08.466523", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pyparsing/#history", "documentation_url": null}, {"name": "pytest", "category": "python", "current_version": "8.0.0", "latest_version": "8.0.0", "last_checked": "2025-07-24T20:48:08.466527", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pytest/#history", "documentation_url": null}, {"name": "pytest-asyncio", "category": "python", "current_version": "0.23.5", "latest_version": "0.23.5", "last_checked": "2025-07-24T20:48:08.466531", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pytest-asyncio/#history", "documentation_url": null}, {"name": "pytest-cov", "category": "python", "current_version": "4.1.0", "latest_version": "4.1.0", "last_checked": "2025-07-24T20:48:08.466534", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pytest-cov/#history", "documentation_url": null}, {"name": "pytest-mock", "category": "python", "current_version": "3.12.0", "latest_version": "3.12.0", "last_checked": "2025-07-24T20:48:08.466538", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/pytest-mock/#history", "documentation_url": null}, {"name": "python-dateutil", "category": "python", "current_version": "2.9.0.post0", "latest_version": "2.9.0.post0", "last_checked": "2025-07-24T20:48:08.466542", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/python-dateutil/#history", "documentation_url": null}, {"name": "python-dotenv", "category": "python", "current_version": "1.0.0", "latest_version": "1.0.0", "last_checked": "2025-07-24T20:48:08.466547", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/python-dotenv/#history", "documentation_url": null}, {"name": "python-engineio", "category": "python", "current_version": "4.12.2", "latest_version": "4.12.2", "last_checked": "2025-07-24T20:48:08.466551", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/python-engineio/#history", "documentation_url": null}, {"name": "python-jose", "category": "python", "current_version": "3.4.0", "latest_version": "3.4.0", "last_checked": "2025-07-24T20:48:08.466555", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/python-jose/#history", "documentation_url": null}, {"name": "python-multipart", "category": "python", "current_version": "0.0.18", "latest_version": "0.0.18", "last_checked": "2025-07-24T20:48:08.466559", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/python-multipart/#history", "documentation_url": null}, {"name": "python-socketio", "category": "python", "current_version": "5.13.0", "latest_version": "5.13.0", "last_checked": "2025-07-24T20:48:08.466563", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/python-socketio/#history", "documentation_url": null}, {"name": "PyYAML", "category": "python", "current_version": "6.0.2", "latest_version": "6.0.2", "last_checked": "2025-07-24T20:48:08.466567", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/PyYAML/#history", "documentation_url": null}, {"name": "realtime", "category": "python", "current_version": "2.6.0", "latest_version": "2.6.0", "last_checked": "2025-07-24T20:48:08.466571", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/realtime/#history", "documentation_url": null}, {"name": "regex", "category": "python", "current_version": "2024.11.6", "latest_version": "2024.11.6", "last_checked": "2025-07-24T20:48:08.466575", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/regex/#history", "documentation_url": null}, {"name": "requests", "category": "python", "current_version": "2.32.4", "latest_version": "2.32.4", "last_checked": "2025-07-24T20:48:08.466578", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/requests/#history", "documentation_url": null}, {"name": "rich", "category": "python", "current_version": "14.0.0", "latest_version": "14.0.0", "last_checked": "2025-07-24T20:48:08.466582", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/rich/#history", "documentation_url": null}, {"name": "rsa", "category": "python", "current_version": "4.9.1", "latest_version": "4.9.1", "last_checked": "2025-07-24T20:48:08.466586", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/rsa/#history", "documentation_url": null}, {"name": "ruamel.yaml", "category": "python", "current_version": "0.18.14", "latest_version": "0.18.14", "last_checked": "2025-07-24T20:48:08.466590", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/ruamel.yaml/#history", "documentation_url": null}, {"name": "ruamel.yaml.clib", "category": "python", "current_version": "0.2.12", "latest_version": "0.2.12", "last_checked": "2025-07-24T20:48:08.466594", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/ruamel.yaml.clib/#history", "documentation_url": null}, {"name": "safety", "category": "python", "current_version": "3.6.0", "latest_version": "3.6.0", "last_checked": "2025-07-24T20:48:08.466598", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/safety/#history", "documentation_url": null}, {"name": "safety-schemas", "category": "python", "current_version": "0.0.14", "latest_version": "0.0.14", "last_checked": "2025-07-24T20:48:08.466602", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/safety-schemas/#history", "documentation_url": null}, {"name": "setuptools", "category": "python", "current_version": "80.9.0", "latest_version": "80.9.0", "last_checked": "2025-07-24T20:48:08.466606", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/setuptools/#history", "documentation_url": null}, {"name": "shellingham", "category": "python", "current_version": "1.5.4", "latest_version": "1.5.4", "last_checked": "2025-07-24T20:48:08.466611", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/shellingham/#history", "documentation_url": null}, {"name": "simple-websocket", "category": "python", "current_version": "1.1.0", "latest_version": "1.1.0", "last_checked": "2025-07-24T20:48:08.466615", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/simple-websocket/#history", "documentation_url": null}, {"name": "six", "category": "python", "current_version": "1.17.0", "latest_version": "1.17.0", "last_checked": "2025-07-24T20:48:08.466619", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/six/#history", "documentation_url": null}, {"name": "smmap", "category": "python", "current_version": "5.0.2", "latest_version": "5.0.2", "last_checked": "2025-07-24T20:48:08.466623", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/smmap/#history", "documentation_url": null}, {"name": "sniffio", "category": "python", "current_version": "1.3.1", "latest_version": "1.3.1", "last_checked": "2025-07-24T20:48:08.466627", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/sniffio/#history", "documentation_url": null}, {"name": "snowballstemmer", "category": "python", "current_version": "3.0.1", "latest_version": "3.0.1", "last_checked": "2025-07-24T20:48:08.466630", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/snowballstemmer/#history", "documentation_url": null}, {"name": "sortedcontainers", "category": "python", "current_version": "2.4.0", "latest_version": "2.4.0", "last_checked": "2025-07-24T20:48:08.466634", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/sortedcontainers/#history", "documentation_url": null}, {"name": "Sphinx", "category": "python", "current_version": "7.2.6", "latest_version": "7.2.6", "last_checked": "2025-07-24T20:48:08.466638", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/Sphinx/#history", "documentation_url": null}, {"name": "sphinx-rtd-theme", "category": "python", "current_version": "2.0.0", "latest_version": "2.0.0", "last_checked": "2025-07-24T20:48:08.466642", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/sphinx-rtd-theme/#history", "documentation_url": null}, {"name": "sphinxcontrib-applehelp", "category": "python", "current_version": "2.0.0", "latest_version": "2.0.0", "last_checked": "2025-07-24T20:48:08.466646", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/sphinxcontrib-applehelp/#history", "documentation_url": null}, {"name": "sphinxcontrib-devhelp", "category": "python", "current_version": "2.0.0", "latest_version": "2.0.0", "last_checked": "2025-07-24T20:48:08.466650", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/sphinxcontrib-devhelp/#history", "documentation_url": null}, {"name": "sphinxcontrib-htmlhelp", "category": "python", "current_version": "2.1.0", "latest_version": "2.1.0", "last_checked": "2025-07-24T20:48:08.466653", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/sphinxcontrib-htmlhelp/#history", "documentation_url": null}, {"name": "sphinxcontrib-j<PERSON>y", "category": "python", "current_version": "4.1", "latest_version": "4.1", "last_checked": "2025-07-24T20:48:08.466657", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/sphinxcontrib-jquery/#history", "documentation_url": null}, {"name": "sphinxcontrib-j<PERSON>th", "category": "python", "current_version": "1.0.1", "latest_version": "1.0.1", "last_checked": "2025-07-24T20:48:08.466661", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/sphinxcontrib-jsmath/#history", "documentation_url": null}, {"name": "sphinxcontrib-qthelp", "category": "python", "current_version": "2.0.0", "latest_version": "2.0.0", "last_checked": "2025-07-24T20:48:08.466665", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/sphinxcontrib-qthelp/#history", "documentation_url": null}, {"name": "sphinxcontrib-serializinghtml", "category": "python", "current_version": "2.0.0", "latest_version": "2.0.0", "last_checked": "2025-07-24T20:48:08.466668", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/sphinxcontrib-serializinghtml/#history", "documentation_url": null}, {"name": "SQLAlchemy", "category": "python", "current_version": "2.0.41", "latest_version": "2.0.41", "last_checked": "2025-07-24T20:48:08.466672", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/SQLAlchemy/#history", "documentation_url": null}, {"name": "starlette", "category": "python", "current_version": "0.47.2", "latest_version": "0.47.2", "last_checked": "2025-07-24T20:48:08.466676", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/starlette/#history", "documentation_url": null}, {"name": "s<PERSON><PERSON><PERSON>", "category": "python", "current_version": "5.4.1", "latest_version": "5.4.1", "last_checked": "2025-07-24T20:48:08.466679", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/stevedore/#history", "documentation_url": null}, {"name": "storage3", "category": "python", "current_version": "0.12.0", "latest_version": "0.12.0", "last_checked": "2025-07-24T20:48:08.466683", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/storage3/#history", "documentation_url": null}, {"name": "StrEnum", "category": "python", "current_version": "0.4.15", "latest_version": "0.4.15", "last_checked": "2025-07-24T20:48:08.466688", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/StrEnum/#history", "documentation_url": null}, {"name": "supabase", "category": "python", "current_version": "2.17.0", "latest_version": "2.17.0", "last_checked": "2025-07-24T20:48:08.466692", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/supabase/#history", "documentation_url": null}, {"name": "supafunc", "category": "python", "current_version": "0.10.1", "latest_version": "0.10.1", "last_checked": "2025-07-24T20:48:08.466695", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/supafunc/#history", "documentation_url": null}, {"name": "tenacity", "category": "python", "current_version": "9.1.2", "latest_version": "9.1.2", "last_checked": "2025-07-24T20:48:08.466699", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/tenacity/#history", "documentation_url": null}, {"name": "toml", "category": "python", "current_version": "0.10.2", "latest_version": "0.10.2", "last_checked": "2025-07-24T20:48:08.466703", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/toml/#history", "documentation_url": null}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "category": "python", "current_version": "0.13.3", "latest_version": "0.13.3", "last_checked": "2025-07-24T20:48:08.466707", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/tomlkit/#history", "documentation_url": null}, {"name": "tqdm", "category": "python", "current_version": "4.67.1", "latest_version": "4.67.1", "last_checked": "2025-07-24T20:48:08.466711", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/tqdm/#history", "documentation_url": null}, {"name": "typer", "category": "python", "current_version": "0.16.0", "latest_version": "0.16.0", "last_checked": "2025-07-24T20:48:08.466715", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/typer/#history", "documentation_url": null}, {"name": "types-passlib", "category": "python", "current_version": "1.7.7.20250602", "latest_version": "1.7.7.20250602", "last_checked": "2025-07-24T20:48:08.466719", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/types-passlib/#history", "documentation_url": null}, {"name": "types-psutil", "category": "python", "current_version": "7.0.0.20250601", "latest_version": "7.0.0.20250601", "last_checked": "2025-07-24T20:48:08.466723", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/types-psutil/#history", "documentation_url": null}, {"name": "types-pyasn1", "category": "python", "current_version": "0.6.0.20250516", "latest_version": "0.6.0.20250516", "last_checked": "2025-07-24T20:48:08.466727", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/types-pyasn1/#history", "documentation_url": null}, {"name": "types-python-jose", "category": "python", "current_version": "3.5.0.20250531", "latest_version": "3.5.0.20250531", "last_checked": "2025-07-24T20:48:08.466731", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/types-python-jose/#history", "documentation_url": null}, {"name": "types-requests", "category": "python", "current_version": "2.32.4.20250611", "latest_version": "2.32.4.20250611", "last_checked": "2025-07-24T20:48:08.466735", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/types-requests/#history", "documentation_url": null}, {"name": "typing-inspection", "category": "python", "current_version": "0.4.1", "latest_version": "0.4.1", "last_checked": "2025-07-24T20:48:08.466739", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/typing-inspection/#history", "documentation_url": null}, {"name": "typing_extensions", "category": "python", "current_version": "4.14.1", "latest_version": "4.14.1", "last_checked": "2025-07-24T20:48:08.466744", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/typing_extensions/#history", "documentation_url": null}, {"name": "urllib3", "category": "python", "current_version": "2.5.0", "latest_version": "2.5.0", "last_checked": "2025-07-24T20:48:08.466748", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/urllib3/#history", "documentation_url": null}, {"name": "u<PERSON><PERSON>", "category": "python", "current_version": "0.35.0", "latest_version": "0.35.0", "last_checked": "2025-07-24T20:48:08.466752", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/uvicorn/#history", "documentation_url": null}, {"name": "virtualenv", "category": "python", "current_version": "20.31.2", "latest_version": "20.31.2", "last_checked": "2025-07-24T20:48:08.466756", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/virtualenv/#history", "documentation_url": null}, {"name": "watchdog", "category": "python", "current_version": "3.0.0", "latest_version": "3.0.0", "last_checked": "2025-07-24T20:48:08.466760", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/watchdog/#history", "documentation_url": null}, {"name": "watchfiles", "category": "python", "current_version": "1.1.0", "latest_version": "1.1.0", "last_checked": "2025-07-24T20:48:08.466764", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/watchfiles/#history", "documentation_url": null}, {"name": "websockets", "category": "python", "current_version": "15.0", "latest_version": "15.0", "last_checked": "2025-07-24T20:48:08.466769", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/websockets/#history", "documentation_url": null}, {"name": "Werkzeug", "category": "python", "current_version": "3.1.3", "latest_version": "3.1.3", "last_checked": "2025-07-24T20:48:08.466773", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/Werkzeug/#history", "documentation_url": null}, {"name": "wsproto", "category": "python", "current_version": "1.2.0", "latest_version": "1.2.0", "last_checked": "2025-07-24T20:48:08.466778", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://pypi.org/project/wsproto/#history", "documentation_url": null}, {"name": "@monaco-editor/react", "category": "node", "current_version": "4.7.0", "latest_version": "4.7.0", "last_checked": "2025-07-24T20:48:08.467648", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/@monaco-editor/react", "documentation_url": null}, {"name": "@tailwindcss/postcss", "category": "node", "current_version": "4.1.11", "latest_version": "4.1.11", "last_checked": "2025-07-24T20:48:08.467653", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/@tailwindcss/postcss", "documentation_url": null}, {"name": "@tanstack/react-query", "category": "node", "current_version": "5.83.0", "latest_version": "5.83.0", "last_checked": "2025-07-24T20:48:08.467655", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/@tanstack/react-query", "documentation_url": null}, {"name": "autoprefixer", "category": "node", "current_version": "10.4.21", "latest_version": "10.4.21", "last_checked": "2025-07-24T20:48:08.467657", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/autoprefixer", "documentation_url": null}, {"name": "axios", "category": "node", "current_version": "1.11.0", "latest_version": "1.11.0", "last_checked": "2025-07-24T20:48:08.467659", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/axios", "documentation_url": null}, {"name": "clsx", "category": "node", "current_version": "2.1.1", "latest_version": "2.1.1", "last_checked": "2025-07-24T20:48:08.467661", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/clsx", "documentation_url": null}, {"name": "lucide-react", "category": "node", "current_version": "0.525.0", "latest_version": "0.525.0", "last_checked": "2025-07-24T20:48:08.467662", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/lucide-react", "documentation_url": null}, {"name": "next", "category": "node", "current_version": "14.0.0", "latest_version": "14.0.0", "last_checked": "2025-07-24T20:48:08.467663", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/next", "documentation_url": null}, {"name": "postcss", "category": "node", "current_version": "8.5.6", "latest_version": "8.5.6", "last_checked": "2025-07-24T20:48:08.467665", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/postcss", "documentation_url": null}, {"name": "react", "category": "node", "current_version": "18.2.0", "latest_version": "18.2.0", "last_checked": "2025-07-24T20:48:08.467666", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/react", "documentation_url": null}, {"name": "react-dom", "category": "node", "current_version": "18.2.0", "latest_version": "18.2.0", "last_checked": "2025-07-24T20:48:08.467668", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/react-dom", "documentation_url": null}, {"name": "react-dropzone", "category": "node", "current_version": "14.2.3", "latest_version": "14.2.3", "last_checked": "2025-07-24T20:48:08.467669", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/react-dropzone", "documentation_url": null}, {"name": "react-hook-form", "category": "node", "current_version": "7.61.1", "latest_version": "7.61.1", "last_checked": "2025-07-24T20:48:08.467671", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/react-hook-form", "documentation_url": null}, {"name": "react-hot-toast", "category": "node", "current_version": "2.5.2", "latest_version": "2.5.2", "last_checked": "2025-07-24T20:48:08.467672", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/react-hot-toast", "documentation_url": null}, {"name": "react-resizable-panels", "category": "node", "current_version": "3.0.3", "latest_version": "3.0.3", "last_checked": "2025-07-24T20:48:08.467674", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/react-resizable-panels", "documentation_url": null}, {"name": "tailwind-merge", "category": "node", "current_version": "3.3.1", "latest_version": "3.3.1", "last_checked": "2025-07-24T20:48:08.467676", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/tailwind-merge", "documentation_url": null}, {"name": "tailwindcss", "category": "node", "current_version": "4.1.11", "latest_version": "4.1.11", "last_checked": "2025-07-24T20:48:08.467677", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/tailwindcss", "documentation_url": null}, {"name": "typescript", "category": "node", "current_version": "5.0.0", "latest_version": "5.0.0", "last_checked": "2025-07-24T20:48:08.467679", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/typescript", "documentation_url": null}, {"name": "zustand", "category": "node", "current_version": "4.5.7", "latest_version": "4.5.7", "last_checked": "2025-07-24T20:48:08.467681", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/zustand", "documentation_url": null}, {"name": "@types/node", "category": "node", "current_version": "20.0.0", "latest_version": "20.0.0", "last_checked": "2025-07-24T20:48:08.467683", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/@types/node", "documentation_url": null}, {"name": "@types/react", "category": "node", "current_version": "18.2.0", "latest_version": "18.2.0", "last_checked": "2025-07-24T20:48:08.467685", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/@types/react", "documentation_url": null}, {"name": "@types/react-dom", "category": "node", "current_version": "18.2.0", "latest_version": "18.2.0", "last_checked": "2025-07-24T20:48:08.467686", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/@types/react-dom", "documentation_url": null}, {"name": "cypress", "category": "node", "current_version": "14.5.2", "latest_version": "14.5.2", "last_checked": "2025-07-24T20:48:08.467704", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/cypress", "documentation_url": null}, {"name": "cypress-file-upload", "category": "node", "current_version": "5.0.8", "latest_version": "5.0.8", "last_checked": "2025-07-24T20:48:08.467705", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/cypress-file-upload", "documentation_url": null}, {"name": "eslint", "category": "node", "current_version": "8.0.0", "latest_version": "8.0.0", "last_checked": "2025-07-24T20:48:08.467707", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/eslint", "documentation_url": null}, {"name": "eslint-config-next", "category": "node", "current_version": "14.0.0", "latest_version": "14.0.0", "last_checked": "2025-07-24T20:48:08.467708", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/eslint-config-next", "documentation_url": null}, {"name": "prettier", "category": "node", "current_version": "3.0.0", "latest_version": "3.0.0", "last_checked": "2025-07-24T20:48:08.467709", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://www.npmjs.com/package/prettier", "documentation_url": null}, {"name": "Next.js", "category": "frontend", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467761", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "Nuxt.js", "category": "frontend", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467784", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "Angular", "category": "frontend", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467802", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "Vue.js", "category": "frontend", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467817", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "Svelte", "category": "frontend", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467832", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "Tailwind CSS", "category": "frontend", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467848", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "Webpack", "category": "tool", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467862", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "Vite", "category": "tool", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467877", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "Rollup", "category": "tool", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467892", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "<PERSON><PERSON>", "category": "tool", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467906", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "<PERSON>er", "category": "tool", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467921", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "Kubernetes", "category": "tool", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467935", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "Terraform", "category": "tool", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467953", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "Ansible", "category": "tool", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467969", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "<PERSON>", "category": "tool", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467983", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "GitHub Actions", "category": "tool", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.467996", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "GitLab CI", "category": "tool", "current_version": "detected", "latest_version": "unknown", "last_checked": "2025-07-24T20:48:08.468009", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": null, "documentation_url": null}, {"name": "CoreFramework1", "category": "frontend", "current_version": "1.1.0", "latest_version": "1.2.0", "last_checked": "2025-07-24T20:48:08.567247", "update_available": true, "update_type": "minor", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://coreframework1.org/changelog", "documentation_url": null}, {"name": "CoreFramework2", "category": "backend", "current_version": "1.2.0", "latest_version": "1.3.0", "last_checked": "2025-07-24T20:48:08.667841", "update_available": true, "update_type": "minor", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://coreframework2.org/changelog", "documentation_url": null}, {"name": "CoreFramework3", "category": "frontend", "current_version": "1.3.0", "latest_version": "1.4.0", "last_checked": "2025-07-24T20:48:08.768772", "update_available": true, "update_type": "minor", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://coreframework3.org/changelog", "documentation_url": null}, {"name": "CoreFramework4", "category": "backend", "current_version": "1.4.0", "latest_version": "1.5.0", "last_checked": "2025-07-24T20:48:08.869190", "update_available": true, "update_type": "minor", "release_date": null, "end_of_life": null, "supported_versions": [], "changelog_url": "https://coreframework4.org/changelog", "documentation_url": null}, {"name": "LegacyFramework0", "category": "backend", "current_version": "1.0.0", "latest_version": "1.0.0", "last_checked": "2025-07-24T20:48:10.376580", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": "2025-06-24T20:48:10.376568", "supported_versions": [], "changelog_url": "https://legacyframework0.org/changelog", "documentation_url": null}, {"name": "LegacyFramework2", "category": "backend", "current_version": "1.2.0", "latest_version": "1.2.0", "last_checked": "2025-07-24T20:48:10.577923", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": "2025-06-24T20:48:10.577908", "supported_versions": [], "changelog_url": "https://legacyframework2.org/changelog", "documentation_url": null}, {"name": "LegacyFramework4", "category": "backend", "current_version": "1.4.0", "latest_version": "1.4.0", "last_checked": "2025-07-24T20:48:10.778405", "update_available": false, "update_type": "none", "release_date": null, "end_of_life": "2025-06-24T20:48:10.778390", "supported_versions": [], "changelog_url": "https://legacyframework4.org/changelog", "documentation_url": null}], "vulnerabilities": [{"vulnerability_id": "sec_vuln_0", "framework_name": "CoreFramework0", "severity": "critical", "cve_id": null, "title": "Security vulnerability in CoreFramework0", "description": "Security issue in CoreFramework0", "affected_versions": ["CoreFramework0 1.x"], "fixed_versions": ["CoreFramework0 2.x"], "published_date": null, "last_updated": "2025-07-24T20:48:08.970036", "cvss_score": 8.0, "references": [], "status": "open"}, {"vulnerability_id": "sec_vuln_3", "framework_name": "CoreFramework3", "severity": "high", "cve_id": null, "title": "Security vulnerability in CoreFramework3", "description": "Security issue in CoreFramework3", "affected_versions": ["CoreFramework3 1.x"], "fixed_versions": ["CoreFramework3 2.x"], "published_date": null, "last_updated": "2025-07-24T20:48:09.271195", "cvss_score": 8.6, "references": [], "status": "open"}, {"vulnerability_id": "sec_vuln_6", "framework_name": "CoreFramework6", "severity": "critical", "cve_id": null, "title": "Security vulnerability in CoreFramework6", "description": "Security issue in CoreFramework6", "affected_versions": ["CoreFramework6 1.x"], "fixed_versions": ["CoreFramework6 2.x"], "published_date": null, "last_updated": "2025-07-24T20:48:09.572225", "cvss_score": 9.2, "references": [], "status": "open"}], "compatibility_issues": [{"issue_id": "compat_issue_0", "framework_name": "CoreFramework0", "issue_type": "breaking_change", "title": "Breaking changes in CoreFramework0 2.x", "description": "Major changes in CoreFramework0 2.x", "affected_versions": ["CoreFramework0 2.x"], "introduced_in_version": null, "fixed_in_version": null, "severity": "high", "created_at": "2025-07-24T20:48:09.773205", "status": "open", "workaround": null, "migration_guide": null}, {"issue_id": "compat_issue_2", "framework_name": "CoreFramework2", "issue_type": "breaking_change", "title": "Breaking changes in CoreFramework2 2.x", "description": "Major changes in CoreFramework2 2.x", "affected_versions": ["CoreFramework2 2.x"], "introduced_in_version": null, "fixed_in_version": null, "severity": "high", "created_at": "2025-07-24T20:48:09.973957", "status": "open", "workaround": null, "migration_guide": null}, {"issue_id": "compat_issue_4", "framework_name": "CoreFramework4", "issue_type": "breaking_change", "title": "Breaking changes in CoreFramework4 2.x", "description": "Major changes in CoreFramework4 2.x", "affected_versions": ["CoreFramework4 2.x"], "introduced_in_version": null, "fixed_in_version": null, "severity": "high", "created_at": "2025-07-24T20:48:10.175216", "status": "open", "workaround": null, "migration_guide": null}], "recommendations": [{"recommendation_id": "security_critical_1753411693", "framework_name": "CoreFramework0", "recommendation_type": "security_fix", "priority": "critical", "title": "Critical Security Fix Required: Security vulnerability in CoreFramework0", "description": "Critical security vulnerability detected in CoreFramework0: Security issue in CoreFramework0", "impact": "high", "effort": "medium", "created_at": "2025-07-24T20:48:13.881534", "target_version": "CoreFramework0 2.x", "deadline": "2025-07-31T20:48:13.881522", "action_items": ["Update CoreFramework0 to latest secure version", "Test application thoroughly after update", "Monitor for any breaking changes"], "risks": ["Breaking changes in new version", "Potential compatibility issues"], "benefits": ["Security vulnerability fixed", "Improved security posture"]}, {"recommendation_id": "security_critical_1753411693", "framework_name": "CoreFramework6", "recommendation_type": "security_fix", "priority": "critical", "title": "Critical Security Fix Required: Security vulnerability in CoreFramework6", "description": "Critical security vulnerability detected in CoreFramework6: Security issue in CoreFramework6", "impact": "high", "effort": "medium", "created_at": "2025-07-24T20:48:13.881543", "target_version": "CoreFramework6 2.x", "deadline": "2025-07-31T20:48:13.881540", "action_items": ["Update CoreFramework6 to latest secure version", "Test application thoroughly after update", "Monitor for any breaking changes"], "risks": ["Breaking changes in new version", "Potential compatibility issues"], "benefits": ["Security vulnerability fixed", "Improved security posture"]}, {"recommendation_id": "major_update_1753411693", "framework_name": "alabaster", "recommendation_type": "update", "priority": "high", "title": "Major Update Available: alabaster", "description": "Major version update available for alabaster: 0.7.16 → 1.0.0", "impact": "high", "effort": "high", "created_at": "2025-07-24T20:48:13.881562", "target_version": "1.0.0", "deadline": "2025-08-23T20:48:13.881559", "action_items": ["Review changelog for alabaster", "Test application with new version", "Update code for breaking changes", "Deploy to staging environment first"], "risks": ["Breaking changes", "Compatibility issues", "Extended testing required"], "benefits": ["New features", "Performance improvements", "Bug fixes"]}, {"recommendation_id": "major_update_1753411693", "framework_name": "black", "recommendation_type": "update", "priority": "high", "title": "Major Update Available: black", "description": "Major version update available for black: 24.3.0 → 25.1.0", "impact": "high", "effort": "high", "created_at": "2025-07-24T20:48:13.881567", "target_version": "25.1.0", "deadline": "2025-08-23T20:48:13.881565", "action_items": ["Review changelog for black", "Test application with new version", "Update code for breaking changes", "Deploy to staging environment first"], "risks": ["Breaking changes", "Compatibility issues", "Extended testing required"], "benefits": ["New features", "Performance improvements", "Bug fixes"]}, {"recommendation_id": "eol_1753411693", "framework_name": "LegacyFramework0", "recommendation_type": "end_of_life", "priority": "high", "title": "End of Life: LegacyFramework0", "description": "LegacyFramework0 has reached end of life and is no longer supported", "impact": "high", "effort": "high", "created_at": "2025-07-24T20:48:13.881585", "target_version": null, "deadline": "2025-09-22T20:48:13.881582", "action_items": ["Research alternatives to LegacyFramework0", "Plan migration strategy", "Allocate resources for migration", "Test alternative frameworks"], "risks": ["No security updates", "No bug fixes", "Compatibility issues"], "benefits": ["Modern framework features", "Active support", "Better security"]}, {"recommendation_id": "eol_1753411693", "framework_name": "LegacyFramework2", "recommendation_type": "end_of_life", "priority": "high", "title": "End of Life: LegacyFramework2", "description": "LegacyFramework2 has reached end of life and is no longer supported", "impact": "high", "effort": "high", "created_at": "2025-07-24T20:48:13.881590", "target_version": null, "deadline": "2025-09-22T20:48:13.881588", "action_items": ["Research alternatives to LegacyFramework2", "Plan migration strategy", "Allocate resources for migration", "Test alternative frameworks"], "risks": ["No security updates", "No bug fixes", "Compatibility issues"], "benefits": ["Modern framework features", "Active support", "Better security"]}, {"recommendation_id": "eol_1753411693", "framework_name": "LegacyFramework4", "recommendation_type": "end_of_life", "priority": "high", "title": "End of Life: LegacyFramework4", "description": "LegacyFramework4 has reached end of life and is no longer supported", "impact": "high", "effort": "high", "created_at": "2025-07-24T20:48:13.881595", "target_version": null, "deadline": "2025-09-22T20:48:13.881592", "action_items": ["Research alternatives to LegacyFramework4", "Plan migration strategy", "Allocate resources for migration", "Test alternative frameworks"], "risks": ["No security updates", "No bug fixes", "Compatibility issues"], "benefits": ["Modern framework features", "Active support", "Better security"]}, {"recommendation_id": "minor_update_1753411693", "framework_name": "CoreFramework0", "recommendation_type": "update", "priority": "medium", "title": "Minor Update Available: CoreFramework0", "description": "Minor version update available for CoreFramework0: 1.0.0 → 1.1.0", "impact": "medium", "effort": "low", "created_at": "2025-07-24T20:48:13.881609", "target_version": "1.1.0", "deadline": "2025-10-22T20:48:13.881606", "action_items": ["Update CoreFramework0 to 1.1.0", "Run test suite", "Deploy to production"], "risks": ["Minor breaking changes", "Testing required"], "benefits": ["New features", "Bug fixes", "Performance improvements"]}, {"recommendation_id": "minor_update_1753411693", "framework_name": "bandit", "recommendation_type": "update", "priority": "medium", "title": "Minor Update Available: bandit", "description": "Minor version update available for bandit: 1.7.5 → 1.8.6", "impact": "medium", "effort": "low", "created_at": "2025-07-24T20:48:13.881614", "target_version": "1.8.6", "deadline": "2025-10-22T20:48:13.881611", "action_items": ["Update bandit to 1.8.6", "Run test suite", "Deploy to production"], "risks": ["Minor breaking changes", "Testing required"], "benefits": ["New features", "Bug fixes", "Performance improvements"]}, {"recommendation_id": "minor_update_1753411693", "framework_name": "CoreFramework1", "recommendation_type": "update", "priority": "medium", "title": "Minor Update Available: CoreFramework1", "description": "Minor version update available for CoreFramework1: 1.1.0 → 1.2.0", "impact": "medium", "effort": "low", "created_at": "2025-07-24T20:48:13.881619", "target_version": "1.2.0", "deadline": "2025-10-22T20:48:13.881616", "action_items": ["Update CoreFramework1 to 1.2.0", "Run test suite", "Deploy to production"], "risks": ["Minor breaking changes", "Testing required"], "benefits": ["New features", "Bug fixes", "Performance improvements"]}, {"recommendation_id": "minor_update_1753411693", "framework_name": "CoreFramework2", "recommendation_type": "update", "priority": "medium", "title": "Minor Update Available: CoreFramework2", "description": "Minor version update available for CoreFramework2: 1.2.0 → 1.3.0", "impact": "medium", "effort": "low", "created_at": "2025-07-24T20:48:13.881628", "target_version": "1.3.0", "deadline": "2025-10-22T20:48:13.881622", "action_items": ["Update CoreFramework2 to 1.3.0", "Run test suite", "Deploy to production"], "risks": ["Minor breaking changes", "Testing required"], "benefits": ["New features", "Bug fixes", "Performance improvements"]}, {"recommendation_id": "minor_update_1753411693", "framework_name": "CoreFramework3", "recommendation_type": "update", "priority": "medium", "title": "Minor Update Available: CoreFramework3", "description": "Minor version update available for CoreFramework3: 1.3.0 → 1.4.0", "impact": "medium", "effort": "low", "created_at": "2025-07-24T20:48:13.881638", "target_version": "1.4.0", "deadline": "2025-10-22T20:48:13.881632", "action_items": ["Update CoreFramework3 to 1.4.0", "Run test suite", "Deploy to production"], "risks": ["Minor breaking changes", "Testing required"], "benefits": ["New features", "Bug fixes", "Performance improvements"]}, {"recommendation_id": "minor_update_1753411693", "framework_name": "CoreFramework4", "recommendation_type": "update", "priority": "medium", "title": "Minor Update Available: CoreFramework4", "description": "Minor version update available for CoreFramework4: 1.4.0 → 1.5.0", "impact": "medium", "effort": "low", "created_at": "2025-07-24T20:48:13.881650", "target_version": "1.5.0", "deadline": "2025-10-22T20:48:13.881643", "action_items": ["Update CoreFramework4 to 1.5.0", "Run test suite", "Deploy to production"], "risks": ["Minor breaking changes", "Testing required"], "benefits": ["New features", "Bug fixes", "Performance improvements"]}], "monitoring_history": [], "config": {"data_dir": "data/frameworks", "check_interval": 1, "project_root": "."}}