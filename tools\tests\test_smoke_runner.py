#!/usr/bin/env python3
"""
Unit tests for smoke test runner

Tests the smoke test runner with mocked backends to ensure
proper functionality without requiring actual services.
"""

import asyncio
import json
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from typing import Dict, Any

from .smoke_runner import (
    SmokeTestRunner,
    SmokeTestBackend,
    SmokeTestStatus,
    run_smoke_tests,
    stream_smoke_tests
)


class TestSmokeTestRunner:
    """Test cases for SmokeTestRunner class"""

    def test_init_default_config(self):
        """Test runner initialization with default config"""
        runner = SmokeTestRunner()
        assert runner.backend == SmokeTestBackend.HTTP
        assert runner.base_url == "http://localhost:8000"
        assert runner.timeout == 30
        assert len(runner.scenarios) == 2  # health_check and authentication

    def test_init_custom_config(self):
        """Test runner initialization with custom config"""
        config = {
            "base_url": "http://localhost:3000",
            "timeout": 60
        }
        runner = SmokeTestRunner(backend=SmokeTestBackend.PLAYWRIGHT, config=config)
        assert runner.backend == SmokeTestBackend.PLAYWRIGHT
        assert runner.base_url == "http://localhost:3000"
        assert runner.timeout == 60

    def test_load_scenarios(self):
        """Test scenario loading"""
        runner = SmokeTestRunner()
        scenarios = runner.scenarios

        # Check health check scenario
        health_scenario = next(s for s in scenarios if s.id == "health_check")
        assert health_scenario.name == "Health Check"
        assert len(health_scenario.steps) == 2
        assert health_scenario.steps[0].id == "api_health"
        assert health_scenario.steps[1].id == "db_health"

        # Check authentication scenario
        auth_scenario = next(s for s in scenarios if s.id == "authentication")
        assert auth_scenario.name == "Authentication Flow"
        assert len(auth_scenario.steps) == 3
        assert auth_scenario.steps[0].id == "login_page"
        assert auth_scenario.steps[1].id == "login_submit"
        assert auth_scenario.steps[2].id == "dashboard_access"

    @pytest.mark.asyncio
    async def test_run_all_http_success(self):
        """Test successful HTTP test run"""
        runner = SmokeTestRunner(backend=SmokeTestBackend.HTTP)

        # Mock httpx client
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.elapsed.total_seconds.return_value = 0.1

        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)

            result = await runner.run_all()

            assert result.run_id.startswith("smoke_run_")
            assert result.backend == SmokeTestBackend.HTTP
            assert result.end_time is not None
            assert result.duration > 0
            assert result.total_steps == 5  # 2 + 3 steps
            assert result.passed_steps == 5
            assert result.failed_steps == 0
            assert result.success_rate == 100.0

    @pytest.mark.asyncio
    async def test_run_all_http_failure(self):
        """Test HTTP test run with failures"""
        runner = SmokeTestRunner(backend=SmokeTestBackend.HTTP)

        # Mock failing response
        mock_response = MagicMock()
        mock_response.status_code = 500

        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)

            result = await runner.run_all()

            assert result.failed_steps > 0
            assert result.success_rate < 100.0

    @pytest.mark.asyncio
    async def test_stream_results(self):
        """Test streaming results functionality"""
        runner = SmokeTestRunner(backend=SmokeTestBackend.HTTP)

        # Mock httpx client
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.elapsed.total_seconds.return_value = 0.1

        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)

            events = []
            async for event in runner.stream_results():
                events.append(event)

            # Check event types
            event_types = [event["type"] for event in events]
            assert "run_start" in event_types
            assert "scenario_start" in event_types
            assert "step_start" in event_types
            assert "step_complete" in event_types
            assert "scenario_complete" in event_types
            assert "run_complete" in event_types

            # Check run_start event
            run_start = next(e for e in events if e["type"] == "run_start")
            assert "run_id" in run_start
            assert run_start["backend"] == "http"
            assert run_start["total_scenarios"] == 2

            # Check run_complete event
            run_complete = next(e for e in events if e["type"] == "run_complete")
            assert "duration" in run_complete
            assert "success_rate" in run_complete

    @pytest.mark.asyncio
    async def test_execute_http_step_api_health(self):
        """Test HTTP API health step execution"""
        runner = SmokeTestRunner()
        step = runner.scenarios[0].steps[0]  # api_health step
        scenario = runner.scenarios[0]

        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.elapsed.total_seconds.return_value = 0.1

        mock_client = AsyncMock()
        mock_client.get.return_value = mock_response

        await runner._execute_http_step(mock_client, step, scenario)

        assert step.details["status_code"] == 200
        assert step.details["response_time"] == 0.1
        mock_client.get.assert_called_once_with("http://localhost:8000/health")

    @pytest.mark.asyncio
    async def test_execute_http_step_failure(self):
        """Test HTTP step execution with failure"""
        runner = SmokeTestRunner()
        step = runner.scenarios[0].steps[0]  # api_health step
        scenario = runner.scenarios[0]

        mock_response = MagicMock()
        mock_response.status_code = 500

        mock_client = AsyncMock()
        mock_client.get.return_value = mock_response

        with pytest.raises(Exception, match="Health check failed: 500"):
            await runner._execute_http_step(mock_client, step, scenario)

    @pytest.mark.asyncio
    async def test_run_playwright_tests(self):
        """Test Playwright test execution (mocked)"""
        runner = SmokeTestRunner(backend=SmokeTestBackend.PLAYWRIGHT)

        # Mock Playwright components
        mock_response = AsyncMock()
        mock_response.ok = True
        mock_response.status = 200

        mock_page = AsyncMock()
        mock_page.request.get.return_value = mock_response
        mock_page.title.return_value = "Test Page"
        mock_page.goto = AsyncMock()
        mock_page.fill = AsyncMock()
        mock_page.click = AsyncMock()
        mock_page.wait_for_timeout = AsyncMock()

        mock_context = AsyncMock()
        mock_context.new_page.return_value = mock_page

        mock_browser = AsyncMock()
        mock_browser.new_context.return_value = mock_context
        mock_browser.close = AsyncMock()

        mock_playwright = AsyncMock()
        mock_playwright.chromium.launch.return_value = mock_browser

        with patch('playwright.async_api.async_playwright') as mock_pw:
            mock_pw.return_value.__aenter__.return_value = mock_playwright

            result = await runner.run_all()

            assert result.backend == SmokeTestBackend.PLAYWRIGHT
            assert result.passed_steps > 0
            assert result.failed_steps == 0
            assert result.success_rate == 100.0


class TestConvenienceFunctions:
    """Test convenience functions"""

    @pytest.mark.asyncio
    async def test_run_smoke_tests_function(self):
        """Test run_smoke_tests convenience function"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.elapsed.total_seconds.return_value = 0.1

        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)

            result = await run_smoke_tests(backend="http")

            assert isinstance(result, dict)
            assert "run_id" in result
            assert "backend" in result
            assert "scenarios" in result

    @pytest.mark.asyncio
    async def test_stream_smoke_tests_function(self):
        """Test stream_smoke_tests convenience function"""
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.elapsed.total_seconds.return_value = 0.1

        with patch('httpx.AsyncClient') as mock_client:
            mock_client.return_value.__aenter__.return_value.get = AsyncMock(return_value=mock_response)
            mock_client.return_value.__aenter__.return_value.post = AsyncMock(return_value=mock_response)

            events = []
            async for event in stream_smoke_tests(backend="http"):
                events.append(event)
                if len(events) >= 3:  # Limit for test
                    break

            assert len(events) >= 3
            assert all(isinstance(event, dict) for event in events)


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
