repos:
  # Cursor rules enforcement (uses existing project script)
  - repo: local
    hooks:
      - id: cursor-rules-enforcement
        name: Cursor Rules Enforcement
        entry: python agent/scripts/cursor_rules_check.py
        language: system
        stages: [commit, push]
        always_run: true
        pass_filenames: false
        description: "Enforce cursor rules compliance before commit"

      - id: compose-blob-guard
        name: Compose Blob Guard
        entry: python tools/scripts/compose_blob_guard.py
        language: system
        stages: [commit, push]
        always_run: true
        pass_filenames: false
        description: "Block large inline blobs, base64, or certificates in compose files"

      - id: separation-guard
        name: Agent↔Project Separation Guard
        entry: python tools/scripts/separation_guard.py
        language: system
        stages: [commit, push]
        always_run: true
        pass_filenames: false
        description: "Detect forbidden writes to projects/ and host-execution patterns"

  # Python formatters/linters
  - repo: https://github.com/psf/black
    rev: 24.3.0
    hooks:
      - id: black
        language_version: python3

  - repo: https://github.com/PyCQA/isort
    rev: 5.13.2
    hooks:
      - id: isort
        name: isort (python)

  - repo: https://github.com/PyCQA/flake8
    rev: 7.0.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-bugbear]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.8.0
    hooks:
      - id: mypy
        additional_dependencies: [types-requests, types-PyYAML]

