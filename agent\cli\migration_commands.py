#!/usr/bin/env python3
"""
Database Migration CLI Commands
Provides CLI interface for database migration management with rollback capabilities
"""

import asyncio
import json
import logging
import subprocess
import sys
import time
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

from agent.core.validators.safety_validator import SafetyValidator
from agent.database.database_manager import DatabaseManager
from agent.core.site_container_manager import SiteContainerManager
from agent.core.db.migration_runner import MigrationRunner

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


logger = logging.getLogger(__name__)


class MigrationCommands:
    """CLI commands for database migration management"""

    def __init__(self, agent=None):
        self.agent = agent
        self.db_manager = DatabaseManager()
        self.safety_validator = SafetyValidator()
        self.container_manager = SiteContainerManager()

        # Migration configuration
        self.migrations_dir = Path("migrations")
        self.backups_dir = Path("backups")
        self.logs_dir = Path("logs")

        # Ensure directories exist
        self.migrations_dir.mkdir(exist_ok=True)
        self.backups_dir.mkdir(exist_ok=True)
        self.logs_dir.mkdir(exist_ok=True)

    async def migration_status(self, **kwargs) -> Dict[str, Any]:
        """Get migration status and health"""
        try:
            # Check database connection
            db_status = await self.db_manager.get_connection_status()

            # Get migration history
            migration_history = await self.db_manager.get_migration_history()

            # Get pending migrations
            pending_migrations = await self.db_manager.get_pending_migrations()

            # Get backup status
            backup_status = await self._get_backup_status()

            return {
                "success": True,
                "status": "healthy" if db_status else "unhealthy",
                "database_connected": db_status,
                "migration_history": migration_history,
                "pending_migrations": pending_migrations,
                "backup_status": backup_status,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        except Exception as e:
            logger.error(f"Error getting migration status: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def migration_summary(self, **kwargs) -> Dict[str, Any]:
        """Get migration summary and statistics"""
        try:
            # Get migration statistics
            stats = await self.db_manager.get_migration_statistics()

            # Get recent migrations
            recent_migrations = await self.db_manager.get_recent_migrations(limit=10)

            # Get backup statistics
            backup_stats = await self._get_backup_statistics()

            return {
                "success": True,
                "statistics": stats,
                "recent_migrations": recent_migrations,
                "backup_statistics": backup_stats,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        except Exception as e:
            logger.error(f"Error getting migration summary: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def list_migrations(self, **kwargs) -> Dict[str, Any]:
        """List all available migrations"""
        try:
            # Get all migrations
            all_migrations = await self.db_manager.get_all_migrations()

            # Get applied migrations
            applied_migrations = await self.db_manager.get_applied_migrations()

            # Get pending migrations
            pending_migrations = await self.db_manager.get_pending_migrations()

            return {
                "success": True,
                "all_migrations": all_migrations,
                "applied_migrations": applied_migrations,
                "pending_migrations": pending_migrations,
                "total_count": len(all_migrations),
                "applied_count": len(applied_migrations),
                "pending_count": len(pending_migrations),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        except Exception as e:
            logger.error(f"Error listing migrations: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def run_migration(
        self, migration_name: str = None, **kwargs
    ) -> Dict[str, Any]:
        """Run a specific migration or all pending migrations"""
        try:
            # Validate migration name if provided
            if migration_name and not self.safety_validator.validate_file_path(
                f"migrations/{migration_name}"
            ):
                return {
                    "success": False,
                    "error": f"Invalid migration name: {migration_name}",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            # Create backup before migration
            backup_result = await self._create_backup("pre_migration")
            if not backup_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to create backup: {backup_result['error']}",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            # Run migration
            if migration_name:
                result = await self.db_manager.run_migration(migration_name)
            else:
                result = await self.db_manager.run_pending_migrations()

            if result["success"]:
                # Log successful migration
                await self._log_migration(
                    "success", migration_name or "all_pending", result
                )

                return {
                    "success": True,
                    "migration_name": migration_name or "all_pending",
                    "backup_created": backup_result["backup_path"],
                    "result": result,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
            else:
                return {
                    "success": False,
                    "error": result["error"],
                    "backup_created": backup_result["backup_path"],
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

        except Exception as e:
            logger.error(f"Error running migration: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def rollback_migration(
        self, migration_name: str = None, **kwargs
    ) -> Dict[str, Any]:
        """Rollback a specific migration or the last migration"""
        try:
            # Validate migration name if provided
            if migration_name and not self.safety_validator.validate_file_path(
                f"migrations/{migration_name}"
            ):
                return {
                    "success": False,
                    "error": f"Invalid migration name: {migration_name}",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            # Create backup before rollback
            backup_result = await self._create_backup("pre_rollback")
            if not backup_result["success"]:
                return {
                    "success": False,
                    "error": f"Failed to create backup: {backup_result['error']}",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            # Run rollback
            if migration_name:
                result = await self.db_manager.rollback_migration(migration_name)
            else:
                result = await self.db_manager.rollback_last_migration()

            if result["success"]:
                # Log successful rollback
                await self._log_migration("rollback", migration_name or "last", result)

                return {
                    "success": True,
                    "migration_name": migration_name or "last",
                    "backup_created": backup_result["backup_path"],
                    "result": result,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
            else:
                return {
                    "success": False,
                    "error": result["error"],
                    "backup_created": backup_result["backup_path"],
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

        except Exception as e:
            logger.error(f"Error rolling back migration: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def validate_migration(self, migration_name: str, **kwargs) -> Dict[str, Any]:
        """Validate a migration before running it"""
        try:
            # Validate migration name
            if not self.safety_validator.validate_file_path(
                f"migrations/{migration_name}"
            ):
                return {
                    "success": False,
                    "error": f"Invalid migration name: {migration_name}",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            # Validate migration syntax
            syntax_valid = await self.db_manager.validate_migration_syntax(
                migration_name
            )

            # Check dependencies
            dependencies = await self.db_manager.check_migration_dependencies(
                migration_name
            )

            # Check for conflicts
            conflicts = await self.db_manager.check_migration_conflicts(migration_name)

            return {
                "success": True,
                "migration_name": migration_name,
                "syntax_valid": syntax_valid,
                "dependencies": dependencies,
                "conflicts": conflicts,
                "can_run": syntax_valid and not conflicts,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        except Exception as e:
            logger.error(f"Error validating migration: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def create_backup(self, backup_name: str = None, **kwargs) -> Dict[str, Any]:
        """Create a database backup"""
        try:
            if not backup_name:
                backup_name = (
                    f"manual_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
                )

            result = await self._create_backup(backup_name)
            return result
        except Exception as e:
            logger.error(f"Error creating backup: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def restore_backup(self, backup_name: str, **kwargs) -> Dict[str, Any]:
        """Restore from a database backup"""
        try:
            # Validate backup name
            if not self.safety_validator.validate_file_path(f"backups/{backup_name}"):
                return {
                    "success": False,
                    "error": f"Invalid backup name: {backup_name}",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            # Create backup before restore
            pre_restore_backup = await self._create_backup("pre_restore")

            # Restore from backup
            result = await self.db_manager.restore_from_backup(backup_name)

            if result["success"]:
                return {
                    "success": True,
                    "backup_name": backup_name,
                    "pre_restore_backup": pre_restore_backup["backup_path"],
                    "result": result,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
            else:
                return {
                    "success": False,
                    "error": result["error"],
                    "pre_restore_backup": pre_restore_backup["backup_path"],
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

        except Exception as e:
            logger.error(f"Error restoring backup: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def list_backups(self, **kwargs) -> Dict[str, Any]:
        """List all available backups"""
        try:
            backup_files = list(self.backups_dir.glob("*.sql"))
            backup_list = []

            for backup_file in backup_files:
                stat = backup_file.stat()
                backup_list.append(
                    {
                        "name": backup_file.name,
                        "size": stat.st_size,
                        "created": datetime.fromtimestamp(
                            stat.st_ctime, tz=timezone.utc
                        ).isoformat(),
                        "modified": datetime.fromtimestamp(
                            stat.st_mtime, tz=timezone.utc
                        ).isoformat(),
                    }
                )

            return {
                "success": True,
                "backups": backup_list,
                "total_count": len(backup_list),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        except Exception as e:
            logger.error(f"Error listing backups: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def migration_metrics(self, **kwargs) -> Dict[str, Any]:
        """Get migration performance metrics"""
        try:
            # Get migration performance data
            performance_data = await self.db_manager.get_migration_performance()

            # Get error rates
            error_rates = await self.db_manager.get_migration_error_rates()

            # Get rollback statistics
            rollback_stats = await self.db_manager.get_rollback_statistics()

            return {
                "success": True,
                "performance_data": performance_data,
                "error_rates": error_rates,
                "rollback_statistics": rollback_stats,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        except Exception as e:
            logger.error(f"Error getting migration metrics: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def export_migration_report(
        self, format: str = "json", **kwargs
    ) -> Dict[str, Any]:
        """Export migration report in specified format"""
        try:
            # Get comprehensive migration data
            migration_data = {
                "status": await self.migration_status(),
                "summary": await self.migration_summary(),
                "migrations": await self.list_migrations(),
                "backups": await self.list_backups(),
                "metrics": await self.migration_metrics(),
                "export_timestamp": datetime.now(timezone.utc).isoformat(),
            }

            # Export based on format
            if format.lower() == "json":
                report_path = (
                    self.logs_dir
                    / f"migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                )
                with open(report_path, "w") as f:
                    json.dump(migration_data, f, indent=2)
            else:
                return {
                    "success": False,
                    "error": f"Unsupported format: {format}",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            return {
                "success": True,
                "report_path": str(report_path),
                "format": format,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        except Exception as e:
            logger.error(f"Error exporting migration report: {e}")
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def _create_backup(self, backup_name: str) -> Dict[str, Any]:
        """Create a database backup"""
        try:
            backup_path = (
                self.backups_dir
                / f"{backup_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.sql"
            )
            result = await self.db_manager.create_backup(str(backup_path))

            if result["success"]:
                return {
                    "success": True,
                    "backup_path": str(backup_path),
                    "backup_size": (
                        backup_path.stat().st_size if backup_path.exists() else 0
                    ),
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
            else:
                return {
                    "success": False,
                    "error": result["error"],
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def _get_backup_status(self) -> Dict[str, Any]:
        """Get backup status information"""
        try:
            backup_files = list(self.backups_dir.glob("*.sql"))
            latest_backup = (
                max(backup_files, key=lambda x: x.stat().st_mtime)
                if backup_files
                else None
            )

            return {
                "total_backups": len(backup_files),
                "latest_backup": latest_backup.name if latest_backup else None,
                "latest_backup_time": (
                    datetime.fromtimestamp(
                        latest_backup.stat().st_mtime, tz=timezone.utc
                    ).isoformat()
                    if latest_backup
                    else None
                ),
                "backup_directory": str(self.backups_dir),
            }
        except Exception as e:
            return {"error": str(e)}

    async def _get_backup_statistics(self) -> Dict[str, Any]:
        """Get backup statistics"""
        try:
            backup_files = list(self.backups_dir.glob("*.sql"))
            total_size = sum(f.stat().st_size for f in backup_files)

            return {
                "total_backups": len(backup_files),
                "total_size_bytes": total_size,
                "total_size_mb": total_size / (1024 * 1024),
                "average_size_mb": (
                    (total_size / (1024 * 1024)) / len(backup_files)
                    if backup_files
                    else 0
                ),
            }
        except Exception as e:
            return {"error": str(e)}

    async def _log_migration(
        self, action: str, migration_name: str, result: Dict[str, Any]
    ):
        """Log migration action"""
        try:
            log_entry = {
                "action": action,
                "migration_name": migration_name,
                "result": result,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

            log_file = self.logs_dir / "migration_log.jsonl"
            with open(log_file, "a") as f:
                f.write(json.dumps(log_entry) + "\n")
        except Exception as e:
            logger.error(f"Error logging migration: {e}")

    # Container Migration Commands
    async def apply_container_migrations(
        self, site_name: str, migration_config: Dict[str, Any] = None, **kwargs
    ) -> Dict[str, Any]:
        """Apply database migrations within a site container"""
        try:
            if not site_name:
                return {
                    "success": False,
                    "error": "Site name is required",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            # Validate site name
            if not self.safety_validator.validate_file_path(site_name):
                return {
                    "success": False,
                    "error": f"Invalid site name: {site_name}",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            logger.info(f"Applying migrations for container site: {site_name}")

            # Apply migrations via container manager
            result = await self.container_manager.apply_database_migrations(
                site_name, migration_config
            )

            if result["success"]:
                # Log successful container migration
                await self._log_migration("container_migration", site_name, result)

            return {
                "success": result["success"],
                "site_name": site_name,
                "container_name": result.get("container_name"),
                "migration_config": result.get("migration_config"),
                "init_output": result.get("init_output"),
                "apply_output": result.get("apply_output"),
                "error": result.get("error"),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except Exception as e:
            logger.error(f"Error applying container migrations for {site_name}: {e}")
            return {
                "success": False,
                "error": str(e),
                "site_name": site_name,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def execute_container_command(
        self, site_name: str, command: List[str], working_dir: str = "/app", **kwargs
    ) -> Dict[str, Any]:
        """Execute a command within a site container"""
        try:
            if not site_name:
                return {
                    "success": False,
                    "error": "Site name is required",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            if not command:
                return {
                    "success": False,
                    "error": "Command is required",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            # Validate site name
            if not self.safety_validator.validate_file_path(site_name):
                return {
                    "success": False,
                    "error": f"Invalid site name: {site_name}",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            logger.info(f"Executing command in container {site_name}: {' '.join(command)}")

            # Execute command via container manager
            result = await self.container_manager.execute_command_in_container(
                site_name, command, working_dir
            )

            return {
                "success": result["success"],
                "site_name": site_name,
                "container_name": result.get("container_name"),
                "command": result.get("command"),
                "return_code": result.get("return_code"),
                "stdout": result.get("stdout"),
                "stderr": result.get("stderr"),
                "working_dir": working_dir,
                "error": result.get("error"),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except Exception as e:
            logger.error(f"Error executing command in container {site_name}: {e}")
            return {
                "success": False,
                "error": str(e),
                "site_name": site_name,
                "command": command,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

    async def init_container_migrations(
        self, site_name: str, database_url: str = None, **kwargs
    ) -> Dict[str, Any]:
        """Initialize migration environment for a site container"""
        try:
            if not site_name:
                return {
                    "success": False,
                    "error": "Site name is required",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            # Validate site name
            if not self.safety_validator.validate_file_path(site_name):
                return {
                    "success": False,
                    "error": f"Invalid site name: {site_name}",
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }

            # Default database URL if not provided
            if not database_url:
                database_url = f"sqlite:///app/data/{site_name}.db"

            logger.info(f"Initializing migrations for container site: {site_name}")

            # Create migration runner command
            init_command = [
                "python", "-c",
                f"""
import sys
sys.path.insert(0, '/app')
from agent.core.db.migration_runner import MigrationRunner

runner = MigrationRunner(
    project_name='{site_name}',
    database_url='{database_url}',
    projects_root='/app/projects'
)

result = runner.init_migrations()
print(f'Success: {{result.success}}')
print(f'Message: {{result.message}}')
if not result.success and result.error_analysis:
    print(f'Error: {{result.error_analysis.error_message}}')
"""
            ]

            # Execute initialization command
            result = await self.container_manager.execute_command_in_container(
                site_name, init_command, "/app"
            )

            return {
                "success": result["success"],
                "site_name": site_name,
                "database_url": database_url,
                "container_name": result.get("container_name"),
                "stdout": result.get("stdout"),
                "stderr": result.get("stderr"),
                "error": result.get("error"),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }

        except Exception as e:
            logger.error(f"Error initializing container migrations for {site_name}: {e}")
            return {
                "success": False,
                "error": str(e),
                "site_name": site_name,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
