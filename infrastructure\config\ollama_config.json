{"ollama_base_url": "http://localhost:11434", "timeout": 30, "default_model": "mistral:7b-instruct-q4_0", "models": {"deepseek-coder:1.3b": {"max_tokens": 4096, "temperature": 0.7, "top_p": 0.9, "enabled": true, "priority": 1, "description": "Fast code generation model with good performance"}, "yi-coder:1.5b": {"max_tokens": 4096, "temperature": 0.7, "top_p": 0.9, "enabled": true, "priority": 2, "description": "Balanced code generation model"}, "qwen2.5-coder:3b": {"max_tokens": 8192, "temperature": 0.7, "top_p": 0.9, "enabled": true, "priority": 3, "description": "High-quality code generation with larger context"}, "starcoder2:3b": {"max_tokens": 8192, "temperature": 0.7, "top_p": 0.9, "enabled": true, "priority": 4, "description": "Specialized code generation model"}, "mistral:7b-instruct-q4_0": {"max_tokens": 8192, "temperature": 0.7, "top_p": 0.9, "enabled": true, "priority": 5, "description": "Default model with good general capabilities"}}, "fallback_chain": ["mistral:7b-instruct-q4_0", "qwen2.5-coder:3b", "starcoder2:3b", "yi-coder:1.5b", "deepseek-coder:1.3b"], "learning_integration": {"enabled": true, "capture_patterns": true, "performance_tracking": true, "auto_optimization": true}, "performance_monitoring": {"enabled": true, "track_response_times": true, "track_success_rates": true, "track_error_counts": true, "optimization_threshold": 0.8}, "error_handling": {"max_retries": 3, "retry_delay": 1.0, "fallback_on_error": true, "log_errors": true}}