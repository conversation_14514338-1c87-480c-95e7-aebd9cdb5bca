networks:
  ai-coding-network:
    driver: bridge
# Secrets now managed via environment variables in .env.secrets
# See .env.secrets.example for template
services:
  api:
    build:
      context: ..
      dockerfile: containers/Dockerfile.api
    container_name: ai-coding-api
    depends_on:
      db:
        condition: service_healthy
      ollama:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - ../.env
      - ../.env.secrets
    environment:
      API_PORT: 8000
      CACHE_TTL: 3600
      DATABASE_URL: postgresql://${DB_USER:-ai_coding_user}:${DB_PASSWORD:-ai_coding_password}@db:5432/${DB_NAME:-ai_coding_agent}
      JWT_SECRET: ${JWT_SECRET}
      LOG_LEVEL: INFO
      OLLAMA_URL: http://host.docker.internal:11434
      PYTHONPATH: /app
      PYTHONUNBUFFERED: 1
      REDIS_URL: redis://redis:6379
      SECRET_KEY: ${SECRET_KEY}
      SUPABASE_ANON_KEY: ${SUPABASE_ANON_KEY}
      SUPABASE_URL: ${SUPABASE_URL}
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 60s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8000/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    ports: []
    restart: unless-stopped
    # Security options
    security_opt:
    - no-new-privileges:true
    read_only: false
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M

    volumes:
    - app_data:/app/data
    - app_logs:/app/logs
    - app_config:/app/config:ro
    - app_backups:/app/backups
    - app_sites:/app/sites
    - app_uploads:/app/uploads
    - app_ssl:/app/ssl
    - app_database:/app/database
    - app_test_reports:/app/test_reports
    - containers\extracted\docker-compose_api_ports.json:/app/config/ports.json:ro
  backup:
    build:
      context: ..
      dockerfile: containers/Dockerfile.backup
    container_name: ai-coding-backup
    depends_on:
      db:
        condition: service_healthy
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8086/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    restart: unless-stopped
    # Security options
    security_opt:
    - no-new-privileges:true
    read_only: false
    # Resource limits
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.25'
          memory: 256M
    volumes:
    - app_backups:/app/backups
  container_agent:
    build:
      context: ..
      dockerfile: containers/Dockerfile.container
    container_name: ai-coding-container-agent
    depends_on:
      api:
        condition: service_healthy
      nginx:
        condition: service_healthy
    environment:
      CONFIG_PATH: /app/config/container_agent_config.json
      DOCKER_HOST: unix:///var/run/docker.sock
      NETWORK_NAME: ai-coding-network
    healthcheck:
      interval: 30s
      retries: 3
      start_period: 40s
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8093/health
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    networks:
    - ai-coding-network
    restart: unless-stopped
    volumes:
    - /var/run/docker.sock:/var/run/docker.sock:ro
    - app_config:/app/config:roversion: '3.8'
volumes:
  app_backups:
    driver: local
  app_config:
    driver: local
  app_dashboard:
    driver: local
  app_data:
    driver: local
  app_database:
    driver: local
  app_fine_tuning:
    driver: local
  app_fine_tuning_data:
    driver: local
  app_learning:
    driver: local
  app_logs:
    driver: local
  app_sites:
    driver: local
  app_ssl:
    driver: local
  app_test_reports:
    driver: local
  app_uploads:
    driver: local
  app_validation_data:
    driver: local
  grafana_data:
    driver: local
  ollama_data:
    driver: local
  pgdata:
    driver: local
  prometheus_data:
    driver: local
  redis_data:
    driver: local
x-common-env:
  CACHE_TTL: 3600
  DATABASE_URL: postgresql://${DB_USER:-ai_coding_user}:${DB_PASSWORD:-ai_coding_password}@db:5432/${DB_NAME:-ai_coding_agent}
  LOG_LEVEL: INFO
  OLLAMA_URL: http://host.docker.internal:11434
  PYTHONPATH: /app
  PYTHONUNBUFFERED: 1
  REDIS_URL: redis://redis:6379
x-common-healthcheck:
  interval: 30s
  retries: 3
  start_period: 40s
  timeout: 10s
x-common-resources:
  limits:
    cpus: '1.0'
    memory: 1G
  reservations:
    cpus: '0.5'
    memory: 512M
x-common-volumes:
- app_data:/app/data
- app_logs:/app/logs
- app_config:/app/config:ro
- app_backups:/app/backups
x-security-config:
  cap_add:
  - NET_BIND_SERVICE
  cap_drop:
  - ALL
  read_only: true
  security_opt:
  - no-new-privileges:true
