#!/usr/bin/env python3
"""
TemplateManager - Comprehensive template and project structure management

Handles:
- Template loading and processing
- Project structure generation
- Template customization
- Directory creation
- File organization
- Git-based template management
"""

import json
import logging
import os
from datetime import datetime
from pathlib import Path
from string import Template
from typing import Any, Dict, List, Optional

try:
    import git

    GIT_AVAILABLE = True
except ImportError:
    GIT_AVAILABLE = False

logger = logging.getLogger(__name__)


class TemplateManager:
    """Comprehensive manager for templates and project structures"""

    def __init__(self, templates_dir: str = "templates"):
        """Initialize the TemplateManager"""
        self.templates_dir = Path(templates_dir)
        self.templates_dir.mkdir(exist_ok=True)
        self.template_config = self._load_template_config()
        logger.info("TemplateManager initialized successfully")

    def _load_template_config(self) -> Dict[str, Any]:
        """Load template configuration from JSON"""
        config_path = self.templates_dir / "templates.json"
        if not config_path.exists():
            return {}
        try:
            with open(config_path, "r") as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"Error loading template config: {e}")
            return {}

    def _save_template_config(self) -> None:
        """Save template configuration to JSON"""
        config_path = self.templates_dir / "templates.json"
        try:
            with open(config_path, "w") as f:
                json.dump(self.template_config, f, indent=2)
        except Exception as e:
            logger.error(f"Error saving template config: {e}")

    def create_project_structure(
        self, project_name: str, project_type: str = "nextjs"
    ) -> Dict[str, Any]:
        """
        Create a complete project structure

        Args:
            project_name: Name of the project
            project_type: Type of project (nextjs, react, vue, etc.)

        Returns:
            Dictionary with success status and created structure
        """
        try:
            project_path = Path(project_name)

            if project_path.exists():
                return {
                    "success": False,
                    "error": f"Project directory {project_name} already exists",
                }

            # Create project directory
            project_path.mkdir(parents=True, exist_ok=True)

            # Create structure based on project type
            if project_type == "nextjs":
                structure = self._create_nextjs_structure(project_path)
            elif project_type == "react":
                structure = self._create_react_structure(project_path)
            elif project_type == "vue":
                structure = self._create_vue_structure(project_path)
            else:
                structure = self._create_basic_structure(project_path)

            logger.info(f"Created project structure for {project_name}")
            return {
                "success": True,
                "project_path": str(project_path),
                "structure": structure,
                "message": f"Project structure created successfully",
            }

        except Exception as e:
            logger.error(f"Error creating project structure: {e}")
            return {"success": False, "error": str(e)}

    def _create_nextjs_structure(self, project_path: Path) -> Dict[str, Any]:
        """Create Next.js project structure"""
        structure = {"directories": [], "files": []}

        # Create directories
        directories = [
            "src/app",
            "src/components",
            "src/lib",
            "src/styles",
            "public",
            "components",
            "pages",
            "styles",
        ]

        for dir_name in directories:
            dir_path = project_path / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
            structure["directories"].append(str(dir_path))

        # Create basic files
        files = [
            ("package.json", '{"name": "nextjs-project", "version": "0.1.0"}'),
            ("next.config.js", "module.exports = {}"),
            ("README.md", f"# {project_path.name}\n\nNext.js project."),
        ]

        for file_name, content in files:
            file_path = project_path / file_name
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            structure["files"].append(str(file_path))

        return structure

    def _create_react_structure(self, project_path: Path) -> Dict[str, Any]:
        """Create React project structure"""
        structure = {"directories": [], "files": []}

        # Create directories
        directories = ["src", "public", "src/components", "src/pages", "src/styles"]

        for dir_name in directories:
            dir_path = project_path / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
            structure["directories"].append(str(dir_path))

        # Create basic files
        files = [
            ("package.json", '{"name": "react-project", "version": "0.1.0"}'),
            ("README.md", f"# {project_path.name}\n\nReact project."),
        ]

        for file_name, content in files:
            file_path = project_path / file_name
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            structure["files"].append(str(file_path))

        return structure

    def _create_vue_structure(self, project_path: Path) -> Dict[str, Any]:
        """Create Vue project structure"""
        structure = {"directories": [], "files": []}

        # Create directories
        directories = ["src", "public", "src/components", "src/views", "src/assets"]

        for dir_name in directories:
            dir_path = project_path / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
            structure["directories"].append(str(dir_path))

        # Create basic files
        files = [
            ("package.json", '{"name": "vue-project", "version": "0.1.0"}'),
            ("README.md", f"# {project_path.name}\n\nVue project."),
        ]

        for file_name, content in files:
            file_path = project_path / file_name
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            structure["files"].append(str(file_path))

        return structure

    def _create_basic_structure(self, project_path: Path) -> Dict[str, Any]:
        """Create basic project structure"""
        structure = {"directories": [], "files": []}

        # Create basic directories
        directories = ["src", "docs", "tests"]

        for dir_name in directories:
            dir_path = project_path / dir_name
            dir_path.mkdir(parents=True, exist_ok=True)
            structure["directories"].append(str(dir_path))

        # Create basic files
        files = [
            ("README.md", f"# {project_path.name}\n\nThis is a basic project."),
            (".gitignore", "node_modules/\n.env\n.DS_Store"),
        ]

        for file_name, content in files:
            file_path = project_path / file_name
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            structure["files"].append(str(file_path))

        return structure

    def load_template(self, template_name: str) -> Optional[str]:
        """
        Load a template by name

        Args:
            template_name: Name of the template to load

        Returns:
            Template content or None if not found
        """
        try:
            template_path = self.templates_dir / f"{template_name}.template"

            if not template_path.exists():
                logger.warning(f"Template {template_name} not found")
                return None

            with open(template_path, "r", encoding="utf-8") as f:
                return f.read()

        except Exception as e:
            logger.error(f"Error loading template {template_name}: {e}")
            return None

    def save_template(self, template_name: str, content: str) -> bool:
        """
        Save a template

        Args:
            template_name: Name of the template
            content: Template content

        Returns:
            True if template was saved successfully
        """
        try:
            template_path = self.templates_dir / f"{template_name}.template"

            with open(template_path, "w", encoding="utf-8") as f:
                f.write(content)

            logger.info(f"Saved template: {template_name}")
            return True

        except Exception as e:
            logger.error(f"Error saving template {template_name}: {e}")
            return False

    def process_template(self, template_content: str, variables: Dict[str, Any]) -> str:
        """
        Process a template with variables

        Args:
            template_content: Template content
            variables: Variables to substitute

        Returns:
            Processed template content
        """
        try:
            template = Template(template_content)
            return template.substitute(variables)

        except Exception as e:
            logger.error(f"Error processing template: {e}")
            return template_content

    def list_template_files(self) -> List[str]:
        """
        List all available template files

        Returns:
            List of template file names
        """
        try:
            templates = []
            for template_file in self.templates_dir.glob("*.template"):
                templates.append(template_file.stem)
            return templates

        except Exception as e:
            logger.error(f"Error listing template files: {e}")
            return []

    # Git-based template management methods
    def add_template(self, name: str, url: str, version: str = "main") -> None:
        """
        Add a new template from a git repository
        """
        if not GIT_AVAILABLE:
            raise ValueError("Git support not available. Install gitpython package.")

        try:
            template_path = self.templates_dir / name
            if template_path.exists():
                raise ValueError(f"Template '{name}' already exists")

            git.Repo.clone_from(url, template_path)
            self.template_config[name] = {
                "url": url,
                "version": version,
                "path": str(template_path),
                "last_updated": self._get_current_timestamp(),
            }
            self._save_template_config()
        except Exception as e:
            raise ValueError(f"Failed to add template: {str(e)}")

    def get_template(self, name: str) -> Dict[str, Any]:
        """Get template configuration"""
        if name not in self.template_config:
            raise ValueError(f"Template '{name}' not found")
        return self.template_config[name]

    def list_templates(self) -> Dict[str, Any]:
        """List all available templates"""
        return self.template_config

    def update_template(self, name: str) -> None:
        """Update a template to its latest version"""
        if not GIT_AVAILABLE:
            raise ValueError("Git support not available. Install gitpython package.")

        if name not in self.template_config:
            raise ValueError(f"Template '{name}' not found")

        template_path = Path(self.template_config[name]["path"])
        try:
            repo = git.Repo(template_path)
            repo.git.pull()
            self.template_config[name]["last_updated"] = self._get_current_timestamp()
            self._save_template_config()
        except Exception as e:
            raise ValueError(f"Failed to update template: {str(e)}")

    def _get_current_timestamp(self) -> str:
        """Get current timestamp in ISO format"""
        return datetime.now().isoformat()

    def validate_template(self, name: str) -> bool:
        """Validate template structure"""
        try:
            template_path = Path(self.template_config[name]["path"])
            required_files = ["index.html", "style.css", "script.js"]

            for file in required_files:
                if not (template_path / file).exists():
                    return False

            return True
        except Exception:
            return False


# Example usage
if __name__ == "__main__":
    manager = TemplateManager()
    try:
        # Add a template (example)
        if GIT_AVAILABLE:
            manager.add_template(
                "modern", "https://github.com/example/modern-template.git"
            )
    except Exception as e:
        print(f"Error: {e}")

        # List templates
        print("Available templates:", manager.list_templates())

    except ValueError as e:
        print(f"Error: {str(e)}")
