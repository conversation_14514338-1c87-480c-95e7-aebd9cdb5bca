#!/usr/bin/env python3
"""
Unit tests for the Enhanced Cursor Rules Auto-Fix System
Tests all major functionality including fix strategies, validation, and safety features
"""

import json
import os
import sys
import tempfile
import unittest
from pathlib import Path
from unittest.mock import MagicMock, Mock, call, patch

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from agent.scripts.enhanced_cursor_rules_fixer import (
    EnhancedCursorRulesMonitor,
    EnhancedViolationFixer,
)


class TestEnhancedViolationFixer(unittest.TestCase):
    """Test suite for EnhancedViolationFixer class"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.temp_dir, "test_config.json")

        # Create test configuration
        test_config = {
            "auto_fix": {
                "enabled": True,
                "strict_mode": True,
                "max_retries": 3,
                "cooldown_seconds": 60,
                "verify_fixes": True,
                "aggressive_mode": True,
                "backup_before_fix": False,  # Disabled - using Git for version control
            },
            "fixes": {
                "test_failures": {"enabled": True, "priority": "high"},
                "import_errors": {"enabled": True, "priority": "high"},
                "syntax_errors": {"enabled": True, "priority": "critical"},
                "todo_violations": {"enabled": True, "priority": "medium"},
                "documentation_missing": {"enabled": True, "priority": "medium"},
                "code_style": {"enabled": True, "priority": "low"},
            },
        }

        with open(self.config_path, "w") as f:
            json.dump(test_config, f)

        self.fixer = EnhancedViolationFixer(self.config_path)

    def tearDown(self):
        """Clean up test fixtures"""
        import shutil

        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_load_config(self):
        """Test configuration loading"""
        config = self.fixer.config
        self.assertIsInstance(config, dict)
        self.assertTrue(config["auto_fix"]["enabled"])
        self.assertEqual(config["auto_fix"]["max_retries"], 3)

    def test_deep_merge(self):
        """Test deep merge functionality"""
        default = {"a": 1, "b": {"c": 2, "d": 3}}
        user = {"b": {"d": 4, "e": 5}, "f": 6}
        result = self.fixer._deep_merge(default, user)

        expected = {"a": 1, "b": {"c": 2, "d": 4, "e": 5}, "f": 6}
        self.assertEqual(result, expected)

    def test_prioritize_violations(self):
        """Test violation prioritization"""
        violations = [
            "Code style violation: Line too long",
            "Test failures detected in test_api.py",
            "Import error: Module 'requests' not found",
            "TODO: Add error handling",
        ]

        prioritized = self.fixer._prioritize_violations(violations)

        # Critical violations should come first
        self.assertIn("Import error", prioritized[0])
        self.assertIn("Test failures", prioritized[1])

    @patch("subprocess.run")
    def test_install_package(self, mock_run):
        """Test package installation"""
        mock_run.return_value.returncode = 0

        result = self.fixer._install_package("requests")
        self.assertTrue(result)
        mock_run.assert_called_once()

    @patch("subprocess.run")
    def test_run_black_formatter(self, mock_run):
        """Test Black formatter execution"""
        mock_run.return_value.returncode = 0

        result = self.fixer._run_black_formatter()
        self.assertTrue(result)
        mock_run.assert_called_once()

    @patch("subprocess.run")
    def test_run_isort(self, mock_run):
        """Test isort execution"""
        mock_run.return_value.returncode = 0

        result = self.fixer._run_isort()
        self.assertTrue(result)
        mock_run.assert_called_once()

    def test_find_files_with_todos(self):
        """Test finding files with TODOs"""
        # Create a test file with TODOs
        test_file = os.path.join(self.temp_dir, "test_file.py")
        with open(test_file, "w") as f:
            f.write("# TODO: Add implementation\n")

        todo_files = self.fixer._find_files_with_todos()
        self.assertIn(test_file, todo_files)

    def test_convert_todos_to_implementations(self):
        """Test TODO conversion"""
        test_file = os.path.join(self.temp_dir, "test_file.py")
        with open(test_file, "w") as f:
            f.write("def test_function():\n    # TODO: Add implementation\n    pass\n")

        self.fixer._convert_todos_to_implementations(test_file, "TODO violation")

        with open(test_file, "r") as f:
            content = f.read()

        self.assertIn("DONE:", content)
        self.assertNotIn("TODO:", content)

    def test_add_missing_docstrings(self):
        """Test adding missing docstrings"""
        test_file = os.path.join(self.temp_dir, "test_file.py")
        with open(test_file, "w") as f:
            f.write("def test_function():\n    pass\n")

        result = self.fixer._add_missing_docstrings()
        self.assertTrue(result)

    def test_fix_test_failures(self):
        """Test test failure fixes"""
        violation = "Test failures detected in test_api.py"
        result = self.fixer._fix_test_failures(violation)
        self.assertIsInstance(result, bool)

    def test_fix_import_errors(self):
        """Test import error fixes"""
        violation = "Import error: Module 'requests' not found"
        result = self.fixer._fix_import_errors(violation)
        self.assertIsInstance(result, bool)

    def test_fix_syntax_errors(self):
        """Test syntax error fixes"""
        violation = "Syntax error: Invalid indentation in main.py"
        result = self.fixer._fix_syntax_errors(violation)
        self.assertIsInstance(result, bool)

    def test_fix_todo_violations(self):
        """Test TODO violation fixes"""
        violation = "TODO: Add error handling in database.py"
        result = self.fixer._fix_todo_violations(violation)
        self.assertIsInstance(result, bool)

    def test_fix_documentation_issues(self):
        """Test documentation issue fixes"""
        violation = "Missing docstring for function 'calculate_score'"
        result = self.fixer._fix_documentation_issues(violation)
        self.assertIsInstance(result, bool)

    def test_fix_code_style_issues(self):
        """Test code style issue fixes"""
        violation = "Code style violation: Line too long in utils.py"
        result = self.fixer._fix_code_style_issues(violation)
        self.assertIsInstance(result, bool)

    def test_should_skip_violation(self):
        """Test violation skip logic"""
        violation = "Test violation"

        # Should not skip initially
        self.assertFalse(self.fixer._should_skip_violation(violation))

        # Record multiple attempts
        for _ in range(5):
            self.fixer._record_fix_attempt(violation)

        # Should skip after max attempts
        self.assertTrue(self.fixer._should_skip_violation(violation))

    def test_verify_fix(self):
        """Test fix verification"""
        violation = "Test failures detected in test_api.py"
        result = self.fixer._verify_fix(violation)
        self.assertIsInstance(result, bool)

    def test_create_backup(self):
        """Test backup creation (disabled - using Git for version control)"""
        result = self.fixer._create_backup()
        # Backup creation should be disabled and return None
        self.assertIsNone(result)

    def test_fix_all_violations(self):
        """Test comprehensive violation fixing"""
        violations = [
            "Test failures detected in test_api.py",
            "Import error: Module 'requests' not found",
            "TODO: Add error handling in database.py",
        ]

        results = self.fixer.fix_all_violations(violations)
        self.assertIsInstance(results, dict)
        self.assertEqual(len(results), len(violations))

    @patch("subprocess.run")
    def test_install_test_dependencies(self, mock_run):
        """Test test dependency installation"""
        mock_run.return_value.returncode = 0

        result = self.fixer._install_test_dependencies()
        self.assertTrue(result)

    def test_fix_test_imports(self):
        """Test test import fixes"""
        result = self.fixer._fix_test_imports()
        self.assertIsInstance(result, bool)

    @patch("subprocess.run")
    def test_run_test_discovery(self, mock_run):
        """Test test discovery"""
        mock_run.return_value.returncode = 0

        result = self.fixer._run_test_discovery()
        self.assertTrue(result)

    def test_fix_test_syntax(self):
        """Test test syntax fixes"""
        result = self.fixer._fix_test_syntax()
        self.assertIsInstance(result, bool)

    def test_create_missing_test_files(self):
        """Test missing test file creation"""
        result = self.fixer._create_missing_test_files()
        self.assertIsInstance(result, bool)

    def test_install_missing_packages(self):
        """Test missing package installation"""
        result = self.fixer._install_missing_packages()
        self.assertIsInstance(result, bool)

    def test_fix_import_paths(self):
        """Test import path fixes"""
        result = self.fixer._fix_import_paths("requests")
        self.assertIsInstance(result, bool)

    def test_add_init_files(self):
        """Test init file creation"""
        result = self.fixer._add_init_files()
        self.assertIsInstance(result, bool)

    def test_fix_circular_imports(self):
        """Test circular import fixes"""
        result = self.fixer._fix_circular_imports("requests")
        self.assertIsInstance(result, bool)

    def test_convert_todos_to_done(self):
        """Test TODO to DONE conversion"""
        result = self.fixer._convert_todos_to_done()
        self.assertIsInstance(result, bool)

    def test_implement_todo_placeholders(self):
        """Test TODO placeholder implementation"""
        result = self.fixer._implement_todo_placeholders()
        self.assertIsInstance(result, bool)

    def test_add_todo_tracking(self):
        """Test TODO tracking addition"""
        result = self.fixer._add_todo_tracking()
        self.assertIsInstance(result, bool)

    def test_create_issue_for_complex_todos(self):
        """Test complex TODO issue creation"""
        result = self.fixer._create_issue_for_complex_todos()
        self.assertIsInstance(result, bool)


class TestEnhancedCursorRulesMonitor(unittest.TestCase):
    """Test suite for EnhancedCursorRulesMonitor class"""

    def setUp(self):
        """Set up test fixtures"""
        self.monitor = EnhancedCursorRulesMonitor()

    def test_init(self):
        """Test monitor initialization"""
        self.assertIsNotNone(self.monitor.violation_fixer)
        self.assertIsNotNone(self.monitor.logger)

    def test_take_automatic_action(self):
        """Test automatic action taking"""
        violations = [
            "Test failures detected in test_api.py",
            "Import error: Module 'requests' not found",
        ]

        # Should not raise any exceptions
        self.monitor._take_automatic_action(violations, 0.5)


class TestIntegration(unittest.TestCase):
    """Integration tests for the enhanced auto-fix system"""

    def setUp(self):
        """Set up integration test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.temp_dir, "integration_config.json")

        # Create integration test configuration
        test_config = {
            "auto_fix": {
                "enabled": True,
                "strict_mode": False,  # Less strict for integration tests
                "max_retries": 2,
                "cooldown_seconds": 10,
                "verify_fixes": True,
                "aggressive_mode": True,
                "backup_before_fix": False,  # Disable backup for tests
            }
        }

        with open(self.config_path, "w") as f:
            json.dump(test_config, f)

        self.fixer = EnhancedViolationFixer(self.config_path)

    def tearDown(self):
        """Clean up integration test fixtures"""
        import shutil

        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_end_to_end_fix_workflow(self):
        """Test complete end-to-end fix workflow"""
        # Create test files with violations
        test_file = os.path.join(self.temp_dir, "test_module.py")
        with open(test_file, "w") as f:
            f.write(
                """def test_function():
    # TODO: Add implementation
    pass

def another_function():
    pass  # Missing docstring
"""
            )

        violations = [
            "TODO: Add implementation in test_module.py",
            "Missing docstring for function 'another_function'",
        ]

        results = self.fixer.fix_all_violations(violations)

        self.assertIsInstance(results, dict)
        self.assertEqual(len(results), len(violations))

    def test_fix_prioritization_integration(self):
        """Test fix prioritization in integration"""
        violations = [
            "Code style violation: Line too long",
            "Test failures detected in test_api.py",
            "Import error: Module 'requests' not found",
            "TODO: Add error handling",
        ]

        prioritized = self.fixer._prioritize_violations(violations)

        # Should maintain all violations
        self.assertEqual(len(prioritized), len(violations))

        # Critical violations should be prioritized
        critical_keywords = ["import", "test", "syntax"]
        first_violation = prioritized[0].lower()
        self.assertTrue(
            any(keyword in first_violation for keyword in critical_keywords)
        )


if __name__ == "__main__":
    # Run tests with verbose output
    unittest.main(verbosity=2)
