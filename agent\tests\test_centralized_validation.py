"""
Unit tests for the new centralized validation system
"""

import tempfile
import unittest
from pathlib import Path
from typing import Any, Dict, Optional

import pytest

from agent.core.validation import BaseValidator, ValidationRegistry, ValidationResult


class ConcreteValidator(BaseValidator):
    def validate(self, data: Any) -> ValidationResult:
        if data is None:
            return ValidationResult(is_valid=False, errors=["Data cannot be None"])
        return ValidationResult(is_valid=True)


class TestBaseValidator(unittest.TestCase):
    def test_init(self):
        validator = ConcreteValidator("test")
        self.assertEqual(validator.name, "test")


class TestValidationRegistry(unittest.TestCase):
    def test_register_and_get(self):
        registry = ValidationRegistry()
        validator = ConcreteValidator("test")
        registry.register("test", validator)
        self.assertIs(registry.get("test"), validator)

    def test_validate(self):
        registry = ValidationRegistry()
        validator = ConcreteValidator("test")
        registry.register("test", validator)
        self.assertTrue(registry.validate("test", "data").is_valid)
        self.assertFalse(registry.validate("test", None).is_valid)

    def test_validate_unregistered(self):
        registry = ValidationRegistry()
        result = registry.validate("unregistered", "data")
        self.assertFalse(result.is_valid)
        self.assertIn("Validator 'unregistered' not found", result.errors)


if __name__ == "__main__":
    unittest.main()
