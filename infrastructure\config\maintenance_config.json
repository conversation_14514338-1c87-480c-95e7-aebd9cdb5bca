{"maintenance": {"base_url": "http://localhost:5000", "check_interval_hours": 24, "approval_required": true, "notification_email": null, "auto_approve_low_priority": true, "max_concurrent_tasks": 5, "task_timeout_minutes": 60}, "link_checker": {"enabled": true, "max_workers": 10, "timeout": 30, "retry_attempts": 3, "check_interval_hours": 6, "check_external_links": true, "check_internal_links": true, "check_media_links": true, "exclude_patterns": ["javascript:", "mailto:", "tel:", "#", "data:"], "user_agent": "AI-Coding-Agent-Maintenance-Engine/1.0", "follow_redirects": true, "max_redirects": 5, "respect_robots_txt": true, "rate_limit_requests_per_second": 2}, "dependency_manager": {"enabled": true, "check_interval_hours": 24, "auto_update_patch": true, "auto_update_minor": false, "auto_update_major": false, "security_updates_only": false, "check_python_dependencies": true, "check_node_dependencies": false, "exclude_packages": ["pip", "setuptools", "wheel"], "include_dev_dependencies": false, "backup_before_update": true, "rollback_on_failure": true, "test_after_update": true}, "content_audit": {"enabled": true, "check_interval_hours": 12, "audit_content_quality": true, "check_seo_optimization": true, "validate_html": true, "check_accessibility": true, "check_performance": true, "generate_audit_report": true, "auto_fix_issues": false}, "backup_system": {"enabled": true, "backup_interval_hours": 24, "retention_days": 30, "backup_content": true, "backup_database": true, "backup_configuration": true, "backup_media": true, "compression_enabled": true, "encryption_enabled": false, "backup_location": "backups/", "max_backup_size_mb": 1000}, "notification_system": {"enabled": true, "email_notifications": false, "webhook_notifications": false, "slack_notifications": false, "notification_levels": ["critical", "error", "warning", "info"], "notification_channels": {"critical": ["email", "webhook"], "error": ["email", "webhook"], "warning": ["webhook"], "info": ["webhook"]}, "email_settings": {"smtp_server": "", "smtp_port": 587, "username": "", "password": "", "use_tls": true, "from_address": "", "to_addresses": []}, "webhook_settings": {"url": "", "headers": {}, "timeout": 30}}, "logging": {"retention_days": 30, "log_level": "INFO", "log_format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "log_file": "logs/maintenance_engine.log", "max_log_size_mb": 10, "backup_count": 5, "console_logging": true, "file_logging": true}, "security": {"scan_for_vulnerabilities": true, "check_dependency_vulnerabilities": true, "monitor_file_changes": true, "detect_unauthorized_access": true, "security_scan_interval_hours": 12, "vulnerability_reporting": true, "auto_block_suspicious_ips": false, "security_notification_level": "warning"}, "performance_monitoring": {"enabled": true, "monitor_response_times": true, "monitor_resource_usage": true, "monitor_error_rates": true, "performance_thresholds": {"max_response_time_ms": 5000, "max_cpu_usage_percent": 80, "max_memory_usage_percent": 85, "max_disk_usage_percent": 90, "max_error_rate_percent": 5}, "alert_on_threshold_exceeded": true, "performance_reporting": true}, "integration": {"cms_integration": {"enabled": true, "check_content_links": true, "audit_content_quality": true, "monitor_content_changes": true, "auto_optimize_content": false}, "deployment_integration": {"enabled": true, "pre_deployment_checks": true, "post_deployment_verification": true, "rollback_on_failure": true, "deployment_notifications": true}, "database_integration": {"enabled": true, "backup_database": true, "optimize_database": true, "monitor_database_performance": true, "cleanup_old_data": true}}, "workflow": {"approval_workflow": {"enabled": true, "require_approval_for": ["major_dependency_updates", "security_changes", "configuration_changes", "backup_restoration"], "auto_approve_for": ["patch_updates", "link_checks", "performance_monitoring"], "approval_timeout_hours": 24, "escalation_after_hours": 48}, "scheduling": {"use_cron_scheduling": false, "timezone": "UTC", "business_hours_only": false, "business_hours": {"start": "09:00", "end": "17:00", "timezone": "UTC"}, "maintenance_windows": [{"day": "sunday", "start": "02:00", "end": "06:00", "timezone": "UTC"}]}}, "reporting": {"generate_reports": true, "report_schedule": {"daily": true, "weekly": true, "monthly": true}, "report_formats": ["json", "html", "pdf"], "report_retention_days": 90, "include_metrics": true, "include_recommendations": true, "auto_send_reports": false}}