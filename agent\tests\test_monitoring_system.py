# tests/test_monitoring_system.py
"""
Comprehensive tests for monitoring system components.
Tests metrics collection, health checks, alerting, and dashboard functionality.
"""

import asyncio
import json
import pytest
import tempfile
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

from agent.monitoring.container_metrics_collector import ContainerMetricsCollector, ContainerMetrics
from agent.monitoring.health_check_system import HealthCheckSystem, HealthStatus, HealthCheckResult
from agent.monitoring.alerting_system import AlertingSystem, Alert, AlertSeverity, AlertType, MonitoringManager
from agent.monitoring.monitoring_dashboard import MonitoringDashboard


class TestContainerMetricsCollector:
    """Test suite for ContainerMetricsCollector"""

    @pytest.fixture
    def collector(self):
        """Create ContainerMetricsCollector instance for testing"""
        return ContainerMetricsCollector(collection_interval=1, history_size=100)

    @pytest.fixture
    def mock_container(self):
        """Create mock Docker container"""
        container = MagicMock()
        container.name = "site-test-container"
        container.id = "abc123def456"
        container.status = "running"
        container.stats.return_value = {
            "cpu_stats": {
                "cpu_usage": {"total_usage": **********},
                "system_cpu_usage": **********0,
                "cpu_usage": {"percpu_usage": [500000000, 500000000]}
            },
            "precpu_stats": {
                "cpu_usage": {"total_usage": 900000000},
                "system_cpu_usage": 9000000000
            },
            "memory_stats": {
                "usage": 536870912,  # 512MB
                "limit": 1073741824  # 1GB
            },
            "networks": {
                "eth0": {"rx_bytes": 1024000, "tx_bytes": 2048000}
            },
            "blkio_stats": {
                "io_service_bytes_recursive": [
                    {"op": "Read", "value": 4096000},
                    {"op": "Write", "value": 8192000}
                ]
            }
        }
        container.attrs = {
            "State": {"StartedAt": "2024-01-01T12:00:00Z"},
            "RestartCount": 0
        }
        return container

    @pytest.mark.asyncio
    async def test_metrics_collection(self, collector, mock_container):
        """Test container metrics collection"""
        with patch('docker.from_env') as mock_docker:
            mock_client = MagicMock()
            mock_docker.return_value = mock_client
            mock_client.containers.list.return_value = [mock_container]
            
            metrics = await collector.collect_all_metrics()
            
            assert len(metrics) == 1
            assert "site-test-container" in metrics
            
            metric = metrics["site-test-container"]
            assert isinstance(metric, ContainerMetrics)
            assert metric.container_name == "site-test-container"
            assert metric.cpu_usage_percent >= 0
            assert metric.memory_usage_mb > 0
            assert metric.memory_usage_percent > 0

    @pytest.mark.asyncio
    async def test_cpu_calculation(self, collector):
        """Test CPU usage calculation"""
        stats = {
            "cpu_stats": {
                "cpu_usage": {"total_usage": 2000000000},
                "system_cpu_usage": 20000000000,
                "cpu_usage": {"percpu_usage": [**********, **********]}
            },
            "precpu_stats": {
                "cpu_usage": {"total_usage": **********},
                "system_cpu_usage": **********0
            }
        }
        
        cpu_usage = collector._calculate_cpu_usage(stats)
        assert cpu_usage == 20.0  # (********** / **********0) * 2 * 100

    @pytest.mark.asyncio
    async def test_metrics_history(self, collector, mock_container):
        """Test metrics history management"""
        with patch('docker.from_env') as mock_docker:
            mock_client = MagicMock()
            mock_docker.return_value = mock_client
            mock_client.containers.list.return_value = [mock_container]
            
            # Collect metrics multiple times
            for _ in range(5):
                await collector.collect_all_metrics()
            
            history = collector.get_container_metrics("site-test-container")
            assert history is not None
            assert len(history.metrics) == 5

    @pytest.mark.asyncio
    async def test_performance_summary(self, collector, mock_container):
        """Test performance summary generation"""
        with patch('docker.from_env') as mock_docker:
            mock_client = MagicMock()
            mock_docker.return_value = mock_client
            mock_client.containers.list.return_value = [mock_container]
            
            await collector.collect_all_metrics()
            
            summary = collector.get_performance_summary("site-test-container", minutes=60)
            
            assert "container_name" in summary
            assert "current_cpu_percent" in summary
            assert "current_memory_percent" in summary
            assert "average_cpu_percent" in summary


class TestHealthCheckSystem:
    """Test suite for HealthCheckSystem"""

    @pytest.fixture
    def health_system(self):
        """Create HealthCheckSystem instance for testing"""
        return HealthCheckSystem(check_interval=1)

    @pytest.fixture
    def mock_container(self):
        """Create mock Docker container for health checks"""
        container = MagicMock()
        container.name = "site-health-test"
        container.id = "health123"
        container.status = "running"
        container.attrs = {
            "NetworkSettings": {
                "Ports": {"80/tcp": [{"HostPort": "8080"}]}
            }
        }
        return container

    @pytest.mark.asyncio
    async def test_health_check_configuration(self, health_system, mock_container):
        """Test health check auto-configuration"""
        with patch('docker.from_env') as mock_docker:
            mock_client = MagicMock()
            mock_docker.return_value = mock_client
            mock_client.containers.list.return_value = [mock_container]
            
            await health_system._auto_configure_containers()
            
            assert "site-health-test" in health_system.health_configs
            config = health_system.health_configs["site-health-test"]
            assert config.container_name == "site-health-test"
            assert config.port == 8080

    @pytest.mark.asyncio
    async def test_health_check_execution(self, health_system):
        """Test health check execution"""
        from agent.monitoring.health_check_system import HealthCheckConfig
        
        config = HealthCheckConfig(
            container_name="test-container",
            endpoint="/",
            port=8080,
            timeout_seconds=5
        )
        
        with patch('docker.from_env') as mock_docker, \
             patch('aiohttp.ClientSession.get') as mock_get:
            
            # Mock Docker container
            mock_container = MagicMock()
            mock_container.id = "test123"
            mock_container.status = "running"
            mock_client = MagicMock()
            mock_client.containers.get.return_value = mock_container
            mock_docker.return_value = mock_client
            
            # Mock HTTP response
            mock_response = AsyncMock()
            mock_response.status = 200
            mock_response.text.return_value = "OK"
            mock_response.headers = {"content-type": "text/html"}
            mock_get.return_value.__aenter__.return_value = mock_response
            
            result = await health_system._perform_health_check(config)
            
            assert isinstance(result, HealthCheckResult)
            assert result.status == HealthStatus.HEALTHY
            assert result.status_code == 200
            assert result.response_time_ms > 0

    @pytest.mark.asyncio
    async def test_health_check_failure(self, health_system):
        """Test health check failure handling"""
        from agent.monitoring.health_check_system import HealthCheckConfig
        
        config = HealthCheckConfig(
            container_name="failing-container",
            endpoint="/",
            port=8080,
            timeout_seconds=1
        )
        
        with patch('docker.from_env') as mock_docker, \
             patch('aiohttp.ClientSession.get') as mock_get:
            
            # Mock Docker container
            mock_container = MagicMock()
            mock_container.id = "fail123"
            mock_container.status = "running"
            mock_client = MagicMock()
            mock_client.containers.get.return_value = mock_container
            mock_docker.return_value = mock_client
            
            # Mock timeout
            mock_get.side_effect = asyncio.TimeoutError()
            
            result = await health_system._perform_health_check(config)
            
            assert result.status == HealthStatus.UNHEALTHY
            assert "timeout" in result.error_message.lower()

    @pytest.mark.asyncio
    async def test_health_summary(self, health_system):
        """Test health summary generation"""
        # Add mock health results
        container_name = "summary-test"
        health_system.health_history[container_name] = []
        
        # Add some health results
        for i in range(10):
            result = HealthCheckResult(
                container_name=container_name,
                container_id="sum123",
                status=HealthStatus.HEALTHY if i < 8 else HealthStatus.UNHEALTHY,
                response_time_ms=100.0 + i * 10,
                timestamp=datetime.now() - timedelta(minutes=i),
                endpoint="/"
            )
            health_system.health_history[container_name].append(result)
        
        summary = health_system.get_health_summary(container_name, minutes=60)
        
        assert summary["container_name"] == container_name
        assert summary["health_percentage"] == 80.0  # 8 out of 10 healthy
        assert summary["total_checks"] == 10


class TestAlertingSystem:
    """Test suite for AlertingSystem"""

    @pytest.fixture
    def alerting_system(self):
        """Create AlertingSystem instance for testing"""
        return AlertingSystem(check_interval=1)

    @pytest.fixture
    def mock_metrics(self):
        """Create mock container metrics"""
        return {
            "site-alert-test": ContainerMetrics(
                container_id="alert123",
                container_name="site-alert-test",
                timestamp=datetime.now(),
                cpu_usage_percent=85.0,  # High CPU
                memory_usage_mb=400.0,
                memory_limit_mb=512.0,
                memory_usage_percent=78.1,
                network_rx_bytes=1024000,
                network_tx_bytes=2048000,
                disk_read_bytes=4096000,
                disk_write_bytes=8192000,
                status="running",
                health_status="healthy",
                uptime_seconds=3600,
                restart_count=0
            )
        }

    @pytest.mark.asyncio
    async def test_alert_rule_evaluation(self, alerting_system, mock_metrics):
        """Test alert rule evaluation"""
        # Get high CPU rule
        high_cpu_rule = next(
            rule for rule in alerting_system.alert_rules 
            if rule.alert_type == AlertType.HIGH_CPU_USAGE and rule.threshold_value == 80.0
        )
        
        metric = mock_metrics["site-alert-test"]
        health = {"status": "healthy"}
        
        # Should trigger alert (CPU is 85%, threshold is 80%)
        result = await alerting_system._evaluate_rule_condition(high_cpu_rule, metric, health)
        assert result is True

    @pytest.mark.asyncio
    async def test_alert_generation(self, alerting_system, mock_metrics):
        """Test alert generation and management"""
        with patch.object(alerting_system.metrics_collector, 'get_all_current_metrics') as mock_get_metrics, \
             patch.object(alerting_system.health_system, 'get_all_health_status') as mock_get_health:
            
            mock_get_metrics.return_value = mock_metrics
            mock_get_health.return_value = {"site-alert-test": {"status": "healthy"}}
            
            await alerting_system._check_alerts()
            
            # Should have generated high CPU alert
            active_alerts = alerting_system.get_active_alerts()
            assert len(active_alerts) > 0
            
            cpu_alerts = [a for a in active_alerts if a.alert_type == AlertType.HIGH_CPU_USAGE]
            assert len(cpu_alerts) > 0
            assert cpu_alerts[0].container_name == "site-alert-test"

    @pytest.mark.asyncio
    async def test_alert_resolution(self, alerting_system):
        """Test alert resolution when conditions improve"""
        # Create an active alert
        alert = Alert(
            id="test-alert-123",
            alert_type=AlertType.HIGH_CPU_USAGE,
            severity=AlertSeverity.MEDIUM,
            container_name="site-resolve-test",
            message="High CPU usage",
            timestamp=datetime.now()
        )
        
        alert_key = "High CPU Usage:site-resolve-test"
        alerting_system.active_alerts[alert_key] = alert
        
        # Mock improved conditions
        with patch.object(alerting_system.metrics_collector, 'get_all_current_metrics') as mock_get_metrics, \
             patch.object(alerting_system.health_system, 'get_all_health_status') as mock_get_health:
            
            # Low CPU usage now
            improved_metrics = {
                "site-resolve-test": ContainerMetrics(
                    container_id="resolve123",
                    container_name="site-resolve-test",
                    timestamp=datetime.now(),
                    cpu_usage_percent=30.0,  # Low CPU
                    memory_usage_mb=200.0,
                    memory_limit_mb=512.0,
                    memory_usage_percent=39.1,
                    network_rx_bytes=1024000,
                    network_tx_bytes=2048000,
                    disk_read_bytes=4096000,
                    disk_write_bytes=8192000,
                    status="running",
                    health_status="healthy",
                    uptime_seconds=3600,
                    restart_count=0
                )
            }
            
            mock_get_metrics.return_value = improved_metrics
            mock_get_health.return_value = {"site-resolve-test": {"status": "healthy"}}
            
            await alerting_system._check_alerts()
            
            # Alert should be resolved
            assert alert_key not in alerting_system.active_alerts
            assert alert.resolved is True

    def test_alert_summary(self, alerting_system):
        """Test alert summary generation"""
        # Add some mock alerts
        alerts = [
            Alert("1", AlertType.HIGH_CPU_USAGE, AlertSeverity.CRITICAL, "test1", "msg1", datetime.now()),
            Alert("2", AlertType.HIGH_MEMORY_USAGE, AlertSeverity.HIGH, "test2", "msg2", datetime.now()),
            Alert("3", AlertType.HEALTH_CHECK_FAILED, AlertSeverity.MEDIUM, "test3", "msg3", datetime.now())
        ]
        
        for i, alert in enumerate(alerts):
            alerting_system.active_alerts[f"alert-{i}"] = alert
        
        summary = alerting_system.get_alert_summary()
        
        assert summary["total_active_alerts"] == 3
        assert summary["critical_alerts"] == 1
        assert summary["high_alerts"] == 1
        assert summary["medium_alerts"] == 1


class TestMonitoringDashboard:
    """Test suite for MonitoringDashboard"""

    @pytest.fixture
    def dashboard(self):
        """Create MonitoringDashboard instance for testing"""
        return MonitoringDashboard(port=8091, host="localhost")

    @pytest.mark.asyncio
    async def test_dashboard_initialization(self, dashboard):
        """Test dashboard initialization"""
        assert dashboard.port == 8091
        assert dashboard.host == "localhost"
        assert dashboard.app is not None
        assert dashboard.is_running is False

    @pytest.mark.asyncio
    async def test_api_endpoints(self, dashboard):
        """Test dashboard API endpoints"""
        # Mock metrics and health data
        mock_metrics = {
            "site-dashboard-test": ContainerMetrics(
                container_id="dash123",
                container_name="site-dashboard-test",
                timestamp=datetime.now(),
                cpu_usage_percent=45.0,
                memory_usage_mb=256.0,
                memory_limit_mb=512.0,
                memory_usage_percent=50.0,
                network_rx_bytes=1024000,
                network_tx_bytes=2048000,
                disk_read_bytes=4096000,
                disk_write_bytes=8192000,
                status="running",
                health_status="healthy",
                uptime_seconds=3600,
                restart_count=0
            )
        }
        
        with patch.object(dashboard.metrics_collector, 'get_all_current_metrics') as mock_get_metrics:
            mock_get_metrics.return_value = mock_metrics
            
            # Test metrics API
            from aiohttp.test_utils import make_mocked_request
            request = make_mocked_request('GET', '/api/metrics')
            
            response = await dashboard._api_get_metrics(request)
            assert response.status == 200


class TestMonitoringManager:
    """Test suite for MonitoringManager"""

    @pytest.fixture
    def monitoring_manager(self):
        """Create MonitoringManager instance for testing"""
        return MonitoringManager(dashboard_port=8092)

    @pytest.mark.asyncio
    async def test_monitoring_manager_initialization(self, monitoring_manager):
        """Test monitoring manager initialization"""
        assert monitoring_manager.metrics_collector is not None
        assert monitoring_manager.health_system is not None
        assert monitoring_manager.alerting_system is not None
        assert monitoring_manager.dashboard is not None
        assert monitoring_manager.is_running is False

    def test_monitoring_status(self, monitoring_manager):
        """Test monitoring status reporting"""
        status = monitoring_manager.get_monitoring_status()
        
        assert "monitoring_active" in status
        assert "metrics_collector" in status
        assert "health_system" in status
        assert "alerting_system" in status
        assert "dashboard" in status


@pytest.mark.integration
class TestMonitoringIntegration:
    """Integration tests for monitoring system"""

    @pytest.mark.asyncio
    async def test_end_to_end_monitoring(self):
        """Test complete monitoring workflow"""
        manager = MonitoringManager(dashboard_port=8093)
        
        # Test initialization
        assert manager.is_running is False
        
        # Test status reporting
        status = manager.get_monitoring_status()
        assert status["monitoring_active"] is False

    @pytest.mark.asyncio
    async def test_monitoring_with_mock_containers(self):
        """Test monitoring with mock container data"""
        collector = ContainerMetricsCollector(collection_interval=1)
        
        with patch('docker.from_env') as mock_docker:
            mock_container = MagicMock()
            mock_container.name = "site-integration-test"
            mock_container.id = "int123"
            mock_container.status = "running"
            mock_container.stats.return_value = {
                "cpu_stats": {"cpu_usage": {"total_usage": **********}, "system_cpu_usage": **********0, "cpu_usage": {"percpu_usage": [500000000, 500000000]}},
                "precpu_stats": {"cpu_usage": {"total_usage": 900000000}, "system_cpu_usage": 9000000000},
                "memory_stats": {"usage": 268435456, "limit": 536870912},
                "networks": {"eth0": {"rx_bytes": 1024, "tx_bytes": 2048}},
                "blkio_stats": {"io_service_bytes_recursive": []}
            }
            mock_container.attrs = {"State": {"StartedAt": "2024-01-01T12:00:00Z"}, "RestartCount": 0}
            
            mock_client = MagicMock()
            mock_client.containers.list.return_value = [mock_container]
            mock_docker.return_value = mock_client
            
            metrics = await collector.collect_all_metrics()
            assert len(metrics) == 1
            assert "site-integration-test" in metrics


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
