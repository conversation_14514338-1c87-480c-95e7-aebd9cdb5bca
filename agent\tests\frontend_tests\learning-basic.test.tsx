/**
 * Basic Learning Integration Test
 * Simple test to verify learning components can be imported and rendered
 */

import React from 'react';
import { render, screen } from '@testing-library/react';

// Mock the learning store
jest.mock('../store/learningStore', () => ({
  useLearningStore: jest.fn(() => ({
    status: null,
    summary: null,
    recommendations: [],
    loading: false,
    error: null,
    lastUpdate: null,
    refreshAll: jest.fn(),
    refreshStatus: jest.fn(),
    refreshSummary: jest.fn(),
    refreshRecommendations: jest.fn(),
    learnCodePattern: jest.fn(),
    learnUserPreference: jest.fn(),
    recordLearningEvent: jest.fn(),
    learnPerformanceInsight: jest.fn(),
    runEnhancementCycle: jest.fn(),
    enableEnhancement: jest.fn(),
    disableEnhancement: jest.fn(),
    getAnalytics: jest.fn(() => ({
      totalPatterns: 0,
      userPreferences: 0,
      performanceInsights: 0,
      recommendationsGenerated: 0,
      systemHealth: 'unknown',
      activeModels: 0,
      accuracy: 0,
      responseTime: 0,
      userSatisfaction: 0,
      highPriorityRecommendations: 0,
      mediumPriorityRecommendations: 0,
      lowPriorityRecommendations: 0,
    })),
  })),
}));

describe('Learning Integration - Basic Tests', () => {
  it('should have Jest working', () => {
    expect(true).toBe(true);
  });

  it('should have React Testing Library available', () => {
    const TestComponent = () => <div>Test Component</div>;
    render(<TestComponent />);
    expect(screen.getByText('Test Component')).toBeInTheDocument();
  });

  it('should be able to import learning components', () => {
    // This test verifies that the components can be imported without errors
    expect(() => {
      require('../components/learning/LearningDashboard');
      require('../components/learning/LearningCard');
      require('../components/learning/LearningAnalytics');
      require('../components/learning/LearningStatus');
      require('../components/learning/LearningRecommendations');
    }).not.toThrow();
  });

  it('should be able to import learning services', () => {
    // This test verifies that the services can be imported without errors
    expect(() => {
      require('../services/learningService');
      require('../store/learningStore');
      require('../hooks/useLearning');
    }).not.toThrow();
  });
});
