#!/usr/bin/env python3
"""
Test script for the rollback test functionality
"""

import asyncio
import json
import sys
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agent.core.home_server import HomeServer, RollbackManager


async def test_rollback_test_manager():
    """Test the RollbackTestManager directly"""
    print("🧪 Testing RollbackTestManager")
    print("=" * 50)

    try:
        # Initialize the test manager
        test_manager = RollbackManager()
        print("✅ RollbackTestManager initialized")

        # Test finding previous deployment
        previous_deployment = test_manager._find_previous_deployment()
        if previous_deployment:
            print(f"✅ Found previous deployment: {previous_deployment['site']}")
            print(f"   Timestamp: {previous_deployment['timestamp']}")
            print(f"   Path: {previous_deployment['deployment_path']}")
        else:
            print("⚠️  No previous deployment found")

        # Test finding available port
        port = test_manager._find_available_port()
        if port:
            print(f"✅ Found available port: {port}")
        else:
            print("❌ No available ports found")

        # Test test history
        history = test_manager.get_test_history(limit=5)
        print(f"✅ Test history retrieved: {len(history)} results")

        print("✅ RollbackTestManager tests completed")
        return True

    except Exception as e:
        print(f"❌ RollbackTestManager test failed: {e}")
        return False


async def test_pipeline_integration():
    """Test the pipeline integration"""
    print("\n🧪 Testing Pipeline Integration")
    print("=" * 50)

    try:
        # Initialize the pipeline
        pipeline = HomeServerPipeline()
        print("✅ Pipeline initialized")

        # Test rollback test history
        history = pipeline.get_rollback_test_history(limit=5)
        print(f"✅ Rollback test history: {len(history)} results")

        # Test specific deployment testing (if we have deployments)
        deployments = pipeline.list_deployments(limit=1)
        if deployments:
            deployment_id = deployments[0].get("deployment_id", "test-site")
            print(f"🧪 Testing specific deployment: {deployment_id}")

            # Note: This might fail if the deployment doesn't have a proper server setup
            # That's expected for this test
            try:
                result = await pipeline.test_specific_deployment(deployment_id)
                print(f"   Result: {result.get('overall_status', 'unknown')}")
                if result.get("error"):
                    print(f"   Error: {result['error']}")
            except Exception as e:
                print(f"   Expected error (deployment may not be server-ready): {e}")
        else:
            print("⚠️  No deployments found for testing")

        print("✅ Pipeline integration tests completed")
        return True

    except Exception as e:
        print(f"❌ Pipeline integration test failed: {e}")
        return False


async def test_nightly_rollback_test():
    """Test the nightly rollback test (without actually running it)"""
    print("\n🧪 Testing Nightly Rollback Test Setup")
    print("=" * 50)

    try:
        # Test that the script can be imported
        from agent.scripts.nightly_rollback_test import (
            run_nightly_rollback_test,
            setup_logging,
        )

        print("✅ Nightly rollback test script imported successfully")

        # Test logging setup
        logger = setup_logging()
        logger.info("Test log message")
        print("✅ Logging setup works")

        # Test that the pipeline can be initialized
        pipeline = HomeServerPipeline()
        print("✅ Pipeline can be initialized for nightly test")

        print("✅ Nightly rollback test setup completed")
        return True

    except Exception as e:
        print(f"❌ Nightly rollback test setup failed: {e}")
        return False


async def test_cli_commands():
    """Test CLI commands"""
    print("\n🧪 Testing CLI Commands")
    print("=" * 50)

    try:
        # Test that the pipeline CLI can be imported and has the new commands
        # Check if the script can be run with help
        import subprocess

        from home_server import main

        result = subprocess.run(
            [sys.executable, "src/home_server_pipeline.py", "--help"],
            capture_output=True,
            text=True,
        )

        if result.returncode == 0:
            print("✅ CLI help command works")

            # Check if new commands are in help
            help_text = result.stdout
            if "rollback-test" in help_text:
                print("✅ rollback-test command available")
            else:
                print("❌ rollback-test command not found in help")

            if "test-deployment" in help_text:
                print("✅ test-deployment command available")
            else:
                print("❌ test-deployment command not found in help")
        else:
            print(f"❌ CLI help command failed: {result.stderr}")
            return False

        print("✅ CLI command tests completed")
        return True

    except Exception as e:
        print(f"❌ CLI command test failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 Testing Rollback Test Functionality")
    print("=" * 60)

    tests = [
        ("RollbackTestManager", test_rollback_test_manager),
        ("Pipeline Integration", test_pipeline_integration),
        ("Nightly Test Setup", test_nightly_rollback_test),
        ("CLI Commands", test_cli_commands),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} test crashed: {e}")
            results.append((test_name, False))

    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 60)

    passed = 0
    failed = 0

    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
        else:
            failed += 1

    print(f"\nTotal: {len(results)} tests")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed / len(results) * 100):.1f}%")

    if failed == 0:
        print(
            "\n🎉 All tests passed! Rollback test functionality is working correctly."
        )
        return True
    else:
        print(f"\n⚠️  {failed} test(s) failed. Please review the issues above.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
