#!/usr/bin/env python3
"""
Debug script to test smoke runner step by step
"""

import asyncio
import httpx
from tools.tests.smoke_runner import SmokeTestRunner, SmokeTestBackend

async def debug_smoke_tests():
    print("🔍 Debug: Testing smoke runner step by step...")
    
    # Test direct HTTP calls first
    print("\n1. Testing direct HTTP calls:")
    async with httpx.AsyncClient() as client:
        try:
            response = await client.get("http://localhost:8000/health")
            print(f"   /health: {response.status_code} - {response.text[:100]}...")
        except Exception as e:
            print(f"   /health: ERROR - {e}")
            
        try:
            response = await client.get("http://localhost:8000/api/v1/health/db")
            print(f"   /api/v1/health/db: {response.status_code} - {response.text[:100]}...")
        except Exception as e:
            print(f"   /api/v1/health/db: ERROR - {e}")
    
    # Test smoke runner
    print("\n2. Testing smoke runner:")
    runner = SmokeTestRunner(backend=SmokeTestBackend.HTTP)
    print(f"   Runner created with {len(runner.scenarios)} scenarios")
    
    for scenario in runner.scenarios:
        print(f"   Scenario: {scenario.name} ({len(scenario.steps)} steps)")
        for step in scenario.steps:
            print(f"      Step: {step.name}")
    
    # Run the tests
    print("\n3. Running smoke tests:")
    try:
        result = await runner.run_all()
        print(f"   Result type: {type(result)}")
        print(f"   Run ID: {result.run_id}")
        print(f"   Backend: {result.backend}")
        print(f"   Total scenarios: {len(result.scenarios)}")
        print(f"   Total steps: {result.total_steps}")
        print(f"   Passed steps: {result.passed_steps}")
        print(f"   Failed steps: {result.failed_steps}")
        print(f"   Success rate: {result.success_rate}")
        
        print("\n4. Detailed results:")
        for scenario in result.scenarios:
            print(f"   Scenario: {scenario.name} - {scenario.status}")
            for step in scenario.steps:
                print(f"      Step: {step.name} - {step.status}")
                if step.error:
                    print(f"         Error: {step.error}")
                if step.details:
                    print(f"         Details: {step.details}")
                    
    except Exception as e:
        print(f"   ERROR: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(debug_smoke_tests())
