{"enabled": true, "version": "1.0.0", "model": {"name": "deepseek-coder:1.3b", "temperature": 0.3, "max_tokens": 2048, "top_p": 0.95}, "features": {"code_completion": {"enabled": true, "context_lines": 50, "suggestion_count": 5}, "bug_detection": {"enabled": true, "severity_levels": ["error", "warning", "info"], "auto_fix": true}, "optimization": {"enabled": true, "performance_threshold": 0.8, "memory_threshold": 0.7}, "pattern_recognition": {"enabled": true, "learning_enabled": true, "pattern_threshold": 0.6}}, "languages": {"python": {"enabled": true, "priority": "high"}, "javascript": {"enabled": true, "priority": "high"}, "typescript": {"enabled": true, "priority": "high"}, "java": {"enabled": true, "priority": "medium"}, "cpp": {"enabled": true, "priority": "medium"}}, "editor_integration": {"enabled": true, "real_time_suggestions": true, "inline_completion": true, "quick_fixes": true}, "performance": {"max_response_time": 2.0, "cache_enabled": true, "cache_ttl": 300, "batch_size": 10}, "logging": {"level": "INFO", "file_enabled": true, "console_enabled": true}}