#!/usr/bin/env python3
"""
Test Script for LLM Security Agent

This script tests the LLM-powered security features using local Ollama models.
"""

import asyncio
import json
import sys
from datetime import datetime
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


async def test_llm_security_agent():
    """Test the LLM Security Agent functionality"""
    print("🤖 Testing LLM Security Agent...")
    print("=" * 50)

    try:
        from agent.security.llm_security_agent import LLMSecurityAgent

        # Load configuration
        config = {
            "llm_security": {
                "enabled": True,
                "models": {
                    "threat_analysis": "deepseek-coder:1.3b",
                    "code_review": "deepseek-coder:1.3b",
                    "compliance": "mistral:7b-instruct-q4_0",
                    "general": "qwen2.5-coder:3b",
                },
            }
        }

        # Initialize agent
        agent = LLMSecurityAgent(config)
        print("✅ LLM Security Agent initialized")

        # Test 1: Threat Analysis
        print("\n🔍 Test 1: Threat Analysis")
        threat_data = {
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0 (compatible; Bot/1.0)",
            "request_count": 150,
            "time_window": "5 minutes",
            "suspicious_patterns": ["rapid_requests", "bot_signature"],
            "geographic_location": "unknown",
            "previous_incidents": 3,
        }

        analysis = await agent.analyze_threat(threat_data)
        print(f"   ✅ Threat Analysis Completed")
        print(f"   📊 Severity: {analysis.severity.value}")
        print(f"   🎯 Confidence: {analysis.confidence:.2f}")
        print(f"   💡 Recommendations: {len(analysis.recommendations)} items")
        print(f"   ⏱️ Processing Time: {analysis.processing_time:.2f}s")

        # Test 2: Anomaly Detection
        print("\n🚨 Test 2: Anomaly Detection")
        system_data = {
            "cpu_usage": 95,
            "memory_usage": 87,
            "network_traffic": "unusual_spike",
            "failed_logins": 23,
            "time": "02:30 AM",
            "disk_usage": 78,
            "active_connections": 150,
            "error_rate": 0.15,
        }

        incidents = await agent.detect_anomalies(system_data)
        print(f"   ✅ Anomaly Detection Completed")
        print(f"   🚨 Incidents Detected: {len(incidents)}")

        for i, incident in enumerate(incidents, 1):
            print(
                f"   📋 Incident {i}: {incident.incident_type} ({incident.severity.value})"
            )

        # Test 3: Code Security Review
        print("\n🔒 Test 3: Code Security Review")
        code_data = {
            "language": "python",
            "code": """
user_input = input('Enter data: ')
exec(user_input)

password=os.getenv("PASSWORD")
db_query = f"SELECT * FROM users WHERE password=os.getenv("PASSWORD")"

import os
os.system(user_input)
            """,
            "file": "test_vulnerable.py",
            "context": "User input handling and database operations",
        }

        code_analysis = await agent.review_code_security(code_data)
        print(f"   ✅ Code Security Review Completed")
        print(f"   🛡️ Overall Severity: {code_analysis.severity.value}")
        print(f"   🎯 Confidence: {code_analysis.confidence:.2f}")
        print(f"   💡 Recommendations: {len(code_analysis.recommendations)} items")

        # Test 4: Compliance Audit
        print("\n📋 Test 4: Compliance Audit")
        compliance_data = {
            "framework": "GDPR",
            "data_processing": {
                "personal_data": True,
                "consent_mechanism": "checkbox",
                "data_retention": "indefinite",
                "data_encryption": False,
                "access_controls": "basic",
            },
            "user_rights": {
                "right_to_access": False,
                "right_to_erasure": False,
                "right_to_portability": False,
            },
            "breach_notification": {"procedure": "manual", "timeframe": "72_hours"},
        }

        compliance_analysis = await agent.audit_compliance(compliance_data)
        print(f"   ✅ Compliance Audit Completed")
        print(f"   📊 Severity: {compliance_analysis.severity.value}")
        print(f"   🎯 Confidence: {compliance_analysis.confidence:.2f}")
        print(
            f"   💡 Recommendations: {len(compliance_analysis.recommendations)} items"
        )

        # Test 5: Security Recommendations
        print("\n💡 Test 5: Security Recommendations")
        context = {
            "environment": "production",
            "user_count": 1000,
            "sensitive_data": True,
            "external_access": True,
            "compliance_requirements": ["GDPR", "SOC2"],
            "recent_incidents": ["data_breach", "ddos_attack"],
            "budget_constraints": "medium",
        }

        recommendations = await agent.generate_security_recommendations(context)
        print(f"   ✅ Security Recommendations Generated")
        print(f"   💡 Recommendations: {len(recommendations)} items")

        for i, rec in enumerate(recommendations[:3], 1):  # Show first 3
            print(f"   {i}. {rec}")

        # Test 6: Metrics
        print("\n📊 Test 6: Performance Metrics")
        metrics = agent.get_metrics()
        print(f"   ✅ Metrics Retrieved")
        print(f"   📈 Analyses Performed: {metrics['analyses_performed']}")
        print(f"   🚨 Incidents Detected: {metrics['incidents_detected']}")
        print(f"   ⏱️ Avg Processing Time: {metrics['average_processing_time']:.2f}s")
        print(f"   🤖 Models Used: {list(metrics['model_usage'].keys())}")

        # Test 7: Analysis History
        print("\n📚 Test 7: Analysis History")
        history = agent.get_analysis_history(limit=5)
        print(f"   ✅ History Retrieved")
        print(f"   📖 Recent Analyses: {len(history)} items")

        # Test 8: Incident History
        print("\n🚨 Test 8: Incident History")
        incidents = agent.get_incidents(limit=5)
        print(f"   ✅ Incidents Retrieved")
        print(f"   📋 Recent Incidents: {len(incidents)} items")

        print("\n" + "=" * 50)
        print("🎉 All LLM Security Agent tests completed successfully!")

        return True

    except Exception as e:
        print(f"❌ LLM Security Agent test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


async def test_security_manager_integration():
    """Test LLM Security Agent integration with SecurityManager"""
    print("\n🔗 Testing SecurityManager LLM Integration...")
    print("=" * 50)

    try:
        from agent.security.security_manager import SecurityManager

        # Load configuration with LLM security enabled
        config = {
            "security": {"enabled": True},
            "llm_security": {
                "enabled": True,
                "models": {
                    "threat_analysis": "deepseek-coder:1.3b",
                    "code_review": "deepseek-coder:1.3b",
                    "compliance": "mistral:7b-instruct-q4_0",
                    "general": "qwen2.5-coder:3b",
                },
            },
        }

        # Initialize SecurityManager
        security_manager = SecurityManager(config)
        print("✅ SecurityManager initialized with LLM integration")

        # Test LLM threat analysis
        print("\n🔍 Testing LLM Threat Analysis Integration")
        threat_data = {
            "ip_address": "*********",
            "user_agent": "Mozilla/5.0 (compatible; MaliciousBot/1.0)",
            "request_pattern": "aggressive_scanning",
            "target_endpoints": ["/admin", "/api/users", "/config"],
        }

        result = await security_manager.analyze_threat_with_llm(threat_data)
        if result.get("success"):
            print(f"   ✅ LLM Threat Analysis: {result['severity']} severity")
            print(f"   💡 Recommendations: {len(result['recommendations'])} items")
        else:
            print(f"   ❌ LLM Threat Analysis failed: {result.get('error')}")

        # Test LLM anomaly detection
        print("\n🚨 Testing LLM Anomaly Detection Integration")
        system_data = {
            "load_average": 8.5,
            "memory_usage": 92,
            "disk_io": "high",
            "network_connections": 500,
            "error_logs": "excessive",
        }

        result = await security_manager.detect_anomalies_with_llm(system_data)
        if result.get("success"):
            print(f"   ✅ LLM Anomaly Detection: {result['count']} incidents")
        else:
            print(f"   ❌ LLM Anomaly Detection failed: {result.get('error')}")

        # Test LLM code security review
        print("\n🔒 Testing LLM Code Security Review Integration")
        code_data = {
            "language": "javascript",
            "code": "const userInput = req.body.data; eval(userInput);",
            "file": "api_handler.js",
            "context": "API endpoint handler",
        }

        result = await security_manager.review_code_security_with_llm(code_data)
        if result.get("success"):
            print(f"   ✅ LLM Code Review: {result['severity']} severity")
            print(f"   🛡️ Vulnerabilities: {len(result['vulnerabilities'])} found")
        else:
            print(f"   ❌ LLM Code Review failed: {result.get('error')}")

        # Test LLM security recommendations
        print("\n💡 Testing LLM Security Recommendations Integration")
        context = {
            "current_security_level": "basic",
            "budget": "limited",
            "team_size": "small",
            "compliance_needs": ["GDPR"],
        }

        result = await security_manager.get_security_recommendations_with_llm(context)
        if result.get("success"):
            print(f"   ✅ LLM Recommendations: {result['count']} items")
        else:
            print(f"   ❌ LLM Recommendations failed: {result.get('error')}")

        # Test LLM metrics
        print("\n📊 Testing LLM Security Metrics")
        metrics = security_manager.get_llm_security_metrics()
        if "error" not in metrics:
            print(
                f"   ✅ LLM Metrics: {metrics['analyses_performed']} analyses performed"
            )
        else:
            print(f"   ❌ LLM Metrics failed: {metrics.get('error')}")

        print("\n" + "=" * 50)
        print("🎉 SecurityManager LLM integration tests completed!")

        return True

    except Exception as e:
        print(f"❌ SecurityManager LLM integration test failed: {e}")
        import traceback

        traceback.print_exc()
        return False


async def main():
    """Run all LLM Security Agent tests"""
    print("🚀 Starting LLM Security Agent Tests")
    print("=" * 60)

    # Test 1: LLM Security Agent
    test1_success = await test_llm_security_agent()

    # Test 2: SecurityManager Integration
    test2_success = await test_security_manager_integration()

    # Summary
    print("\n" + "=" * 60)
    print("📊 LLM Security Agent Test Results")
    print("=" * 60)

    print(f"🤖 LLM Security Agent: {'✅ PASS' if test1_success else '❌ FAIL'}")
    print(
        f"🔗 SecurityManager Integration: {'✅ PASS' if test2_success else '❌ FAIL'}"
    )

    total_tests = 2
    passed_tests = sum([test1_success, test2_success])
    success_rate = (passed_tests / total_tests) * 100

    print(f"\n📈 Success Rate: {success_rate:.1f}% ({passed_tests}/{total_tests})")

    if success_rate == 100:
        print("\n🎉 All LLM Security Agent tests passed!")
        print("✅ LLM-powered security features are fully operational")
        return 0
    else:
        print(f"\n⚠️ {total_tests - passed_tests} test(s) failed")
        print("Please review the failed tests and fix any issues")
        return 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
