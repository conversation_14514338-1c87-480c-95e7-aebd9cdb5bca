#!/usr/bin/env python3
"""
Smoke Test Runner for Live Preview & Auto-Fix

This module provides a pluggable smoke test runner that can use different backends:
1. HTTP-based minimal tests (default)
2. Playwright browser automation (when enabled)

Features:
- Async test execution with streaming results
- Configurable test scenarios
- JSON output for API integration
- Event streaming for WebSocket integration
"""

import asyncio
import json
import logging
import time
from dataclasses import asdict, dataclass
from datetime import datetime
from enum import Enum
from pathlib import Path
from typing import Any, AsyncGenerator, Dict, List, Optional, Union

import httpx
from playwright.async_api import async_playwright

logger = logging.getLogger(__name__)


class SmokeTestStatus(Enum):
    """Test execution status"""
    PENDING = "pending"
    RUNNING = "running"
    PASSED = "passed"
    FAILED = "failed"
    SKIPPED = "skipped"


class SmokeTestBackend(Enum):
    """Available test backends"""
    HTTP = "http"
    PLAYWRIGHT = "playwright"


@dataclass
class TestStep:
    """Individual test step"""
    id: str
    name: str
    description: str
    status: SmokeTestStatus = SmokeTestStatus.PENDING
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    error: Optional[str] = None
    details: Optional[Dict[str, Any]] = None

    @property
    def duration(self) -> Optional[float]:
        """Calculate step duration in seconds"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


@dataclass
class TestScenario:
    """Test scenario containing multiple steps"""
    id: str
    name: str
    description: str
    steps: List[TestStep]
    status: SmokeTestStatus = SmokeTestStatus.PENDING
    start_time: Optional[float] = None
    end_time: Optional[float] = None

    @property
    def duration(self) -> Optional[float]:
        """Calculate scenario duration in seconds"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None

    @property
    def passed_steps(self) -> int:
        """Count of passed steps"""
        return sum(1 for step in self.steps if step.status == SmokeTestStatus.PASSED)

    @property
    def failed_steps(self) -> int:
        """Count of failed steps"""
        return sum(1 for step in self.steps if step.status == SmokeTestStatus.FAILED)


@dataclass
class TestResult:
    """Complete test run result"""
    run_id: str
    scenarios: List[TestScenario]
    backend: SmokeTestBackend
    start_time: float
    end_time: Optional[float] = None
    config: Optional[Dict[str, Any]] = None

    @property
    def duration(self) -> Optional[float]:
        """Calculate total run duration in seconds"""
        if self.end_time:
            return self.end_time - self.start_time
        return None

    @property
    def total_steps(self) -> int:
        """Total number of test steps"""
        return sum(len(scenario.steps) for scenario in self.scenarios)

    @property
    def passed_steps(self) -> int:
        """Total passed steps"""
        return sum(scenario.passed_steps for scenario in self.scenarios)

    @property
    def failed_steps(self) -> int:
        """Total failed steps"""
        return sum(scenario.failed_steps for scenario in self.scenarios)

    @property
    def success_rate(self) -> float:
        """Success rate as percentage"""
        if self.total_steps == 0:
            return 0.0
        return (self.passed_steps / self.total_steps) * 100


class SmokeTestRunner:
    """Main smoke test runner with pluggable backends"""

    def __init__(self, backend: SmokeTestBackend = SmokeTestBackend.HTTP, config: Optional[Dict[str, Any]] = None):
        self.backend = backend
        self.config = config or {}
        self.base_url = self.config.get("base_url", "http://localhost:8000")
        self.timeout = self.config.get("timeout", 30)
        self.scenarios = self._load_scenarios()

    def _load_scenarios(self) -> List[TestScenario]:
        """Load test scenarios based on configuration"""
        scenarios = []

        # Basic health check scenario
        health_scenario = TestScenario(
            id="health_check",
            name="Health Check",
            description="Verify basic application health",
            steps=[
                TestStep(
                    id="api_health",
                    name="API Health Check",
                    description="Check if API is responding"
                ),
                TestStep(
                    id="db_health",
                    name="Database Health Check",
                    description="Verify database connectivity"
                )
            ]
        )
        scenarios.append(health_scenario)

        # Authentication scenario
        auth_scenario = TestScenario(
            id="authentication",
            name="Authentication Flow",
            description="Test user authentication",
            steps=[
                TestStep(
                    id="login_page",
                    name="Login Page Load",
                    description="Load login page"
                ),
                TestStep(
                    id="login_submit",
                    name="Login Submission",
                    description="Submit login credentials"
                ),
                TestStep(
                    id="dashboard_access",
                    name="Dashboard Access",
                    description="Access authenticated dashboard"
                )
            ]
        )
        scenarios.append(auth_scenario)

        return scenarios

    async def run_all(self) -> TestResult:
        """Run all test scenarios and return complete results"""
        run_id = f"smoke_run_{int(time.time())}"
        start_time = time.time()

        logger.info(f"Starting smoke test run {run_id} with backend {self.backend.value}")

        result = TestResult(
            run_id=run_id,
            scenarios=self.scenarios,
            backend=self.backend,
            start_time=start_time,
            config=self.config
        )

        try:
            if self.backend == SmokeTestBackend.HTTP:
                await self._run_http_tests(result)
            elif self.backend == SmokeTestBackend.PLAYWRIGHT:
                await self._run_playwright_tests(result)
            else:
                raise ValueError(f"Unsupported backend: {self.backend}")

        except Exception as e:
            logger.error(f"Smoke test run failed: {e}")
            # Mark all scenarios as failed
            for scenario in result.scenarios:
                scenario.status = SmokeTestStatus.FAILED
                for step in scenario.steps:
                    if step.status == SmokeTestStatus.PENDING:
                        step.status = SmokeTestStatus.FAILED
                        step.error = str(e)

        result.end_time = time.time()
        logger.info(f"Smoke test run {run_id} completed in {result.duration:.2f}s")
        return result

    async def stream_results(self) -> AsyncGenerator[Dict[str, Any], None]:
        """Stream test results as they execute"""
        run_id = f"smoke_stream_{int(time.time())}"
        start_time = time.time()

        # Yield start event
        yield {
            "type": "run_start",
            "run_id": run_id,
            "backend": self.backend.value,
            "timestamp": datetime.now().isoformat(),
            "total_scenarios": len(self.scenarios)
        }

        for scenario in self.scenarios:
            scenario.start_time = time.time()
            scenario.status = SmokeTestStatus.RUNNING

            # Yield scenario start
            yield {
                "type": "scenario_start",
                "run_id": run_id,
                "scenario": asdict(scenario),
                "timestamp": datetime.now().isoformat()
            }

            for step in scenario.steps:
                step.start_time = time.time()
                step.status = SmokeTestStatus.RUNNING

                # Yield step start
                yield {
                    "type": "step_start",
                    "run_id": run_id,
                    "scenario_id": scenario.id,
                    "step": asdict(step),
                    "timestamp": datetime.now().isoformat()
                }

                # Execute step
                try:
                    await self._execute_step(step, scenario)
                    step.status = SmokeTestStatus.PASSED
                except Exception as e:
                    step.status = SmokeTestStatus.FAILED
                    step.error = str(e)

                step.end_time = time.time()

                # Yield step result
                yield {
                    "type": "step_complete",
                    "run_id": run_id,
                    "scenario_id": scenario.id,
                    "step": asdict(step),
                    "timestamp": datetime.now().isoformat()
                }

            scenario.end_time = time.time()
            scenario.status = SmokeTestStatus.PASSED if scenario.failed_steps == 0 else SmokeTestStatus.FAILED

            # Yield scenario result
            yield {
                "type": "scenario_complete",
                "run_id": run_id,
                "scenario": asdict(scenario),
                "timestamp": datetime.now().isoformat()
            }

        # Yield final summary
        end_time = time.time()
        total_steps = sum(len(s.steps) for s in self.scenarios)
        passed_steps = sum(s.passed_steps for s in self.scenarios)

        yield {
            "type": "run_complete",
            "run_id": run_id,
            "duration": end_time - start_time,
            "total_scenarios": len(self.scenarios),
            "total_steps": total_steps,
            "passed_steps": passed_steps,
            "success_rate": (passed_steps / total_steps * 100) if total_steps > 0 else 0,
            "timestamp": datetime.now().isoformat()
        }

    async def _run_http_tests(self, result: TestResult) -> None:
        """Execute tests using HTTP client"""
        async with httpx.AsyncClient(timeout=self.timeout) as client:
            for scenario in result.scenarios:
                scenario.start_time = time.time()
                scenario.status = SmokeTestStatus.RUNNING

                for step in scenario.steps:
                    step.start_time = time.time()
                    step.status = SmokeTestStatus.RUNNING

                    try:
                        await self._execute_http_step(client, step, scenario)
                        step.status = SmokeTestStatus.PASSED
                    except Exception as e:
                        step.status = SmokeTestStatus.FAILED
                        step.error = str(e)
                        logger.error(f"Step {step.id} failed: {e}")

                    step.end_time = time.time()

                scenario.end_time = time.time()
                scenario.status = SmokeTestStatus.PASSED if scenario.failed_steps == 0 else SmokeTestStatus.FAILED

    async def _run_playwright_tests(self, result: TestResult) -> None:
        """Execute tests using Playwright browser automation"""
        async with async_playwright() as p:
            browser = await p.chromium.launch(headless=True)
            context = await browser.new_context()
            page = await context.new_page()

            try:
                for scenario in result.scenarios:
                    scenario.start_time = time.time()
                    scenario.status = SmokeTestStatus.RUNNING

                    for step in scenario.steps:
                        step.start_time = time.time()
                        step.status = SmokeTestStatus.RUNNING

                        try:
                            await self._execute_playwright_step(page, step, scenario)
                            step.status = SmokeTestStatus.PASSED
                        except Exception as e:
                            step.status = SmokeTestStatus.FAILED
                            step.error = str(e)
                            logger.error(f"Step {step.id} failed: {e}")

                        step.end_time = time.time()

                    scenario.end_time = time.time()
                    scenario.status = SmokeTestStatus.PASSED if scenario.failed_steps == 0 else SmokeTestStatus.FAILED

            finally:
                await browser.close()

    async def _execute_step(self, step: TestStep, scenario: TestScenario) -> None:
        """Execute a single test step (used by stream_results)"""
        if self.backend == SmokeTestBackend.HTTP:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                await self._execute_http_step(client, step, scenario)
        elif self.backend == SmokeTestBackend.PLAYWRIGHT:
            async with async_playwright() as p:
                browser = await p.chromium.launch(headless=True)
                context = await browser.new_context()
                page = await context.new_page()
                try:
                    await self._execute_playwright_step(page, step, scenario)
                finally:
                    await browser.close()

    async def _execute_http_step(self, client: httpx.AsyncClient, step: TestStep, scenario: TestScenario) -> None:
        """Execute HTTP-based test step"""
        if step.id == "api_health":
            response = await client.get(f"{self.base_url}/health")
            if response.status_code != 200:
                raise Exception(f"Health check failed: {response.status_code}")
            step.details = {"status_code": response.status_code, "response_time": response.elapsed.total_seconds()}

        elif step.id == "db_health":
            response = await client.get(f"{self.base_url}/api/v1/health/db")
            if response.status_code != 200:
                raise Exception(f"DB health check failed: {response.status_code}")
            step.details = {"status_code": response.status_code}

        elif step.id == "login_page":
            response = await client.get(f"{self.base_url}/login")
            # 404 is acceptable if no login page exists
            step.details = {"status_code": response.status_code, "acceptable": True}

        elif step.id == "login_submit":
            # Mock login attempt
            login_data = {"username": "test", "password": "test"}
            response = await client.post(f"{self.base_url}/api/v1/auth/login", json=login_data)
            # Accept various responses (401 for invalid creds, 404 for no auth endpoint)
            step.details = {"status_code": response.status_code, "acceptable": True}

        elif step.id == "dashboard_access":
            response = await client.get(f"{self.base_url}/dashboard")
            # Various responses are acceptable (404 if no dashboard exists)
            step.details = {"status_code": response.status_code, "acceptable": True}

        else:
            logger.warning(f"Unknown HTTP step: {step.id}")

    async def _execute_playwright_step(self, page, step: TestStep, scenario: TestScenario) -> None:
        """Execute Playwright-based test step"""
        if step.id == "api_health":
            # Use page.request for API calls
            response = await page.request.get(f"{self.base_url}/health")
            if not response.ok:
                raise Exception(f"Health check failed: {response.status}")
            step.details = {"status_code": response.status}

        elif step.id == "db_health":
            response = await page.request.get(f"{self.base_url}/api/v1/health/db")
            if not response.ok:
                raise Exception(f"DB health check failed: {response.status}")
            step.details = {"status_code": response.status}

        elif step.id == "login_page":
            await page.goto(f"{self.base_url}/login")
            # Check if page loaded (title or specific element)
            title = await page.title()
            step.details = {"page_title": title}

        elif step.id == "login_submit":
            await page.goto(f"{self.base_url}/login")
            # Try to find and fill login form
            try:
                await page.fill('input[name="username"]', 'test')
                await page.fill('input[name="password"]', 'test')
                await page.click('button[type="submit"]')
                await page.wait_for_timeout(1000)  # Wait for response
                step.details = {"form_submitted": True}
            except Exception as e:
                # Form might not exist, which is acceptable
                step.details = {"form_submitted": False, "reason": str(e)}

        elif step.id == "dashboard_access":
            await page.goto(f"{self.base_url}/dashboard")
            title = await page.title()
            step.details = {"page_title": title}

        else:
            logger.warning(f"Unknown Playwright step: {step.id}")


# Convenience functions for API integration
async def run_smoke_tests(backend: str = "http", config: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """Run smoke tests and return JSON result"""
    backend_enum = SmokeTestBackend.HTTP if backend == "http" else SmokeTestBackend.PLAYWRIGHT
    runner = SmokeTestRunner(backend=backend_enum, config=config)
    result = await runner.run_all()
    return asdict(result)


async def stream_smoke_tests(backend: str = "http", config: Optional[Dict[str, Any]] = None) -> AsyncGenerator[Dict[str, Any], None]:
    """Stream smoke test results"""
    backend_enum = SmokeTestBackend.HTTP if backend == "http" else SmokeTestBackend.PLAYWRIGHT
    runner = SmokeTestRunner(backend=backend_enum, config=config)
    async for event in runner.stream_results():
        yield event
