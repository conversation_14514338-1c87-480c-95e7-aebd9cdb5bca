#!/usr/bin/env python3
"""
ContainerAgent - Specialized agent for container and Docker tasks

Handles:
- Docker container creation and management
- Docker Compose orchestration
- Container health monitoring
- Resource allocation and optimization
- Container security configuration
- Multi-stage builds
"""

import asyncio
import json
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import docker
import uvicorn
from docker.errors import DockerException
from fastapi import FastAPI

from agent.core.agents.enhanced_base_agent import Enhanced<PERSON><PERSON>Agent
from agent.core.agents.enhanced_base_agent import TaskStatus as EnhancedTaskStatus
from agent.core.agents.enhanced_base_agent import VerificationLevel
from agent.core.site_container_manager import SiteContainerManager
from agent.models.ollama_manager import OllamaModelManager

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI()


@app.get("/health")
def health_check():
    return {"status": "ok"}


class ContainerAgent(EnhancedBaseAgent):
    """Specialized agent for container and Docker tasks"""

    def __init__(self, config_path: str = "config/container_agent_config.json"):
        """Initialize the ContainerAgent"""
        default_config = {
            "memory_tracking": {
                "enabled": True,
                "max_attempts_per_task": 3,
                "cooldown_seconds": 300,
                "verify_tasks": True,
                "reset_on_success": True,
                "verification_level": "comprehensive",
                "persistence": True,
                "cleanup_interval": 86400,
            },
            "model_settings": {
                "model_name": "deepseek-coder:1.3b",
                "system_prompt": "You are a container AI assistant specialized in Docker and container orchestration.",
            },
            "base_image": "python:3.11-slim",
            "multi_stage_build": True,
            "non_root_user": True,
            "health_checks": True,
            "resource_limits": True,
            "security_scanning": True,
            "port_range": [8080, 9000],
            "network": "ai-coding-network",
        }
        super().__init__(config_path, default_config)

        self.site_container_manager = SiteContainerManager()

        logger.info(
            "ContainerAgent initialized successfully with enhanced memory tracking"
        )

    async def _parse_task_requirements(self, task_description: str) -> Dict[str, Any]:
        """
        Parse task requirements from description for container agent

        Args:
            task_description: Description of the task

        Returns:
            Dictionary with parsed requirements
        """
        try:
            # Use existing container requirements parser
            requirements = await self._parse_container_requirements(task_description)
            requirements["parsed"] = True
            return requirements
        except Exception as e:
            logger.error(f"Error parsing container task requirements: {e}")
            return {"description": task_description, "parsed": False, "error": str(e)}

    async def _execute_specific_task(
        self, requirements: Dict[str, Any], task_id: str
    ) -> Dict[str, Any]:
        """
        Execute the specific task implementation for container agent

        Args:
            requirements: Parsed task requirements
            task_id: Unique identifier for the task

        Returns:
            Dictionary with task results
        """
        try:
            if not requirements.get("parsed", False):
                return {
                    "success": False,
                    "error": "Failed to parse task requirements",
                    "task_id": task_id,
                }

            logger.info(
                f"Executing container task: {requirements.get('description', task_id)}"
            )

            # Execute container tasks based on requirements
            results = {}

            # Generate Dockerfile
            results["dockerfile"] = await self._generate_dockerfile(requirements)

            # Generate docker-compose configuration
            results["docker_compose"] = await self._generate_docker_compose(
                requirements
            )

            # Generate container configuration
            results["container_config"] = await self._generate_container_config(
                requirements
            )

            # Create container if requested
            if requirements.get("create_container", False):
                results["container_info"] = await self._create_container(
                    requirements, task_id
                )

            # Determine overall success
            all_successful = all(
                result is not None
                and (not isinstance(result, dict) or result.get("success", True))
                for result in results.values()
            )

            return {
                "success": all_successful,
                "task_id": task_id,
                "results": results,
                "requirements": requirements,
            }

        except Exception as e:
            logger.error(f"Error executing container task {task_id}: {e}")
            return {"success": False, "error": str(e), "task_id": task_id}

    async def _verify_task_specific(
        self, task_description: str, task_id: str, result: Dict[str, Any]
    ) -> bool:
        """
        Container-specific task verification

        Args:
            task_description: Description of the task
            task_id: Unique identifier for the task
            result: Task execution result

        Returns:
            True if task was successful, False otherwise
        """
        try:
            # Check if task was successful
            if not result.get("success", False):
                return False

            # Check if all container components were generated successfully
            results = result.get("results", {})
            if not results:
                return False

            # Verify each container component
            for component, component_result in results.items():
                if component_result is None:
                    logger.warning(f"Container component {component} is None")
                    return False

            # Additional verification for container-specific tasks
            if (
                "container" in task_description.lower()
                or "docker" in task_description.lower()
            ):
                # Check if Dockerfile was generated
                if "dockerfile" not in results:
                    logger.warning("Dockerfile not generated")
                    return False

                # Check if docker-compose was generated
                if "docker_compose" not in results:
                    logger.warning("Docker Compose not generated")
                    return False

            return True

        except Exception as e:
            logger.error(f"Error during container task verification: {e}")
            return False

    async def _parse_container_requirements(
        self, task_description: str
    ) -> Dict[str, Any]:
        """Parse container requirements from task description"""
        prompt = f"""
        Parse the following container development task and extract requirements:

        Task: {task_description}

        Extract:
        1. Application type (frontend, backend, full-stack)
        2. Required dependencies and packages
        3. Port requirements
        4. Volume mounts
        5. Environment variables
        6. Resource requirements (CPU, memory)
        7. Security requirements
        8. Health check requirements

        Return as JSON.
        """

        response_dict = await self.ollama_manager.generate_response(
            prompt, model_name=self.model_name
        )
        response = response_dict.get("response", "")

        try:
            # Try to parse JSON from response
            if "```json" in response:
                json_start = response.find("```json") + 7
                json_end = response.find("```", json_start)
                json_str = response[json_start:json_end].strip()
                return json.loads(json_str)
            else:
                # Fallback parsing
                return self._fallback_parse_requirements(task_description)
        except Exception as e:
            logger.warning(f"Failed to parse JSON response: {e}")
            return self._fallback_parse_requirements(task_description)

    def _fallback_parse_requirements(self, task_description: str) -> Dict[str, Any]:
        """Fallback parsing for requirements"""
        requirements = {
            "application_type": "full-stack",
            "dependencies": [],
            "ports": [3000, 8000],
            "volumes": [],
            "environment_variables": [],
            "resource_requirements": {"cpu": "1.0", "memory": "2G"},
            "security_requirements": [],
            "health_check": True,
            "create_container": False,
        }

        # Simple keyword-based parsing
        description_lower = task_description.lower()

        if "frontend" in description_lower:
            requirements["application_type"] = "frontend"
            requirements["ports"] = [3000]
            requirements["dependencies"].extend(["node", "npm"])

        if "backend" in description_lower:
            requirements["application_type"] = "backend"
            requirements["ports"] = [8000]
            requirements["dependencies"].extend(["python", "pip"])

        if "portfolio" in description_lower or "website" in description_lower:
            requirements["create_container"] = True
            requirements["volumes"].extend(
                ["./public:/app/public", "./uploads:/app/uploads"]
            )

        if "database" in description_lower:
            requirements["dependencies"].append("postgresql")
            requirements["ports"].append(5432)

        return requirements

    async def _generate_dockerfile(self, requirements: Dict[str, Any]) -> str:
        """Generate Dockerfile based on requirements"""
        app_type = requirements.get("application_type", "full-stack")

        if app_type == "frontend":
            return await self._generate_frontend_dockerfile(requirements)
        elif app_type == "backend":
            return await self._generate_backend_dockerfile(requirements)
        else:
            return await self._generate_fullstack_dockerfile(requirements)

    async def _generate_frontend_dockerfile(self, requirements: Dict[str, Any]) -> str:
        """Generate Dockerfile for frontend applications"""
        return f"""
# Frontend Dockerfile
FROM node:18-alpine AS builder

WORKDIR /app
COPY package*.json ./
RUN npm ci --only=production

COPY . .
RUN npm run build

FROM node:18-alpine AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public
COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE {requirements.get('ports', [3000])[0]}

ENV PORT {requirements.get('ports', [3000])[0]}

CMD ["node", "server.js"]
"""

    async def _generate_backend_dockerfile(self, requirements: Dict[str, Any]) -> str:
        """Generate Dockerfile for backend applications"""
        return f"""
# Backend Dockerfile
FROM python:3.11-slim AS builder

WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

FROM python:3.11-slim AS production

WORKDIR /app
COPY --from=builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

COPY . .

RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

EXPOSE {requirements.get('ports', [8000])[0]}

CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "{requirements.get('ports', [8000])[0]}"]
"""

    async def _generate_fullstack_dockerfile(self, requirements: Dict[str, Any]) -> str:
        """Generate Dockerfile for full-stack applications"""
        return f"""
# Full-stack Dockerfile
FROM node:18-alpine AS frontend-builder

WORKDIR /app/frontend
COPY frontend/package*.json ./
RUN npm ci --only=production

COPY frontend/ .
RUN npm run build

FROM python:3.11-slim AS backend-builder

WORKDIR /app/backend
COPY backend/requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

FROM python:3.11-slim AS production

WORKDIR /app

# Copy backend dependencies
COPY --from=backend-builder /usr/local/lib/python3.11/site-packages /usr/local/lib/python3.11/site-packages
COPY --from=backend-builder /usr/local/bin /usr/local/bin

# Copy frontend build
COPY --from=frontend-builder /app/frontend/.next /app/frontend/.next
COPY --from=frontend-builder /app/frontend/public /app/frontend/public

# Copy application code
COPY backend/ ./backend/
COPY frontend/ ./frontend/

RUN groupadd -r appuser && useradd -r -g appuser appuser
RUN chown -R appuser:appuser /app
USER appuser

EXPOSE {requirements.get('ports', [3000, 8000])[0]} {requirements.get('ports', [3000, 8000])[1]}

CMD ["python", "backend/main.py"]
"""

    async def _generate_docker_compose(self, requirements: Dict[str, Any]) -> str:
        """Generate docker-compose.yml configuration"""
        service_name = f"app-{requirements.get('application_type', 'fullstack')}"
        ports = requirements.get("ports", [3000])

        compose_config = f"""
version: '3.8'

services:
  {service_name}:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: {service_name}
    restart: unless-stopped
    ports:
"""

        for port in ports:
            compose_config += f'      - "{port}:{port}"\n'

        compose_config += f"""
    volumes:
"""

        for volume in requirements.get("volumes", []):
            compose_config += f"      - {volume}\n"

        compose_config += f"""
    environment:
      - NODE_ENV=production
      - PYTHONPATH=/app
"""

        for env_var in requirements.get("environment_variables", []):
            compose_config += f"      - {env_var}\n"

        compose_config += f"""
    networks:
      - {self.config['network']}
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:{ports[0]}/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    deploy:
      resources:
        limits:
          cpus: '{requirements.get("resource_requirements", {}).get("cpu", "1.0")}'
          memory: {requirements.get("resource_requirements", {}).get("memory", "2G")}
        reservations:
          cpus: '0.5'
          memory: 1G

networks:
  {self.config['network']}:
    external: true
"""

        return compose_config

    async def _generate_container_config(
        self, requirements: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Generate container configuration"""
        return {
            "application_type": requirements.get("application_type", "full-stack"),
            "ports": requirements.get("ports", [3000, 8000]),
            "volumes": requirements.get("volumes", []),
            "environment_variables": requirements.get("environment_variables", []),
            "resource_requirements": requirements.get(
                "resource_requirements", {"cpu": "1.0", "memory": "2G"}
            ),
            "health_check": requirements.get("health_check", True),
            "security_scanning": self.config.get("security_scanning", True),
            "multi_stage_build": self.config.get("multi_stage_build", True),
            "non_root_user": self.config.get("non_root_user", True),
        }

    async def _create_container(
        self, requirements: Dict[str, Any], task_id: str
    ) -> Dict[str, Any]:
        """Create a container using SiteContainerManager"""
        try:
            # Generate a site name based on the task
            site_name = f"site-{task_id}"

            # Create container using SiteContainerManager
            site_config = {
                "name": site_name,
                "port": requirements.get("ports", [None])[0],
                "environment": requirements.get("environment", "production"),
            }

            container_info = await self.site_container_manager.create_site_container(
                site_name=site_name,
                site_config=site_config,
            )

            return {
                "site_name": site_name,
                "container_name": container_info.get("container_name"),
                "port": container_info.get("port"),
                "status": container_info.get("status"),
                "created_at": datetime.now().isoformat(),
            }

        except Exception as e:
            logger.error(f"Error creating container: {e}")
            return {"error": str(e), "status": "failed"}

    async def shutdown(self):
        """Shutdown the ContainerAgent"""
        logger.info("Shutting down ContainerAgent...")
        # No need to shutdown ollama_manager here, as BaseAgent will handle it.
        logger.info("ContainerAgent shutdown complete")


def run_health_check_server():
    uvicorn.run(app, host="0.0.0.0", port=5001)


if __name__ == "__main__":
    # In a real scenario, you would have a more robust way
    # of running the agent and the health check server.
    # For this example, we'll run the health check in a separate thread.
    import threading
    import time

    health_thread = threading.Thread(target=run_health_check_server, daemon=True)
    health_thread.start()

    # The agent would have its own execution loop, but for this example,
    # we'll just keep the main thread alive.
    logger.info("ContainerAgent entrypoint started. Health check running on port 5001.")
    while True:
        time.sleep(1)
