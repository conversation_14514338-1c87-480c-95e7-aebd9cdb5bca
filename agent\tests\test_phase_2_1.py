#!/usr/bin/env python3
"""
Simple test script for Phase 2.1: Advanced Generator
Tests core functionality without database dependencies.
"""

import json
import sys
from pathlib import Path

# Add src to path
sys.path.append(str(Path(__file__).parent.parent / "src"))

# Import only the core components
from agent.core.website_generator import Asset<PERSON><PERSON>eline, ThemeManager


def test_theme_manager():
    """Test ThemeManager functionality"""
    print("🧪 Testing ThemeManager...")

    # Create a temporary theme manager
    theme_manager = ThemeManager("test_themes")

    # Create a sample template
    template_dir = Path("templates") / "modern"
    template_dir.mkdir(parents=True, exist_ok=True)
    (template_dir / "index.html").write_text("<html>{{title}}</html>")
    (template_dir / "style.css").write_text(":root { --primary-color: #007bff; }")

    # Test theme creation
    customizations = {
        "css": {"primary-color": "#ff6b6b"},
        "html": {"title": "Custom Title"},
    }

    result = theme_manager.create_theme("test-theme", "modern", customizations)

    if result["status"] == "success":
        print("✅ ThemeManager test passed!")
        print(f"   Theme created: {result['theme_path']}")

        # Test theme listing
        themes = theme_manager.list_themes()
        print(f"   Available themes: {list(themes.keys())}")

        return True
    else:
        print(f"❌ ThemeManager test failed: {result['message']}")
        return False


def test_asset_pipeline():
    """Test AssetPipeline functionality"""
    print("\n🧪 Testing AssetPipeline...")

    # Create a temporary asset pipeline
    asset_pipeline = AssetPipeline("test_dist")

    # Create test assets
    test_dir = Path("test_assets")
    test_dir.mkdir(exist_ok=True)
    (test_dir / "test.jpg").write_text("fake image")
    (test_dir / "style.css").write_text("body { color: red; }")
    (test_dir / "script.js").write_text("console.log('test');")

    # Test asset optimization
    result = asset_pipeline.optimize_assets(test_dir, "test-site")

    if result["status"] == "success":
        print("✅ AssetPipeline test passed!")
        if "results" in result:
            results = result["results"]
            print(f"   Images processed: {results['images']['processed']}")
            print(f"   Styles processed: {results['styles']['processed']}")
            print(f"   Scripts processed: {results['scripts']['processed']}")

        return True
    else:
        print(f"❌ AssetPipeline test failed: {result['message']}")
        return False


def test_css_variable_replacement():
    """Test CSS variable replacement functionality"""
    print("\n🧪 Testing CSS variable replacement...")

    theme_manager = ThemeManager("test_themes")

    # Test CSS variable replacement
    css_content = ":root { --primary-color: #007bff; --secondary-color: #6c757d; }"
    result = theme_manager._replace_css_variable(
        css_content, "primary-color", "#ff6b6b"
    )

    if "--primary-color: #ff6b6b;" in result:
        print("✅ CSS variable replacement test passed!")
        print(f"   Original: {css_content}")
        print(f"   Modified: {result}")
        return True
    else:
        print("❌ CSS variable replacement test failed!")
        return False


def test_asset_scanning():
    """Test asset scanning functionality"""
    print("\n🧪 Testing asset scanning...")

    theme_manager = ThemeManager("test_themes")

    # Create test assets
    assets_dir = Path("test_assets_scan")
    assets_dir.mkdir(exist_ok=True)
    (assets_dir / "image.jpg").write_text("fake image")
    (assets_dir / "style.css").write_text("fake css")
    (assets_dir / "script.js").write_text("fake js")
    (assets_dir / "font.woff2").write_text("fake font")

    # Test asset scanning
    assets = theme_manager._scan_assets(assets_dir)

    expected_assets = {
        "images": ["image.jpg"],
        "styles": ["style.css"],
        "scripts": ["script.js"],
        "fonts": ["font.woff2"],
    }

    success = True
    for asset_type, expected_files in expected_assets.items():
        if asset_type in assets:
            for expected_file in expected_files:
                if expected_file in assets[asset_type]:
                    print(f"   ✅ {asset_type}: {expected_file}")
                else:
                    print(f"   ❌ {asset_type}: {expected_file} not found")
                    success = False
        else:
            print(f"   ❌ {asset_type} not found in assets")
            success = False

    if success:
        print("✅ Asset scanning test passed!")
    else:
        print("❌ Asset scanning test failed!")

    return success


def main():
    """Run all tests"""
    print("🚀 Phase 2.1: Advanced Generator Tests")
    print("=" * 50)

    tests = [
        test_theme_manager,
        test_asset_pipeline,
        test_css_variable_replacement,
        test_asset_scanning,
    ]

    passed = 0
    total = len(tests)

    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {str(e)}")

    print(f"\n📊 Test Results: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Phase 2.1 features are working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the implementation.")

    # Cleanup
    import shutil

    for cleanup_dir in ["test_themes", "test_dist", "test_assets", "test_assets_scan"]:
        if Path(cleanup_dir).exists():
            shutil.rmtree(cleanup_dir)

    return passed == total


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
