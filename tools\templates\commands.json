{"create_website": {"intent": "create", "type": "website", "parameters": ["style", "purpose", "features"], "ai_model": "deepseek-coder:1.3b", "output": ["html", "css", "js"], "examples": ["Create a photography portfolio website", "Build a business landing page", "Make an ecommerce store", "Create a personal blog"]}, "add_component": {"intent": "modify", "type": "component", "parameters": ["component_type", "location", "styling"], "ai_model": "deepseek-coder:1.3b", "output": ["html", "css", "js"], "examples": ["Add a contact form to the homepage", "Include a navigation menu", "Add a gallery section", "Insert a footer with social links"]}, "modify_styling": {"intent": "modify", "type": "styling", "parameters": ["colors", "fonts", "layout"], "ai_model": "deepseek-coder:1.3b", "output": ["css"], "examples": ["Change the color scheme to blue and white", "Update the font to <PERSON><PERSON>", "Make the layout responsive", "Add a dark theme"]}, "deploy_website": {"intent": "deploy", "type": "hosting", "parameters": ["domain", "ssl", "cdn"], "ai_model": "yi-coder:1.5b", "output": ["deployment_config"], "examples": ["Deploy the website to my domain", "Publish the website online", "Set up hosting with SSL", "Deploy to a custom domain"]}, "maintain_website": {"intent": "maintain", "type": "maintenance", "parameters": ["issues", "optimization", "security"], "ai_model": "starcoder2:3b", "output": ["diagnostics", "fixes"], "examples": ["Check for broken links and fix them", "Optimize the website performance", "Fix responsive design issues", "Update security headers"]}, "create_content": {"intent": "create", "type": "content", "parameters": ["content_type", "tone", "seo"], "ai_model": "qwen2.5-coder:3b", "output": ["text", "meta_tags"], "examples": ["Write homepage content for a restaurant", "Create product descriptions", "Write blog post about web development", "Generate meta tags for SEO"]}, "debug_issues": {"intent": "maintain", "type": "debugging", "parameters": ["error_type", "browser", "environment"], "ai_model": "starcoder2:3b", "output": ["diagnostics", "solutions"], "examples": ["Fix the JavaScript error on the contact form", "Debug the mobile layout issues", "Fix the broken image links", "Resolve the CSS conflicts"]}, "optimize_performance": {"intent": "maintain", "type": "optimization", "parameters": ["speed", "seo", "accessibility"], "ai_model": "starcoder2:3b", "output": ["optimizations", "recommendations"], "examples": ["Optimize images for faster loading", "Minimize CSS and JavaScript files", "Improve Core Web Vitals", "Add lazy loading to images"]}, "add_functionality": {"intent": "modify", "type": "functionality", "parameters": ["feature", "interaction", "data"], "ai_model": "deepseek-coder:1.3b", "output": ["js", "api_integration"], "examples": ["Add a search function to the website", "Implement a shopping cart", "Add form validation", "Create a newsletter signup"]}, "responsive_design": {"intent": "modify", "type": "responsive", "parameters": ["breakpoints", "devices", "navigation"], "ai_model": "deepseek-coder:1.3b", "output": ["css", "html"], "examples": ["Make the website mobile-friendly", "Add responsive navigation menu", "Optimize for tablet devices", "Create a mobile-first design"]}, "seo_optimization": {"intent": "modify", "type": "seo", "parameters": ["keywords", "meta_tags", "structure"], "ai_model": "qwen2.5-coder:3b", "output": ["meta_tags", "structured_data"], "examples": ["Add SEO meta tags to all pages", "Optimize for search engines", "Add structured data markup", "Create an XML sitemap"]}, "accessibility_improvements": {"intent": "modify", "type": "accessibility", "parameters": ["aria_labels", "contrast", "navigation"], "ai_model": "deepseek-coder:1.3b", "output": ["html", "css"], "examples": ["Add alt text to all images", "Improve keyboard navigation", "Increase color contrast", "Add ARIA labels to form elements"]}, "analytics_setup": {"intent": "modify", "type": "analytics", "parameters": ["tracking", "events", "goals"], "ai_model": "deepseek-coder:1.3b", "output": ["js", "config"], "examples": ["Add Google Analytics tracking", "Set up conversion tracking", "Track form submissions", "Monitor page load times"]}, "security_enhancement": {"intent": "maintain", "type": "security", "parameters": ["headers", "validation", "encryption"], "ai_model": "starcoder2:3b", "output": ["config", "code"], "examples": ["Add security headers", "Implement CSRF protection", "Secure form submissions", "Add input validation"]}, "backup_restore": {"intent": "maintain", "type": "backup", "parameters": ["frequency", "storage", "recovery"], "ai_model": "yi-coder:1.5b", "output": ["backup_config", "scripts"], "examples": ["Set up automatic backups", "Create a backup of the website", "<PERSON><PERSON> from a backup", "Configure backup scheduling"]}, "domain_management": {"intent": "deploy", "type": "domain", "parameters": ["domain_name", "dns", "ssl"], "ai_model": "yi-coder:1.5b", "output": ["dns_config", "ssl_cert"], "examples": ["Configure custom domain", "Set up SSL certificate", "Update DNS records", "Transfer domain to new provider"]}}