#!/usr/bin/env python3
"""
Advanced Learning Integration Test

Tests the integration of the advanced learning system with the main AI Coding Agent.
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path

# Fix Windows console encoding for emoji support
if hasattr(sys.stdout, "reconfigure"):
    # on Windows, switch the console to UTF-8 output to support emojis
    sys.stdout.reconfigure(encoding="utf-8", errors="replace")

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_learning_system_imports():
    """Test that all learning system components can be imported"""
    print("🧪 Testing Learning System Imports")
    print("=" * 50)

    try:
        # Test advanced learning enhancements
        from agent.learning.advanced_learning_enhancements import (
            AdversarialDetector,
            CapabilityDiscovery,
            CascadePredictor,
            DegradationManager,
            FederatedLearningManager,
            MetaLearningOptimizer,
            ParetoOptimizer,
            WorkloadPredictor,
        )

        print("✅ Advanced Learning Enhancements imported successfully")

        # Test adaptive learning system
        from agent.learning.adaptive_learning_system import AdaptiveLearningSystem

        print("✅ Adaptive Learning System imported successfully")

        # Test automated learner
        from agent.learning.automated_learner import AutomatedLearner

        print("✅ Automated Learner imported successfully")

        return True
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return False


def test_learning_system_initialization():
    """Test that learning systems can be initialized"""
    print("\n🧪 Testing Learning System Initialization")
    print("=" * 50)

    try:
        # Test automated learner initialization
        import json

        from agent.learning.automated_learner import AutomatedLearner

        with open("config/automated_learner_config.json", "r") as f:
            config = json.load(f)
        automated_learner = AutomatedLearner(config)
        print("✅ Automated Learner initialized successfully")

        # Test adaptive learning system initialization
        from agent.learning.adaptive_learning_system import AdaptiveLearningSystem

        adaptive_learning = AdaptiveLearningSystem("config/learning_config.json")
        print("✅ Adaptive Learning System initialized successfully")

        # Test advanced learning components
        from agent.learning.advanced_learning_enhancements import (
            AdversarialDetector,
            CapabilityDiscovery,
            CascadePredictor,
            DegradationManager,
            FederatedLearningManager,
            MetaLearningOptimizer,
            ParetoOptimizer,
            WorkloadPredictor,
        )

        meta_learning = MetaLearningOptimizer()
        print("✅ Meta Learning Optimizer initialized successfully")

        pareto_optimizer = ParetoOptimizer(
            [
                "speed",
                "quality",
                "resource_efficiency",
                "reliability",
                "user_satisfaction",
            ]
        )
        print("✅ Pareto Optimizer initialized successfully")

        workload_predictor = WorkloadPredictor()
        print("✅ Workload Predictor initialized successfully")

        cascade_predictor = CascadePredictor()
        print("✅ Cascade Predictor initialized successfully")

        federated_learning = FederatedLearningManager()
        print("✅ Federated Learning Manager initialized successfully")

        capability_discovery = CapabilityDiscovery()
        print("✅ Capability Discovery initialized successfully")

        adversarial_detector = AdversarialDetector()
        print("✅ Adversarial Detector initialized successfully")

        degradation_manager = DegradationManager()
        print("✅ Degradation Manager initialized successfully")

        return True
    except Exception as e:
        print(f"❌ Initialization failed: {e}")
        return False


async def test_agent_integration():
    """Test that the learning system integrates with the main agent"""
    print("\n🧪 Testing Agent Integration")
    print("=" * 50)

    try:
        from agent.core.agent import AIAgent

        # Initialize agent
        agent = AIAgent("config/smart_routing_config.json")
        print("✅ AI Agent initialized successfully")

        # Check if learning systems are available
        if hasattr(agent, "automated_learner") and agent.automated_learner:
            print("✅ Automated Learner integrated with agent")
        else:
            print("❌ Automated Learner not integrated with agent")
            return False

        if hasattr(agent, "adaptive_learning") and agent.adaptive_learning:
            print("✅ Adaptive Learning System integrated with agent")
        else:
            print("❌ Adaptive Learning System not integrated with agent")
            return False

        if hasattr(agent, "meta_learning_optimizer") and agent.meta_learning_optimizer:
            print("✅ Meta Learning Optimizer integrated with agent")
        else:
            print("❌ Meta Learning Optimizer not integrated with agent")
            return False

        return True
    except Exception as e:
        print(f"❌ Agent integration failed: {e}")
        return False


async def test_learning_functionality():
    """Test learning functionality through the agent"""
    print("\n🧪 Testing Learning Functionality")
    print("=" * 50)

    try:
        from agent.core.agent import AIAgent

        # Initialize agent
        agent = AIAgent("config/smart_routing_config.json")

        # Test learning summary
        result = await agent.get_learning_summary({})
        if result.get("success"):
            print("✅ Learning summary retrieved successfully")
            print(f"   Summary: {result.get('summary', {})}")
        else:
            print(f"❌ Learning summary failed: {result.get('error')}")
            return False

        # Test learning recommendations
        result = await agent.get_learning_recommendations({})
        if result.get("success"):
            print("✅ Learning recommendations retrieved successfully")
            recommendations = result.get("recommendations", [])
            print(f"   Found {len(recommendations)} recommendations")
        else:
            print(f"❌ Learning recommendations failed: {result.get('error')}")
            return False

        # Test recording learning event
        result = await agent.record_learning_event(
            {
                "event_type": "test_interaction",
                "user_id": "test_user",
                "session_id": "test_session",
                "context": {"test": True},
                "data": {"test_data": "value"},
                "outcome": "success",
                "feedback_score": 1.0,
            }
        )
        if result.get("success"):
            print("✅ Learning event recorded successfully")
        else:
            print(f"❌ Learning event recording failed: {result.get('error')}")
            return False

        # Test learning code pattern
        result = await agent.learn_code_pattern(
            {
                "code": "def test_function():\n    return 'test'",
                "language": "python",
                "context": "test context",
                "outcome": "success",
                "feedback_score": 1.0,
            }
        )
        if result.get("success"):
            print("✅ Code pattern learning successful")
        else:
            print(f"❌ Code pattern learning failed: {result.get('error')}")
            return False

        # Test learning user preference
        result = await agent.learn_user_preference(
            {
                "user_id": "test_user",
                "preference_type": "coding_style",
                "preference_key": "indentation",
                "preference_value": "spaces",
                "confidence": 0.9,
            }
        )
        if result.get("success"):
            print("✅ User preference learning successful")
        else:
            print(f"❌ User preference learning failed: {result.get('error')}")
            return False

        # Test learning performance insight
        result = await agent.learn_performance_insight(
            {
                "component": "code_generator",
                "metric": "response_time",
                "value": 1500.0,
                "threshold": 2000.0,
                "trend": "improving",
            }
        )
        if result.get("success"):
            print("✅ Performance insight learning successful")
        else:
            print(f"❌ Performance insight learning failed: {result.get('error')}")
            return False

        return True
    except Exception as e:
        print(f"❌ Learning functionality test failed: {e}")
        return False


async def test_api_integration():
    """Test API integration for learning endpoints"""
    print("\n🧪 Testing API Integration")
    print("=" * 50)

    try:
        import subprocess
        import time

        import requests

        # Use existing API server (assuming it's running on port 8000)
        print("🔗 Using existing API server on port 8000...")

        # Wait a moment for any existing requests to complete
        time.sleep(1)

        # Test learning summary endpoint
        response = requests.get(
            "http://127.0.0.1:8000/api/v1/advanced-learning/learning/summary"
        )
        if response.status_code == 200:
            print("✅ Learning summary API endpoint working")
        else:
            print(f"❌ Learning summary API failed: {response.status_code}")
            server_process.terminate()
            return False

        # Test learning recommendations endpoint
        response = requests.get(
            "http://127.0.0.1:8000/api/v1/advanced-learning/learning/recommendations"
        )
        if response.status_code == 200:
            print("✅ Learning recommendations API endpoint working")
        else:
            print(f"❌ Learning recommendations API failed: {response.status_code}")
            server_process.terminate()
            return False

        # Test learning event recording endpoint
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/advanced-learning/learning/event",
            params={
                "event_type": "api_test",
                "user_id": "test_user",
                "outcome": "success",
            },
        )
        if response.status_code == 200:
            print("✅ Learning event recording API endpoint working")
        else:
            print(f"❌ Learning event recording API failed: {response.status_code}")
            server_process.terminate()
            return False

        # No need to stop server since we're using existing one
        print("✅ API integration tests completed")

        return True
    except Exception as e:
        print(f"❌ API integration test failed: {e}")
        return False


async def main():
    """Run all integration tests"""
    print("🚀 Advanced Learning Integration Test Suite")
    print("=" * 60)

    tests = [
        ("Learning System Imports", test_learning_system_imports),
        ("Learning System Initialization", test_learning_system_initialization),
        ("Agent Integration", test_agent_integration),
        ("Learning Functionality", test_learning_functionality),
        ("API Integration", test_api_integration),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 40)

        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()

            results.append((test_name, result))

            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")

        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))

    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 40)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")

    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Advanced Learning System is fully integrated.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the integration.")
        return False


if __name__ == "__main__":
    asyncio.run(main())
