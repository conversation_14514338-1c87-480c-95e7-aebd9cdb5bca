#!/bin/bash

# Build script for site containers with conditional nginx.conf handling

set -e

SITE_NAME=${1:-"test-site"}
IMAGE_NAME=${2:-"site-${SITE_NAME}"}
TAG=${3:-"latest"}

echo "Building container for site: ${SITE_NAME}"

# Create temporary directory for build context
BUILD_DIR="/tmp/docker-build-${SITE_NAME}"
mkdir -p "${BUILD_DIR}"

# Copy site files
if [ -d "sites/${SITE_NAME}" ]; then
    echo "Copying site files from sites/${SITE_NAME}"
    cp -r "sites/${SITE_NAME}" "${BUILD_DIR}/site"
else
    echo "Warning: sites/${SITE_NAME} not found, creating empty site"
    mkdir -p "${BUILD_DIR}/site"
fi

# Handle nginx.conf conditionally
if [ -f "sites/${SITE_NAME}/nginx.conf" ]; then
    echo "Using custom nginx.conf from sites/${SITE_NAME}/nginx.conf"
    cp "sites/${SITE_NAME}/nginx.conf" "${BUILD_DIR}/default.conf"
else
    echo "No custom nginx.conf found, using default nginx configuration"
    # Create a basic default nginx.conf
    cat > "${BUILD_DIR}/default.conf" << 'EOF'
server {
    listen 80;
    server_name localhost;

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html;
    }

    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF
fi

# Create Dockerfile in build context
cat > "${BUILD_DIR}/Dockerfile" << 'EOF'
FROM nginx:alpine

# Copy site files
COPY site/ /usr/share/nginx/html/

# Copy nginx configuration
COPY default.conf /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/health || exit 1

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
EOF

# Build the Docker image
echo "Building Docker image: ${IMAGE_NAME}:${TAG}"
docker build -t "${IMAGE_NAME}:${TAG}" "${BUILD_DIR}"

# Clean up build context
rm -rf "${BUILD_DIR}"

echo "Successfully built ${IMAGE_NAME}:${TAG}"
echo "To run the container:"
echo "  docker run -d -p 8080:80 --name ${SITE_NAME}-container ${IMAGE_NAME}:${TAG}"
echo "  Then visit: http://localhost:8080"
