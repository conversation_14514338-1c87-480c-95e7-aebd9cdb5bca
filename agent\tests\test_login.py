#!/usr/bin/env python3
"""
Test login functionality for the IDE interface
"""

import json

import requests


def test_login():
    """Test the login endpoint"""
    url = "http://127.0.0.1:8000/api/v1/auth/login"
    data = {"username": "admin", "password": "admin123"}

    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")

        if response.status_code == 200:
            print("✅ Login successful!")
            return True
        else:
            print("❌ Login failed!")
            return False

    except Exception as e:
        print(f"❌ Error: {e}")
        return False


def test_health():
    """Test the health endpoint"""
    url = "http://127.0.0.1:8000/health"

    try:
        response = requests.get(url)
        print(f"Health Status Code: {response.status_code}")
        print(f"Health Response: {response.text}")
        return response.status_code == 200
    except Exception as e:
        print(f"Health check error: {e}")
        return False


if __name__ == "__main__":
    print("🧪 Testing API Server...")
    print("=" * 40)

    # Test health first
    if test_health():
        print("\n✅ Health check passed")

        # Test login
        print("\n🔐 Testing login...")
        test_login()
    else:
        print("\n❌ Health check failed - server may not be running")
