#!/usr/bin/env python3
"""
Test script for Validation Service container.
Tests all major functionality including health checks, validation jobs, and API endpoints.
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

import httpx
import pytest

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

VALIDATION_HOST = os.getenv("VALIDATION_SERVICE_HOST", "http://localhost")
VALIDATION_PORT = os.getenv("VALIDATION_PORT", "8004")
BASE_URL = f"{VALIDATION_HOST}:{VALIDATION_PORT}"


class ValidationServiceTester:
    """Test suite for Validation Service"""

    def __init__(self):
        self.client = httpx.AsyncClient(timeout=30)
        self.test_results = []

    async def test_health_check(self) -> Dict[str, Any]:
        """Test health check endpoint"""
        try:
            r = await self.client.get(f"{BASE_URL}/health")
            r.raise_for_status()
            data = r.json()

            assert data["status"] == "healthy"
            assert data["service"] == "validation"
            assert "timestamp" in data

            return {"test": "health_check", "status": "PASS", "data": data}
        except Exception as e:
            return {"test": "health_check", "status": "FAIL", "error": str(e)}

    async def test_status_endpoint(self) -> Dict[str, Any]:
        """Test status endpoint"""
        try:
            r = await self.client.get(f"{BASE_URL}/status")
            r.raise_for_status()
            data = r.json()

            assert data["status"] == "running"
            assert data["service"] == "validation"
            assert "active_jobs" in data

            return {"test": "status_endpoint", "status": "PASS", "data": data}
        except Exception as e:
            return {"test": "status_endpoint", "status": "FAIL", "error": str(e)}

    async def test_validation_rules(self) -> Dict[str, Any]:
        """Test validation rules endpoint"""
        try:
            r = await self.client.get(f"{BASE_URL}/rules")
            r.raise_for_status()
            rules = r.json()

            assert isinstance(rules, list)
            assert len(rules) > 0

            # Check for required rules
            rule_names = [rule["name"] for rule in rules]
            required_rules = [
                "file_system",
                "configuration",
                "dependencies",
                "security",
                "performance",
            ]

            for required_rule in required_rules:
                assert required_rule in rule_names

            return {"test": "validation_rules", "status": "PASS", "data": rules}
        except Exception as e:
            return {"test": "validation_rules", "status": "FAIL", "error": str(e)}

    async def test_start_validation_job(self) -> Dict[str, Any]:
        """Test starting a validation job"""
        try:
            payload = {
                "validation_type": "configuration",
                "detailed": True,
                "rules": [],
            }

            r = await self.client.post(f"{BASE_URL}/start", json=payload)
            r.raise_for_status()
            data = r.json()

            assert data["success"] == True
            assert "job_id" in data
            assert data["status"] == "started"

            return {"test": "start_validation_job", "status": "PASS", "data": data}
        except Exception as e:
            return {"test": "start_validation_job", "status": "FAIL", "error": str(e)}

    async def test_validation_progress(self, job_id: str) -> Dict[str, Any]:
        """Test validation progress endpoint"""
        try:
            r = await self.client.get(f"{BASE_URL}/progress/{job_id}")
            r.raise_for_status()
            data = r.json()

            assert data["job_id"] == job_id
            assert "status" in data
            assert "progress" in data

            return {"test": "validation_progress", "status": "PASS", "data": data}
        except Exception as e:
            return {"test": "validation_progress", "status": "FAIL", "error": str(e)}

    async def test_validation_results(self, job_id: str) -> Dict[str, Any]:
        """Test validation results endpoint"""
        try:
            r = await self.client.get(f"{BASE_URL}/results/{job_id}")
            if r.status_code == 400:
                # Job might not be completed yet
                return {
                    "test": "validation_results",
                    "status": "SKIP",
                    "message": "Job not completed yet",
                }

            r.raise_for_status()
            data = r.json()

            assert data["job_id"] == job_id
            assert "results" in data

            return {"test": "validation_results", "status": "PASS", "data": data}
        except Exception as e:
            return {"test": "validation_results", "status": "FAIL", "error": str(e)}

    async def test_quality_metrics(self) -> Dict[str, Any]:
        """Test quality metrics endpoint"""
        try:
            r = await self.client.get(f"{BASE_URL}/metrics")
            r.raise_for_status()
            data = r.json()

            assert "total_jobs" in data
            assert "completed_jobs" in data
            assert "failed_jobs" in data
            assert "success_rate" in data

            return {"test": "quality_metrics", "status": "PASS", "data": data}
        except Exception as e:
            return {"test": "quality_metrics", "status": "FAIL", "error": str(e)}

    async def test_stop_validation_job(self, job_id: str) -> Dict[str, Any]:
        """Test stopping a validation job"""
        try:
            r = await self.client.post(f"{BASE_URL}/stop/{job_id}")
            r.raise_for_status()
            data = r.json()

            assert data["success"] == True
            assert data["job_id"] == job_id
            assert data["status"] == "stopped"

            return {"test": "stop_validation_job", "status": "PASS", "data": data}
        except Exception as e:
            return {"test": "stop_validation_job", "status": "FAIL", "error": str(e)}

    async def test_validation_site(self) -> Dict[str, Any]:
        """Test site validation"""
        try:
            payload = {
                "site_path": "/tmp/test-site",
                "validation_type": "full",
                "detailed": False,
                "rules": [],
            }

            r = await self.client.post(f"{BASE_URL}/start", json=payload)
            r.raise_for_status()
            data = r.json()

            assert data["success"] == True
            assert "job_id" in data

            return {"test": "validation_site", "status": "PASS", "data": data}
        except Exception as e:
            return {"test": "validation_site", "status": "FAIL", "error": str(e)}

    async def run_all_tests(self) -> List[Dict[str, Any]]:
        """Run all tests"""
        print("🚀 Starting Validation Service tests...")

        # Test 1: Health check
        result = await self.test_health_check()
        self.test_results.append(result)
        print(f"✅ Health check: {result['status']}")

        # Test 2: Status endpoint
        result = await self.test_status_endpoint()
        self.test_results.append(result)
        print(f"✅ Status endpoint: {result['status']}")

        # Test 3: Validation rules
        result = await self.test_validation_rules()
        self.test_results.append(result)
        print(f"✅ Validation rules: {result['status']}")

        # Test 4: Start validation job
        result = await self.test_start_validation_job()
        self.test_results.append(result)
        print(f"✅ Start validation job: {result['status']}")

        if result["status"] == "PASS":
            job_id = result["data"]["job_id"]

            # Test 5: Validation progress
            result = await self.test_validation_progress(job_id)
            self.test_results.append(result)
            print(f"✅ Validation progress: {result['status']}")

            # Test 6: Validation results (might be skipped if job not completed)
            result = await self.test_validation_results(job_id)
            self.test_results.append(result)
            print(f"✅ Validation results: {result['status']}")

            # Test 7: Stop validation job
            result = await self.test_stop_validation_job(job_id)
            self.test_results.append(result)
            print(f"✅ Stop validation job: {result['status']}")

        # Test 8: Quality metrics
        result = await self.test_quality_metrics()
        self.test_results.append(result)
        print(f"✅ Quality metrics: {result['status']}")

        # Test 9: Site validation
        result = await self.test_validation_site()
        self.test_results.append(result)
        print(f"✅ Site validation: {result['status']}")

        return self.test_results

    async def close(self):
        """Close the client"""
        await self.client.aclose()

    def generate_report(self) -> Dict[str, Any]:
        """Generate test report"""
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["status"] == "PASS"])
        failed_tests = len([r for r in self.test_results if r["status"] == "FAIL"])
        skipped_tests = len([r for r in self.test_results if r["status"] == "SKIP"])

        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        return {
            "timestamp": datetime.now().isoformat(),
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "skipped_tests": skipped_tests,
            "success_rate": success_rate,
            "results": self.test_results,
        }


async def main():
    """Main test function"""
    tester = ValidationServiceTester()

    try:
        results = await tester.run_all_tests()
        report = tester.generate_report()

        print("\n" + "=" * 50)
        print("📊 VALIDATION SERVICE TEST REPORT")
        print("=" * 50)
        print(f"Total Tests: {report['total_tests']}")
        print(f"Passed: {report['passed_tests']}")
        print(f"Failed: {report['failed_tests']}")
        print(f"Skipped: {report['skipped_tests']}")
        print(f"Success Rate: {report['success_rate']:.1f}%")
        print("=" * 50)

        # Save report
        report_path = Path("test_reports/validation_service_test_report.json")
        report_path.parent.mkdir(exist_ok=True)

        with open(report_path, "w") as f:
            json.dump(report, f, indent=2)

        print(f"📄 Test report saved to: {report_path}")

        # Exit with appropriate code
        if report["failed_tests"] > 0:
            print("❌ Some tests failed!")
            sys.exit(1)
        else:
            print("🎉 All tests passed!")
            sys.exit(0)

    except Exception as e:
        print(f"❌ Test execution failed: {e}")
        sys.exit(1)
    finally:
        await tester.close()


if __name__ == "__main__":
    asyncio.run(main())
