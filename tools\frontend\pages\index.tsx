import React from 'react';
import Link from 'next/link';
import { UploadPage } from './UploadPage';

export default function HomePage() {
  return (
    <div className="home-page">
      <nav className="main-nav">
        <div className="nav-container">
          <div className="nav-brand">
            <h1>🤖 AI Coding Agent</h1>
          </div>
          <div className="nav-links">
            <Link href="/" className="nav-link active">
              🏠 Home
            </Link>
            <Link href="/upload" className="nav-link">
              📤 Import Project
            </Link>
            <Link href="/ide" className="nav-link">
              💻 IDE
            </Link>
            <Link href="/sites" className="nav-link">
              📁 Sites
            </Link>
          </div>
        </div>
      </nav>

      <main className="main-content">
        <div className="hero-section">
          <h2>Welcome to AI Coding Agent</h2>
          <p>Your intelligent development environment with AI-powered assistance</p>

          <div className="hero-actions">
            <Link href="/upload" className="cta-button primary">
              📤 Import Web Project
            </Link>
            <Link href="/ide" className="cta-button secondary">
              💻 Open IDE
            </Link>
          </div>
        </div>

        <div className="features-grid">
          <div className="feature-card">
            <div className="feature-icon">🚀</div>
            <h3>AI-Powered Development</h3>
            <p>Get intelligent code suggestions and assistance from local AI models</p>
          </div>

          <div className="feature-card">
            <div className="feature-icon">📤</div>
            <h3>Project Import</h3>
            <p>Import external web projects with automatic framework detection and security scanning</p>
          </div>

          <div className="feature-card">
            <div className="feature-icon">🔒</div>
            <h3>Safe & Secure</h3>
            <p>All uploads are scanned for security issues and validated before import</p>
          </div>

          <div className="feature-card">
            <div className="feature-icon">⚡</div>
            <h3>Fast & Efficient</h3>
            <p>Optimized for performance with local AI models and efficient file handling</p>
          </div>
        </div>

        <div className="quick-start">
          <h3>Quick Start</h3>
          <div className="steps">
            <div className="step">
              <div className="step-number">1</div>
              <div className="step-content">
                <h4>Import Your Project</h4>
                <p>Upload a web project folder or drag & drop files</p>
              </div>
            </div>

            <div className="step">
              <div className="step-number">2</div>
              <div className="step-content">
                <h4>Review & Validate</h4>
                <p>Check security analysis and framework detection</p>
              </div>
            </div>

            <div className="step">
              <div className="step-number">3</div>
              <div className="step-content">
                <h4>Start Coding</h4>
                <p>Open in the IDE and start developing with AI assistance</p>
              </div>
            </div>
          </div>
        </div>
      </main>

      <style jsx>{`
        .home-page {
          min-height: 100vh;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }

        .main-nav {
          background: rgba(255, 255, 255, 0.1);
          backdrop-filter: blur(10px);
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);
          padding: 1rem 0;
        }

        .nav-container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 2rem;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .nav-brand h1 {
          margin: 0;
          color: white;
          font-size: 1.5rem;
          font-weight: 700;
        }

        .nav-links {
          display: flex;
          gap: 2rem;
        }

        .nav-link {
          color: white;
          text-decoration: none;
          padding: 0.5rem 1rem;
          border-radius: 6px;
          transition: all 0.3s ease;
        }

        .nav-link:hover {
          background: rgba(255, 255, 255, 0.2);
        }

        .nav-link.active {
          background: rgba(255, 255, 255, 0.3);
        }

        .main-content {
          max-width: 1200px;
          margin: 0 auto;
          padding: 4rem 2rem;
        }

        .hero-section {
          text-align: center;
          color: white;
          margin-bottom: 4rem;
        }

        .hero-section h2 {
          font-size: 3rem;
          margin: 0 0 1rem 0;
          font-weight: 700;
        }

        .hero-section p {
          font-size: 1.25rem;
          margin: 0 0 2rem 0;
          opacity: 0.9;
        }

        .hero-actions {
          display: flex;
          gap: 1rem;
          justify-content: center;
          flex-wrap: wrap;
        }

        .cta-button {
          padding: 1rem 2rem;
          border-radius: 8px;
          text-decoration: none;
          font-weight: 600;
          transition: all 0.3s ease;
        }

        .cta-button.primary {
          background: #28a745;
          color: white;
        }

        .cta-button.primary:hover {
          background: #218838;
          transform: translateY(-2px);
        }

        .cta-button.secondary {
          background: rgba(255, 255, 255, 0.2);
          color: white;
          border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .cta-button.secondary:hover {
          background: rgba(255, 255, 255, 0.3);
          transform: translateY(-2px);
        }

        .features-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
          gap: 2rem;
          margin-bottom: 4rem;
        }

        .feature-card {
          background: white;
          padding: 2rem;
          border-radius: 12px;
          text-align: center;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          transition: transform 0.3s ease;
        }

        .feature-card:hover {
          transform: translateY(-4px);
        }

        .feature-icon {
          font-size: 3rem;
          margin-bottom: 1rem;
        }

        .feature-card h3 {
          margin: 0 0 1rem 0;
          color: #333;
          font-size: 1.5rem;
        }

        .feature-card p {
          margin: 0;
          color: #666;
          line-height: 1.6;
        }

        .quick-start {
          background: white;
          padding: 3rem;
          border-radius: 12px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .quick-start h3 {
          text-align: center;
          margin: 0 0 2rem 0;
          color: #333;
          font-size: 2rem;
        }

        .steps {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
          gap: 2rem;
        }

        .step {
          display: flex;
          align-items: flex-start;
          gap: 1rem;
        }

        .step-number {
          background: #007bff;
          color: white;
          width: 2.5rem;
          height: 2.5rem;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 700;
          flex-shrink: 0;
        }

        .step-content h4 {
          margin: 0 0 0.5rem 0;
          color: #333;
        }

        .step-content p {
          margin: 0;
          color: #666;
          line-height: 1.5;
        }

        @media (max-width: 768px) {
          .nav-container {
            flex-direction: column;
            gap: 1rem;
          }

          .nav-links {
            flex-wrap: wrap;
            justify-content: center;
          }

          .hero-section h2 {
            font-size: 2rem;
          }

          .hero-section p {
            font-size: 1rem;
          }

          .hero-actions {
            flex-direction: column;
            align-items: center;
          }

          .cta-button {
            width: 100%;
            max-width: 300px;
            text-align: center;
          }

          .features-grid {
            grid-template-columns: 1fr;
          }

          .steps {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
}
