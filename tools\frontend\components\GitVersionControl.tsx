import React, { useState, useEffect } from 'react';
import { api } from '@/lib/api';

interface GitVersionControlProps {
  siteName: string;
  onClose?: () => void;
}

interface GitCommit {
  hash: string;
  author: string;
  date: string;
  message: string;
  files: string[];
  additions: number;
  deletions: number;
}

interface GitStatus {
  branch: string;
  isClean: boolean;
  stagedFiles: string[];
  unstagedFiles: string[];
  untrackedFiles: string[];
  lastCommit?: GitCommit;
}

interface FileDiff {
  file: string;
  status: 'modified' | 'added' | 'deleted' | 'renamed';
  additions: number;
  deletions: number;
  diff: string;
}

const GitVersionControl: React.FC<GitVersionControlProps> = ({ siteName, onClose }) => {
  const [gitStatus, setGitStatus] = useState<GitStatus | null>(null);
  const [commits, setCommits] = useState<GitCommit[]>([]);
  const [selectedCommit, setSelectedCommit] = useState<string>('');
  const [commitDiff, setCommitDiff] = useState<FileDiff[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isCommitting, setIsCommitting] = useState(false);
  const [commitMessage, setCommitMessage] = useState('');
  const [selectedFiles, setSelectedFiles] = useState<string[]>([]);

  // Load Git status and history
  useEffect(() => {
    const loadGitInfo = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const [statusData, historyData] = await Promise.all([
          api.getGitStatus(siteName),
          api.getGitHistory(siteName)
        ]);

        if (statusData.status === 'success' && historyData.status === 'success') {
          setGitStatus(statusData.status_info);
          setCommits(historyData.commits || []);
        } else {
          throw new Error('Failed to load Git information');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    };

    if (siteName) {
      loadGitInfo();
    }
  }, [siteName]);

  // Load commit diff
  useEffect(() => {
    const loadCommitDiff = async () => {
      if (!selectedCommit) {
        setCommitDiff([]);
        return;
      }

      try {
        const data = await api.getGitCommitDiff(siteName, selectedCommit);

        if (data.status === 'success') {
          setCommitDiff(data.diff || []);
        }
      } catch (err) {
        console.error('Failed to load commit diff:', err);
      }
    };

    loadCommitDiff();
  }, [siteName, selectedCommit]);

  // Stage files
  const handleStageFiles = async (files: string[]) => {
    try {
      await api.stageGitFiles(siteName, files);
      // Refresh Git status
      window.location.reload();
    } catch (err) {
      console.error('Failed to stage files:', err);
    }
  };

  // Unstage files
  const handleUnstageFiles = async (files: string[]) => {
    try {
      await api.unstageGitFiles(siteName, files);
      // Refresh Git status
      window.location.reload();
    } catch (err) {
      console.error('Failed to unstage files:', err);
    }
  };

  // Commit changes
  const handleCommit = async () => {
    if (!commitMessage.trim()) return;

    try {
      setIsCommitting(true);

      const data = await api.commitGitChanges(siteName, commitMessage.trim());

      if (data.status === 'success') {
        setCommitMessage('');
        setSelectedFiles([]);
        // Refresh Git info
        window.location.reload();
      }
    } catch (err) {
      console.error('Failed to commit:', err);
    } finally {
      setIsCommitting(false);
    }
  };

  // Toggle file selection
  const toggleFileSelection = (file: string) => {
    setSelectedFiles(prev =>
      prev.includes(file)
        ? prev.filter(f => f !== file)
        : [...prev, file]
    );
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'modified': return 'text-yellow-600 bg-yellow-100';
      case 'added': return 'text-green-600 bg-green-100';
      case 'deleted': return 'text-red-600 bg-red-100';
      case 'renamed': return 'text-blue-600 bg-blue-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2">Loading Git information...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center">
          <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          <span className="text-red-800 font-medium">Git Error</span>
        </div>
        <p className="text-red-600 mt-1">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Git Header */}
      <div className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="flex items-center">
            <svg className="w-4 h-4 text-gray-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
            </svg>
            <span className="font-medium text-gray-700">Git Version Control</span>
          </div>

          {gitStatus && (
            <>
              <span className="text-sm text-gray-600">
                Branch: {gitStatus.branch}
              </span>
              <span className={`px-2 py-1 text-xs rounded ${
                gitStatus.isClean ? 'text-green-600 bg-green-100' : 'text-yellow-600 bg-yellow-100'
              }`}>
                {gitStatus.isClean ? 'Clean' : 'Modified'}
              </span>
            </>
          )}
        </div>

        {onClose && (
          <button
            onClick={onClose}
            className="p-1 text-gray-400 hover:text-gray-600"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        )}
      </div>

      <div className="flex-1 min-h-0 flex">
        {/* Left Panel - Status and Commits */}
        <div className="w-1/2 border-r border-gray-200 flex flex-col">
          {/* Git Status */}
          {gitStatus && (
            <div className="p-3 border-b border-gray-200">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Working Directory</h3>

              {/* Staged Files */}
              {gitStatus.stagedFiles.length > 0 && (
                <div className="mb-3">
                  <h4 className="text-xs font-medium text-green-600 mb-1">Staged Files</h4>
                  <div className="space-y-1">
                    {gitStatus.stagedFiles.map((file) => (
                      <div key={file} className="flex items-center justify-between text-xs">
                        <span className="text-gray-700">{file}</span>
                        <button
                          onClick={() => handleUnstageFiles([file])}
                          className="text-red-500 hover:text-red-700"
                        >
                          Unstage
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Unstaged Files */}
              {gitStatus.unstagedFiles.length > 0 && (
                <div className="mb-3">
                  <h4 className="text-xs font-medium text-yellow-600 mb-1">Modified Files</h4>
                  <div className="space-y-1">
                    {gitStatus.unstagedFiles.map((file) => (
                      <div key={file} className="flex items-center justify-between text-xs">
                        <span className="text-gray-700">{file}</span>
                        <button
                          onClick={() => handleStageFiles([file])}
                          className="text-green-500 hover:text-green-700"
                        >
                          Stage
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Untracked Files */}
              {gitStatus.untrackedFiles.length > 0 && (
                <div className="mb-3">
                  <h4 className="text-xs font-medium text-blue-600 mb-1">Untracked Files</h4>
                  <div className="space-y-1">
                    {gitStatus.untrackedFiles.map((file) => (
                      <div key={file} className="flex items-center justify-between text-xs">
                        <span className="text-gray-700">{file}</span>
                        <button
                          onClick={() => handleStageFiles([file])}
                          className="text-green-500 hover:text-green-700"
                        >
                          Add
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Commit Form */}
              {gitStatus.stagedFiles.length > 0 && (
                <div className="mt-3 pt-3 border-t border-gray-200">
                  <textarea
                    value={commitMessage}
                    onChange={(e) => setCommitMessage(e.target.value)}
                    placeholder="Enter commit message..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
                    rows={3}
                  />
                  <button
                    onClick={handleCommit}
                    disabled={!commitMessage.trim() || isCommitting}
                    className={`mt-2 px-3 py-1 text-sm rounded ${
                      !commitMessage.trim() || isCommitting
                        ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                        : 'bg-green-500 text-white hover:bg-green-600'
                    }`}
                  >
                    {isCommitting ? 'Committing...' : 'Commit Changes'}
                  </button>
                </div>
              )}
            </div>
          )}

          {/* Commit History */}
          <div className="flex-1 overflow-y-auto">
            <div className="p-3">
              <h3 className="text-sm font-medium text-gray-700 mb-2">Commit History</h3>
              <div className="space-y-2">
                {commits.map((commit) => (
                  <div
                    key={commit.hash}
                    onClick={() => setSelectedCommit(commit.hash)}
                    className={`p-2 rounded cursor-pointer border ${
                      selectedCommit === commit.hash
                        ? 'border-blue-500 bg-blue-50'
                        : 'border-gray-200 hover:border-gray-300'
                    }`}
                  >
                    <div className="flex items-center justify-between mb-1">
                      <span className="text-xs font-mono text-gray-500">
                        {commit.hash.substring(0, 8)}
                      </span>
                      <span className="text-xs text-gray-500">
                        {new Date(commit.date).toLocaleDateString()}
                      </span>
                    </div>
                    <p className="text-sm font-medium text-gray-900 mb-1">
                      {commit.message}
                    </p>
                    <div className="flex items-center justify-between text-xs text-gray-500">
                      <span>{commit.author}</span>
                      <span>
                        +{commit.additions} -{commit.deletions}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>

        {/* Right Panel - Commit Diff */}
        <div className="w-1/2 flex flex-col">
          <div className="p-3 border-b border-gray-200">
            <h3 className="text-sm font-medium text-gray-700">
              {selectedCommit ? 'Commit Diff' : 'Select a commit to view changes'}
            </h3>
          </div>

          <div className="flex-1 overflow-y-auto">
            {selectedCommit && commitDiff.length > 0 ? (
              <div className="p-3 space-y-4">
                {commitDiff.map((fileDiff, index) => (
                  <div key={index} className="border border-gray-200 rounded">
                    <div className="flex items-center justify-between p-2 bg-gray-50 border-b border-gray-200">
                      <span className="text-sm font-medium text-gray-700">
                        {fileDiff.file}
                      </span>
                      <span className={`px-2 py-1 text-xs rounded ${getStatusColor(fileDiff.status)}`}>
                        {fileDiff.status}
                      </span>
                    </div>
                    <div className="p-2">
                      <div className="text-xs text-gray-500 mb-2">
                        +{fileDiff.additions} -{fileDiff.deletions}
                      </div>
                      <pre className="text-xs bg-gray-900 text-gray-300 p-2 rounded overflow-x-auto">
                        {fileDiff.diff}
                      </pre>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="flex items-center justify-center h-full text-gray-500">
                {selectedCommit ? 'No changes in this commit' : 'Select a commit to view changes'}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default GitVersionControl;
