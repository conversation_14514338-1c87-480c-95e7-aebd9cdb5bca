import asyncio
import json
import logging
import time
import uuid
from concurrent.futures import <PERSON>hr<PERSON><PERSON><PERSON><PERSON>xecutor, as_completed
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Union

logger = logging.getLogger(__name__)


class CollaborationPattern(Enum):
    """Types of collaboration patterns for agent coordination."""

    SEQUENTIAL = "sequential"
    PARALLEL = "parallel"
    PIPELINE = "pipeline"
    HIERARCHICAL = "hierarchical"
    COMPETITIVE = "competitive"


class TaskStatus(Enum):
    """Status of tasks in collaboration."""

    PENDING = "pending"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class CollaborationTask:
    """Represents a task within a collaboration."""

    id: str
    agent_id: str
    task_type: str
    task_data: Dict[str, Any]
    dependencies: List[str] = field(default_factory=list)
    status: TaskStatus = TaskStatus.PENDING
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    priority: int = 1

    def to_dict(self) -> Dict[str, Any]:
        """Convert task to dictionary."""
        return {
            "id": self.id,
            "agent_id": self.agent_id,
            "task_type": self.task_type,
            "task_data": self.task_data,
            "dependencies": self.dependencies,
            "status": self.status.value,
            "result": self.result,
            "error": self.error,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "priority": self.priority,
        }


@dataclass
class Collaboration:
    """Represents a collaboration between multiple agents."""

    id: str
    name: str
    pattern: CollaborationPattern
    tasks: List[CollaborationTask] = field(default_factory=list)
    shared_context: Dict[str, Any] = field(default_factory=dict)
    status: TaskStatus = TaskStatus.PENDING
    start_time: Optional[float] = None
    end_time: Optional[float] = None
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    max_parallel_tasks: int = 5
    timeout_seconds: int = 300

    def add_task(self, task: CollaborationTask) -> None:
        """Add a task to the collaboration."""
        self.tasks.append(task)
        logger.info(f"Added task {task.id} to collaboration {self.id}")

    def get_ready_tasks(self) -> List[CollaborationTask]:
        """Get tasks that are ready to execute (dependencies satisfied)."""
        completed_task_ids = {
            t.id for t in self.tasks if t.status == TaskStatus.COMPLETED
        }
        ready_tasks = []

        for task in self.tasks:
            if task.status == TaskStatus.PENDING:
                if all(dep_id in completed_task_ids for dep_id in task.dependencies):
                    ready_tasks.append(task)

        return sorted(ready_tasks, key=lambda t: t.priority, reverse=True)

    def get_task_by_id(self, task_id: str) -> Optional[CollaborationTask]:
        """Get task by ID."""
        for task in self.tasks:
            if task.id == task_id:
                return task
        return None

    def to_dict(self) -> Dict[str, Any]:
        """Convert collaboration to dictionary."""
        return {
            "id": self.id,
            "name": self.name,
            "pattern": self.pattern.value,
            "tasks": [task.to_dict() for task in self.tasks],
            "shared_context": self.shared_context,
            "status": self.status.value,
            "start_time": self.start_time,
            "end_time": self.end_time,
            "result": self.result,
            "error": self.error,
            "max_parallel_tasks": self.max_parallel_tasks,
            "timeout_seconds": self.timeout_seconds,
        }


class CollaborationManager:
    """Manages multi-agent collaborations with different execution patterns."""

    def __init__(self, max_workers: int = 10):
        self.max_workers = max_workers
        self.active_collaborations: Dict[str, Collaboration] = {}
        self.completed_collaborations: Dict[str, Collaboration] = {}
        self.agent_registry: Dict[str, Any] = {}
        self.executor = ThreadPoolExecutor(max_workers=max_workers)
        self.collaboration_history: List[Dict[str, Any]] = []

        logger.info(f"CollaborationManager initialized with {max_workers} workers")

    def register_agent(self, agent_id: str, agent_instance: Any) -> None:
        """Register an agent for collaboration."""
        self.agent_registry[agent_id] = agent_instance
        logger.info(f"Registered agent {agent_id} for collaboration")

    def create_collaboration(
        self,
        name: str,
        pattern: CollaborationPattern,
        max_parallel_tasks: int = 5,
        timeout_seconds: int = 300,
    ) -> Collaboration:
        """Create a new collaboration."""
        collaboration_id = str(uuid.uuid4())
        collaboration = Collaboration(
            id=collaboration_id,
            name=name,
            pattern=pattern,
            max_parallel_tasks=max_parallel_tasks,
            timeout_seconds=timeout_seconds,
        )

        self.active_collaborations[collaboration_id] = collaboration
        logger.info(
            f"Created collaboration {collaboration_id} with pattern {pattern.value}"
        )

        return collaboration

    def add_task_to_collaboration(
        self,
        collaboration_id: str,
        agent_id: str,
        task_type: str,
        task_data: Dict[str, Any],
        dependencies: Optional[List[str]] = None,
        priority: int = 1,
    ) -> str:
        """Add a task to an existing collaboration."""
        if collaboration_id not in self.active_collaborations:
            raise ValueError(f"Collaboration {collaboration_id} not found")

        if agent_id not in self.agent_registry:
            raise ValueError(f"Agent {agent_id} not registered")

        task_id = str(uuid.uuid4())
        task = CollaborationTask(
            id=task_id,
            agent_id=agent_id,
            task_type=task_type,
            task_data=task_data,
            dependencies=dependencies or [],
            priority=priority,
        )

        self.active_collaborations[collaboration_id].add_task(task)
        logger.info(f"Added task {task_id} to collaboration {collaboration_id}")

        return task_id

    async def execute_collaboration(self, collaboration_id: str) -> Dict[str, Any]:
        """Execute a collaboration with the specified pattern."""
        if collaboration_id not in self.active_collaborations:
            raise ValueError(f"Collaboration {collaboration_id} not found")

        collaboration = self.active_collaborations[collaboration_id]
        collaboration.status = TaskStatus.IN_PROGRESS
        collaboration.start_time = time.time()

        logger.info(
            f"Starting collaboration {collaboration_id} with pattern {collaboration.pattern.value}"
        )

        try:
            if collaboration.pattern == CollaborationPattern.SEQUENTIAL:
                result = await self._execute_sequential(collaboration)
            elif collaboration.pattern == CollaborationPattern.PARALLEL:
                result = await self._execute_parallel(collaboration)
            elif collaboration.pattern == CollaborationPattern.PIPELINE:
                result = await self._execute_pipeline(collaboration)
            elif collaboration.pattern == CollaborationPattern.HIERARCHICAL:
                result = await self._execute_hierarchical(collaboration)
            elif collaboration.pattern == CollaborationPattern.COMPETITIVE:
                result = await self._execute_competitive(collaboration)
            else:
                raise ValueError(
                    f"Unsupported collaboration pattern: {collaboration.pattern}"
                )

            collaboration.status = TaskStatus.COMPLETED
            collaboration.result = result
            collaboration.end_time = time.time()

            # Move to completed collaborations
            self.completed_collaborations[collaboration_id] = collaboration
            del self.active_collaborations[collaboration_id]

            # Add to history
            self.collaboration_history.append(
                {
                    "collaboration_id": collaboration_id,
                    "name": collaboration.name,
                    "pattern": collaboration.pattern.value,
                    "duration": collaboration.end_time - collaboration.start_time,
                    "task_count": len(collaboration.tasks),
                    "success": True,
                    "timestamp": collaboration.end_time,
                }
            )

            logger.info(f"Completed collaboration {collaboration_id} successfully")
            return result

        except Exception as e:
            collaboration.status = TaskStatus.FAILED
            collaboration.error = str(e)
            collaboration.end_time = time.time()

            self.collaboration_history.append(
                {
                    "collaboration_id": collaboration_id,
                    "name": collaboration.name,
                    "pattern": collaboration.pattern.value,
                    "duration": collaboration.end_time - collaboration.start_time,
                    "task_count": len(collaboration.tasks),
                    "success": False,
                    "error": str(e),
                    "timestamp": collaboration.end_time,
                }
            )

            logger.error(f"Collaboration {collaboration_id} failed: {e}")
            raise

    async def _execute_sequential(self, collaboration: Collaboration) -> Dict[str, Any]:
        """Execute tasks sequentially in dependency order."""
        results = {}

        while True:
            ready_tasks = collaboration.get_ready_tasks()
            if not ready_tasks:
                break

            # Execute one task at a time
            task = ready_tasks[0]
            result = await self._execute_task(task, collaboration.shared_context)
            results[task.id] = result

            # Update shared context with result
            collaboration.shared_context.update(result.get("context_updates", {}))

        return {
            "pattern": "sequential",
            "task_results": results,
            "shared_context": collaboration.shared_context,
        }

    async def _execute_parallel(self, collaboration: Collaboration) -> Dict[str, Any]:
        """Execute tasks in parallel where possible."""
        results = {}

        while True:
            ready_tasks = collaboration.get_ready_tasks()
            if not ready_tasks:
                break

            # Execute up to max_parallel_tasks at once
            batch_size = min(len(ready_tasks), collaboration.max_parallel_tasks)
            batch_tasks = ready_tasks[:batch_size]

            # Execute batch in parallel
            futures = []
            for task in batch_tasks:
                future = asyncio.create_task(
                    self._execute_task(task, collaboration.shared_context)
                )
                futures.append((task.id, future))

            # Wait for all tasks in batch to complete
            for task_id, future in futures:
                result = await future
                results[task_id] = result
                # Update shared context with result
                collaboration.shared_context.update(result.get("context_updates", {}))

        return {
            "pattern": "parallel",
            "task_results": results,
            "shared_context": collaboration.shared_context,
        }

    async def _execute_pipeline(self, collaboration: Collaboration) -> Dict[str, Any]:
        """Execute tasks in pipeline fashion (output of one feeds into next)."""
        results = {}
        pipeline_context = collaboration.shared_context.copy()

        # Sort tasks by dependencies to create pipeline order
        sorted_tasks = self._topological_sort(collaboration.tasks)

        for task in sorted_tasks:
            result = await self._execute_task(task, pipeline_context)
            results[task.id] = result

            # Pipeline: output becomes input for next task
            pipeline_context.update(result.get("output", {}))
            pipeline_context.update(result.get("context_updates", {}))

        return {
            "pattern": "pipeline",
            "task_results": results,
            "final_context": pipeline_context,
        }

    async def _execute_hierarchical(
        self, collaboration: Collaboration
    ) -> Dict[str, Any]:
        """Execute tasks in hierarchical order (parent tasks before children)."""
        results = {}

        # Group tasks by dependency level
        levels = self._group_by_dependency_level(collaboration.tasks)

        for level in sorted(levels.keys()):
            level_tasks = levels[level]
            level_results = {}

            # Execute all tasks at this level in parallel
            futures = []
            for task in level_tasks:
                future = asyncio.create_task(
                    self._execute_task(task, collaboration.shared_context)
                )
                futures.append((task.id, future))

            for task_id, future in futures:
                result = await future
                level_results[task_id] = result
                results[task_id] = result
                collaboration.shared_context.update(result.get("context_updates", {}))

        return {
            "pattern": "hierarchical",
            "task_results": results,
            "shared_context": collaboration.shared_context,
        }

    async def _execute_competitive(
        self, collaboration: Collaboration
    ) -> Dict[str, Any]:
        """Execute tasks competitively (first successful result wins)."""
        if not collaboration.tasks:
            return {"pattern": "competitive", "result": None}

        # Execute all tasks in parallel
        futures = []
        for task in collaboration.tasks:
            future = asyncio.create_task(
                self._execute_task(task, collaboration.shared_context)
            )
            futures.append((task.id, future))

        # Wait for first successful completion
        for completed_future in asyncio.as_completed([f for _, f in futures]):
            try:
                result = await completed_future
                # Cancel remaining tasks
                for _, future in futures:
                    if not future.done():
                        future.cancel()

                return {
                    "pattern": "competitive",
                    "winner_result": result,
                    "shared_context": collaboration.shared_context,
                }
            except Exception as e:
                logger.warning(f"Task failed in competitive execution: {e}")
                continue

        raise Exception("All tasks failed in competitive execution")

    async def _execute_task(
        self, task: CollaborationTask, shared_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Execute a single task."""
        task.status = TaskStatus.IN_PROGRESS
        task.start_time = time.time()

        logger.info(f"Executing task {task.id} on agent {task.agent_id}")

        try:
            agent = self.agent_registry[task.agent_id]

            # Prepare task data with shared context
            enhanced_task_data = {
                **task.task_data,
                "shared_context": shared_context,
                "task_id": task.id,
            }

            # Execute task on agent
            if hasattr(agent, "execute_task"):
                result = await agent.execute_task(task.task_type, enhanced_task_data)
            else:
                # Fallback for agents without execute_task method
                result = {
                    "success": True,
                    "message": f"Task {task.id} executed",
                    "output": {},
                }

            task.status = TaskStatus.COMPLETED
            task.result = result
            task.end_time = time.time()

            logger.info(f"Task {task.id} completed successfully")
            return result

        except Exception as e:
            task.status = TaskStatus.FAILED
            task.error = str(e)
            task.end_time = time.time()

            logger.error(f"Task {task.id} failed: {e}")
            raise

    def _topological_sort(
        self, tasks: List[CollaborationTask]
    ) -> List[CollaborationTask]:
        """Sort tasks in topological order based on dependencies."""
        # Simple topological sort implementation
        in_degree = {}
        graph = {}
        task_map = {task.id: task for task in tasks}

        # Initialize in-degree and graph
        for task in tasks:
            in_degree[task.id] = 0
            graph[task.id] = []

        # Build graph and calculate in-degrees
        for task in tasks:
            for dep_id in task.dependencies:
                if dep_id in graph:
                    graph[dep_id].append(task.id)
                    in_degree[task.id] += 1

        # Topological sort
        queue = [task_id for task_id in in_degree if in_degree[task_id] == 0]
        sorted_tasks = []

        while queue:
            current_id = queue.pop(0)
            sorted_tasks.append(task_map[current_id])

            for neighbor_id in graph[current_id]:
                in_degree[neighbor_id] -= 1
                if in_degree[neighbor_id] == 0:
                    queue.append(neighbor_id)

        return sorted_tasks

    def _group_by_dependency_level(
        self, tasks: List[CollaborationTask]
    ) -> Dict[int, List[CollaborationTask]]:
        """Group tasks by dependency level (0 = no dependencies, 1 = depends on level 0, etc.)."""
        levels = {}
        task_levels = {}
        task_map = {task.id: task for task in tasks}

        def get_task_level(task_id: str) -> int:
            if task_id in task_levels:
                return task_levels[task_id]

            task = task_map[task_id]
            if not task.dependencies:
                level = 0
            else:
                level = (
                    max(
                        get_task_level(dep_id)
                        for dep_id in task.dependencies
                        if dep_id in task_map
                    )
                    + 1
                )

            task_levels[task_id] = level
            return level

        for task in tasks:
            level = get_task_level(task.id)
            if level not in levels:
                levels[level] = []
            levels[level].append(task)

        return levels

    def get_collaboration_status(self, collaboration_id: str) -> Dict[str, Any]:
        """Get the status of a collaboration."""
        collaboration = self.active_collaborations.get(
            collaboration_id
        ) or self.completed_collaborations.get(collaboration_id)
        if not collaboration:
            return {"error": "Collaboration not found"}

        return collaboration.to_dict()

    def get_active_collaborations(self) -> List[Dict[str, Any]]:
        """Get all active collaborations."""
        return [collab.to_dict() for collab in self.active_collaborations.values()]

    def get_collaboration_history(self) -> List[Dict[str, Any]]:
        """Get collaboration history."""
        return self.collaboration_history.copy()

    def cancel_collaboration(self, collaboration_id: str) -> bool:
        """Cancel an active collaboration."""
        if collaboration_id not in self.active_collaborations:
            return False

        collaboration = self.active_collaborations[collaboration_id]
        collaboration.status = TaskStatus.CANCELLED
        collaboration.end_time = time.time()

        # Cancel all pending tasks
        for task in collaboration.tasks:
            if task.status == TaskStatus.PENDING:
                task.status = TaskStatus.CANCELLED

        del self.active_collaborations[collaboration_id]
        logger.info(f"Cancelled collaboration {collaboration_id}")
        return True

    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics for collaborations."""
        total_collaborations = len(self.collaboration_history)
        successful_collaborations = sum(
            1 for h in self.collaboration_history if h["success"]
        )

        if total_collaborations == 0:
            return {"total": 0, "success_rate": 0, "average_duration": 0}

        success_rate = successful_collaborations / total_collaborations
        average_duration = (
            sum(h["duration"] for h in self.collaboration_history)
            / total_collaborations
        )

        pattern_stats = {}
        for history in self.collaboration_history:
            pattern = history["pattern"]
            if pattern not in pattern_stats:
                pattern_stats[pattern] = {"count": 0, "success": 0, "avg_duration": 0}
            pattern_stats[pattern]["count"] += 1
            if history["success"]:
                pattern_stats[pattern]["success"] += 1
            pattern_stats[pattern]["avg_duration"] += history["duration"]

        for pattern in pattern_stats:
            stats = pattern_stats[pattern]
            stats["success_rate"] = stats["success"] / stats["count"]
            stats["avg_duration"] = stats["avg_duration"] / stats["count"]

        return {
            "total_collaborations": total_collaborations,
            "successful_collaborations": successful_collaborations,
            "success_rate": success_rate,
            "average_duration": average_duration,
            "pattern_statistics": pattern_stats,
            "active_collaborations": len(self.active_collaborations),
        }

    def cleanup(self) -> None:
        """Clean up resources."""
        self.executor.shutdown(wait=True)
        logger.info("CollaborationManager cleaned up")
