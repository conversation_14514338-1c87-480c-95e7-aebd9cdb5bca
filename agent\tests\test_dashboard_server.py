#!/usr/bin/env python3
"""
Test script for dashboard server
Starts the server and tests basic functionality
"""

import json
import logging
import subprocess
import sys
import time
from pathlib import Path

import requests

# Fix Windows console encoding for emoji support
if hasattr(sys.stdout, "reconfigure"):
    # on Windows, switch the console to UTF-8 output to support emojis
    sys.stdout.reconfigure(encoding="utf-8", errors="replace")

# Configure logging
logging.basicConfig(level=logging.INFO)

# Fix logging handler encoding for Windows
for handler in logging.root.handlers:
    if hasattr(handler, "stream") and hasattr(handler.stream, "reconfigure"):
        handler.stream.reconfigure(encoding="utf-8", errors="replace")

logger = logging.getLogger(__name__)


def start_server():
    """Start the dashboard server"""
    try:
        # Start server in background
        process = subprocess.Popen(
            ["python", "src/dashboard/minimal_api.py"],
            stdout=subprocess.PIPE,
            stderr=subprocess.PIPE,
        )

        logger.info("Server started, waiting for it to be ready...")
        time.sleep(3)  # Wait for server to start

        return process
    except Exception as e:
        logger.error(f"Failed to start server: {e}")
        return None


def test_server():
    """Test the server endpoints"""
    base_url = "http://127.0.0.1:8000"

    # Test health endpoint
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ Health check passed: {data}")
            return True
        else:
            logger.error(f"❌ Health check failed: {response.status_code}")
            return False
    except Exception as e:
        logger.error(f"❌ Health check error: {e}")
        return False


def test_api_endpoints():
    """Test API endpoints"""
    base_url = "http://127.0.0.1:8000"
    api_base = f"{base_url}/api/v1"

    # Test root endpoint
    try:
        response = requests.get(base_url, timeout=5)
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ Root endpoint: {data.get('message')}")
        else:
            logger.error(f"❌ Root endpoint failed: {response.status_code}")
    except Exception as e:
        logger.error(f"❌ Root endpoint error: {e}")

    # Test login
    try:
        login_data = {"username": "admin", "password": "admin123"}
        response = requests.post(f"{api_base}/auth/login", json=login_data, timeout=5)
        if response.status_code == 200:
            data = response.json()
            logger.info(f"✅ Login successful: {data.get('user', {}).get('username')}")

            # Test authenticated endpoints
            session = requests.Session()
            session.post(f"{api_base}/auth/login", json=login_data)

            # Test projects endpoint
            response = session.get(f"{api_base}/projects", timeout=5)
            if response.status_code == 200:
                projects = response.json()
                logger.info(f"✅ Projects endpoint: {len(projects)} projects")
            else:
                logger.error(f"❌ Projects endpoint failed: {response.status_code}")

        else:
            logger.error(f"❌ Login failed: {response.status_code}")
    except Exception as e:
        logger.error(f"❌ Login error: {e}")


def main():
    """Main function"""
    logger.info("🚀 Starting dashboard server test")

    # Start server
    server_process = start_server()
    if not server_process:
        logger.error("Failed to start server")
        return

    try:
        # Test server
        if test_server():
            logger.info("✅ Server is running and responding")
            test_api_endpoints()
        else:
            logger.error("❌ Server is not responding")
    finally:
        # Stop server
        if server_process:
            server_process.terminate()
            logger.info("Server stopped")


if __name__ == "__main__":
    main()
