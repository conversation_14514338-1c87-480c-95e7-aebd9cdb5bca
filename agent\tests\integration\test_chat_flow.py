#!/usr/bin/env python3
"""
Test script to verify chat flow behavior in ChatPanel
"""

import json
import time

import requests


def test_chat_flow_behavior():
    """Test the chat flow behavior including message history and loading states"""

    print("🧪 TESTING CHAT FLOW BEHAVIOR")
    print("=" * 60)

    # Test cases for different chat scenarios
    test_cases = [
        {
            "name": "Simple Message Flow",
            "prompt": "Hello, how are you?",
            "expected_flow": [
                "user_message_added",
                "loading_state_shown",
                "ai_response_received",
                "loading_state_cleared",
            ],
        },
        {
            "name": "Code Generation Request",
            "prompt": "Create a simple HTML page",
            "expected_flow": [
                "user_message_added",
                "loading_state_shown",
                "ai_response_received",
                "loading_state_cleared",
            ],
        },
        {
            "name": "Error Handling",
            "prompt": "test error scenario",
            "expected_flow": [
                "user_message_added",
                "loading_state_shown",
                "error_handled",
                "loading_state_cleared",
            ],
        },
    ]

    results = []

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {test_case['name']}")
        print(f"   Input: '{test_case['prompt']}'")

        try:
            # Simulate the chat flow by calling the API
            start_time = time.time()

            response = requests.post(
                "http://127.0.0.1:8000/api/v1/chat/test",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": test_case["prompt"],
                    "context": {
                        "currentProject": "Test Project",
                        "activeFiles": ["index.html"],
                        "recentActions": ["Created project"],
                        "conversationHistory": ["Previous message"],
                    },
                    "intent": {
                        "type": "create",
                        "confidence": 0.8,
                        "action": "test",
                        "target": "website",
                    },
                    "history": [
                        {"role": "user", "content": "Previous message"},
                        {"role": "assistant", "content": "Previous response"},
                    ],
                },
                timeout=15,
            )

            end_time = time.time()
            response_time = end_time - start_time

            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Response received in {response_time:.2f}s")

                # Check response structure
                response_text = data.get("response", "")
                if isinstance(response_text, dict):
                    response_text = response_text.get("content", "")

                print(f"   📝 Response: {response_text[:100]}...")
                print(f"   ⏱️  Response time: {response_time:.2f}s")

                # Verify flow behavior
                flow_verified = True
                if response_time > 0.1:  # Should have some processing time
                    print(f"   ✅ Processing time reasonable")
                else:
                    print(f"   ⚠️  Very fast response (may be cached)")

                results.append(
                    {
                        "test": test_case["name"],
                        "status": "PASS",
                        "response_time": response_time,
                        "flow_verified": flow_verified,
                    }
                )

            else:
                print(f"   ❌ HTTP {response.status_code}: {response.text}")
                results.append(
                    {
                        "test": test_case["name"],
                        "status": "FAIL",
                        "error": f"HTTP {response.status_code}",
                    }
                )

        except requests.exceptions.Timeout:
            print(f"   ⏰ Timeout - server not responding")
            results.append(
                {
                    "test": test_case["name"],
                    "status": "TIMEOUT",
                    "error": "Request timeout",
                }
            )
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            results.append(
                {"test": test_case["name"], "status": "ERROR", "error": str(e)}
            )

    # Summary
    print(f"\n" + "=" * 60)
    print("📊 CHAT FLOW TEST RESULTS")
    print("=" * 60)

    passed = 0
    failed = 0
    avg_response_time = 0

    for result in results:
        if result["status"] == "PASS":
            passed += 1
            avg_response_time += result.get("response_time", 0)
        else:
            failed += 1

        status_icon = "✅" if result["status"] == "PASS" else "❌"
        print(f"{status_icon} {result['test']}: {result['status']}")
        if result.get("response_time"):
            print(f"   Response time: {result['response_time']:.2f}s")

    if passed > 0:
        avg_response_time /= passed

    print(f"\n📈 SUMMARY:")
    print(f"   ✅ Passed: {passed}/{len(results)}")
    print(f"   ❌ Failed: {failed}/{len(results)}")
    print(f"   ⏱️  Average response time: {avg_response_time:.2f}s")

    if passed == len(results):
        print(f"\n🎉 ALL TESTS PASSED! Chat flow is working correctly.")
    else:
        print(f"\n⚠️  Some tests failed. Check the implementation.")

    return passed == len(results)


def test_message_history_structure():
    """Test the structure of message history"""

    print(f"\n🔧 TESTING MESSAGE HISTORY STRUCTURE")
    print("=" * 60)

    # Test message history structure
    test_history = [
        {
            "role": "user",
            "content": "Create a website",
            "timestamp": "2025-07-25T17:00:00.000Z",
        },
        {
            "role": "assistant",
            "content": "I'll help you create a website. What type of website would you like?",
            "timestamp": "2025-07-25T17:00:05.000Z",
        },
        {
            "role": "user",
            "content": "A portfolio website",
            "timestamp": "2025-07-25T17:00:10.000Z",
        },
    ]

    print("📋 Testing message history structure:")
    print(f"   ✅ History length: {len(test_history)} messages")

    for i, message in enumerate(test_history):
        print(f"   ✅ Message {i+1}: {message['role']} - {message['content'][:50]}...")

    # Test with API
    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/chat/test",
            headers={"Content-Type": "application/json"},
            json={
                "prompt": "Continue with the portfolio",
                "context": {"currentProject": "Portfolio Project"},
                "intent": {"type": "create"},
                "history": test_history,
            },
            timeout=10,
        )

        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Backend accepted history structure")

            response_text = data.get("response", "")
            if isinstance(response_text, dict):
                response_text = response_text.get("content", "")

            print(f"   📝 Response: {response_text}")

        else:
            print(f"   ❌ Backend rejected history: {response.status_code}")

    except Exception as e:
        print(f"   ❌ Error: {str(e)}")


def test_loading_state_simulation():
    """Simulate loading state behavior"""

    print(f"\n⏳ TESTING LOADING STATE SIMULATION")
    print("=" * 60)

    # Test different response times
    test_scenarios = [
        {"name": "Fast Response", "timeout": 2},
        {"name": "Medium Response", "timeout": 5},
        {"name": "Slow Response", "timeout": 10},
    ]

    for scenario in test_scenarios:
        print(f"\nTesting: {scenario['name']}")

        try:
            start_time = time.time()

            response = requests.post(
                "http://127.0.0.1:8000/api/v1/chat/test",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": f"Test {scenario['name']}",
                    "context": {"currentProject": "Test"},
                    "intent": {"type": "test"},
                    "history": [],
                },
                timeout=scenario["timeout"],
            )

            end_time = time.time()
            response_time = end_time - start_time

            if response.status_code == 200:
                print(f"   ✅ Response received in {response_time:.2f}s")

                if response_time < 1:
                    print(f"   ⚡ Very fast response")
                elif response_time < 3:
                    print(f"   🚀 Fast response")
                elif response_time < 8:
                    print(f"   🐌 Slow response")
                else:
                    print(f"   🐌 Very slow response")

            else:
                print(f"   ❌ HTTP {response.status_code}")

        except requests.exceptions.Timeout:
            print(f"   ⏰ Timeout after {scenario['timeout']}s")
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")


if __name__ == "__main__":
    print("🚀 Starting Chat Flow Verification")
    print("=" * 60)

    # Check if server is running
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running")
        else:
            print("❌ Backend server not responding properly")
            exit(1)
    except Exception as e:
        print(f"❌ Cannot connect to backend server: {e}")
        print("Please start the backend server first:")
        print(
            "  .\\.venv\\Scripts\\Activate.ps1 && python src/dashboard/minimal_api.py"
        )
        exit(1)

    # Run tests
    success = test_chat_flow_behavior()
    test_message_history_structure()
    test_loading_state_simulation()

    print(f"\n" + "=" * 60)
    if success:
        print("🎉 CHAT FLOW VERIFICATION COMPLETE - ALL TESTS PASSED!")
    else:
        print("⚠️  CHAT FLOW VERIFICATION COMPLETE - SOME ISSUES FOUND")
    print("=" * 60)
