# Compose Cleanup Report

This report summarizes backups and extractions performed on oversized docker-compose files.

| File | Backup | Placeholders | Extracted Artifacts | Env Files Added | Notes |
|---|---|---:|---|---|---|
| containers\docker-compose.yml | containers\docker-compose.yml.bak | 0 | - | - | - |
| containers\docker-compose.dev.yml | containers\docker-compose.dev.yml.bak | 0 | - | - | - |
| containers\docker-compose.prod.yml | containers\docker-compose.prod.yml.bak | 0 | - | - | - |
| containers\docker-compose.test.yml | containers\docker-compose.test.yml.bak | 0 | - | - | - |
| trend_monitoring\docker-compose.yml | trend_monitoring\docker-compose.yml.bak | 2 | containers\extracted\docker-compose_trend-monitor_ports.json, containers\extracted\docker-compose_trend-monitor-redis_ports.json | - | YAML-aware extraction completed |
