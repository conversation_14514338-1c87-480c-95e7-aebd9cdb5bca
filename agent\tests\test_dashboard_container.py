#!/usr/bin/env python3
"""
Dashboard Container Test Script
Tests the dashboard backend container implementation
"""

import asyncio
import json
import logging
import subprocess
import sys
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List

import aiohttp
import websockets

# Add project root to path
sys.path.append(str(Path(__file__).parent.parent))

logger = logging.getLogger(__name__)


class DashboardContainerTester:
    """Test dashboard container implementation"""

    def __init__(self):
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0

    def log_test(self, test_name: str, passed: bool, details: str = ""):
        """Log test result"""
        self.total_tests += 1
        if passed:
            self.passed_tests += 1
            status = "✅ PASSED"
        else:
            self.failed_tests += 1
            status = "❌ FAILED"

        print(f"  {status} {test_name}")
        if details:
            print(f"    {details}")

        self.test_results.append(
            {
                "test": test_name,
                "status": status,
                "success": passed,
                "message": details,
                "timestamp": datetime.now().isoformat(),
            }
        )

    def test_dockerfile_exists(self):
        """Test if Dockerfile.dashboard exists"""
        print("\n🔍 Testing Dockerfile.dashboard...")
        try:
            dockerfile_path = Path("containers/Dockerfile.dashboard")
            exists = dockerfile_path.exists()

            if exists:
                content = dockerfile_path.read_text()

                checks = [
                    (
                        "Multi-stage build",
                        "FROM python:3.11-slim as builder" in content,
                    ),
                    ("Runtime stage", "FROM python:3.11-slim as runtime" in content),
                    ("Non-root user", "useradd -r -g dashboard dashboard" in content),
                    ("Health check", "HEALTHCHECK" in content),
                    ("Port exposure", "EXPOSE 8080" in content),
                    ("Security", "USER dashboard" in content),
                    ("Environment variables", "ENV PYTHONPATH=/app" in content),
                ]

                all_checks_passed = True
                for check_name, check_result in checks:
                    self.log_test(f"Dockerfile {check_name}", check_result)
                    if not check_result:
                        all_checks_passed = False

                self.log_test(
                    "Dockerfile.dashboard Complete",
                    all_checks_passed,
                    "Multi-stage build with security, health checks, and proper configuration",
                )
            else:
                self.log_test(
                    "Dockerfile.dashboard Exists", False, "Dockerfile not found"
                )
        except Exception as e:
            self.log_test("Dockerfile.dashboard Exists", False, f"Error: {str(e)}")

    def test_docker_compose_integration(self):
        """Test docker-compose.yml integration"""
        print("\n🔍 Testing Docker Compose Integration...")

        compose_path = Path("containers/docker-compose.yml")
        if not compose_path.exists():
            self.log_test(
                "Docker Compose File", False, "containers/docker-compose.yml not found"
            )
            return

        with open(compose_path, "r") as f:
            content = f.read()

        checks = [
            ("Dashboard service defined", "dashboard:" in content),
            (
                "Dockerfile reference",
                "dockerfile: containers/Dockerfile.dashboard" in content,
            ),
            ("Port mapping", "8080:8080" in content),
            ("Health check", "healthcheck:" in content),
            ("Resource limits", "cpus: '1.0'" in content and "memory: 2G" in content),
            ("Volume mounts", "./data:/app/data" in content),
            ("Network configuration", "ai-coding-network" in content),
            ("Dependencies", "depends_on:" in content),
            ("Environment variables", "WEBSOCKET_ENABLED=true" in content),
        ]

        all_checks_passed = True
        for check_name, check_result in checks:
            self.log_test(f"Docker Compose {check_name}", check_result)
            if not check_result:
                all_checks_passed = False

        self.log_test(
            "Docker Compose Integration Complete",
            all_checks_passed,
            "Service properly configured with dependencies, resources, and networking",
        )

    def test_configuration_file(self):
        """Test dashboard Docker configuration file"""
        print("\n🔍 Testing Dashboard Configuration File...")

        config_path = Path("config/dashboard_docker_config.json")
        exists = config_path.exists()

        if exists:
            try:
                with open(config_path, "r") as f:
                    config = json.load(f)

                checks = [
                    ("Service configuration", "service" in config.get("dashboard", {})),
                    ("Resource limits", "resources" in config.get("dashboard", {})),
                    ("Volume configuration", "volumes" in config.get("dashboard", {})),
                    (
                        "Environment variables",
                        "environment" in config.get("dashboard", {}),
                    ),
                    ("Security settings", "security" in config.get("dashboard", {})),
                    (
                        "Monitoring configuration",
                        "monitoring" in config.get("dashboard", {}),
                    ),
                    ("WebSocket settings", "websocket" in config.get("dashboard", {})),
                    (
                        "Notifications settings",
                        "notifications" in config.get("dashboard", {}),
                    ),
                    ("API configuration", "api" in config.get("dashboard", {})),
                    ("CLI configuration", "cli" in config.get("dashboard", {})),
                ]

                all_checks_passed = True
                for check_name, check_result in checks:
                    self.log_test(f"Configuration {check_name}", check_result)
                    if not check_result:
                        all_checks_passed = False

                self.log_test(
                    "Dashboard Configuration Complete",
                    all_checks_passed,
                    "Comprehensive configuration with all required sections",
                )

            except json.JSONDecodeError as e:
                self.log_test("Configuration JSON Valid", False, f"Invalid JSON: {e}")
            except Exception as e:
                self.log_test("Configuration Loading", False, f"Error: {e}")
        else:
            self.log_test(
                "Configuration File Exists",
                False,
                "config/dashboard_docker_config.json not found",
            )

    def test_cli_commands(self):
        """Test CLI commands implementation"""
        print("\n🔍 Testing CLI Commands...")

        cli_path = Path("cli/dashboard_commands.py")
        exists = cli_path.exists()

        if exists:
            # Test CLI methods
            expected_methods = [
                "dashboard_status",
                "dashboard_summary",
                "dashboard_metrics",
                "dashboard_notifications",
                "dashboard_websocket_test",
                "dashboard_real_time_updates",
                "dashboard_config",
                "dashboard_export",
            ]

            all_methods_exist = True
            for method_name in expected_methods:
                # Check if method exists in file content
                content = cli_path.read_text()
                method_exists = f"async def {method_name}(" in content
                self.log_test(f"CLI Method: {method_name}", method_exists)
                if not method_exists:
                    all_methods_exist = False

            self.log_test(
                "CLI Commands Complete",
                all_methods_exist,
                "All 8 CLI methods implemented",
            )
        else:
            self.log_test(
                "CLI Commands File", False, "cli/dashboard_commands.py not found"
            )

    def test_api_routes(self):
        """Test API routes implementation"""
        print("\n🔍 Testing API Routes...")

        api_path = Path("api/dashboard_routes.py")
        exists = api_path.exists()

        if exists:
            content = api_path.read_text()

            # Test API endpoints
            expected_endpoints = [
                "/health",
                "/summary",
                "/metrics",
                "/notifications",
                "/real-time-status",
                "/config",
                "/export",
                "/ws",
            ]

            all_endpoints_exist = True
            for endpoint in expected_endpoints:
                # Check if endpoint is defined in router
                endpoint_exists = (
                    f'@router.get("{endpoint}")' in content
                    or f'@router.websocket("{endpoint}")' in content
                )
                self.log_test(f"API Endpoint: {endpoint}", endpoint_exists)
                if not endpoint_exists:
                    all_endpoints_exist = False

            # Test other components
            checks = [
                ("Pydantic models", "class DashboardSummary" in content),
                ("WebSocket manager", "class ConnectionManager" in content),
                ("Health check endpoint", "async def health_check" in content),
                ("Error handling", "HTTPException" in content),
            ]

            all_checks_passed = True
            for check_name, check_result in checks:
                self.log_test(f"API {check_name}", check_result)
                if not check_result:
                    all_checks_passed = False

            self.log_test(
                "API Routes Complete",
                all_endpoints_exist and all_checks_passed,
                "All 8 API endpoints with proper models and error handling",
            )
        else:
            self.log_test("API Routes File", False, "api/dashboard_routes.py not found")

    def test_resource_limits(self):
        """Test resource limits configuration"""
        print("\n🔍 Testing Resource Limits...")

        compose_path = Path("containers/docker-compose.yml")
        if compose_path.exists():
            with open(compose_path, "r") as f:
                content = f.read()

            # Find dashboard service section
            if "dashboard:" in content:
                service_start = content.find("dashboard:")
                # Look for the next service or end of file
                next_service_start = content.find("\n  # ", service_start + 1)
                if next_service_start == -1:
                    next_service_start = content.find("\nvolumes:", service_start + 1)
                if next_service_start == -1:
                    next_service_start = len(content)

                service_content = content[service_start:next_service_start]

                checks = [
                    ("CPU limit", "cpus: '1.0'" in service_content),
                    ("Memory limit", "memory: 2G" in service_content),
                    ("CPU reservation", "cpus: '0.5'" in service_content),
                    ("Memory reservation", "memory: 1G" in service_content),
                ]

                all_checks_passed = True
                for check_name, check_result in checks:
                    self.log_test(f"Resource {check_name}", check_result)
                    if not check_result:
                        all_checks_passed = False

                self.log_test(
                    "Resource Limits Complete",
                    all_checks_passed,
                    "CPU: 1.0/0.5, Memory: 2G/1G limits and reservations configured",
                )
            else:
                self.log_test(
                    "Dashboard Service Found",
                    False,
                    "dashboard service not found in containers/docker-compose.yml",
                )
        else:
            self.log_test(
                "Docker Compose File", False, "containers/docker-compose.yml not found"
            )

    def test_service_discovery(self):
        """Test service discovery and networking"""
        print("\n🔍 Testing Service Discovery...")

        compose_path = Path("containers/docker-compose.yml")
        if compose_path.exists():
            with open(compose_path, "r") as f:
                content = f.read()

            checks = [
                ("Network configuration", "ai-coding-network" in content),
                ("Dependencies", "depends_on:" in content),
                ("API dependency", "api:" in content),
                ("Database dependency", "db:" in content),
                ("Redis dependency", "redis:" in content),
                ("Health check dependency", "condition: service_healthy" in content),
            ]

            all_checks_passed = True
            for check_name, check_result in checks:
                self.log_test(f"Service Discovery {check_name}", check_result)
                if not check_result:
                    all_checks_passed = False

            self.log_test(
                "Service Discovery Complete",
                all_checks_passed,
                "Proper networking, dependencies, and health check conditions configured",
            )
        else:
            self.log_test(
                "Docker Compose File", False, "containers/docker-compose.yml not found"
            )

    async def test_container_build(self):
        """Test container build process"""
        print("\n🔍 Testing Container Build...")

        try:
            # Test Docker build
            build_cmd = [
                "docker",
                "build",
                "-f",
                "containers/Dockerfile.dashboard",
                "-t",
                "ai-coding-dashboard:test",
                ".",
            ]

            result = subprocess.run(
                build_cmd,
                capture_output=True,
                text=True,
                timeout=300,
                encoding="utf-8",
                errors="ignore",
            )

            if result.returncode == 0:
                self.log_test(
                    "Container Build", True, "Docker build completed successfully"
                )

                # Test container startup
                run_cmd = [
                    "docker",
                    "run",
                    "--name",
                    "test-dashboard",
                    "--rm",
                    "-d",
                    "-p",
                    "8080:8080",
                    "ai-coding-dashboard:test",
                ]

                run_result = subprocess.run(
                    run_cmd,
                    capture_output=True,
                    text=True,
                    timeout=60,
                    encoding="utf-8",
                    errors="ignore",
                )

                if run_result.returncode == 0:
                    self.log_test(
                        "Container Startup", True, "Container started successfully"
                    )

                    # Wait for container to be ready
                    await asyncio.sleep(10)

                    # Test health endpoint
                    try:
                        async with aiohttp.ClientSession() as session:
                            async with session.get(
                                "http://localhost:8080/health"
                            ) as response:
                                if response.status == 200:
                                    self.log_test(
                                        "Health Endpoint",
                                        True,
                                        "Health endpoint responding",
                                    )
                                else:
                                    self.log_test(
                                        "Health Endpoint",
                                        False,
                                        f"HTTP {response.status}",
                                    )

                    except Exception as e:
                        self.log_test("Health Endpoint", False, f"Error: {str(e)}")

                    # Cleanup
                    subprocess.run(
                        ["docker", "stop", "test-dashboard"], capture_output=True
                    )
                else:
                    self.log_test(
                        "Container Startup",
                        False,
                        f"Startup failed: {run_result.stderr}",
                    )
            else:
                self.log_test(
                    "Container Build", False, f"Build failed: {result.stderr}"
                )

        except subprocess.TimeoutExpired:
            self.log_test("Container Build", False, "Build timeout")
        except Exception as e:
            self.log_test("Container Build", False, f"Error: {str(e)}")

    async def test_api_endpoints(self):
        """Test API endpoints"""
        print("\n🔍 Testing API Endpoints...")

        # Test endpoints (assuming container is running on port 8080)
        endpoints = [
            ("Health Endpoint", "http://localhost:8080/health"),
            ("Root Endpoint", "http://localhost:8080/"),
            ("Summary Endpoint", "http://localhost:8080/api/dashboard/summary"),
            ("Metrics Endpoint", "http://localhost:8080/api/dashboard/metrics"),
            ("Config Endpoint", "http://localhost:8080/api/dashboard/config"),
        ]

        for endpoint_name, url in endpoints:
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(url) as response:
                        if response.status == 200:
                            self.log_test(
                                endpoint_name, True, f"HTTP {response.status}"
                            )
                        else:
                            self.log_test(
                                endpoint_name, False, f"HTTP {response.status}"
                            )
            except Exception as e:
                self.log_test(endpoint_name, False, f"Error: {str(e)}")

    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all dashboard container tests"""
        print("🔒 Testing Dashboard Container Implementation...")
        print("=" * 60)

        # Run synchronous tests
        self.test_dockerfile_exists()
        self.test_docker_compose_integration()
        self.test_configuration_file()
        self.test_cli_commands()
        self.test_api_routes()
        self.test_resource_limits()
        self.test_service_discovery()

        # Run asynchronous tests
        await self.test_container_build()
        await self.test_api_endpoints()

        # Calculate results
        success_rate = (
            (self.passed_tests / self.total_tests * 100) if self.total_tests > 0 else 0
        )

        print("\n" + "=" * 60)
        print("📊 DASHBOARD CONTAINER TEST RESULTS")
        print("=" * 60)
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests}")
        print(f"Failed: {self.failed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")

        if success_rate >= 90:
            print("✅ DASHBOARD CONTAINER IMPLEMENTATION SUCCESSFUL")
        elif success_rate >= 70:
            print("⚠️ DASHBOARD CONTAINER IMPLEMENTATION NEEDS ATTENTION")
        else:
            print("❌ DASHBOARD CONTAINER IMPLEMENTATION NEEDS ATTENTION")

        # Save detailed report
        report_path = Path("test_reports/dashboard_container_test_report.json")
        report_path.parent.mkdir(exist_ok=True)

        report = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": self.total_tests,
            "passed_tests": self.passed_tests,
            "failed_tests": self.failed_tests,
            "success_rate": success_rate,
            "test_results": self.test_results,
        }

        with open(report_path, "w") as f:
            json.dump(report, f, indent=2)

        print(f"\n📄 Detailed report saved to: {report_path}")

        return report


async def main():
    """Main function"""
    tester = DashboardContainerTester()
    await tester.run_all_tests()


if __name__ == "__main__":
    asyncio.run(main())
