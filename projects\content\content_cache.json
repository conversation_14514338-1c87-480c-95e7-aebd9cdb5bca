{"268903c6-9cf2-4e87-93ad-b8786d35c41f": {"id": "268903c6-9cf2-4e87-93ad-b8786d35c41f", "title": "Updated Test Article - Phase 2.3", "content": "<h1>Updated Test Content</h1><p>This article has been updated to test CMS functionality.</p>", "content_type": "article", "author": "Test User", "created_at": "2025-07-19T16:48:10.218770", "updated_at": "2025-07-19T16:49:10.326975", "tags": ["test", "phase-2-3", "cms"], "metadata": {}, "file_path": "content\\268903c6-9cf2-4e87-93ad-b8786d35c41f_Updated_Test_Article_-_Phase_23.html", "file_size": 92, "version": 2, "status": "archived"}, "44a92694-547a-4671-8b25-19d21788e586": {"id": "44a92694-547a-4671-8b25-19d21788e586", "title": "Ai In Content Management - AI Generated Content", "content": "<article>\n<h1>AI in Content Management</h1>\n<p>This is a fallback article about AI in Content Management. The AI content generation service is currently unavailable.</p>\n<p>Please check back later for AI-generated content about this topic.</p>\n</article>", "content_type": "article", "author": "AI Coding Agent", "created_at": "2025-07-19T16:48:12.282400", "updated_at": "2025-07-19T16:48:12.282400", "tags": ["ai in content management"], "metadata": {"ai_generated": true, "topic": "AI in Content Management", "length": "short", "style": "informative", "generation_date": "2025-07-19T16:48:12.282359"}, "file_path": "content\\44a92694-547a-4671-8b25-19d21788e586_Ai_In_Content_Management_-_AI_Generated_Content.html", "file_size": 258, "version": 1, "status": "draft"}, "5b9caba0-52e6-45b0-8545-1a057914b159": {"id": "5b9caba0-52e6-45b0-8545-1a057914b159", "title": "Updated Test Article - Phase 2.3", "content": "<h1>Updated Test Content</h1><p>This article has been updated to test CMS functionality.</p>", "content_type": "article", "author": "Test User", "created_at": "2025-07-19T16:48:28.400244", "updated_at": "2025-07-19T16:48:28.411600", "tags": ["test", "phase-2-3", "cms"], "metadata": {}, "file_path": "content\\5b9caba0-52e6-45b0-8545-1a057914b159_Updated_Test_Article_-_Phase_23.html", "file_size": 92, "version": 2, "status": "published"}, "895bbf0d-e4c0-402e-80cf-5b53996f66a3": {"id": "895bbf0d-e4c0-402e-80cf-5b53996f66a3", "title": "Ai In Content Management - AI Generated Content", "content": "<article>\n<h1>AI in Content Management</h1>\n<p>This is a fallback article about AI in Content Management. The AI content generation service is currently unavailable.</p>\n<p>Please check back later for AI-generated content about this topic.</p>\n</article>", "content_type": "article", "author": "AI Coding Agent", "created_at": "2025-07-19T16:48:30.487817", "updated_at": "2025-07-19T16:48:30.487817", "tags": ["ai in content management"], "metadata": {"ai_generated": true, "topic": "AI in Content Management", "length": "short", "style": "informative", "generation_date": "2025-07-19T16:48:30.487775"}, "file_path": "content\\895bbf0d-e4c0-402e-80cf-5b53996f66a3_Ai_In_Content_Management_-_AI_Generated_Content.html", "file_size": 258, "version": 1, "status": "draft"}, "3088d478-d7e6-478e-8840-c5dca64cd6a3": {"id": "3088d478-d7e6-478e-8840-c5dca64cd6a3", "title": "Updated Standalone Test Article", "content": "<h1>Updated Standalone Test</h1><p>This article has been updated in standalone mode.</p>", "content_type": "article", "author": "Standalone Test", "created_at": "2025-07-19T16:49:08.236824", "updated_at": "2025-07-19T16:49:08.246609", "tags": ["standalone", "test", "phase-2-3"], "metadata": {}, "file_path": "content\\3088d478-d7e6-478e-8840-c5dca64cd6a3_Updated_Standalone_Test_Article.html", "file_size": 88, "version": 2, "status": "published"}, "6fd3b80d-9711-40c6-8f30-94b0df36930c": {"id": "6fd3b80d-9711-40c6-8f30-94b0df36930c", "title": "Standalone Ai Testing - AI Generated Content", "content": "<article>\n<h1>Standalone AI Testing</h1>\n<p>This is a fallback article about Standalone AI Testing. The AI content generation service is currently unavailable.</p>\n<p>Please check back later for AI-generated content about this topic.</p>\n</article>", "content_type": "article", "author": "AI Coding Agent", "created_at": "2025-07-19T16:49:10.321655", "updated_at": "2025-07-19T16:49:10.321655", "tags": ["standalone ai testing"], "metadata": {"ai_generated": true, "topic": "Standalone AI Testing", "length": "short", "style": "informative", "generation_date": "2025-07-19T16:49:10.321628"}, "file_path": "content\\6fd3b80d-9711-40c6-8f30-94b0df36930c_Standalone_Ai_Testing_-_AI_Generated_Content.html", "file_size": 252, "version": 1, "status": "draft"}, "948537b0-fa14-4ef7-99da-bd23e9ef65ec": {"id": "948537b0-fa14-4ef7-99da-bd23e9ef65ec", "title": "Test Article with Links - Phase 2.4 Integration", "content": "<article>\n<h1>Test Article with Links</h1>\n<p>This is a test article to demonstrate link checking integration.</p>\n\n<h2>Internal Links</h2>\n<ul>\n<li><a href=\"/about\">About Page</a></li>\n<li><a href=\"/contact\">Contact Page</a></li>\n<li><a href=\"/blog\">Blog Index</a></li>\n</ul>\n\n<h2>External Links</h2>\n<ul>\n<li><a href=\"https://www.google.com\">Google</a></li>\n<li><a href=\"https://www.github.com\">GitHub</a></li>\n<li><a href=\"https://www.example.com/nonexistent\">Broken Link</a></li>\n</ul>\n\n<h2>Media Links</h2>\n<ul>\n<li><img src=\"/images/test.jpg\" alt=\"Test Image\"></li>\n<li><a href=\"/documents/test.pdf\">Test Document</a></li>\n</ul>\n\n<p>This content will be used to test the maintenance engine's link checking capabilities.</p>\n</article>", "content_type": "article", "author": "Integration Test", "created_at": "2025-07-19T17:04:06.000888", "updated_at": "2025-07-19T17:04:06.000888", "tags": ["integration", "phase-2-4", "link-test", "maintenance"], "metadata": {}, "file_path": "content\\948537b0-fa14-4ef7-99da-bd23e9ef65ec_Test_Article_with_Links_-_Phase_24_Integration.html", "file_size": 765, "version": 1, "status": "draft"}, "d12ab053-5746-4c36-83e6-37a1aba6dc10": {"id": "d12ab053-5746-4c36-83e6-37a1aba6dc10", "title": "Content with Maintenance Issues", "content": "<article>\n<h1>Content with Potential Issues</h1>\n<p>This content has some potential maintenance issues.</p>\n\n<h2>Short Content Section</h2>\n<p>Too short.</p>\n\n<h2>Links Section</h2>\n<ul>\n<li><a href=\"https://www.example.com/broken\">Broken external link</a></li>\n<li><a href=\"/nonexistent-page\">Broken internal link</a></li>\n<li><a href=\"https://www.google.com\">Valid link</a></li>\n</ul>\n\n<p>This content demonstrates how the maintenance engine can identify and report issues.</p>\n</article>", "content_type": "article", "author": "Integration Test", "created_at": "2025-07-19T17:04:50.201677", "updated_at": "2025-07-19T17:04:50.201677", "tags": ["maintenance", "issues", "test"], "metadata": {}, "file_path": "content\\d12ab053-5746-4c36-83e6-37a1aba6dc10_Content_with_Maintenance_Issues.html", "file_size": 505, "version": 1, "status": "draft"}}