networks:
  ai-coding-network:
    driver: bridge
services:
  api:
    build:
      context: ..
      dockerfile: api/Dockerfile
    command:
    - uvicorn
    - api.main:app
    - --host
    - 0.0.0.0
    - --port
    - '8000'
    - --reload
    - --reload-dir
    - /app
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 4G
        reservations:
          cpus: '1.0'
          memory: 2G
    env_file: ../.env
    environment:
      API_URL: http://localhost:8000
      DATABASE_URL: ******************************************************/ai_coding_agent
      ENVIRONMENT: development
      LOG_LEVEL: DEBUG
      OLLAMA_URL: http://host.docker.internal:11434
      PYTHONPATH: /app
      REDIS_URL: redis://redis:6379
    healthcheck:
      interval: 30s
      retries: 3
      test:
      - CMD
      - curl
      - -f
      - http://localhost:8000/health || exit 1
      timeout: 10s
    logging:
      driver: json-file
      options:
        max-file: '3'
        max-size: 10m
    restart: unless-stopped
    volumes:
      - app_code:/app
      - /app/node_modules
      - /app/__pycache__
      - /app/.pytest_cache
  db:
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 1G
    environment:
      POSTGRES_DB: ai_coding_agent_dev
      POSTGRES_INITDB_ARGS: --encoding=UTF-8
      POSTGRES_PASSWORD: ai_coding_password
      POSTGRES_USER: ai_coding_user
    image: alpine:latest
    restart: unless-stopped
    volumes:
    - pgdata:/var/lib/postgresql/data
    - ../database/init.sql:/docker-entrypoint-initdb.d/init.sql
    - ../database/migrations:/docker-entrypoint-initdb.d/migrations
  dev-tools:
    build:
      context: ..
      dockerfile: api/Dockerfile
    command:
    - tail
    - -f
    - /dev/null
    container_name: ai-coding-dev-tools
    environment:
      ENVIRONMENT: development
      PYTHONPATH: /app
      OLLAMA_URL: http://host.docker.internal:11434
    networks:
    - ai-coding-network
    profiles:
    - dev
    restart: unless-stopped
    volumes:
      - app_code:/app
      - /app/node_modules
      - /app/__pycache__
  redis:
    command:
    - redis-server
    - --appendonly
    - 'yes'
    - --loglevel
    - verbose
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 1G
        reservations:
          cpus: '0.25'
          memory: 512M
    image: alpine:latest
    restart: unless-stopped
    volumes:
    - redis_data:/data
version: '3.8'
volumes:
  app_code: {}
  pgdata: {}
  redis_data: {}
