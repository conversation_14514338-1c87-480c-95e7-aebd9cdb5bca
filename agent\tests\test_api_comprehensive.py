#!/usr/bin/env python3
"""
Comprehensive API Test Script for AI Coding Agent
Tests all API endpoints and verifies functionality.
"""

import asyncio
import json
import os
import signal
import socket
import subprocess
import sys
import time
from pathlib import Path
from typing import Any, Dict, List

import requests

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class APITester:
    """Comprehensive API testing class"""

    def __init__(self, base_url: str = "http://127.0.0.1:8000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_results = []
        self.server_process = None

    def is_port_available(self, port: int) -> bool:
        """Check if a port is available"""
        try:
            with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                s.bind(("127.0.0.1", port))
                return True
        except OSError:
            return False

    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        result = {
            "test": test_name,
            "success": success,
            "details": details,
            "timestamp": time.time(),
        }
        self.test_results.append(result)

        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")

    def start_server(self):
        """Start the API server"""
        try:
            print("🚀 Starting API server...")

            # Check if port 8000 is available
            if not self.is_port_available(8000):
                print(
                    "⚠️  Port 8000 is already in use. Attempting to kill existing processes..."
                )
                try:
                    subprocess.run(
                        ["taskkill", "/F", "/IM", "python.exe"],
                        capture_output=True,
                        timeout=10,
                    )
                    time.sleep(2)
                except:
                    pass

                if not self.is_port_available(8000):
                    self.log_test(
                        "Server Startup", False, "Port 8000 still in use after cleanup"
                    )
                    return False

            # Use direct uvicorn command
            uvicorn_cmd = [
                sys.executable,
                "-m",
                "uvicorn",
                "api.main:app",
                "--host",
                "127.0.0.1",
                "--port",
                "8000",
                "--log-level",
                "info",
            ]
            print(f"🔧 Command: {' '.join(uvicorn_cmd)}")
            print("=" * 60)

            self.server_process = subprocess.Popen(
                uvicorn_cmd,
                cwd=project_root,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
            )

            # Wait for server to start with better timeout handling
            time.sleep(3)

            # Test if server is running with retry logic
            max_retries = 5
            for attempt in range(max_retries):
                try:
                    print(f"🔄 Health check attempt {attempt + 1}/{max_retries}")
                    response = self.session.get(f"{self.base_url}/health", timeout=5)
                    if response.status_code == 200:
                        self.log_test(
                            "Server Startup",
                            True,
                            f"API server started successfully on attempt {attempt + 1}",
                        )
                        return True
                    else:
                        print(f"⚠️  Health check returned status {response.status_code}")
                except requests.exceptions.RequestException as e:
                    print(f"⚠️  Health check attempt {attempt + 1} failed: {e}")

                if attempt < max_retries - 1:
                    time.sleep(2)

            self.log_test(
                "Server Startup",
                False,
                f"Server not responding after {max_retries} attempts",
            )
            return False

        except Exception as e:
            self.log_test("Server Startup", False, f"Failed to start server: {e}")
            return False

    def stop_server(self):
        """Stop the API server"""
        if self.server_process:
            try:
                self.server_process.terminate()
                self.server_process.wait(timeout=10)
                print("🛑 API server stopped")
            except subprocess.TimeoutExpired:
                self.server_process.kill()
                print("🛑 API server force killed")

    def test_root_endpoint(self):
        """Test root endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/")
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and "AI Coding Agent API" in data.get(
                    "message", ""
                ):
                    self.log_test("Root Endpoint", True)
                    return True
                else:
                    self.log_test("Root Endpoint", False, "Invalid response format")
                    return False
            else:
                self.log_test(
                    "Root Endpoint", False, f"Status code: {response.status_code}"
                )
                return False
        except Exception as e:
            self.log_test("Root Endpoint", False, f"Request failed: {e}")
            return False

    def test_health_endpoint(self):
        """Test health endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/health")
            if response.status_code == 200:
                data = response.json()
                if "status" in data and "services" in data:
                    self.log_test("Health Endpoint", True)
                    return True
                else:
                    self.log_test(
                        "Health Endpoint", False, "Invalid health response format"
                    )
                    return False
            else:
                self.log_test(
                    "Health Endpoint", False, f"Status code: {response.status_code}"
                )
                return False
        except Exception as e:
            self.log_test("Health Endpoint", False, f"Request failed: {e}")
            return False

    def test_chat_endpoint(self):
        """Test chat endpoint"""
        try:
            chat_data = {
                "prompt": "Hello, how are you?",
                "model": "deepseek-coder:1.3b",
                "intent": "general",
            }

            response = self.session.post(
                f"{self.base_url}/api/v1/chat/", json=chat_data, timeout=30
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success") and "response" in data:
                    self.log_test("Chat Endpoint", True)
                    return True
                else:
                    self.log_test(
                        "Chat Endpoint", False, "Invalid chat response format"
                    )
                    return False
            else:
                self.log_test(
                    "Chat Endpoint", False, f"Status code: {response.status_code}"
                )
                return False
        except Exception as e:
            self.log_test("Chat Endpoint", False, f"Request failed: {e}")
            return False

    def test_models_endpoint(self):
        """Test models endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/chat/models")
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list) and len(data) > 0:
                    self.log_test("Models Endpoint", True, f"Found {len(data)} models")
                    return True
                else:
                    self.log_test(
                        "Models Endpoint", False, "Invalid models response format"
                    )
                    return False
            else:
                self.log_test(
                    "Models Endpoint", False, f"Status code: {response.status_code}"
                )
                return False
        except Exception as e:
            self.log_test("Models Endpoint", False, f"Request failed: {e}")
            return False

    def test_sites_list_endpoint(self):
        """Test sites list endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/sites/list")
            if response.status_code == 200:
                data = response.json()
                if isinstance(data, list):
                    self.log_test(
                        "Sites List Endpoint", True, f"Found {len(data)} sites"
                    )
                    return True
                else:
                    self.log_test(
                        "Sites List Endpoint", False, "Invalid sites response format"
                    )
                    return False
            else:
                self.log_test(
                    "Sites List Endpoint", False, f"Status code: {response.status_code}"
                )
                return False
        except Exception as e:
            self.log_test("Sites List Endpoint", False, f"Request failed: {e}")
            return False

    def test_advanced_learning_endpoints(self):
        """Test advanced learning endpoints"""
        endpoints = [
            ("/api/v1/advanced-learning/status", "GET"),
            ("/api/v1/advanced-learning/meta-learning/status", "GET"),
            ("/api/v1/advanced-learning/pareto-optimization/status", "GET"),
        ]

        for endpoint, method in endpoints:
            try:
                if method == "GET":
                    response = self.session.get(f"{self.base_url}{endpoint}")
                else:
                    response = self.session.post(f"{self.base_url}{endpoint}")

                if response.status_code in [
                    200,
                    404,
                ]:  # 404 is acceptable for mock endpoints
                    self.log_test(f"Advanced Learning {endpoint}", True)
                else:
                    self.log_test(
                        f"Advanced Learning {endpoint}",
                        False,
                        f"Status code: {response.status_code}",
                    )
            except Exception as e:
                self.log_test(
                    f"Advanced Learning {endpoint}", False, f"Request failed: {e}"
                )

    def test_error_reporting(self):
        """Test error reporting endpoint"""
        try:
            error_data = {
                "message": "Test error message",
                "type": "test_error",
                "severity": "low",
            }

            response = self.session.post(
                f"{self.base_url}/api/v1/errors/report", json=error_data
            )

            if response.status_code == 200:
                data = response.json()
                if data.get("success"):
                    self.log_test("Error Reporting", True)
                    return True
                else:
                    self.log_test("Error Reporting", False, "Error reporting failed")
                    return False
            else:
                self.log_test(
                    "Error Reporting", False, f"Status code: {response.status_code}"
                )
                return False
        except Exception as e:
            self.log_test("Error Reporting", False, f"Request failed: {e}")
            return False

    def test_config_endpoint(self):
        """Test config endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/config")
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and "config" in data:
                    self.log_test("Config Endpoint", True)
                    return True
                else:
                    self.log_test(
                        "Config Endpoint", False, "Invalid config response format"
                    )
                    return False
            else:
                self.log_test(
                    "Config Endpoint", False, f"Status code: {response.status_code}"
                )
                return False
        except Exception as e:
            self.log_test("Config Endpoint", False, f"Request failed: {e}")
            return False

    def test_metrics_endpoint(self):
        """Test metrics endpoint"""
        try:
            response = self.session.get(f"{self.base_url}/api/v1/metrics")
            if response.status_code == 200:
                data = response.json()
                if data.get("success") and "metrics" in data:
                    self.log_test("Metrics Endpoint", True)
                    return True
                else:
                    self.log_test(
                        "Metrics Endpoint", False, "Invalid metrics response format"
                    )
                    return False
            else:
                self.log_test(
                    "Metrics Endpoint", False, f"Status code: {response.status_code}"
                )
                return False
        except Exception as e:
            self.log_test("Metrics Endpoint", False, f"Request failed: {e}")
            return False

    def test_monitoring_endpoints(self):
        """Test monitoring endpoints"""
        try:
            # Test health endpoint
            response = self.session.get(f"{self.base_url}/api/v1/monitoring/health")
            if response.status_code == 200:
                self.log_test("Monitoring Health", True)
            else:
                self.log_test(
                    "Monitoring Health", False, f"Status code: {response.status_code}"
                )
                return False

            # Test metrics endpoint
            response = self.session.get(f"{self.base_url}/api/v1/monitoring/metrics")
            if response.status_code == 200:
                self.log_test("Monitoring Metrics", True)
            else:
                self.log_test(
                    "Monitoring Metrics", False, f"Status code: {response.status_code}"
                )
                return False

            # Test config endpoint
            response = self.session.get(f"{self.base_url}/api/v1/monitoring/config")
            if response.status_code == 200:
                self.log_test("Monitoring Config", True)
            else:
                self.log_test(
                    "Monitoring Config", False, f"Status code: {response.status_code}"
                )
                return False

            return True
        except Exception as e:
            self.log_test("Monitoring Endpoints", False, f"Request failed: {e}")
            return False

    def test_dashboard_endpoints(self):
        """Test dashboard endpoints"""
        try:
            # Test overview endpoint
            response = self.session.get(f"{self.base_url}/api/v1/dashboard/overview")
            if response.status_code == 200:
                self.log_test("Dashboard Overview", True)
            else:
                self.log_test(
                    "Dashboard Overview", False, f"Status code: {response.status_code}"
                )
                return False

            # Test sites endpoint
            response = self.session.get(f"{self.base_url}/api/v1/dashboard/sites")
            if response.status_code == 200:
                self.log_test("Dashboard Sites", True)
            else:
                self.log_test(
                    "Dashboard Sites", False, f"Status code: {response.status_code}"
                )
                return False

            # Test deployments endpoint
            response = self.session.get(f"{self.base_url}/api/v1/dashboard/deployments")
            if response.status_code == 200:
                self.log_test("Dashboard Deployments", True)
            else:
                self.log_test(
                    "Dashboard Deployments",
                    False,
                    f"Status code: {response.status_code}",
                )
                return False

            return True
        except Exception as e:
            self.log_test("Dashboard Endpoints", False, f"Request failed: {e}")
            return False

    def run_all_tests(self):
        """Run all API tests"""
        print("🧪 Starting Comprehensive API Tests")
        print("=" * 50)

        # Start server
        if not self.start_server():
            print("❌ Failed to start server, aborting tests")
            return False

        try:
            # Run tests
            self.test_root_endpoint()
            self.test_health_endpoint()
            self.test_chat_endpoint()
            self.test_models_endpoint()
            self.test_sites_list_endpoint()
            self.test_advanced_learning_endpoints()
            self.test_error_reporting()
            self.test_config_endpoint()
            self.test_metrics_endpoint()
            self.test_monitoring_endpoints()
            self.test_dashboard_endpoints()

            # Calculate results
            total_tests = len(self.test_results)
            passed_tests = sum(1 for result in self.test_results if result["success"])
            failed_tests = total_tests - passed_tests

            print("\n" + "=" * 50)
            print(f"📊 Test Results: {passed_tests}/{total_tests} tests passed")

            if failed_tests > 0:
                print("\n❌ Failed Tests:")
                for result in self.test_results:
                    if not result["success"]:
                        print(f"   - {result['test']}: {result['details']}")

            success_rate = (passed_tests / total_tests) * 100
            print(f"\n🎯 Success Rate: {success_rate:.1f}%")

            return failed_tests == 0

        finally:
            self.stop_server()

    def save_results(self, filename: str = "api_test_results.json"):
        """Save test results to file"""
        results = {
            "timestamp": time.time(),
            "total_tests": len(self.test_results),
            "passed_tests": sum(1 for result in self.test_results if result["success"]),
            "failed_tests": sum(
                1 for result in self.test_results if not result["success"]
            ),
            "results": self.test_results,
        }

        with open(filename, "w") as f:
            json.dump(results, f, indent=2)

        print(f"\n💾 Test results saved to {filename}")


def main():
    """Main test function"""
    tester = APITester()

    try:
        success = tester.run_all_tests()
        tester.save_results()

        if success:
            print("\n🎉 All API tests passed!")
            return 0
        else:
            print("\n⚠️  Some API tests failed. Check the results above.")
            return 1

    except KeyboardInterrupt:
        print("\n🛑 Tests interrupted by user")
        tester.stop_server()
        return 1
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
        tester.stop_server()
        return 1


if __name__ == "__main__":
    sys.exit(main())
