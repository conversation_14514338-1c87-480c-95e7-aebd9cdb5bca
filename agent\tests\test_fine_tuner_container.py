import os
import sys

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

try:
    # Try to import the fine-tuner service
    from docs.fine_tuning.fine_tuner_service import app

    print("✓ SUCCESS: fine_tuning.fine_tuner_service imported successfully")
    print("✓ Service app object created")

    # Verify the app is a FastAPI instance
    from fastapi import FastAPI

    if isinstance(app, FastAPI):
        print("✓ SUCCESS: app is a FastAPI instance")
    else:
        print("✗ ERROR: app is not a FastAPI instance")

    # Basic verification that the app has routes
    if hasattr(app, "routes") and len(app.routes) > 0:
        print("✓ SUCCESS: app has routes defined")
        for route in app.routes:
            print(f"  - Route: {route}")
    else:
        print("✗ ERROR: app has no routes")

except Exception as e:
    print(f"✗ ERROR: Could not import module 'fine_tuning.fine_tuner_service'")
    print(f"Exception: {str(e)}")
    import traceback

    traceback.print_exc()
    sys.exit(1)

# If we get here, the import was successful
print("✓ All tests passed - fine_tuner_service.py is ready for containerization")
sys.exit(0)
