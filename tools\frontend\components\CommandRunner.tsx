import React, { useState, useEffect, useRef } from 'react';

interface CommandRunnerProps {
  siteName: string;
  onClose?: () => void;
}

interface Command {
  id: string;
  name: string;
  command: string;
  description: string;
  category: 'build' | 'test' | 'dev' | 'custom';
  safe: boolean;
}

interface CommandExecution {
  id: string;
  command: string;
  status: 'running' | 'completed' | 'failed' | 'cancelled';
  output: string[];
  startTime: string;
  endTime?: string;
  exitCode?: number;
}

const CommandRunner: React.FC<CommandRunnerProps> = ({ siteName, onClose }) => {
  const [availableCommands, setAvailableCommands] = useState<Command[]>([]);
  const [executions, setExecutions] = useState<CommandExecution[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCommand, setSelectedCommand] = useState<string>('');
  const [customCommand, setCustomCommand] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);
  const outputRef = useRef<HTMLDivElement>(null);

  // Load available commands
  useEffect(() => {
    const loadCommands = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/sites/${siteName}/commands`);

        if (!response.ok) {
          throw new Error(`Failed to load commands: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status === 'success') {
          setAvailableCommands(data.commands || []);
        } else {
          throw new Error(data.message || 'Failed to load commands');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    };

    if (siteName) {
      loadCommands();
    }
  }, [siteName]);

  // Auto-scroll to bottom of output
  useEffect(() => {
    if (outputRef.current) {
      outputRef.current.scrollTop = outputRef.current.scrollHeight;
    }
  }, [executions]);

  // Execute command
  const executeCommand = async (commandToRun: string) => {
    try {
      setIsExecuting(true);
      setError(null);

      const executionId = `exec_${Date.now()}`;
      const newExecution: CommandExecution = {
        id: executionId,
        command: commandToRun,
        status: 'running',
        output: [],
        startTime: new Date().toISOString()
      };

      setExecutions(prev => [...prev, newExecution]);

      const response = await fetch(`/api/sites/${siteName}/commands/execute`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          command: commandToRun
        })
      });

      if (!response.ok) {
        throw new Error(`Failed to execute command: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.status === 'success') {
        // Update execution with results
        setExecutions(prev => prev.map(exec =>
          exec.id === executionId
            ? {
                ...exec,
                status: data.exit_code === 0 ? 'completed' : 'failed',
                output: data.output || [],
                endTime: new Date().toISOString(),
                exitCode: data.exit_code
              }
            : exec
        ));
      } else {
        throw new Error(data.message || 'Failed to execute command');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
      // Update execution with error
      setExecutions(prev => prev.map(exec =>
        exec.status === 'running'
          ? {
              ...exec,
              status: 'failed',
              output: [...exec.output, `Error: ${err instanceof Error ? err.message : 'Unknown error'}`],
              endTime: new Date().toISOString()
            }
          : exec
      ));
    } finally {
      setIsExecuting(false);
    }
  };

  // Handle command execution
  const handleExecute = () => {
    const command = selectedCommand || customCommand;
    if (command.trim()) {
      executeCommand(command.trim());
      setSelectedCommand('');
      setCustomCommand('');
    }
  };

  // Cancel running command
  const handleCancel = async (executionId: string) => {
    try {
      const response = await fetch(`/api/sites/${siteName}/commands/cancel`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          execution_id: executionId
        })
      });

      if (response.ok) {
        setExecutions(prev => prev.map(exec =>
          exec.id === executionId
            ? { ...exec, status: 'cancelled', endTime: new Date().toISOString() }
            : exec
        ));
      }
    } catch (err) {
      console.error('Failed to cancel command:', err);
    }
  };

  // Clear executions
  const handleClear = () => {
    setExecutions([]);
  };

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-blue-600 bg-blue-100';
      case 'completed': return 'text-green-600 bg-green-100';
      case 'failed': return 'text-red-600 bg-red-100';
      case 'cancelled': return 'text-gray-600 bg-gray-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch (category) {
      case 'build':
        return (
          <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'test':
        return (
          <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
          </svg>
        );
      case 'dev':
        return (
          <svg className="w-4 h-4 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2">Loading commands...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center">
          <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          <span className="text-red-800 font-medium">Command Error</span>
        </div>
        <p className="text-red-600 mt-1">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Command Runner Header */}
      <div className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center">
          <svg className="w-4 h-4 text-gray-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
          </svg>
          <span className="font-medium text-gray-700">Command Runner</span>
        </div>

        <div className="flex items-center space-x-2">
          <button
            onClick={handleClear}
            className="px-3 py-1 text-sm bg-gray-500 text-white rounded hover:bg-gray-600"
          >
            Clear
          </button>

          {onClose && (
            <button
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-gray-600"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Command Selection */}
      <div className="p-3 border-b border-gray-200">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          {/* Available Commands */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Available Commands
            </label>
            <select
              value={selectedCommand}
              onChange={(e) => setSelectedCommand(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select a command...</option>
              {availableCommands.map((cmd) => (
                <option key={cmd.id} value={cmd.command}>
                  {cmd.name} - {cmd.description}
                </option>
              ))}
            </select>
          </div>

          {/* Custom Command */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Custom Command
            </label>
            <input
              type="text"
              value={customCommand}
              onChange={(e) => setCustomCommand(e.target.value)}
              placeholder="Enter custom command..."
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>

        {/* Execute Button */}
        <div className="mt-3">
          <button
            onClick={handleExecute}
            disabled={isExecuting || (!selectedCommand && !customCommand.trim())}
            className={`px-4 py-2 rounded ${
              isExecuting || (!selectedCommand && !customCommand.trim())
                ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                : 'bg-blue-500 text-white hover:bg-blue-600'
            }`}
          >
            {isExecuting ? 'Executing...' : 'Execute Command'}
          </button>
        </div>
      </div>

      {/* Command Output */}
      <div className="flex-1 min-h-0 flex flex-col">
        <div className="flex items-center justify-between p-2 bg-gray-100 border-b border-gray-200">
          <span className="text-sm font-medium text-gray-700">Output</span>
          <span className="text-xs text-gray-500">
            {executions.length} execution{executions.length !== 1 ? 's' : ''}
          </span>
        </div>

        <div
          ref={outputRef}
          className="flex-1 overflow-y-auto p-3 bg-black text-green-400 font-mono text-sm"
        >
          {executions.length === 0 ? (
            <div className="text-gray-500 text-center py-8">
              No commands executed yet. Select a command above to get started.
            </div>
          ) : (
            executions.map((execution) => (
              <div key={execution.id} className="mb-4">
                {/* Command Header */}
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    <span className="text-white font-semibold">$ {execution.command}</span>
                    <span className={`px-2 py-1 text-xs rounded ${getStatusColor(execution.status)}`}>
                      {execution.status}
                    </span>
                  </div>

                  {execution.status === 'running' && (
                    <button
                      onClick={() => handleCancel(execution.id)}
                      className="px-2 py-1 text-xs bg-red-500 text-white rounded hover:bg-red-600"
                    >
                      Cancel
                    </button>
                  )}
                </div>

                {/* Command Output */}
                <div className="ml-4">
                  {execution.output.map((line, index) => (
                    <div key={index} className="text-gray-300">
                      {line}
                    </div>
                  ))}

                  {execution.status === 'completed' && execution.exitCode !== undefined && (
                    <div className={`mt-2 text-sm ${
                      execution.exitCode === 0 ? 'text-green-400' : 'text-red-400'
                    }`}>
                      Exit code: {execution.exitCode}
                    </div>
                  )}

                  {execution.status === 'running' && (
                    <div className="mt-2 text-yellow-400">
                      <span className="animate-pulse">●</span> Running...
                    </div>
                  )}
                </div>
              </div>
            ))
          )}
        </div>
      </div>
    </div>
  );
};

export default CommandRunner;
