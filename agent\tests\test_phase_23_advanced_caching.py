#!/usr/bin/env python3
"""
Phase 23 Advanced Caching - Comprehensive Test Suite

This test suite validates all advanced caching features including:
- Multi-level caching (L1, L2, L3)
- Smart cache invalidation
- Cache warming
- Cache analytics
- Configuration management
- Performance monitoring
- Resource optimization
- Cache validation
"""

import asyncio
import json
import threading
import time
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional
from unittest.mock import AsyncMock, Mock, patch

import pytest

# Import the caching components
from performance.cache_manager import CacheManager, L1Cache, L2Cache
from performance.performance_manager import PerformanceManager


class TestL1Cache:
    """Test Level 1 Cache (Memory Cache)"""

    @pytest.fixture
    def l1_cache(self):
        """L1 cache instance"""
        return L1Cache(max_size_mb=1, ttl_seconds=60)

    def test_l1_cache_initialization(self, l1_cache):
        """Test L1 cache initialization"""
        assert l1_cache.max_size_bytes == 1024 * 1024  # 1MB
        assert l1_cache.ttl_seconds == 60
        assert len(l1_cache.cache) == 0
        assert l1_cache.size_bytes == 0
        assert l1_cache.hits == 0
        assert l1_cache.misses == 0

    def test_l1_cache_set_get(self, l1_cache):
        """Test basic set/get operations"""
        # Set value
        success = l1_cache.set("test_key", "test_value")
        assert success is True

        # Get value
        value = l1_cache.get("test_key")
        assert value == "test_value"

        # Check stats
        stats = l1_cache.get_stats()
        assert stats["hits"] == 1
        assert stats["misses"] == 0
        assert stats["hit_ratio"] == 100.0

    def test_l1_cache_miss(self, l1_cache):
        """Test cache miss behavior"""
        # Try to get non-existent key
        value = l1_cache.get("non_existent")
        assert value is None

        # Check stats
        stats = l1_cache.get_stats()
        assert stats["hits"] == 0
        assert stats["misses"] == 1
        assert stats["hit_ratio"] == 0.0

    def test_l1_cache_ttl_expiration(self, l1_cache):
        """Test TTL expiration"""
        # Set value with short TTL
        l1_cache.set("test_key", "test_value", ttl_seconds=1)

        # Value should be available immediately
        value = l1_cache.get("test_key")
        assert value == "test_value"

        # Wait for expiration
        time.sleep(1.1)

        # Value should be expired
        value = l1_cache.get("test_key")
        assert value is None

    def test_l1_cache_lru_eviction(self, l1_cache):
        """Test LRU eviction policy"""
        # Fill cache with larger values to trigger eviction
        large_value = "x" * 200000  # 200KB per value (very large)
        for i in range(10):
            l1_cache.set(f"key_{i}", large_value)

        # Check that some entries were evicted due to size limits
        stats = l1_cache.get_stats()
        # With 200KB per entry and 1MB limit, we should only fit ~5 entries
        assert stats["entries"] < 10  # Should be less than 10 due to size limits
        assert stats["size_bytes"] <= l1_cache.max_size_bytes

    def test_l1_cache_invalidation(self, l1_cache):
        """Test cache invalidation"""
        # Add multiple entries
        l1_cache.set("user_1", "data_1")
        l1_cache.set("user_2", "data_2")
        l1_cache.set("config_1", "config_data")

        # Invalidate all
        count = l1_cache.invalidate()
        assert count == 3

        # Check cache is empty
        stats = l1_cache.get_stats()
        assert stats["entries"] == 0

    def test_l1_cache_pattern_invalidation(self, l1_cache):
        """Test pattern-based invalidation"""
        # Add entries with different patterns
        l1_cache.set("user_1", "data_1")
        l1_cache.set("user_2", "data_2")
        l1_cache.set("config_1", "config_data")

        # Invalidate user entries
        count = l1_cache.invalidate("user")
        assert count == 2

        # Check remaining entries
        stats = l1_cache.get_stats()
        assert stats["entries"] == 1

    def test_l1_cache_thread_safety(self, l1_cache):
        """Test thread safety"""
        results = []
        errors = []

        def worker(worker_id):
            try:
                for i in range(10):
                    key = f"worker_{worker_id}_key_{i}"
                    value = f"worker_{worker_id}_value_{i}"
                    l1_cache.set(key, value)
                    retrieved = l1_cache.get(key)
                    if retrieved == value:
                        results.append(True)
                    else:
                        results.append(False)
            except Exception as e:
                errors.append(str(e))

        # Start multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()

        # Wait for all threads
        for thread in threads:
            thread.join()

        # Check results
        assert len(errors) == 0, f"Thread safety errors: {errors}"
        assert all(results), "Some cache operations failed"


class TestL2Cache:
    """Test Level 2 Cache (Redis-like Cache)"""

    @pytest.fixture
    def l2_cache(self):
        """L2 cache instance"""
        return L2Cache(host="localhost", port=6379, max_size_mb=2, ttl_seconds=120)

    def test_l2_cache_initialization(self, l2_cache):
        """Test L2 cache initialization"""
        assert l2_cache.host == "localhost"
        assert l2_cache.port == 6379
        assert l2_cache.max_size_bytes == 2 * 1024 * 1024  # 2MB
        assert l2_cache.ttl_seconds == 120
        assert len(l2_cache.cache) == 0

    def test_l2_cache_set_get(self, l2_cache):
        """Test basic set/get operations"""
        # Set value
        success = l2_cache.set("test_key", "test_value")
        assert success is True

        # Get value
        value = l2_cache.get("test_key")
        assert value == "test_value"

        # Check stats
        stats = l2_cache.get_stats()
        assert stats["hits"] == 1
        assert stats["misses"] == 0

    def test_l2_cache_ttl_expiration(self, l2_cache):
        """Test TTL expiration"""
        # Set value with short TTL
        l2_cache.set("test_key", "test_value", ttl_seconds=1)

        # Value should be available immediately
        value = l2_cache.get("test_key")
        assert value == "test_value"

        # Wait for expiration
        time.sleep(1.1)

        # Value should be expired
        value = l2_cache.get("test_key")
        assert value is None

    def test_l2_cache_invalidation(self, l2_cache):
        """Test cache invalidation"""
        # Add multiple entries
        l2_cache.set("user_1", "data_1")
        l2_cache.set("user_2", "data_2")
        l2_cache.set("config_1", "config_data")

        # Invalidate all
        count = l2_cache.invalidate()
        assert count == 3

        # Check cache is empty
        stats = l2_cache.get_stats()
        assert stats["entries"] == 0


class TestCacheManager:
    """Test Multi-Level Cache Manager"""

    @pytest.fixture
    def config(self):
        """Test configuration"""
        return {
            "caching": {
                "enabled": True,
                "multi_level": {
                    "l1_cache": {"enabled": True, "max_size_mb": 1, "ttl_seconds": 60},
                    "l2_cache": {
                        "enabled": True,
                        "host": "localhost",
                        "port": 6379,
                        "max_size_mb": 2,
                        "ttl_seconds": 120,
                    },
                    "l3_cache": {"enabled": False},
                },
                "analytics": {"enabled": True},
            }
        }

    @pytest.fixture
    def cache_manager(self, config):
        """Cache manager instance"""
        return CacheManager(config)

    def test_cache_manager_initialization(self, cache_manager, config):
        """Test cache manager initialization"""
        assert cache_manager.enabled is True
        assert cache_manager.l1_cache is not None
        assert cache_manager.l2_cache is not None
        assert cache_manager.l3_cache is None
        assert cache_manager.analytics_enabled is True

    def test_cache_hierarchy(self, cache_manager):
        """Test cache hierarchy (L1 -> L2)"""
        # Set value in L2 cache directly
        cache_manager.l2_cache.set("test_key", "test_value")

        # Get from cache manager (should populate L1 from L2)
        value = cache_manager.get("test_key")
        assert value == "test_value"

        # Check that L1 cache was populated
        l1_value = cache_manager.l1_cache.get("test_key")
        assert l1_value == "test_value"

    def test_cache_set_all_levels(self, cache_manager):
        """Test setting value in all cache levels"""
        success = cache_manager.set("test_key", "test_value")
        assert success is True

        # Check L1 cache
        l1_value = cache_manager.l1_cache.get("test_key")
        assert l1_value == "test_value"

        # Check L2 cache
        l2_value = cache_manager.l2_cache.get("test_key")
        assert l2_value == "test_value"

    def test_cache_invalidation_all_levels(self, cache_manager):
        """Test invalidating all cache levels"""
        # Add data to both levels
        cache_manager.set("user_1", "data_1")
        cache_manager.set("user_2", "data_2")

        # Invalidate all
        count = cache_manager.invalidate()
        assert count >= 2  # At least 2 entries should be invalidated

        # Check both levels are empty
        l1_stats = cache_manager.l1_cache.get_stats()
        l2_stats = cache_manager.l2_cache.get_stats()
        assert l1_stats["entries"] == 0
        assert l2_stats["entries"] == 0

    def test_cache_pattern_invalidation(self, cache_manager):
        """Test pattern-based invalidation"""
        # Add entries with different patterns
        cache_manager.set("user_1", "data_1")
        cache_manager.set("user_2", "data_2")
        cache_manager.set("config_1", "config_data")

        # Invalidate user entries
        count = cache_manager.invalidate("user")
        assert count >= 2  # At least 2 user entries should be invalidated

    def test_cache_stats(self, cache_manager):
        """Test cache statistics"""
        # Add some data
        cache_manager.set("test_1", "value_1")
        cache_manager.set("test_2", "value_2")

        # Get stats
        stats = cache_manager.get_stats()
        assert stats["enabled"] is True
        assert "levels" in stats
        assert "overall" in stats
        assert "l1" in stats["levels"]
        assert "l2" in stats["levels"]

    def test_cache_analytics(self, cache_manager):
        """Test cache analytics"""
        # Start analytics
        cache_manager.start_analytics()
        assert cache_manager.analytics_running is True

        # Add some data to generate analytics
        cache_manager.set("test_key", "test_value")
        cache_manager.get("test_key")

        # Get analytics
        analytics = cache_manager.get_analytics(1)
        assert analytics["enabled"] is True

        # Stop analytics
        cache_manager.stop_analytics()
        assert cache_manager.analytics_running is False

    def test_cache_status(self, cache_manager):
        """Test cache status"""
        status = cache_manager.get_status()
        assert status["enabled"] is True
        assert status["levels"]["l1"] is True
        assert status["levels"]["l2"] is True
        assert status["levels"]["l3"] is False
        assert status["analytics"]["enabled"] is True

    def test_cache_config_update(self, cache_manager):
        """Test cache configuration update"""
        new_config = {
            "caching": {
                "enabled": True,
                "multi_level": {
                    "l1_cache": {"enabled": True, "max_size_mb": 2, "ttl_seconds": 120}
                },
            }
        }

        cache_manager.update_config(new_config)
        assert cache_manager.enabled is True


class TestPerformanceManager:
    """Test Performance Manager Integration"""

    @pytest.fixture
    def performance_manager(self):
        """Performance manager instance"""
        return PerformanceManager("config/performance_config.json")

    def test_performance_manager_initialization(self, performance_manager):
        """Test performance manager initialization"""
        assert performance_manager.cache_manager is not None
        assert performance_manager.resource_monitor is not None
        assert performance_manager.load_tester is not None
        assert performance_manager.analytics is not None
        assert performance_manager.bundle_optimizer is not None

    def test_cache_operations(self, performance_manager):
        """Test cache operations through performance manager"""
        # Set cache
        success = performance_manager.set_cache("test_key", "test_value")
        assert success is True

        # Get cache
        value = performance_manager.get_cache("test_key")
        assert value == "test_value"

        # Get cache stats
        stats = performance_manager.get_cache_stats()
        assert "enabled" in stats

    def test_cache_invalidation(self, performance_manager):
        """Test cache invalidation through performance manager"""
        # Add data
        performance_manager.set_cache("user_1", "data_1")
        performance_manager.set_cache("user_2", "data_2")

        # Invalidate
        count = performance_manager.invalidate_cache("user")
        assert count >= 0  # Should return number of invalidated entries

    def test_resource_monitoring(self, performance_manager):
        """Test resource monitoring"""
        # Get resource usage
        usage = performance_manager.get_resource_usage()
        assert isinstance(usage, dict)

        # Get performance metrics
        metrics = performance_manager.get_performance_metrics()
        assert isinstance(metrics, dict)

    def test_monitoring_control(self, performance_manager):
        """Test monitoring start/stop"""
        # Start monitoring
        success = performance_manager.start_monitoring()
        assert success is True

        # Stop monitoring
        success = performance_manager.stop_monitoring()
        assert success is True


class TestAdvancedCachingFeatures:
    """Test Advanced Caching Features"""

    @pytest.fixture
    def config(self):
        """Advanced caching configuration"""
        return {
            "caching": {
                "enabled": True,
                "multi_level": {
                    "l1_cache": {"enabled": True, "max_size_mb": 1, "ttl_seconds": 60},
                    "l2_cache": {"enabled": True, "max_size_mb": 2, "ttl_seconds": 120},
                },
                "strategies": {
                    "smart_invalidation": {
                        "enabled": True,
                        "time_based": {"enabled": True, "default_ttl_seconds": 1800},
                        "event_based": {"enabled": True, "invalidate_on_write": True},
                    },
                    "cache_warming": {
                        "enabled": True,
                        "predictive_preloading": {"enabled": True},
                        "background_warming": {"enabled": True},
                    },
                },
                "analytics": {"enabled": True},
            }
        }

    @pytest.fixture
    def cache_manager(self, config):
        """Cache manager with advanced features"""
        return CacheManager(config)

    def test_smart_invalidation(self, cache_manager):
        """Test smart invalidation features"""
        # Add data with different patterns
        cache_manager.set("user:1:profile", "user_data")
        cache_manager.set("user:2:profile", "user_data")
        cache_manager.set("config:app:settings", "config_data")

        # Test pattern invalidation
        count = cache_manager.invalidate("user:")
        assert count >= 2

        # Test remaining data
        remaining = cache_manager.get("config:app:settings")
        assert remaining == "config_data"

    def test_cache_warming_simulation(self, cache_manager):
        """Test cache warming simulation"""
        # Simulate predictive preloading
        warm_data = {
            "frequently_accessed": "data",
            "critical_path": "data",
            "user_preferences": "data",
        }

        # Preload data
        for key, value in warm_data.items():
            cache_manager.set(key, value)

        # Verify preloaded data is accessible
        for key, expected_value in warm_data.items():
            value = cache_manager.get(key)
            assert value == expected_value

    def test_cache_analytics_comprehensive(self, cache_manager):
        """Test comprehensive cache analytics"""
        # Start analytics
        cache_manager.start_analytics()

        # Generate various cache events
        cache_manager.set("test_1", "value_1")
        cache_manager.set("test_2", "value_2")
        cache_manager.get("test_1")  # Hit
        cache_manager.get("test_3")  # Miss
        cache_manager.invalidate("test")

        # Get analytics
        analytics = cache_manager.get_analytics(1)
        assert analytics["enabled"] is True
        assert "event_counts" in analytics

        # Stop analytics
        cache_manager.stop_analytics()

    def test_cache_performance_metrics(self, cache_manager):
        """Test cache performance metrics"""
        # Generate load
        for i in range(10):
            cache_manager.set(f"key_{i}", f"value_{i}")
            cache_manager.get(f"key_{i}")

        # Get stats
        stats = cache_manager.get_stats()
        assert stats["enabled"] is True
        assert "overall" in stats
        assert "overall_hit_ratio" in stats["overall"]
        assert stats["overall"]["overall_hit_ratio"] > 0

    def test_cache_memory_optimization(self, cache_manager):
        """Test cache memory optimization"""
        # Fill cache with large data
        large_data = "x" * 100000  # 100KB per entry

        for i in range(5):
            cache_manager.set(f"large_key_{i}", large_data)

        # Check memory usage
        stats = cache_manager.get_stats()
        l1_stats = stats["levels"]["l1"]
        l2_stats = stats["levels"]["l2"]

        # Verify memory limits are respected
        assert l1_stats["size_mb"] <= l1_stats["max_size_mb"]
        assert l2_stats["size_mb"] <= l2_stats["max_size_mb"]

    def test_cache_thread_safety_advanced(self, cache_manager):
        """Test advanced thread safety"""
        results = []
        errors = []

        def concurrent_worker(worker_id):
            try:
                for i in range(20):
                    key = f"worker_{worker_id}_key_{i}"
                    value = f"worker_{worker_id}_value_{i}"

                    # Set value
                    cache_manager.set(key, value)

                    # Get value
                    retrieved = cache_manager.get(key)

                    # Invalidate some entries
                    if i % 5 == 0:
                        cache_manager.invalidate(f"worker_{worker_id}")

                    if retrieved == value:
                        results.append(True)
                    else:
                        results.append(False)

            except Exception as e:
                errors.append(str(e))

        # Start multiple threads
        threads = []
        for i in range(3):
            thread = threading.Thread(target=concurrent_worker, args=(i,))
            threads.append(thread)
            thread.start()

        # Wait for all threads
        for thread in threads:
            thread.join()

        # Check results
        assert len(errors) == 0, f"Thread safety errors: {errors}"
        success_rate = sum(results) / len(results) if results else 0
        assert success_rate > 0.8, f"Low success rate: {success_rate}"


class TestCacheConfiguration:
    """Test Cache Configuration Management"""

    def test_performance_config_loading(self):
        """Test performance configuration loading"""
        try:
            with open("config/performance_config.json", "r") as f:
                config = json.load(f)

            # Check caching configuration
            caching_config = config.get("caching", {})
            assert caching_config.get("enabled") is True

            # Check multi-level configuration
            multi_level = caching_config.get("multi_level", {})
            assert "l1_cache" in multi_level
            assert "l2_cache" in multi_level
            assert "l3_cache" in multi_level

            # Check strategies
            strategies = caching_config.get("strategies", {})
            assert "smart_invalidation" in strategies
            assert "cache_warming" in strategies

        except FileNotFoundError:
            pytest.skip("Performance config file not found")

    def test_cache_policies(self):
        """Test cache policies configuration"""
        try:
            with open("config/performance_config.json", "r") as f:
                config = json.load(f)

            caching_config = config.get("caching", {})

            # Check L1 cache policies
            l1_config = caching_config.get("multi_level", {}).get("l1_cache", {})
            assert l1_config.get("enabled") is True
            assert l1_config.get("eviction_policy") == "lru"
            assert l1_config.get("max_size_mb") > 0
            assert l1_config.get("ttl_seconds") > 0

            # Check L2 cache policies
            l2_config = caching_config.get("multi_level", {}).get("l2_cache", {})
            assert l2_config.get("enabled") is True
            assert l2_config.get("eviction_policy") == "lru"
            assert l2_config.get("max_size_mb") > 0
            assert l2_config.get("ttl_seconds") > 0

        except FileNotFoundError:
            pytest.skip("Performance config file not found")

    def test_expiration_rules(self):
        """Test expiration rules configuration"""
        try:
            with open("config/performance_config.json", "r") as f:
                config = json.load(f)

            strategies = config.get("caching", {}).get("strategies", {})
            smart_invalidation = strategies.get("smart_invalidation", {})

            # Check time-based invalidation
            time_based = smart_invalidation.get("time_based", {})
            assert time_based.get("enabled") is True
            assert time_based.get("default_ttl_seconds") > 0
            assert time_based.get("short_ttl_seconds") > 0
            assert time_based.get("long_ttl_seconds") > 0

        except FileNotFoundError:
            pytest.skip("Performance config file not found")

    def test_memory_limits(self):
        """Test memory limits configuration"""
        try:
            with open("config/performance_config.json", "r") as f:
                config = json.load(f)

            multi_level = config.get("caching", {}).get("multi_level", {})

            # Check L1 memory limits
            l1_config = multi_level.get("l1_cache", {})
            assert l1_config.get("max_size_mb") > 0
            assert l1_config.get("max_size_mb") <= 1000  # Reasonable limit

            # Check L2 memory limits
            l2_config = multi_level.get("l2_cache", {})
            assert l2_config.get("max_size_mb") > 0
            assert l2_config.get("max_size_mb") <= 10000  # Reasonable limit

        except FileNotFoundError:
            pytest.skip("Performance config file not found")


class TestCacheIntegration:
    """Test Cache Integration with Other Systems"""

    @pytest.fixture
    def performance_manager(self):
        """Performance manager for integration tests"""
        return PerformanceManager("config/performance_config.json")

    def test_cache_performance_monitoring_integration(self, performance_manager):
        """Test cache integration with performance monitoring"""
        # Start monitoring
        performance_manager.start_monitoring()

        # Perform cache operations
        performance_manager.set_cache("integration_test", "test_data")
        retrieved = performance_manager.get_cache("integration_test")

        # Get resource usage
        usage = performance_manager.get_resource_usage()

        # Stop monitoring
        performance_manager.stop_monitoring()

        # Verify integration
        assert retrieved == "test_data"
        assert isinstance(usage, dict)

    def test_cache_resource_optimization_integration(self, performance_manager):
        """Test cache integration with resource optimization"""
        # Get initial resource usage
        initial_usage = performance_manager.get_resource_usage()

        # Perform cache operations
        for i in range(10):
            performance_manager.set_cache(f"opt_test_{i}", f"data_{i}")
            performance_manager.get_cache(f"opt_test_{i}")

        # Get final resource usage
        final_usage = performance_manager.get_resource_usage()

        # Verify resource usage is tracked
        assert isinstance(initial_usage, dict)
        assert isinstance(final_usage, dict)

    def test_cache_validation_integration(self, performance_manager):
        """Test cache validation integration"""
        # Set cache data
        test_data = {"key": "value", "number": 42, "list": [1, 2, 3]}
        performance_manager.set_cache("validation_test", test_data)

        # Retrieve and validate
        retrieved = performance_manager.get_cache("validation_test")

        # Validate data integrity
        assert retrieved == test_data
        assert retrieved["key"] == "value"
        assert retrieved["number"] == 42
        assert retrieved["list"] == [1, 2, 3]

    def test_cache_analytics_integration(self, performance_manager):
        """Test cache analytics integration"""
        # Get cache stats
        stats = performance_manager.get_cache_stats()

        # Verify analytics data
        assert "enabled" in stats
        assert "levels" in stats
        assert "overall" in stats

        # Check cache manager analytics
        cache_manager = performance_manager.cache_manager
        if cache_manager.analytics_enabled:
            analytics = cache_manager.get_analytics(1)
            assert analytics["enabled"] is True


class TestCachePerformance:
    """Test Cache Performance Characteristics"""

    @pytest.fixture
    def cache_manager(self):
        """Cache manager for performance tests"""
        config = {
            "caching": {
                "enabled": True,
                "multi_level": {
                    "l1_cache": {
                        "enabled": True,
                        "max_size_mb": 10,
                        "ttl_seconds": 300,
                    },
                    "l2_cache": {
                        "enabled": True,
                        "max_size_mb": 50,
                        "ttl_seconds": 3600,
                    },
                },
                "analytics": {"enabled": True},
            }
        }
        return CacheManager(config)

    def test_cache_response_time(self, cache_manager):
        """Test cache response time performance"""
        import time

        # Measure set performance
        start_time = time.time()
        for i in range(100):
            cache_manager.set(f"perf_key_{i}", f"perf_value_{i}")
        set_time = time.time() - start_time

        # Measure get performance
        start_time = time.time()
        for i in range(100):
            cache_manager.get(f"perf_key_{i}")
        get_time = time.time() - start_time

        # Verify reasonable performance (should be very fast)
        assert set_time < 1.0, f"Set operations too slow: {set_time}s"
        assert get_time < 1.0, f"Get operations too slow: {get_time}s"

    def test_cache_hit_ratio_performance(self, cache_manager):
        """Test cache hit ratio performance"""
        # Generate cache hits
        for i in range(50):
            cache_manager.set(f"hit_key_{i}", f"hit_value_{i}")
            cache_manager.get(f"hit_key_{i}")  # Hit

        # Generate cache misses
        for i in range(50):
            cache_manager.get(f"miss_key_{i}")  # Miss

        # Get stats
        stats = cache_manager.get_stats()
        overall_stats = stats["overall"]

        # Verify hit ratio is reasonable (should be around 33% in this test due to L1/L2 structure)
        hit_ratio = overall_stats["overall_hit_ratio"]
        assert 25 <= hit_ratio <= 40, f"Unexpected hit ratio: {hit_ratio}%"

    def test_cache_memory_efficiency(self, cache_manager):
        """Test cache memory efficiency"""
        # Add data and measure memory usage
        initial_stats = cache_manager.get_stats()

        # Add moderate amount of data
        for i in range(100):
            cache_manager.set(f"mem_key_{i}", f"mem_value_{i}" * 100)  # Larger values

        final_stats = cache_manager.get_stats()

        # Verify memory usage is reasonable
        l1_final = final_stats["levels"]["l1"]
        l2_final = final_stats["levels"]["l2"]

        # Check that memory usage is within limits
        assert l1_final["size_mb"] <= l1_final["max_size_mb"]
        assert l2_final["size_mb"] <= l2_final["max_size_mb"]

    def test_cache_concurrent_performance(self, cache_manager):
        """Test cache performance under concurrent access"""
        results = []
        errors = []

        def concurrent_performance_worker(worker_id):
            try:
                start_time = time.time()

                # Perform cache operations
                for i in range(50):
                    key = f"concurrent_{worker_id}_{i}"
                    value = f"value_{worker_id}_{i}"

                    cache_manager.set(key, value)
                    retrieved = cache_manager.get(key)

                    if retrieved != value:
                        results.append(False)
                        return

                end_time = time.time()
                duration = end_time - start_time

                # Verify reasonable performance
                if duration < 5.0:  # Should complete within 5 seconds
                    results.append(True)
                else:
                    results.append(False)

            except Exception as e:
                errors.append(str(e))

        # Start multiple threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=concurrent_performance_worker, args=(i,))
            threads.append(thread)
            thread.start()

        # Wait for all threads
        for thread in threads:
            thread.join()

        # Check results
        assert len(errors) == 0, f"Concurrent performance errors: {errors}"
        success_rate = sum(results) / len(results) if results else 0
        assert (
            success_rate > 0.8
        ), f"Low concurrent performance success rate: {success_rate}"


if __name__ == "__main__":
    # Run all tests
    pytest.main([__file__, "-v"])
