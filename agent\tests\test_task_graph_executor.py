#!/usr/bin/env python3
"""
Tests for TaskGraphExecutor - Dependency-aware task execution
"""
import asyncio
from unittest.mock import AsyncMock

import pytest

from agent.core.agents.task_graph_executor import (
    ExecutionState,
    RetryPolicy,
    TaskGraphExecutor,
    TaskNode,
)


@pytest.fixture
def graph_executor():
    return TaskGraphExecutor(max_concurrent_tasks=2)


@pytest.fixture
def sample_tasks():
    """Create sample tasks with dependencies"""
    tasks = []
    
    # Task 1: No dependencies
    tasks.append(TaskNode(
        task_id="task1",
        title="Initialize Repository",
        description="Set up git repository",
        agent_type="shell",
        dependencies=[],
        max_retries=2
    ))
    
    # Task 2: Depends on task1
    tasks.append(TaskNode(
        task_id="task2",
        title="Setup Database",
        description="Configure PostgreSQL",
        agent_type="backend",
        dependencies=["task1"],
        max_retries=2
    ))
    
    # Task 3: Depends on task1
    tasks.append(TaskNode(
        task_id="task3",
        title="Create Frontend",
        description="Initialize React app",
        agent_type="frontend",
        dependencies=["task1"],
        max_retries=2
    ))
    
    # Task 4: Depends on task2 and task3
    tasks.append(TaskNode(
        task_id="task4",
        title="Setup Containers",
        description="Create Docker configuration",
        agent_type="container",
        dependencies=["task2", "task3"],
        max_retries=2
    ))
    
    return tasks


def test_task_node_creation():
    """Test TaskNode creation and basic properties"""
    task = TaskNode(
        task_id="test_task",
        title="Test Task",
        description="A test task",
        agent_type="test",
        dependencies=["dep1", "dep2"]
    )
    
    assert task.task_id == "test_task"
    assert task.title == "Test Task"
    assert task.state == ExecutionState.PENDING
    assert task.dependencies == ["dep1", "dep2"]
    assert task.retry_count == 0


def test_task_node_ready_to_execute():
    """Test task readiness based on dependencies"""
    task = TaskNode(
        task_id="test_task",
        title="Test Task",
        description="A test task",
        agent_type="test",
        dependencies=["dep1", "dep2"]
    )
    
    # Not ready when dependencies not completed
    assert not task.is_ready_to_execute(set())
    assert not task.is_ready_to_execute({"dep1"})
    
    # Ready when all dependencies completed
    assert task.is_ready_to_execute({"dep1", "dep2"})
    assert task.is_ready_to_execute({"dep1", "dep2", "extra"})


def test_retry_delay_calculation():
    """Test retry delay calculation for different policies"""
    task = TaskNode(
        task_id="test_task",
        title="Test Task",
        description="A test task",
        agent_type="test",
        retry_delay_base=5
    )
    
    # Test exponential backoff
    task.retry_policy = RetryPolicy.EXPONENTIAL_BACKOFF
    task.retry_count = 0
    assert task.calculate_retry_delay() == 5
    task.retry_count = 1
    assert task.calculate_retry_delay() == 10
    task.retry_count = 2
    assert task.calculate_retry_delay() == 20
    
    # Test linear backoff
    task.retry_policy = RetryPolicy.LINEAR_BACKOFF
    task.retry_count = 0
    assert task.calculate_retry_delay() == 5
    task.retry_count = 1
    assert task.calculate_retry_delay() == 10
    task.retry_count = 2
    assert task.calculate_retry_delay() == 15
    
    # Test fixed delay
    task.retry_policy = RetryPolicy.FIXED_DELAY
    task.retry_count = 0
    assert task.calculate_retry_delay() == 5
    task.retry_count = 5
    assert task.calculate_retry_delay() == 5


def test_add_task_to_graph(graph_executor, sample_tasks):
    """Test adding tasks to the graph"""
    for task in sample_tasks:
        result = graph_executor.add_task(task)
        assert result is True
    
    assert len(graph_executor.tasks) == 4
    assert graph_executor.stats.total_tasks == 4
    
    # Check dependency graph construction
    assert "task1" in graph_executor.dependency_graph["task2"]
    assert "task1" in graph_executor.dependency_graph["task3"]
    assert "task2" in graph_executor.dependency_graph["task4"]
    assert "task3" in graph_executor.dependency_graph["task4"]


def test_graph_validation_success(graph_executor, sample_tasks):
    """Test successful graph validation"""
    for task in sample_tasks:
        graph_executor.add_task(task)
    
    is_valid, errors = graph_executor.validate_graph()
    assert is_valid is True
    assert len(errors) == 0


def test_graph_validation_missing_dependency(graph_executor):
    """Test graph validation with missing dependency"""
    task = TaskNode(
        task_id="task1",
        title="Test Task",
        description="A test task",
        agent_type="test",
        dependencies=["missing_task"]
    )
    
    graph_executor.add_task(task)
    is_valid, errors = graph_executor.validate_graph()
    
    assert is_valid is False
    assert len(errors) == 1
    assert "missing_task" in errors[0]


def test_graph_validation_circular_dependency(graph_executor):
    """Test graph validation with circular dependency"""
    task1 = TaskNode(
        task_id="task1",
        title="Task 1",
        description="First task",
        agent_type="test",
        dependencies=["task2"]
    )
    
    task2 = TaskNode(
        task_id="task2",
        title="Task 2",
        description="Second task",
        agent_type="test",
        dependencies=["task1"]
    )
    
    graph_executor.add_task(task1)
    graph_executor.add_task(task2)
    
    is_valid, errors = graph_executor.validate_graph()
    
    assert is_valid is False
    assert len(errors) == 1
    assert "Circular dependency" in errors[0]


def test_execution_order(graph_executor, sample_tasks):
    """Test topological sorting for execution order"""
    for task in sample_tasks:
        graph_executor.add_task(task)
    
    execution_batches = graph_executor.get_execution_order()
    
    # Should have 3 batches: [task1], [task2, task3], [task4]
    assert len(execution_batches) == 3
    assert execution_batches[0] == ["task1"]
    assert set(execution_batches[1]) == {"task2", "task3"}
    assert execution_batches[2] == ["task4"]


@pytest.mark.asyncio
async def test_single_task_execution(graph_executor):
    """Test execution of a single task"""
    executed = False
    
    async def mock_executor():
        nonlocal executed
        executed = True
        return {"success": True, "message": "Task completed"}
    
    task = TaskNode(
        task_id="test_task",
        title="Test Task",
        description="A test task",
        agent_type="test",
        executor_func=mock_executor
    )
    
    graph_executor.add_task(task)
    result = await graph_executor.execute_graph()
    
    assert result["success"] is True
    assert result["completed_tasks"] == 1
    assert result["failed_tasks"] == 0
    assert executed is True
    assert task.state == ExecutionState.COMPLETED


@pytest.mark.asyncio
async def test_task_execution_with_dependencies(graph_executor, sample_tasks):
    """Test execution of tasks with dependencies"""
    execution_order = []
    
    async def create_mock_executor(task_id):
        async def mock_executor():
            execution_order.append(task_id)
            await asyncio.sleep(0.01)  # Simulate work
            return {"success": True, "message": f"Task {task_id} completed"}
        return mock_executor
    
    # Add executor functions to tasks
    for task in sample_tasks:
        task.executor_func = await create_mock_executor(task.task_id)
        graph_executor.add_task(task)
    
    result = await graph_executor.execute_graph()
    
    assert result["success"] is True
    assert result["completed_tasks"] == 4
    assert result["failed_tasks"] == 0
    
    # Verify execution order respects dependencies
    task1_index = execution_order.index("task1")
    task2_index = execution_order.index("task2")
    task3_index = execution_order.index("task3")
    task4_index = execution_order.index("task4")
    
    # task1 should execute before task2 and task3
    assert task1_index < task2_index
    assert task1_index < task3_index
    
    # task4 should execute after task2 and task3
    assert task4_index > task2_index
    assert task4_index > task3_index


@pytest.mark.asyncio
async def test_task_failure_and_retry(graph_executor):
    """Test task failure and retry logic"""
    attempt_count = 0
    
    async def failing_executor():
        nonlocal attempt_count
        attempt_count += 1
        if attempt_count < 3:
            raise Exception(f"Attempt {attempt_count} failed")
        return {"success": True, "message": "Finally succeeded"}
    
    task = TaskNode(
        task_id="failing_task",
        title="Failing Task",
        description="A task that fails initially",
        agent_type="test",
        executor_func=failing_executor,
        max_retries=3,
        retry_policy=RetryPolicy.FIXED_DELAY,
        retry_delay_base=0  # No delay for testing
    )
    
    graph_executor.add_task(task)
    result = await graph_executor.execute_graph()
    
    assert result["success"] is True
    assert result["completed_tasks"] == 1
    assert result["failed_tasks"] == 0
    assert attempt_count == 3
    assert task.retry_count == 2  # 2 retries after initial failure


@pytest.mark.asyncio
async def test_task_permanent_failure(graph_executor):
    """Test task that fails permanently"""
    async def always_failing_executor():
        raise Exception("This task always fails")
    
    task = TaskNode(
        task_id="failing_task",
        title="Always Failing Task",
        description="A task that always fails",
        agent_type="test",
        executor_func=always_failing_executor,
        max_retries=2,
        retry_delay_base=0  # No delay for testing
    )
    
    graph_executor.add_task(task)
    result = await graph_executor.execute_graph()
    
    assert result["success"] is False
    assert result["completed_tasks"] == 0
    assert result["failed_tasks"] == 1
    assert task.state == ExecutionState.FAILED
    assert task.retry_count == 3  # Initial attempt + 2 retries


def test_progress_tracking(graph_executor, sample_tasks):
    """Test progress tracking functionality"""
    for task in sample_tasks:
        graph_executor.add_task(task)
    
    progress = graph_executor.get_progress_info()
    
    assert progress["total_tasks"] == 4
    assert progress["completed_tasks"] == 0
    assert progress["failed_tasks"] == 0
    assert progress["pending_tasks"] == 4
    assert progress["completion_percentage"] == 0.0
    assert progress["is_running"] is False


def test_task_status_retrieval(graph_executor, sample_tasks):
    """Test getting status of specific tasks"""
    for task in sample_tasks:
        graph_executor.add_task(task)
    
    status = graph_executor.get_task_status("task1")
    
    assert status is not None
    assert status["task_id"] == "task1"
    assert status["title"] == "Initialize Repository"
    assert status["state"] == "pending"
    assert status["dependencies"] == []
    
    # Test non-existent task
    assert graph_executor.get_task_status("non_existent") is None
