# Session Management System

## Overview

The AI Coding Agent now includes a comprehensive session management system that provides enhanced security, user control, and audit capabilities. This system tracks user sessions, monitors login attempts, and provides detailed authentication logs.

## Features

### 🔐 **Session Tracking**
- **Multi-device Support**: Track sessions across different devices and browsers
- **Device Detection**: Automatically detect browser, OS, and device type
- **IP Address Tracking**: Monitor login locations for security
- **Activity Monitoring**: Track last activity and session duration

### 🛡️ **Security Features**
- **Account Lockout**: Automatic account locking after failed login attempts
- **Session Revocation**: Users can revoke individual or all sessions
- **Token Refresh**: Secure token refresh mechanism
- **Expired Session Cleanup**: Automatic cleanup of expired sessions

### 📊 **Audit & Monitoring**
- **Authentication Logs**: Complete audit trail of all auth events
- **Login Attempt Tracking**: Monitor failed login attempts
- **Security Event Logging**: Track security-related activities
- **Export Capabilities**: Export logs for compliance

## Database Schema

### User Sessions Table
```sql
CREATE TABLE user_sessions (
    id INTEGER PRIMARY KEY,
    user_id INTEGER NOT NULL,
    session_token VARCHAR(100) UNIQUE NOT NULL,
    refresh_token VARCHAR(100) UNIQUE,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    device_info JSON,
    expires_at DATETIME NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_activity DATETIME DEFAULT CURRENT_TIMESTAMP,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### Login Attempts Table
```sql
CREATE TABLE login_attempts (
    id INTEGER PRIMARY KEY,
    user_id INTEGER,
    username VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45) NOT NULL,
    user_agent VARCHAR(500),
    success BOOLEAN DEFAULT FALSE,
    failure_reason VARCHAR(200),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

### Authentication Logs Table
```sql
CREATE TABLE auth_logs (
    id INTEGER PRIMARY KEY,
    user_id INTEGER,
    event_type VARCHAR(50) NOT NULL,
    ip_address VARCHAR(45),
    user_agent VARCHAR(500),
    details JSON,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id)
);
```

## API Endpoints

### Session Management
```typescript
// Get user sessions
GET /api/v1/auth/sessions
Response: Session[]

// Revoke specific session
DELETE /api/v1/auth/sessions/{sessionId}
Response: { success: boolean }

// Revoke all other sessions
DELETE /api/v1/auth/sessions/others
Response: { revoked_count: number }

// Refresh access token
POST /api/v1/auth/refresh
Body: { refresh_token: string }
Response: { access_token: string, expires_at: string }
```

### Authentication Logs
```typescript
// Get authentication logs
GET /api/v1/auth/logs?filter=all&skip=0&limit=50
Response: AuthLog[]

// Export authentication logs
GET /api/v1/auth/logs/export
Response: File download
```

## Security Manager Methods

### Session Management
```python
# Create session tokens
def create_session_tokens(self, user_id: int, username: str,
                         is_superuser: bool = False,
                         ip_address: str = None,
                         user_agent: str = None) -> Dict[str, Any]

# Verify session token
def verify_session_token(self, token: str, update_activity: bool = True) -> Optional[Dict[str, Any]]

# Refresh session token
def refresh_session_token(self, refresh_token: str, ip_address: str = None) -> Optional[Dict[str, Any]]

# Revoke session
def revoke_session(self, session_id: int, user_id: int) -> bool

# Revoke all user sessions
def revoke_all_user_sessions(self, user_id: int) -> int

# Get user sessions
def get_user_sessions(self, user_id: int, skip: int = 0, limit: int = 50) -> List[Dict[str, Any]]

# Cleanup expired sessions
def cleanup_expired_sessions(self) -> int
```

### Authentication & Security
```python
# Enhanced authentication with security features
def authenticate_user(self, username: str, password: str,
                     ip_address: str = None,
                     user_agent: str = None) -> Optional[Dict[str, Any]]

# Logout user
def logout_user(self, session_id: int, user_id: int) -> bool

# Get authentication logs
def get_auth_logs(self, user_id: int, skip: int = 0, limit: int = 50) -> List[Dict[str, Any]]
```

## Frontend Components

### Session Manager Component
```typescript
import { SessionManager } from '@/components/auth/SessionManager';

// Features:
// - View all active sessions
// - Device and location information
// - Session activity status
// - Revoke individual sessions
// - Revoke all other sessions
// - Security tips and guidance
```

### Authentication Logs Component
```typescript
import { AuthLogs } from '@/components/auth/AuthLogs';

// Features:
// - View authentication history
// - Filter by event type
// - Export logs
// - Security monitoring alerts
// - Detailed event information
```

## Security Features

### Account Protection
1. **Failed Login Tracking**: Monitor and count failed login attempts
2. **Account Lockout**: Automatically lock accounts after too many failed attempts
3. **IP-based Monitoring**: Track login attempts by IP address
4. **User Agent Tracking**: Monitor login patterns by device/browser

### Session Security
1. **Token-based Authentication**: Secure session tokens with expiration
2. **Refresh Token Rotation**: Automatic token refresh with new access tokens
3. **Session Revocation**: Users can revoke sessions from any device
4. **Activity Monitoring**: Track and update last activity timestamps

### Audit & Compliance
1. **Complete Audit Trail**: Log all authentication events
2. **GDPR Compliance**: Support for data export and deletion
3. **Security Event Logging**: Track security-related activities
4. **Export Capabilities**: Export logs for compliance requirements

## Configuration

### Environment Variables
```bash
# Session configuration
JWT_SECRET_KEY=your-jwt-secret-change-in-production
ENCRYPTION_KEY=your-secret-key-change-in-production

# Security settings
MAX_LOGIN_ATTEMPTS=5
LOCKOUT_DURATION_MINUTES=30
SESSION_TIMEOUT_MINUTES=60
```

### Security Settings
```json
{
  "security": {
    "password_min_length": 8,
    "require_2fa": false,
    "session_timeout_minutes": 60,
    "max_login_attempts": 5,
    "lockout_duration_minutes": 30
  }
}
```

## Usage Examples

### Backend Usage
```python
from src.security_manager import security_manager

# Authenticate user with session creation
auth_result = security_manager.authenticate_user(
    username="admin",
    password="admin123",
    ip_address="*************",
    user_agent="Mozilla/5.0..."
)

if auth_result:
    user = auth_result["user"]
    session = auth_result["session"]
    print(f"User {user['username']} logged in successfully")
    print(f"Session ID: {session['session_id']}")

# Verify session token
session_info = security_manager.verify_session_token("session_token_here")
if session_info:
    print(f"Valid session for user: {session_info['username']}")

# Get user sessions
sessions = security_manager.get_user_sessions(user_id=1)
for session in sessions:
    print(f"Session {session['id']}: {session['ip_address']}")

# Revoke session
success = security_manager.revoke_session(session_id=1, user_id=1)
if success:
    print("Session revoked successfully")
```

### Frontend Usage
```typescript
import { useAuthStore } from '@/store/auth';
import { SessionManager } from '@/components/auth/SessionManager';
import { AuthLogs } from '@/components/auth/AuthLogs';

// In your component
function SecurityPage() {
  return (
    <div className="space-y-8">
      <SessionManager />
      <AuthLogs />
    </div>
  );
}
```

## Security Best Practices

### For Users
1. **Regular Session Review**: Check active sessions regularly
2. **Revoke Unused Sessions**: Remove sessions from devices you no longer use
3. **Monitor Login Activity**: Review authentication logs for suspicious activity
4. **Strong Passwords**: Use strong, unique passwords
5. **Two-Factor Authentication**: Enable 2FA when available

### For Developers
1. **Secure Token Storage**: Store tokens securely (httpOnly cookies, secure storage)
2. **Token Rotation**: Implement automatic token refresh
3. **Session Cleanup**: Regularly clean up expired sessions
4. **Rate Limiting**: Implement rate limiting for authentication endpoints
5. **Audit Logging**: Log all security-related events

## Monitoring & Maintenance

### Automated Tasks
```python
# Cleanup expired sessions (run daily)
def cleanup_expired_sessions():
    count = security_manager.cleanup_expired_sessions()
    logger.info(f"Cleaned up {count} expired sessions")

# Cleanup old auth logs (run weekly)
def cleanup_old_auth_logs():
    # Implementation for cleaning old logs
    pass
```

### Health Checks
```python
# Check session health
def check_session_health():
    with get_db() as db:
        expired_sessions = user_session_manager.get_expired_sessions(db)
        active_sessions = user_session_manager.get_multi(db, limit=1000)

        return {
            "expired_sessions": len(expired_sessions),
            "active_sessions": len(active_sessions),
            "health_status": "healthy"
        }
```

## Benefits

### 🔒 **Enhanced Security**
- Complete session visibility and control
- Automatic account protection
- Comprehensive audit trails
- Real-time security monitoring

### 👤 **User Control**
- Manage sessions across devices
- Revoke access from any location
- View detailed login history
- Export personal data

### 📊 **Compliance**
- GDPR-compliant data handling
- Complete audit trails
- Data export capabilities
- Privacy-focused design

### 🚀 **Scalability**
- Efficient session management
- Automatic cleanup processes
- Optimized database queries
- Future-ready architecture

## Future Enhancements

### Planned Features
1. **Real-time Notifications**: Alert users of new login attempts
2. **Geographic Tracking**: Map login locations
3. **Device Fingerprinting**: Advanced device identification
4. **Risk-based Authentication**: Adaptive security measures
5. **Session Analytics**: Usage patterns and insights

### Integration Possibilities
1. **Email Notifications**: Login attempt alerts
2. **SMS Verification**: Two-factor authentication
3. **Biometric Authentication**: Fingerprint/face recognition
4. **Hardware Security Keys**: FIDO2 support
5. **Single Sign-On**: OAuth/OpenID Connect integration

This session management system provides enterprise-level security while maintaining user privacy and control. It's designed to scale with your application and can be extended with additional security features as needed.
