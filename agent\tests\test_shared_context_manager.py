#!/usr/bin/env python3
"""
Tests for SharedContextManager - Session memory and cross-agent communication
"""
import tempfile
import time
from datetime import datetime, timedelta
from pathlib import Path

import pytest

from agent.core.context.shared_context_manager import (
    ContextEntry,
    SessionContext,
    SharedContextManager,
)


@pytest.fixture
def temp_storage_dir():
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir


@pytest.fixture
def context_manager(temp_storage_dir):
    return SharedContextManager(storage_dir=temp_storage_dir, max_sessions=5)


def test_context_entry_creation():
    """Test ContextEntry creation and basic properties"""
    entry = ContextEntry(
        key="test_key",
        value="test_value",
        agent_id="test_agent",
        session_id="test_session",
        ttl_seconds=300,
        tags={"tag1", "tag2"}
    )
    
    assert entry.key == "test_key"
    assert entry.value == "test_value"
    assert entry.agent_id == "test_agent"
    assert entry.session_id == "test_session"
    assert entry.ttl_seconds == 300
    assert entry.tags == {"tag1", "tag2"}
    assert entry.version == 1
    assert entry.access_count == 0


def test_context_entry_expiration():
    """Test ContextEntry TTL and expiration"""
    # Entry without TTL should never expire
    entry_no_ttl = ContextEntry(
        key="test_key",
        value="test_value",
        agent_id="test_agent",
        session_id="test_session"
    )
    assert not entry_no_ttl.is_expired()
    
    # Entry with future TTL should not be expired
    entry_future = ContextEntry(
        key="test_key",
        value="test_value",
        agent_id="test_agent",
        session_id="test_session",
        ttl_seconds=3600  # 1 hour
    )
    assert not entry_future.is_expired()
    
    # Entry with past TTL should be expired
    entry_past = ContextEntry(
        key="test_key",
        value="test_value",
        agent_id="test_agent",
        session_id="test_session",
        ttl_seconds=1,
        timestamp=datetime.now() - timedelta(seconds=2)
    )
    assert entry_past.is_expired()


def test_context_entry_serialization():
    """Test ContextEntry to_dict and from_dict"""
    original_entry = ContextEntry(
        key="test_key",
        value={"nested": "data"},
        agent_id="test_agent",
        session_id="test_session",
        ttl_seconds=300,
        tags={"tag1", "tag2"}
    )
    
    # Convert to dict
    entry_dict = original_entry.to_dict()
    
    # Convert back from dict
    restored_entry = ContextEntry.from_dict(entry_dict)
    
    assert restored_entry.key == original_entry.key
    assert restored_entry.value == original_entry.value
    assert restored_entry.agent_id == original_entry.agent_id
    assert restored_entry.session_id == original_entry.session_id
    assert restored_entry.ttl_seconds == original_entry.ttl_seconds
    assert restored_entry.tags == original_entry.tags


def test_session_context_creation():
    """Test SessionContext creation and basic operations"""
    session = SessionContext(session_id="test_session")
    
    assert session.session_id == "test_session"
    assert len(session.entries) == 0
    assert isinstance(session.created_at, datetime)
    assert isinstance(session.last_activity, datetime)


def test_session_context_entry_management():
    """Test adding, getting, and removing entries in SessionContext"""
    session = SessionContext(session_id="test_session")
    
    # Add entry
    entry = ContextEntry(
        key="test_key",
        value="test_value",
        agent_id="test_agent",
        session_id="test_session"
    )
    session.add_entry(entry)
    
    assert len(session.entries) == 1
    assert "test_key" in session.entries
    
    # Get entry
    retrieved_entry = session.get_entry("test_key")
    assert retrieved_entry is not None
    assert retrieved_entry.value == "test_value"
    assert retrieved_entry.access_count == 1
    
    # Remove entry
    removed = session.remove_entry("test_key")
    assert removed is True
    assert len(session.entries) == 0
    
    # Try to remove non-existent entry
    removed = session.remove_entry("non_existent")
    assert removed is False


def test_session_context_expired_cleanup():
    """Test cleanup of expired entries in SessionContext"""
    session = SessionContext(session_id="test_session")
    
    # Add non-expired entry
    entry1 = ContextEntry(
        key="valid_key",
        value="valid_value",
        agent_id="test_agent",
        session_id="test_session",
        ttl_seconds=3600
    )
    session.add_entry(entry1)
    
    # Add expired entry
    entry2 = ContextEntry(
        key="expired_key",
        value="expired_value",
        agent_id="test_agent",
        session_id="test_session",
        ttl_seconds=1,
        timestamp=datetime.now() - timedelta(seconds=2)
    )
    session.add_entry(entry2)
    
    assert len(session.entries) == 2
    
    # Cleanup expired entries
    cleaned_count = session.cleanup_expired()
    
    assert cleaned_count == 1
    assert len(session.entries) == 1
    assert "valid_key" in session.entries
    assert "expired_key" not in session.entries


def test_shared_context_manager_initialization(context_manager):
    """Test SharedContextManager initialization"""
    assert context_manager.max_sessions == 5
    assert len(context_manager.sessions) == 0
    assert len(context_manager.global_context) == 0
    assert context_manager.stats["sessions_created"] == 0


def test_session_creation_and_management(context_manager):
    """Test session creation, retrieval, and deletion"""
    # Create session
    session = context_manager.create_session("test_session", {"key": "value"})
    
    assert session.session_id == "test_session"
    assert session.metadata == {"key": "value"}
    assert len(context_manager.sessions) == 1
    assert context_manager.stats["sessions_created"] == 1
    
    # Get session
    retrieved_session = context_manager.get_session("test_session")
    assert retrieved_session is not None
    assert retrieved_session.session_id == "test_session"
    
    # Delete session
    deleted = context_manager.delete_session("test_session")
    assert deleted is True
    assert len(context_manager.sessions) == 0
    
    # Try to delete non-existent session
    deleted = context_manager.delete_session("non_existent")
    assert deleted is False


def test_context_setting_and_getting(context_manager):
    """Test setting and getting context values"""
    # Set session-scoped context
    success = context_manager.set_context(
        session_id="test_session",
        key="session_key",
        value="session_value",
        agent_id="test_agent"
    )
    assert success is True
    
    # Get session-scoped context
    value = context_manager.get_context("test_session", "session_key")
    assert value == "session_value"
    
    # Set global-scoped context
    success = context_manager.set_context(
        session_id="test_session",
        key="global_key",
        value="global_value",
        agent_id="test_agent",
        global_scope=True
    )
    assert success is True
    
    # Get global-scoped context from different session
    value = context_manager.get_context("other_session", "global_key")
    assert value == "global_value"


def test_context_with_ttl(context_manager):
    """Test context with TTL expiration"""
    # Set context with short TTL
    success = context_manager.set_context(
        session_id="test_session",
        key="ttl_key",
        value="ttl_value",
        agent_id="test_agent",
        ttl_seconds=1
    )
    assert success is True
    
    # Should be available immediately
    value = context_manager.get_context("test_session", "ttl_key")
    assert value == "ttl_value"
    
    # Wait for expiration
    time.sleep(1.1)
    
    # Should be expired and return default
    value = context_manager.get_context("test_session", "ttl_key", default="default")
    assert value == "default"


def test_context_with_tags(context_manager):
    """Test context with tags and searching"""
    # Set context with tags
    success = context_manager.set_context(
        session_id="test_session",
        key="tagged_key1",
        value="tagged_value1",
        agent_id="test_agent",
        tags={"tag1", "tag2"}
    )
    assert success is True
    
    success = context_manager.set_context(
        session_id="test_session",
        key="tagged_key2",
        value="tagged_value2",
        agent_id="test_agent",
        tags={"tag2", "tag3"}
    )
    assert success is True
    
    # Search by tags
    results = context_manager.search_context(
        session_id="test_session",
        tags={"tag1"}
    )
    assert len(results) == 1
    assert results[0].value == "tagged_value1"
    
    results = context_manager.search_context(
        session_id="test_session",
        tags={"tag2"}
    )
    assert len(results) == 2


def test_context_search_by_agent(context_manager):
    """Test searching context by agent ID"""
    # Set context from different agents
    context_manager.set_context(
        session_id="test_session",
        key="agent1_key",
        value="agent1_value",
        agent_id="agent1"
    )
    
    context_manager.set_context(
        session_id="test_session",
        key="agent2_key",
        value="agent2_value",
        agent_id="agent2"
    )
    
    # Search by agent
    results = context_manager.search_context(
        session_id="test_session",
        agent_id="agent1"
    )
    assert len(results) == 1
    assert results[0].value == "agent1_value"


def test_context_search_by_pattern(context_manager):
    """Test searching context by key pattern"""
    # Set context with different key patterns
    context_manager.set_context(
        session_id="test_session",
        key="user_data_123",
        value="user_value",
        agent_id="test_agent"
    )
    
    context_manager.set_context(
        session_id="test_session",
        key="system_config_456",
        value="system_value",
        agent_id="test_agent"
    )
    
    # Search by pattern
    results = context_manager.search_context(
        session_id="test_session",
        key_pattern=r"user_.*"
    )
    assert len(results) == 1
    assert results[0].value == "user_value"


def test_session_summary(context_manager):
    """Test session summary generation"""
    # Add some context entries
    context_manager.set_context(
        session_id="test_session",
        key="key1",
        value="value1",
        agent_id="agent1",
        tags={"tag1"}
    )
    
    context_manager.set_context(
        session_id="test_session",
        key="key2",
        value="value2",
        agent_id="agent2",
        tags={"tag2"}
    )
    
    # Get summary
    summary = context_manager.get_session_summary("test_session")
    
    assert summary["session_id"] == "test_session"
    assert summary["total_entries"] == 2
    assert "agent1" in summary["entries_by_agent"]
    assert "agent2" in summary["entries_by_agent"]
    assert "tag1" in summary["entries_by_tag"]
    assert "tag2" in summary["entries_by_tag"]


def test_global_summary(context_manager):
    """Test global summary generation"""
    # Create some sessions and context
    context_manager.create_session("session1")
    context_manager.create_session("session2")
    
    context_manager.set_context(
        session_id="session1",
        key="global_key",
        value="global_value",
        agent_id="test_agent",
        global_scope=True
    )
    
    # Get global summary
    summary = context_manager.get_global_summary()
    
    assert summary["total_sessions"] == 2
    assert summary["global_entries"] == 1
    assert "stats" in summary
    assert "memory_usage" in summary


def test_session_persistence(context_manager, temp_storage_dir):
    """Test session persistence to disk"""
    # Create session with context
    context_manager.set_context(
        session_id="persist_session",
        key="persist_key",
        value="persist_value",
        agent_id="test_agent"
    )
    
    # Persist session
    success = context_manager.persist_session("persist_session")
    assert success is True
    
    # Check file was created
    session_file = Path(temp_storage_dir) / "session_persist_session.json"
    assert session_file.exists()
    
    # Load session in new context manager
    new_context_manager = SharedContextManager(storage_dir=temp_storage_dir)
    
    # Should have loaded the persisted session
    value = new_context_manager.get_context("persist_session", "persist_key")
    assert value == "persist_value"


def test_session_limit_enforcement(context_manager):
    """Test that session limit is enforced"""
    # Create sessions up to the limit
    for i in range(6):  # Limit is 5
        context_manager.create_session(f"session_{i}")
    
    # Should have cleaned up old sessions
    assert len(context_manager.sessions) <= 5


def test_context_removal(context_manager):
    """Test context removal"""
    # Set session and global context
    context_manager.set_context(
        session_id="test_session",
        key="session_key",
        value="session_value",
        agent_id="test_agent"
    )
    
    context_manager.set_context(
        session_id="test_session",
        key="global_key",
        value="global_value",
        agent_id="test_agent",
        global_scope=True
    )
    
    # Remove session context
    removed = context_manager.remove_context("test_session", "session_key")
    assert removed is True
    
    value = context_manager.get_context("test_session", "session_key", default="not_found")
    assert value == "not_found"
    
    # Remove global context
    removed = context_manager.remove_context("test_session", "global_key", global_scope=True)
    assert removed is True
    
    value = context_manager.get_context("test_session", "global_key", default="not_found")
    assert value == "not_found"


def test_clear_all(context_manager):
    """Test clearing all context data"""
    # Add some data
    context_manager.create_session("test_session")
    context_manager.set_context(
        session_id="test_session",
        key="test_key",
        value="test_value",
        agent_id="test_agent"
    )
    
    # Clear all
    context_manager.clear_all()
    
    assert len(context_manager.sessions) == 0
    assert len(context_manager.global_context) == 0
    assert context_manager.stats["total_entries"] == 0
