version: "3.9"

# Compose for containerized testing only. No secrets inlined; see .env for overrides.

services:
  postgres:
    image: postgres:16-alpine
    environment:
      POSTGRES_USER: ${POSTGRES_USER:-testuser}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-testpass}
      POSTGRES_DB: ${POSTGRES_DB:-testdb}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U $$POSTGRES_USER -d $$POSTGRES_DB"]
      interval: 10s
      timeout: 5s
      retries: 5
    restart: unless-stopped
    deploy:
      resources:
        limits:
          cpus: "1.0"
          memory: 1g

  # Optional Ollama container; disabled by default. Uncomment to enable.
  # ollama:
  #   image: ollama/ollama:0.1.49
  #   ports:
  #     - "11434:11434"
  #   volumes:
  #     - ollama_models:/root/.ollama
  #   healthcheck:
  #     test: ["CMD", "ollama", "list"]
  #     interval: 30s
  #     timeout: 10s
  #     retries: 5
  #   restart: unless-stopped
  #   deploy:
  #     resources:
  #       limits:
  #         cpus: "2.0"
  #         memory: 4g

  test-runner:
    build:
      context: ..
      dockerfile: containers/Dockerfile.dev
    environment:
      # Point to host Ollama on Windows Docker Desktop by default
      OLLAMA_URL: ${OLLAMA_URL:-http://host.docker.internal:11434}
      PYTEST_ARGS: ${PYTEST_ARGS:--q}
      NLTK_DATA: /data/nltk
      # Example DB env for app code/tests
      DATABASE_URL: postgresql+psycopg2://${POSTGRES_USER:-testuser}:${POSTGRES_PASSWORD:-testpass}@postgres:5432/${POSTGRES_DB:-testdb}
    depends_on:
      postgres:
        condition: service_healthy
      # ollama:
      #   condition: service_started
    working_dir: /workspace
    volumes:
      - ..:/workspace:rw
      - nltk_data:/data/nltk
    command: bash -lc "python /usr/local/bin/ensure_nltk_data.py && pytest ${PYTEST_ARGS}"
    healthcheck:
      test: ["CMD", "python", "-c", "import pytest; import sys; sys.exit(0)"]
      interval: 30s
      timeout: 10s
      retries: 3
    restart: "no"
    deploy:
      resources:
        limits:
          cpus: "2.0"
          memory: 4g

volumes:
  pgdata:
  nltk_data:
  ollama_models:

