# AI Coding Agent - Setup Guides

## 📋 Table of Contents

1. [Quick Start Guide](#quick-start-guide)
2. [Development Environment Setup](#development-environment-setup)
3. [Production Deployment Setup](#production-deployment-setup)
4. [Home Network Setup](#home-network-setup)
5. [SSL Certificate Setup](#ssl-certificate-setup)
6. [Testing Environment Setup](#testing-environment-setup)
7. [Troubleshooting Setup Issues](#troubleshooting-setup-issues)
8. [Advanced Configuration](#advanced-configuration)

---

## 🚀 Quick Start Guide

### Prerequisites

- **Python 3.8+** installed
- **Git** installed
- **Windows 10/11** or **macOS/Linux**
- **4GB RAM** minimum (8GB recommended)
- **2GB free disk space**

### 5-Minute Setup

1. **Clone the repository**:
   ```bash
   git clone https://github.com/your-org/ai-coding-agent.git
   cd ai-coding-agent
   ```

2. **Create virtual environment**:
   ```bash
   python -m venv .venv
   .venv\Scripts\activate  # Windows
   source .venv/bin/activate  # macOS/Linux
   ```

3. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   pip install -e .
   ```

4. **Run initial setup**:
   ```bash
   python scripts/setup_dev_environment.py
   ```

5. **Create your first site**:
   ```bash
   ai-agent create-and-host my-first-site
   ```

6. **Access your site**:
   - Open browser to `http://localhost:8000`
   - Your site is now live!

---

## 🛠️ Development Environment Setup

### Step 1: System Requirements

#### Windows Setup
```powershell
# Install Chocolatey (if not installed)
Set-ExecutionPolicy Bypass -Scope Process -Force
[System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# Install required tools
choco install python git nodejs nginx
```

#### macOS Setup
```bash
# Install Homebrew (if not installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install required tools
brew install python git node nginx
```

#### Linux Setup (Ubuntu/Debian)
```bash
# Update package list
sudo apt update

# Install required tools
sudo apt install python3 python3-pip python3-venv git nginx nodejs npm
```

### Step 2: Python Environment

```bash
# Create project directory
mkdir ai-coding-agent
cd ai-coding-agent

# Clone repository
git clone https://github.com/your-org/ai-coding-agent.git .

# Create virtual environment
python -m venv .venv

# Activate virtual environment
# Windows:
.venv\Scripts\activate
# macOS/Linux:
source .venv/bin/activate

# Upgrade pip
pip install --upgrade pip

# Install dependencies
pip install -r requirements.txt
pip install -r requirements-dev.txt
pip install -e .
```

### Step 3: Development Tools Setup

```bash
# Install pre-commit hooks
pre-commit install

# Setup development configuration
python scripts/setup_dev_environment.py

# Verify installation
python scripts/quality_check.py
```

### Step 4: Database Setup

```bash
# Initialize database
python -c "from src.db.database_manager import DatabaseManager; db = DatabaseManager(); db.initialize_database()"

# Verify database
python -c "from src.db.database_manager import DatabaseManager; db = DatabaseManager(); print('Database tables:', db.get_tables())"
```

### Step 5: Configuration Setup

```bash
# Copy configuration templates
cp config/config.json.example config/config.json
cp config/cli_web_config.json.example config/cli_web_config.json

# Edit configuration files
# Windows:
notepad config/config.json
# macOS:
open -e config/config.json
# Linux:
nano config/config.json
```

### Step 6: Testing Setup

```bash
# Run initial tests
pytest tests/ -v

# Run specific test suites
pytest tests/test_website_generator.py -v
pytest tests/test_cms_content_manager.py -v
pytest tests/test_home_server_hosting.py -v

# Generate test coverage report
pytest --cov=src --cov-report=html tests/
```

---

## 🏭 Production Deployment Setup

### Step 1: Server Requirements

#### Minimum Requirements
- **CPU**: 2 cores
- **RAM**: 4GB
- **Storage**: 20GB SSD
- **OS**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+

#### Recommended Requirements
- **CPU**: 4+ cores
- **RAM**: 8GB+
- **Storage**: 50GB+ SSD
- **Network**: 100Mbps+ connection

### Step 2: Server Preparation

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install python3 python3-pip python3-venv git nginx certbot python3-certbot-nginx

# Create application user
sudo useradd -m -s /bin/bash aiagent
sudo usermod -aG sudo aiagent
```

### Step 3: Application Deployment

```bash
# Switch to application user
sudo su - aiagent

# Clone repository
git clone https://github.com/your-org/ai-coding-agent.git
cd ai-coding-agent

# Setup Python environment
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
pip install -e .

# Setup production configuration
python scripts/setup_production.py
```

### Step 4: Nginx Configuration

```bash
# Create Nginx configuration
sudo tee /etc/nginx/sites-available/ai-coding-agent << 'EOF'
server {
    listen 80;
    server_name your-domain.com;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/ai-coding-agent /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### Step 5: SSL Certificate Setup

```bash
# Install SSL certificate
sudo certbot --nginx -d your-domain.com

# Test automatic renewal
sudo certbot renew --dry-run
```

### Step 6: Systemd Service Setup

```bash
# Create systemd service file
sudo tee /etc/systemd/system/ai-coding-agent.service << 'EOF'
[Unit]
Description=AI Coding Agent
After=network.target

[Service]
Type=simple
User=aiagent
WorkingDirectory=/home/<USER>/ai-coding-agent
Environment=PATH=/home/<USER>/ai-coding-agent/.venv/bin
ExecStart=/home/<USER>/ai-coding-agent/.venv/bin/python -m src.agent
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable ai-coding-agent
sudo systemctl start ai-coding-agent
sudo systemctl status ai-coding-agent
```

---

## 🏠 Home Network Setup

### Step 1: Network Requirements

#### Hardware Requirements
- **Router**: Modern router with port forwarding
- **Computer**: Always-on computer (Raspberry Pi, NAS, or dedicated server)
- **Storage**: 50GB+ available space
- **Network**: Stable internet connection

#### Software Requirements
- **OS**: Linux (Ubuntu Server recommended)
- **Python**: 3.8+
- **Nginx**: Latest stable version
- **Docker**: Optional, for containerized deployment

### Step 2: Home Server Setup

```bash
# Install Ubuntu Server 22.04 LTS
# Follow installation guide for your hardware

# Update system
sudo apt update && sudo apt upgrade -y

# Install required packages
sudo apt install python3 python3-pip python3-venv git nginx ufw

# Setup firewall
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

### Step 3: Application Installation

```bash
# Create application directory
sudo mkdir -p /opt/ai-coding-agent
sudo chown $USER:$USER /opt/ai-coding-agent
cd /opt/ai-coding-agent

# Clone repository
git clone https://github.com/your-org/ai-coding-agent.git .

# Setup Python environment
python3 -m venv .venv
source .venv/bin/activate
pip install -r requirements.txt
pip install -e .

# Setup home network configuration
python scripts/setup_home_network.py
```

### Step 4: Router Configuration

#### Port Forwarding Setup
1. **Access router admin panel** (usually `***********`)
2. **Navigate to Port Forwarding section**
3. **Add port forwarding rules**:
   - **Port 80** → Your server IP
   - **Port 443** → Your server IP
   - **Port 8000** → Your server IP (for development)

#### Dynamic DNS Setup
1. **Sign up for dynamic DNS service** (No-IP, DuckDNS, etc.)
2. **Configure router with dynamic DNS credentials**
3. **Set update interval to 5 minutes**

### Step 5: SSL Certificate Setup

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d your-dynamic-dns-domain.com

# Setup automatic renewal
sudo crontab -e
# Add line: 0 12 * * * /usr/bin/certbot renew --quiet
```

---

## 🔒 SSL Certificate Setup

### Step 1: Domain Preparation

#### Domain Requirements
- **Registered domain name**
- **DNS access** for domain
- **A record** pointing to your server IP

#### DNS Configuration
```bash
# Add A record in your DNS provider
# Type: A
# Name: @ (or subdomain)
# Value: Your server IP address
# TTL: 300 seconds
```

### Step 2: Let's Encrypt Setup

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx

# Stop Nginx temporarily
sudo systemctl stop nginx

# Get certificate
sudo certbot certonly --standalone -d your-domain.com

# Test certificate
sudo certbot certificates
```

### Step 3: Nginx SSL Configuration

```bash
# Create SSL configuration
sudo tee /etc/nginx/sites-available/ai-coding-agent-ssl << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /etc/letsencrypt/live/your-domain.com/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/your-domain.com/privkey.pem;

    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;

    location / {
        proxy_pass http://127.0.0.1:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/ai-coding-agent-ssl /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### Step 4: Automatic Renewal

```bash
# Test renewal
sudo certbot renew --dry-run

# Add to crontab
sudo crontab -e
# Add line: 0 12 * * * /usr/bin/certbot renew --quiet

# Verify crontab
sudo crontab -l
```

---

## 🧪 Testing Environment Setup

### Step 1: Test Environment Requirements

```bash
# Create test environment
python -m venv .venv-test
source .venv-test/bin/activate

# Install test dependencies
pip install -r requirements-dev.txt
pip install -e .
```

### Step 2: Test Database Setup

```bash
# Create test database
python -c "
from src.db.database_manager import DatabaseManager
db = DatabaseManager('test.db')
db.initialize_database()
print('Test database created successfully')
"
```

### Step 3: Test Configuration

```bash
# Create test configuration
cp config/testing_config.json config/testing_config.json.backup

# Edit test configuration
cat > config/testing_config.json << 'EOF'
{
    "test_mode": true,
    "database": {
        "path": "test.db",
        "echo": false
    },
    "logging": {
        "level": "DEBUG",
        "file": "logs/test.log"
    },
    "testing": {
        "timeout": 30,
        "retries": 3,
        "parallel": false
    }
}
EOF
```

### Step 4: Run Test Suite

```bash
# Run all tests
pytest tests/ -v --tb=short

# Run specific test categories
pytest tests/test_website_generator.py -v
pytest tests/test_cms_content_manager.py -v
pytest tests/test_home_server_hosting.py -v

# Run with coverage
pytest --cov=src --cov-report=html --cov-report=term tests/

# Run performance tests
pytest tests/ -m "performance" -v

# Run integration tests
pytest tests/ -m "integration" -v
```

### Step 5: Test Automation

```bash
# Create test automation script
cat > scripts/run_tests.py << 'EOF'
#!/usr/bin/env python3
"""
Automated test runner for AI Coding Agent
"""

import subprocess
import sys
import os
from pathlib import Path

def run_tests():
    """Run comprehensive test suite"""
    print("🧪 Running AI Coding Agent Test Suite")
    print("=" * 50)

    # Change to project root
    project_root = Path(__file__).parent.parent
    os.chdir(project_root)

    # Run tests with different configurations
    test_commands = [
        ["pytest", "tests/", "-v", "--tb=short"],
        ["pytest", "--cov=src", "--cov-report=html", "tests/"],
        ["pytest", "--cov=src", "--cov-report=term", "tests/"],
        ["python", "-m", "mypy", "src/"],
        ["python", "-m", "flake8", "src/"],
        ["python", "scripts/quality_check.py"]
    ]

    results = []
    for cmd in test_commands:
        print(f"\n🔍 Running: {' '.join(cmd)}")
        try:
            result = subprocess.run(cmd, capture_output=True, text=True)
            results.append((cmd, result.returncode == 0, result.stdout, result.stderr))
            if result.returncode == 0:
                print("✅ Success")
            else:
                print("❌ Failed")
                print(result.stderr)
        except Exception as e:
            print(f"❌ Error: {e}")
            results.append((cmd, False, "", str(e)))

    # Generate summary
    print("\n" + "=" * 50)
    print("📊 Test Summary")
    print("=" * 50)

    passed = sum(1 for _, success, _, _ in results if success)
    total = len(results)

    print(f"Tests Passed: {passed}/{total}")
    print(f"Success Rate: {(passed/total)*100:.1f}%")

    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print("⚠️  Some tests failed. Check output above.")
        return False

if __name__ == "__main__":
    success = run_tests()
    sys.exit(0 if success else 1)
EOF

# Make executable
chmod +x scripts/run_tests.py

# Run automated tests
python scripts/run_tests.py
```

---

## 🔧 Troubleshooting Setup Issues

### Common Issues and Solutions

#### 1. Python Environment Issues

**Problem**: Virtual environment not activating
```bash
# Solution: Recreate virtual environment
rm -rf .venv
python -m venv .venv
source .venv/bin/activate  # or .venv\Scripts\activate on Windows
pip install --upgrade pip
pip install -r requirements.txt
```

**Problem**: Package installation fails
```bash
# Solution: Update pip and install build tools
pip install --upgrade pip setuptools wheel
pip install -r requirements.txt
```

#### 2. Database Issues

**Problem**: Database connection fails
```bash
# Solution: Check database file permissions
ls -la data/
chmod 644 data/*.db
chmod 755 data/
```

**Problem**: Database locked
```bash
# Solution: Close all connections and restart
pkill -f "python.*ai-coding-agent"
python -c "from src.db.database_manager import DatabaseManager; db = DatabaseManager(); db.close()"
```

#### 3. Network Issues

**Problem**: Port already in use
```bash
# Solution: Find and kill process using port
sudo lsof -i :8000
sudo kill -9 <PID>

# Or use different port
export AI_AGENT_PORT=8001
```

**Problem**: Firewall blocking connections
```bash
# Solution: Configure firewall
sudo ufw allow 8000
sudo ufw allow 80
sudo ufw allow 443
```

#### 4. SSL Certificate Issues

**Problem**: Certificate not found
```bash
# Solution: Check certificate location
sudo certbot certificates
sudo ls -la /etc/letsencrypt/live/your-domain.com/
```

**Problem**: Certificate expired
```bash
# Solution: Renew certificate
sudo certbot renew
sudo systemctl reload nginx
```

#### 5. Permission Issues

**Problem**: Permission denied errors
```bash
# Solution: Fix file permissions
sudo chown -R $USER:$USER /opt/ai-coding-agent
chmod -R 755 /opt/ai-coding-agent
chmod 644 /opt/ai-coding-agent/config/*.json
```

### Diagnostic Tools

#### System Health Check
```bash
# Create diagnostic script
cat > scripts/diagnostic.py << 'EOF'
#!/usr/bin/env python3
"""
System diagnostic tool for AI Coding Agent
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def check_system():
    """Check system requirements"""
    print("🔍 System Diagnostic Report")
    print("=" * 40)

    # Python version
    print(f"Python Version: {sys.version}")

    # Platform info
    print(f"Platform: {platform.platform()}")

    # Available memory
    try:
        import psutil
        memory = psutil.virtual_memory()
        print(f"Memory: {memory.total // (1024**3)}GB total, {memory.available // (1024**3)}GB available")
    except ImportError:
        print("Memory: psutil not available")

    # Disk space
    try:
        disk = psutil.disk_usage('.')
        print(f"Disk Space: {disk.free // (1024**3)}GB free")
    except ImportError:
        print("Disk Space: psutil not available")

def check_dependencies():
    """Check Python dependencies"""
    print("\n📦 Dependency Check")
    print("=" * 40)

    required_packages = [
        'flask', 'sqlalchemy', 'requests', 'gitpython',
        'python-dotenv', 'watchdog', 'python-jose', 'pyjwt'
    ]

    for package in required_packages:
        try:
            __import__(package)
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package} - Not installed")

def check_configuration():
    """Check configuration files"""
    print("\n⚙️  Configuration Check")
    print("=" * 40)

    config_files = [
        'config/config.json',
        'config/cli_web_config.json',
        'config/cms_config.json',
        'config/home_server_config.json'
    ]

    for config_file in config_files:
        if Path(config_file).exists():
            print(f"✅ {config_file}")
        else:
            print(f"❌ {config_file} - Missing")

def check_database():
    """Check database status"""
    print("\n🗄️  Database Check")
    print("=" * 40)

    try:
        from src.db.database_manager import DatabaseManager
        db = DatabaseManager()
        tables = db.get_tables()
        print(f"✅ Database connected, {len(tables)} tables found")
        for table in tables:
            print(f"  - {table}")
    except Exception as e:
        print(f"❌ Database error: {e}")

def check_network():
    """Check network connectivity"""
    print("\n🌐 Network Check")
    print("=" * 40)

    # Check localhost
    try:
        import socket
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        result = sock.connect_ex(('127.0.0.1', 8000))
        if result == 0:
            print("✅ Port 8000 available")
        else:
            print("❌ Port 8000 in use")
        sock.close()
    except Exception as e:
        print(f"❌ Network check failed: {e}")

if __name__ == "__main__":
    check_system()
    check_dependencies()
    check_configuration()
    check_database()
    check_network()

    print("\n" + "=" * 40)
    print("🏁 Diagnostic complete")
EOF

# Run diagnostic
python scripts/diagnostic.py
```

---

## ⚙️ Advanced Configuration

### Environment Variables

```bash
# Create environment file
cat > .env << 'EOF'
# Application Settings
AI_AGENT_ENV=production
AI_AGENT_DEBUG=false
AI_AGENT_LOG_LEVEL=INFO

# Database Settings
AI_AGENT_DB_PATH=data/ai_agent.db
AI_AGENT_DB_ECHO=false

# Server Settings
AI_AGENT_HOST=0.0.0.0
AI_AGENT_PORT=8000
AI_AGENT_WORKERS=4

# Security Settings
AI_AGENT_SECRET_KEY=your-secret-key-here
AI_AGENT_JWT_SECRET=your-jwt-secret-here

# SSL Settings
AI_AGENT_SSL_ENABLED=true
AI_AGENT_SSL_CERT_PATH=/etc/letsencrypt/live/your-domain.com/fullchain.pem
AI_AGENT_SSL_KEY_PATH=/etc/letsencrypt/live/your-domain.com/privkey.pem

# AI Model Settings
AI_AGENT_MODEL_ENDPOINT=http://localhost:11434
AI_AGENT_MODEL_TIMEOUT=30

# Content Settings
AI_AGENT_CONTENT_PATH=content/
AI_AGENT_MEDIA_PATH=content/media/
AI_AGENT_TEMPLATE_PATH=templates/

# Testing Settings
AI_AGENT_TEST_MODE=false
AI_AGENT_TEST_DB_PATH=test.db
EOF
```

### Performance Tuning

```bash
# Create performance configuration
cat > config/performance_config.json << 'EOF'
{
    "caching": {
        "enabled": true,
        "max_size": "100MB",
        "ttl": 3600
    },
    "database": {
        "pool_size": 10,
        "max_overflow": 20,
        "pool_timeout": 30
    },
    "server": {
        "workers": 4,
        "worker_connections": 1000,
        "keepalive_timeout": 65
    },
    "compression": {
        "enabled": true,
        "level": 6,
        "mime_types": ["text/html", "text/css", "application/javascript"]
    }
}
EOF
```

### Monitoring Setup

```bash
# Create monitoring script
cat > scripts/monitor.py << 'EOF'
#!/usr/bin/env python3
"""
System monitoring for AI Coding Agent
"""

import psutil
import time
import json
from datetime import datetime
from pathlib import Path

def get_system_metrics():
    """Collect system metrics"""
    return {
        "timestamp": datetime.now().isoformat(),
        "cpu_percent": psutil.cpu_percent(interval=1),
        "memory_percent": psutil.virtual_memory().percent,
        "disk_percent": psutil.disk_usage('/').percent,
        "network_io": psutil.net_io_counters()._asdict()
    }

def monitor_system():
    """Continuous system monitoring"""
    log_file = Path("logs/system_metrics.json")
    log_file.parent.mkdir(exist_ok=True)

    print("📊 Starting system monitoring...")
    print("Press Ctrl+C to stop")

    try:
        while True:
            metrics = get_system_metrics()

            # Log metrics
            with open(log_file, 'a') as f:
                f.write(json.dumps(metrics) + '\n')

            # Display current metrics
            print(f"\rCPU: {metrics['cpu_percent']}% | "
                  f"Memory: {metrics['memory_percent']}% | "
                  f"Disk: {metrics['disk_percent']}%", end='')

            time.sleep(5)
    except KeyboardInterrupt:
        print("\n🛑 Monitoring stopped")

if __name__ == "__main__":
    monitor_system()
EOF

# Make executable
chmod +x scripts/monitor.py

# Start monitoring
python scripts/monitor.py
```

---

## 📚 Additional Resources

### Documentation Links
- [Architecture Overview](ARCHITECTURE_OVERVIEW.md)
- [API Reference](API_REFERENCE.md)
- [Development Guidelines](DEVELOPMENT_GUIDELINES.md)
- [Troubleshooting Guide](TROUBLESHOOTING.md)

### Support Channels
- **GitHub Issues**: [Report bugs and feature requests](https://github.com/your-org/ai-coding-agent/issues)
- **Documentation**: [Complete documentation](https://github.com/your-org/ai-coding-agent/docs)
- **Community**: [Discord/Slack channel](https://discord.gg/ai-coding-agent)

### Quick Commands Reference

```bash
# Development
python scripts/setup_dev_environment.py    # Setup development environment
python scripts/quality_check.py            # Run quality checks
python scripts/run_tests.py                # Run test suite

# Production
python scripts/setup_production.py         # Setup production environment
python scripts/setup_home_network.py       # Setup home network
python scripts/monitor.py                  # Monitor system

# Troubleshooting
python scripts/diagnostic.py               # Run system diagnostics
python scripts/get_network_info.py         # Get network information
```

---

## 🎯 Conclusion

This comprehensive setup guide covers all aspects of deploying and configuring the AI Coding Agent. Follow the appropriate section based on your use case:

- **Quick Start**: For immediate testing and evaluation
- **Development**: For developers contributing to the project
- **Production**: For production deployments
- **Home Network**: For home server setups
- **SSL**: For secure HTTPS deployments
- **Testing**: For comprehensive testing environments

For additional help, refer to the troubleshooting section or contact the support channels listed above.
