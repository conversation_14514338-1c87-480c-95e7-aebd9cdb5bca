#!/usr/bin/env python3
"""
Test Persona System Integration
Verifies that the persona system is properly integrated with error handling and site management
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from agent.core.persona_manager import PersonaManager, AgentType, TaskCategory
from agent.core.site_container_manager import SiteContainerManager


async def test_persona_manager():
    """Test the persona manager functionality"""
    
    print("🎭 Testing Persona Manager")
    print("=" * 40)
    
    persona_manager = PersonaManager()
    
    test_results = {
        "persona_loading": False,
        "agent_routing": False,
        "model_selection": False,
        "conversation_styles": False,
        "context_switching": False
    }
    
    # Test 1: Persona Loading
    try:
        if len(persona_manager.personas) >= 5:  # Should have at least 5 personas
            print("✅ Personas loaded successfully")
            print(f"   Available personas: {[p.value for p in persona_manager.personas.keys()]}")
            test_results["persona_loading"] = True
        else:
            print(f"❌ Expected at least 5 personas, got {len(persona_manager.personas)}")
    except Exception as e:
        print(f"❌ Persona loading failed: {e}")
    
    # Test 2: Agent Routing
    try:
        test_cases = [
            (TaskCategory.FRONTEND_DEV, AgentType.FRONTEND),
            (TaskCategory.BACKEND_DEV, AgentType.BACKEND),
            (TaskCategory.SECURITY_REVIEW, AgentType.SECURITY),
            (TaskCategory.DATABASE_OPS, AgentType.DATABASE),
            (TaskCategory.CONVERSATION, AgentType.ARCHITECT)
        ]
        
        routing_success = True
        for task, expected_agent in test_cases:
            actual_agent = persona_manager.get_agent_for_task(task)
            if actual_agent == expected_agent:
                print(f"✅ {task.value} → {actual_agent.value}")
            else:
                print(f"❌ {task.value} → {actual_agent.value} (expected {expected_agent.value})")
                routing_success = False
        
        test_results["agent_routing"] = routing_success
        
    except Exception as e:
        print(f"❌ Agent routing test failed: {e}")
    
    # Test 3: Model Selection
    try:
        model_tests = [
            (AgentType.ARCHITECT, "conversation"),
            (AgentType.FRONTEND, "development"),
            (AgentType.BACKEND, "development"),
            (AgentType.SECURITY, "analysis")
        ]
        
        model_success = True
        for agent, task_type in model_tests:
            model = persona_manager.get_model_for_agent_task(agent, task_type)
            if model and isinstance(model, str):
                print(f"✅ {agent.value} + {task_type} → {model}")
            else:
                print(f"❌ {agent.value} + {task_type} → No model returned")
                model_success = False
        
        test_results["model_selection"] = model_success
        
    except Exception as e:
        print(f"❌ Model selection test failed: {e}")
    
    # Test 4: Conversation Styles
    try:
        style_tests = [
            (AgentType.ARCHITECT, "general"),
            (AgentType.FRONTEND, "design_focus"),
            (AgentType.BACKEND, "performance"),
            (AgentType.SECURITY, "risk_assessment")
        ]
        
        style_success = True
        for agent, situation in style_tests:
            style = persona_manager.get_conversation_style(agent, situation)
            if style and len(style) > 10:  # Should be a meaningful description
                print(f"✅ {agent.value} conversation style available")
            else:
                print(f"❌ {agent.value} conversation style missing or too short")
                style_success = False
        
        test_results["conversation_styles"] = style_success
        
    except Exception as e:
        print(f"❌ Conversation style test failed: {e}")
    
    # Test 5: Context Switching
    try:
        # Test switching from architect to frontend
        switch_result = persona_manager.switch_agent_context(
            AgentType.FRONTEND, 
            TaskCategory.FRONTEND_DEV,
            {"test": "context"}
        )
        
        if (switch_result.get("current_agent") == "frontend" and 
            switch_result.get("persona_name") and
            switch_result.get("transition_message")):
            print("✅ Context switching working")
            print(f"   Transition: {switch_result.get('transition_message')}")
            test_results["context_switching"] = True
        else:
            print(f"❌ Context switching failed: {switch_result}")
            
    except Exception as e:
        print(f"❌ Context switching test failed: {e}")
    
    return test_results


async def test_site_container_integration():
    """Test integration between persona system and site container manager"""
    
    print(f"\n🔗 Testing Site Container Integration")
    print("=" * 45)
    
    integration_results = {
        "persona_manager_init": False,
        "error_handling_integration": False,
        "agent_routing_in_errors": False,
        "persona_messaging": False
    }
    
    # Test 1: Persona Manager Initialization
    try:
        container_manager = SiteContainerManager()
        
        if hasattr(container_manager, 'persona_manager') and container_manager.persona_manager:
            print("✅ PersonaManager initialized in SiteContainerManager")
            integration_results["persona_manager_init"] = True
        else:
            print("❌ PersonaManager not found in SiteContainerManager")
            
    except Exception as e:
        print(f"❌ SiteContainerManager initialization failed: {e}")
        return integration_results
    
    # Test 2: Error Handling Integration
    try:
        # Test different error types
        error_scenarios = [
            {
                "title": "Frontend JavaScript Error",
                "description": "Cannot read property 'innerHTML' of null",
                "category": "frontend",
                "severity": "medium"
            },
            {
                "title": "Database Connection Failed",
                "description": "Connection timeout after 30 seconds",
                "category": "database",
                "severity": "high"
            },
            {
                "title": "Security Vulnerability",
                "description": "SQL injection detected in login form",
                "category": "security",
                "severity": "critical"
            }
        ]
        
        integration_success = True
        for error in error_scenarios:
            try:
                result = await container_manager.handle_error_with_user_escalation(
                    site_name="test-integration",
                    error_details=error
                )
                
                if result.get("agent_used") or result.get("agent_type"):
                    print(f"✅ {error['category']} error handled with agent routing")
                else:
                    print(f"⚠️ {error['category']} error handled but no agent info")
                    
            except Exception as e:
                print(f"❌ {error['category']} error handling failed: {e}")
                integration_success = False
        
        integration_results["error_handling_integration"] = integration_success
        
    except Exception as e:
        print(f"❌ Error handling integration test failed: {e}")
    
    # Test 3: Agent Routing in Errors
    try:
        # Test that errors are routed to appropriate agents
        routing_tests = [
            ("frontend", "frontend"),
            ("database", "database"),
            ("security", "security"),
            ("backend", "backend")
        ]
        
        routing_success = True
        for error_category, expected_agent in routing_tests:
            task_category = container_manager._map_error_to_task_category(error_category)
            actual_agent = container_manager.persona_manager.get_agent_for_task(task_category)
            
            if expected_agent in actual_agent.value:
                print(f"✅ {error_category} errors → {actual_agent.value}")
            else:
                print(f"❌ {error_category} errors → {actual_agent.value} (expected {expected_agent})")
                routing_success = False
        
        integration_results["agent_routing_in_errors"] = routing_success
        
    except Exception as e:
        print(f"❌ Agent routing test failed: {e}")
    
    # Test 4: Persona Messaging
    try:
        # Test persona-specific messaging
        message_tests = [
            (AgentType.FRONTEND, {"fix_description": "Fixed CSS styling"}),
            (AgentType.BACKEND, {"fix_description": "Optimized database queries"}),
            (AgentType.SECURITY, {"fix_description": "Patched security vulnerability"})
        ]
        
        messaging_success = True
        for agent_type, fix_result in message_tests:
            try:
                message = container_manager._generate_success_message(agent_type, fix_result)
                if message and len(message) > 20:  # Should be a meaningful message
                    print(f"✅ {agent_type.value} success message generated")
                else:
                    print(f"❌ {agent_type.value} success message too short or missing")
                    messaging_success = False
            except Exception as e:
                print(f"❌ {agent_type.value} message generation failed: {e}")
                messaging_success = False
        
        integration_results["persona_messaging"] = messaging_success
        
    except Exception as e:
        print(f"❌ Persona messaging test failed: {e}")
    
    return integration_results


async def test_conversation_quality():
    """Test that conversation quality is maintained with personas"""
    
    print(f"\n💬 Testing Conversation Quality")
    print("=" * 35)
    
    conversation_results = {
        "natural_language": False,
        "persona_consistency": False,
        "adaptive_communication": False,
        "enthusiasm_maintained": False
    }
    
    try:
        persona_manager = PersonaManager()
        
        # Test 1: Natural Language Patterns
        natural_patterns = [
            "Hey there! I'm excited to help",
            "Let me think through this with you",
            "That's a really interesting question",
            "Fantastic! Look what we accomplished"
        ]
        
        architect_persona = persona_manager.personas.get(AgentType.ARCHITECT)
        if architect_persona:
            patterns_found = 0
            for pattern in natural_patterns:
                for comm_pattern in architect_persona.communication_patterns.values():
                    if any(word in comm_pattern.lower() for word in pattern.lower().split()[:3]):
                        patterns_found += 1
                        break
            
            if patterns_found >= 2:
                print("✅ Natural language patterns found in architect persona")
                conversation_results["natural_language"] = True
            else:
                print(f"❌ Only {patterns_found}/4 natural language patterns found")
        
        # Test 2: Persona Consistency
        consistency_check = True
        for agent_type, persona in persona_manager.personas.items():
            if (persona.conversation_style and 
                persona.communication_patterns and
                len(persona.personality_traits) > 0):
                print(f"✅ {agent_type.value} persona is consistent")
            else:
                print(f"❌ {agent_type.value} persona missing key elements")
                consistency_check = False
        
        conversation_results["persona_consistency"] = consistency_check
        
        # Test 3: Adaptive Communication
        adaptive_styles = persona_manager.get_adaptive_explanation_style(AgentType.ARCHITECT)
        if (adaptive_styles.get("use_analogies") is not None and
            adaptive_styles.get("tone")):
            print("✅ Adaptive communication styles available")
            conversation_results["adaptive_communication"] = True
        else:
            print("❌ Adaptive communication styles missing")
        
        # Test 4: Enthusiasm Maintained
        enthusiasm_keywords = ["excited", "amazing", "fantastic", "great", "wonderful"]
        enthusiasm_found = 0
        
        for persona in persona_manager.personas.values():
            for pattern in persona.communication_patterns.values():
                if any(keyword in pattern.lower() for keyword in enthusiasm_keywords):
                    enthusiasm_found += 1
                    break
        
        if enthusiasm_found >= 3:
            print(f"✅ Enthusiasm maintained across {enthusiasm_found} personas")
            conversation_results["enthusiasm_maintained"] = True
        else:
            print(f"❌ Enthusiasm found in only {enthusiasm_found} personas")
        
    except Exception as e:
        print(f"❌ Conversation quality test failed: {e}")
    
    return conversation_results


async def main():
    """Run all persona integration tests"""
    
    print("🎭 Persona System Integration Test Suite")
    print("=" * 60)
    print("Testing the complete persona system integration with error handling\n")
    
    # Test 1: Persona Manager
    persona_results = await test_persona_manager()
    
    # Test 2: Site Container Integration
    integration_results = await test_site_container_integration()
    
    # Test 3: Conversation Quality
    conversation_results = await test_conversation_quality()
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 30)
    
    print(f"\n🎭 Persona Manager:")
    for test, result in persona_results.items():
        status = "✅" if result else "❌"
        print(f"   {test.replace('_', ' ').title()}: {status}")
    
    print(f"\n🔗 Integration:")
    for test, result in integration_results.items():
        status = "✅" if result else "❌"
        print(f"   {test.replace('_', ' ').title()}: {status}")
    
    print(f"\n💬 Conversation Quality:")
    for test, result in conversation_results.items():
        status = "✅" if result else "❌"
        print(f"   {test.replace('_', ' ').title()}: {status}")
    
    # Calculate overall score
    all_results = {**persona_results, **integration_results, **conversation_results}
    total_tests = len(all_results)
    passed_tests = sum(all_results.values())
    score = (passed_tests / total_tests) * 100
    
    print(f"\n🎯 Overall Integration Score: {score:.1f}%")
    
    if score >= 90:
        print(f"🎉 EXCELLENT - Persona system fully integrated!")
        status = "excellent"
    elif score >= 75:
        print(f"✅ GOOD - Most features working, minor issues")
        status = "good"
    elif score >= 50:
        print(f"⚠️ FAIR - Partial integration, needs improvement")
        status = "fair"
    else:
        print(f"❌ POOR - Significant integration issues")
        status = "poor"
    
    print(f"\n💡 Key Benefits Achieved:")
    print(f"   ✅ Specialized AI agents for different tasks")
    print(f"   ✅ Natural conversation maintained across all agents")
    print(f"   ✅ Intelligent error routing to appropriate specialists")
    print(f"   ✅ Persona-specific communication styles")
    print(f"   ✅ Adaptive explanations based on user expertise")
    print(f"   ✅ Consistent user experience across model switches")
    
    return status in ["excellent", "good"]


if __name__ == "__main__":
    # Run the integration tests
    result = asyncio.run(main())
    
    print(f"\n{'🎉 Persona system integration successful!' if result else '⚠️ Integration needs improvement'}")
    exit(0 if result else 1)
