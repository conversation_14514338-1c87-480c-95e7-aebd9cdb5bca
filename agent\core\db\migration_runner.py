#!/usr/bin/env python3
"""
Alembic Migration Runner

This module provides comprehensive Alembic migration management for projects,
including generation, application, and rollback of database migrations.
"""

import os
import logging
import tempfile
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
import subprocess
import sys

from alembic import command
from alembic.config import Config
from alembic.script import ScriptDirectory
from alembic.runtime.migration import MigrationContext
from alembic.runtime.environment import EnvironmentContext
from sqlalchemy import create_engine, MetaData, inspect
from sqlalchemy.engine import Engine

from .error_parser import DatabaseErrorParser, ErrorAnalysis

logger = logging.getLogger(__name__)


class MigrationStatus(Enum):
    """Migration status types"""
    PENDING = "pending"
    APPLIED = "applied"
    FAILED = "failed"
    UNKNOWN = "unknown"


@dataclass
class MigrationInfo:
    """Information about a migration"""
    revision: str
    description: str
    status: MigrationStatus
    created_at: Optional[str] = None
    applied_at: Optional[str] = None
    error: Optional[str] = None


@dataclass
class MigrationResult:
    """Result of a migration operation"""
    success: bool
    message: str
    revision: Optional[str] = None
    error_analysis: Optional[ErrorAnalysis] = None
    migrations_applied: List[str] = None


class MigrationRunner:
    """Main Alembic migration runner for project-based migrations"""

    def __init__(self, project_name: str, database_url: Optional[str] = None, projects_root: Optional[str] = None):
        """
        Initialize migration runner for a specific project

        Args:
            project_name: Name of the project
            database_url: Database connection URL (defaults to SQLite)
            projects_root: Root directory for projects (defaults to ./projects)
        """
        self.project_name = project_name
        self.projects_root = Path(projects_root or "projects")
        self.project_dir = self.projects_root / project_name
        self.migrations_dir = self.project_dir / "migrations"

        # Default to SQLite if no URL provided
        if database_url is None:
            # Ensure project directory exists before creating database URL
            self.project_dir.mkdir(parents=True, exist_ok=True)
            db_file = self.project_dir / "database.db"
            self.database_url = f"sqlite:///{db_file}"
        else:
            self.database_url = database_url

        self.error_parser = DatabaseErrorParser()
        self._alembic_config = None

    def _ensure_project_structure(self) -> None:
        """Ensure project directory structure exists"""
        self.project_dir.mkdir(parents=True, exist_ok=True)
        self.migrations_dir.mkdir(parents=True, exist_ok=True)

    def _get_alembic_config(self) -> Config:
        """Get or create Alembic configuration"""
        if self._alembic_config is None:
            self._alembic_config = self._create_alembic_config()
        return self._alembic_config

    def _create_alembic_config(self) -> Config:
        """Create Alembic configuration dynamically"""
        self._ensure_project_structure()

        # Create alembic.ini content
        alembic_ini_content = f"""
[alembic]
script_location = {self.migrations_dir}
prepend_sys_path = .
version_path_separator = os
sqlalchemy.url = {self.database_url}

[post_write_hooks]

[loggers]
keys = root,sqlalchemy,alembic

[handlers]
keys = console

[formatters]
keys = generic

[logger_root]
level = WARN
handlers = console
qualname =

[logger_sqlalchemy]
level = WARN
handlers =
qualname = sqlalchemy.engine

[logger_alembic]
level = INFO
handlers =
qualname = alembic

[handler_console]
class = StreamHandler
args = (sys.stderr,)
level = NOTSET
formatter = generic

[formatter_generic]
format = %(levelname)-5.5s [%(name)s] %(message)s
datefmt = %H:%M:%S
"""

        # Write alembic.ini to migrations directory
        alembic_ini_path = self.migrations_dir / "alembic.ini"
        with open(alembic_ini_path, 'w') as f:
            f.write(alembic_ini_content.strip())

        # Create Alembic config object
        config = Config(str(alembic_ini_path))
        config.set_main_option("script_location", str(self.migrations_dir))
        config.set_main_option("sqlalchemy.url", self.database_url)

        return config

    def _create_env_py(self) -> None:
        """Create env.py file for Alembic"""
        env_py_content = '''
from logging.config import fileConfig
from sqlalchemy import engine_from_config
from sqlalchemy import pool
from alembic import context
import os
import sys

# Add project root to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

# this is the Alembic Config object
config = context.config

# Interpret the config file for Python logging
if config.config_file_name is not None:
    fileConfig(config.config_file_name)

# Import your models here for autogenerate support
# from myapp import mymodel
target_metadata = None

def run_migrations_offline() -> None:
    """Run migrations in 'offline' mode."""
    url = config.get_main_option("sqlalchemy.url")
    context.configure(
        url=url,
        target_metadata=target_metadata,
        literal_binds=True,
        dialect_opts={"paramstyle": "named"},
    )

    with context.begin_transaction():
        context.run_migrations()

def run_migrations_online() -> None:
    """Run migrations in 'online' mode."""
    connectable = engine_from_config(
        config.get_section(config.config_ini_section),
        prefix="sqlalchemy.",
        poolclass=pool.NullPool,
    )

    with connectable.connect() as connection:
        context.configure(
            connection=connection, target_metadata=target_metadata
        )

        with context.begin_transaction():
            context.run_migrations()

if context.is_offline_mode():
    run_migrations_offline()
else:
    run_migrations_online()
'''

        env_py_path = self.migrations_dir / "env.py"
        with open(env_py_path, 'w') as f:
            f.write(env_py_content.strip())

    def _create_script_py_mako(self) -> None:
        """Create script.py.mako template file for Alembic"""
        script_mako_content = '''"""${message}

Revision ID: ${up_revision}
Revises: ${down_revision | comma,n}
Create Date: ${create_date}

"""
from alembic import op
import sqlalchemy as sa
${imports if imports else ""}

# revision identifiers, used by Alembic.
revision = ${repr(up_revision)}
down_revision = ${repr(down_revision)}
branch_labels = ${repr(branch_labels)}
depends_on = ${repr(depends_on)}


def upgrade() -> None:
    ${upgrades if upgrades else "pass"}


def downgrade() -> None:
    ${downgrades if downgrades else "pass"}
'''

        script_mako_path = self.migrations_dir / "script.py.mako"
        with open(script_mako_path, 'w') as f:
            f.write(script_mako_content.strip())

    def init_migrations(self) -> MigrationResult:
        """Initialize Alembic migration environment"""
        try:
            # Ensure project directory exists
            self.project_dir.mkdir(parents=True, exist_ok=True)

            # Check if already initialized
            if (self.migrations_dir / "alembic.ini").exists():
                return MigrationResult(
                    success=True,
                    message=f"Migration environment already initialized for {self.project_name}"
                )

            # Clean up any existing partial initialization
            if self.migrations_dir.exists():
                shutil.rmtree(self.migrations_dir)

            # Create fresh migrations directory
            self.migrations_dir.mkdir(parents=True, exist_ok=True)

            # Create the basic directory structure manually
            versions_dir = self.migrations_dir / "versions"
            versions_dir.mkdir(exist_ok=True)

            # Create our custom alembic.ini and env.py first
            self._create_alembic_config()
            self._create_env_py()
            self._create_script_py_mako()

            logger.info(f"Initialized migration environment for project {self.project_name}")

            return MigrationResult(
                success=True,
                message=f"Successfully initialized migration environment for {self.project_name}"
            )

        except Exception as e:
            error_analysis = self.error_parser.parse_error(e)
            logger.error(f"Failed to initialize migrations for {self.project_name}: {e}")

            return MigrationResult(
                success=False,
                message=f"Failed to initialize migrations: {str(e)}",
                error_analysis=error_analysis
            )

    def generate_migration(self, message: str, auto_generate: bool = True) -> MigrationResult:
        """
        Generate a new migration

        Args:
            message: Description of the migration
            auto_generate: Whether to auto-generate from model changes
        """
        try:
            # Ensure migrations are initialized first
            if not (self.migrations_dir / "alembic.ini").exists():
                init_result = self.init_migrations()
                if not init_result.success:
                    return init_result

            config = self._get_alembic_config()

            # Generate migration
            if auto_generate:
                command.revision(config, message=message, autogenerate=True)
            else:
                command.revision(config, message=message)

            # Get the latest revision
            script_dir = ScriptDirectory.from_config(config)
            latest_revision = script_dir.get_current_head()

            logger.info(f"Generated migration {latest_revision} for project {self.project_name}")

            return MigrationResult(
                success=True,
                message=f"Successfully generated migration: {message}",
                revision=latest_revision
            )

        except Exception as e:
            error_analysis = self.error_parser.parse_error(e)
            logger.error(f"Failed to generate migration for {self.project_name}: {e}")

            return MigrationResult(
                success=False,
                message=f"Failed to generate migration: {str(e)}",
                error_analysis=error_analysis
            )

    def apply_migrations(self, target_revision: str = "head") -> MigrationResult:
        """
        Apply migrations up to target revision

        Args:
            target_revision: Target revision (default: "head" for latest)
        """
        try:
            config = self._get_alembic_config()

            # Apply migrations
            command.upgrade(config, target_revision)

            # Get applied migrations
            applied_migrations = self._get_applied_migrations()

            logger.info(f"Applied migrations for project {self.project_name} to {target_revision}")

            return MigrationResult(
                success=True,
                message=f"Successfully applied migrations to {target_revision}",
                revision=target_revision,
                migrations_applied=applied_migrations
            )

        except Exception as e:
            error_analysis = self.error_parser.parse_error(e)
            logger.error(f"Failed to apply migrations for {self.project_name}: {e}")

            return MigrationResult(
                success=False,
                message=f"Failed to apply migrations: {str(e)}",
                error_analysis=error_analysis
            )

    def rollback_migrations(self, target_revision: str) -> MigrationResult:
        """
        Rollback migrations to target revision

        Args:
            target_revision: Target revision to rollback to
        """
        try:
            config = self._get_alembic_config()

            # Rollback migrations
            command.downgrade(config, target_revision)

            logger.info(f"Rolled back migrations for project {self.project_name} to {target_revision}")

            return MigrationResult(
                success=True,
                message=f"Successfully rolled back migrations to {target_revision}",
                revision=target_revision
            )

        except Exception as e:
            error_analysis = self.error_parser.parse_error(e)
            logger.error(f"Failed to rollback migrations for {self.project_name}: {e}")

            return MigrationResult(
                success=False,
                message=f"Failed to rollback migrations: {str(e)}",
                error_analysis=error_analysis
            )

    def get_current_revision(self) -> Optional[str]:
        """Get current database revision"""
        try:
            config = self._get_alembic_config()

            # Create engine and get current revision
            engine = create_engine(self.database_url)
            with engine.connect() as connection:
                context = MigrationContext.configure(connection)
                return context.get_current_revision()

        except Exception as e:
            logger.error(f"Failed to get current revision for {self.project_name}: {e}")
            return None

    def _get_applied_migrations(self) -> List[str]:
        """Get list of applied migration revisions"""
        try:
            config = self._get_alembic_config()
            script_dir = ScriptDirectory.from_config(config)

            current_rev = self.get_current_revision()
            if not current_rev:
                return []

            # Get all revisions up to current
            revisions = []
            for revision in script_dir.walk_revisions("base", current_rev):
                revisions.append(revision.revision)

            return list(reversed(revisions))  # Return in chronological order

        except Exception as e:
            logger.error(f"Failed to get applied migrations for {self.project_name}: {e}")
            return []

    def get_migration_history(self) -> List[MigrationInfo]:
        """Get complete migration history"""
        try:
            config = self._get_alembic_config()
            script_dir = ScriptDirectory.from_config(config)

            current_rev = self.get_current_revision()
            applied_revisions = set(self._get_applied_migrations())

            migrations = []
            for revision in script_dir.walk_revisions():
                status = MigrationStatus.APPLIED if revision.revision in applied_revisions else MigrationStatus.PENDING

                migration_info = MigrationInfo(
                    revision=revision.revision,
                    description=revision.doc or "No description",
                    status=status,
                    created_at=str(revision.create_date) if hasattr(revision, 'create_date') else None
                )
                migrations.append(migration_info)

            return list(reversed(migrations))  # Return in chronological order

        except Exception as e:
            logger.error(f"Failed to get migration history for {self.project_name}: {e}")
            return []

    def validate_database_schema(self) -> MigrationResult:
        """Validate that database schema matches migrations"""
        try:
            config = self._get_alembic_config()

            # Check if database is up to date
            current_rev = self.get_current_revision()
            script_dir = ScriptDirectory.from_config(config)
            head_rev = script_dir.get_current_head()

            if current_rev == head_rev:
                return MigrationResult(
                    success=True,
                    message="Database schema is up to date",
                    revision=current_rev
                )
            else:
                pending_count = len([r for r in script_dir.walk_revisions(current_rev, head_rev)])
                return MigrationResult(
                    success=False,
                    message=f"Database schema is outdated. {pending_count} migrations pending.",
                    revision=current_rev
                )

        except Exception as e:
            error_analysis = self.error_parser.parse_error(e)
            logger.error(f"Failed to validate schema for {self.project_name}: {e}")

            return MigrationResult(
                success=False,
                message=f"Failed to validate schema: {str(e)}",
                error_analysis=error_analysis
            )

    def create_database_backup(self) -> Optional[str]:
        """Create a backup of the database before migrations (SQLite only)"""
        if not self.database_url.startswith("sqlite"):
            logger.warning("Database backup only supported for SQLite")
            return None

        try:
            # Extract database file path from URL
            db_path = self.database_url.replace("sqlite:///", "")
            if not os.path.exists(db_path):
                return None

            # Create backup with timestamp (including microseconds for uniqueness)
            import datetime
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            backup_path = f"{db_path}.backup_{timestamp}"

            shutil.copy2(db_path, backup_path)
            logger.info(f"Created database backup: {backup_path}")

            return backup_path

        except Exception as e:
            logger.error(f"Failed to create database backup: {e}")
            return None

    def restore_database_backup(self, backup_path: str) -> bool:
        """Restore database from backup (SQLite only)"""
        if not self.database_url.startswith("sqlite"):
            logger.warning("Database restore only supported for SQLite")
            return False

        try:
            # Extract database file path from URL
            db_path = self.database_url.replace("sqlite:///", "")

            if not os.path.exists(backup_path):
                logger.error(f"Backup file not found: {backup_path}")
                return False

            shutil.copy2(backup_path, db_path)
            logger.info(f"Restored database from backup: {backup_path}")

            return True

        except Exception as e:
            logger.error(f"Failed to restore database backup: {e}")
            return False

    def cleanup_old_backups(self, keep_count: int = 5) -> None:
        """Clean up old database backups, keeping only the most recent ones"""
        if not self.database_url.startswith("sqlite"):
            return

        try:
            # Extract database file path from URL
            db_path = self.database_url.replace("sqlite:///", "")
            db_dir = os.path.dirname(db_path)
            db_name = os.path.basename(db_path)

            # Find all backup files
            backup_files = []
            for file in os.listdir(db_dir):
                if file.startswith(f"{db_name}.backup_"):
                    backup_path = os.path.join(db_dir, file)
                    backup_files.append((backup_path, os.path.getmtime(backup_path)))

            # Sort by modification time (newest first)
            backup_files.sort(key=lambda x: x[1], reverse=True)

            # Remove old backups
            for backup_path, _ in backup_files[keep_count:]:
                os.remove(backup_path)
                logger.info(f"Removed old backup: {backup_path}")

        except Exception as e:
            logger.error(f"Failed to cleanup old backups: {e}")

    def get_database_info(self) -> Dict[str, Any]:
        """Get information about the database"""
        try:
            engine = create_engine(self.database_url)
            inspector = inspect(engine)

            info = {
                "database_url": self.database_url,
                "current_revision": self.get_current_revision(),
                "tables": inspector.get_table_names(),
                "table_count": len(inspector.get_table_names()),
                "migrations_applied": len(self._get_applied_migrations()),
                "project_name": self.project_name,
                "migrations_dir": str(self.migrations_dir)
            }

            # Add database-specific info
            if self.database_url.startswith("sqlite"):
                db_path = self.database_url.replace("sqlite:///", "")
                if os.path.exists(db_path):
                    info["database_size"] = os.path.getsize(db_path)
                    info["database_exists"] = True
                else:
                    info["database_exists"] = False

            return info

        except Exception as e:
            logger.error(f"Failed to get database info for {self.project_name}: {e}")
            return {
                "database_url": self.database_url,
                "project_name": self.project_name,
                "error": str(e)
            }
