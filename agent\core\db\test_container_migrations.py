#!/usr/bin/env python3
"""
Test Container Migration Integration
Tests for applying database migrations via SiteContainerManager
"""

import asyncio
import json
import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from agent.core.db.migration_runner import MigrationRunner
from agent.core.site_container_manager import SiteContainerManager


class TestContainerMigrations(unittest.TestCase):
    """Test container migration functionality"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.site_name = "test_site"
        self.container_manager = SiteContainerManager(
            sites_dir=os.path.join(self.temp_dir, "sites"),
            containers_dir=os.path.join(self.temp_dir, "containers")
        )

    def tearDown(self):
        """Clean up test fixtures"""
        import shutil
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    @patch('subprocess.run')
    def test_execute_command_in_container_success(self, mock_subprocess):
        """Test successful command execution in container"""
        # Mock container exists and is running
        from agent.core.site_container_manager import SiteContainer, ContainerStatus
        from datetime import datetime
        
        container = SiteContainer(
            site_name=self.site_name,
            container_name=f"site-{self.site_name}",
            port=8080,
            status=ContainerStatus.RUNNING,
            image_name=f"ai-coding-site-{self.site_name}",
            created_at=datetime.now()
        )
        self.container_manager.site_containers[self.site_name] = container

        # Mock successful subprocess execution
        mock_subprocess.return_value.returncode = 0
        mock_subprocess.return_value.stdout = "Command executed successfully"
        mock_subprocess.return_value.stderr = ""

        # Test command execution
        result = asyncio.run(
            self.container_manager.execute_command_in_container(
                self.site_name, ["echo", "hello"], "/app"
            )
        )

        self.assertTrue(result["success"])
        self.assertEqual(result["return_code"], 0)
        self.assertEqual(result["stdout"], "Command executed successfully")
        self.assertEqual(result["command"], ["echo", "hello"])

    def test_execute_command_container_not_found(self):
        """Test command execution when container doesn't exist"""
        result = asyncio.run(
            self.container_manager.execute_command_in_container(
                "nonexistent_site", ["echo", "hello"], "/app"
            )
        )

        self.assertFalse(result["success"])
        self.assertIn("does not exist", result["error"])

    def test_execute_command_container_not_running(self):
        """Test command execution when container is not running"""
        from agent.core.site_container_manager import SiteContainer, ContainerStatus
        from datetime import datetime
        
        container = SiteContainer(
            site_name=self.site_name,
            container_name=f"site-{self.site_name}",
            port=8080,
            status=ContainerStatus.STOPPED,
            image_name=f"ai-coding-site-{self.site_name}",
            created_at=datetime.now()
        )
        self.container_manager.site_containers[self.site_name] = container

        result = asyncio.run(
            self.container_manager.execute_command_in_container(
                self.site_name, ["echo", "hello"], "/app"
            )
        )

        self.assertFalse(result["success"])
        self.assertIn("is not running", result["error"])

    @patch('subprocess.run')
    def test_execute_command_timeout(self, mock_subprocess):
        """Test command execution timeout"""
        from agent.core.site_container_manager import SiteContainer, ContainerStatus
        from datetime import datetime
        import subprocess
        
        container = SiteContainer(
            site_name=self.site_name,
            container_name=f"site-{self.site_name}",
            port=8080,
            status=ContainerStatus.RUNNING,
            image_name=f"ai-coding-site-{self.site_name}",
            created_at=datetime.now()
        )
        self.container_manager.site_containers[self.site_name] = container

        # Mock timeout exception
        mock_subprocess.side_effect = subprocess.TimeoutExpired("docker", 300)

        result = asyncio.run(
            self.container_manager.execute_command_in_container(
                self.site_name, ["sleep", "600"], "/app"
            )
        )

        self.assertFalse(result["success"])
        self.assertIn("timed out", result["error"])

    @patch.object(SiteContainerManager, 'execute_command_in_container')
    def test_apply_database_migrations_success(self, mock_execute):
        """Test successful database migration application"""
        from agent.core.site_container_manager import SiteContainer, ContainerStatus
        from datetime import datetime
        
        container = SiteContainer(
            site_name=self.site_name,
            container_name=f"site-{self.site_name}",
            port=8080,
            status=ContainerStatus.RUNNING,
            image_name=f"ai-coding-site-{self.site_name}",
            created_at=datetime.now()
        )
        self.container_manager.site_containers[self.site_name] = container

        # Mock successful command executions
        mock_execute.side_effect = [
            # Check migration runner availability
            {"success": True, "stdout": "Migration runner available", "stderr": ""},
            # Create migrations directory
            {"success": True, "stdout": "", "stderr": ""},
            # Initialize migrations
            {"success": True, "stdout": "Init result: True - Successfully initialized", "stderr": ""},
            # Apply migrations
            {"success": True, "stdout": "Apply result: True - Successfully applied migrations", "stderr": ""}
        ]

        result = asyncio.run(
            self.container_manager.apply_database_migrations(self.site_name)
        )

        self.assertTrue(result["success"])
        self.assertEqual(result["container_name"], f"site-{self.site_name}")
        self.assertIn("Database migrations applied", result["message"])

    def test_apply_database_migrations_container_not_found(self):
        """Test migration application when container doesn't exist"""
        result = asyncio.run(
            self.container_manager.apply_database_migrations("nonexistent_site")
        )

        self.assertFalse(result["success"])
        self.assertIn("does not exist", result["error"])

    @patch.object(SiteContainerManager, 'execute_command_in_container')
    def test_apply_database_migrations_init_failure(self, mock_execute):
        """Test migration application when initialization fails"""
        from agent.core.site_container_manager import SiteContainer, ContainerStatus
        from datetime import datetime
        
        container = SiteContainer(
            site_name=self.site_name,
            container_name=f"site-{self.site_name}",
            port=8080,
            status=ContainerStatus.RUNNING,
            image_name=f"ai-coding-site-{self.site_name}",
            created_at=datetime.now()
        )
        self.container_manager.site_containers[self.site_name] = container

        # Mock command executions with init failure
        mock_execute.side_effect = [
            # Check migration runner availability
            {"success": True, "stdout": "Migration runner available", "stderr": ""},
            # Create migrations directory
            {"success": True, "stdout": "", "stderr": ""},
            # Initialize migrations (fails)
            {"success": False, "stdout": "", "stderr": "Failed to initialize migrations"}
        ]

        result = asyncio.run(
            self.container_manager.apply_database_migrations(self.site_name)
        )

        self.assertFalse(result["success"])
        self.assertIn("Failed to initialize migrations", result["error"])

    def test_migration_config_validation(self):
        """Test migration configuration validation"""
        # Test with custom migration config
        custom_config = {
            "database_url": "postgresql://user:pass@localhost/db",
            "migrations_dir": "/custom/migrations",
            "python_path": "/usr/bin/python3"
        }

        from agent.core.site_container_manager import SiteContainer, ContainerStatus
        from datetime import datetime
        
        container = SiteContainer(
            site_name=self.site_name,
            container_name=f"site-{self.site_name}",
            port=8080,
            status=ContainerStatus.RUNNING,
            image_name=f"ai-coding-site-{self.site_name}",
            created_at=datetime.now()
        )
        self.container_manager.site_containers[self.site_name] = container

        # Mock successful execution to test config passing
        with patch.object(self.container_manager, 'execute_command_in_container') as mock_execute:
            mock_execute.side_effect = [
                {"success": True, "stdout": "Migration runner available", "stderr": ""},
                {"success": True, "stdout": "", "stderr": ""},
                {"success": True, "stdout": "Init result: True", "stderr": ""},
                {"success": True, "stdout": "Apply result: True", "stderr": ""}
            ]

            result = asyncio.run(
                self.container_manager.apply_database_migrations(
                    self.site_name, custom_config
                )
            )

            self.assertTrue(result["success"])
            self.assertEqual(result["migration_config"], custom_config)

    def test_container_metadata_update(self):
        """Test that container metadata is updated after migration"""
        from agent.core.site_container_manager import SiteContainer, ContainerStatus
        from datetime import datetime
        
        container = SiteContainer(
            site_name=self.site_name,
            container_name=f"site-{self.site_name}",
            port=8080,
            status=ContainerStatus.RUNNING,
            image_name=f"ai-coding-site-{self.site_name}",
            created_at=datetime.now()
        )
        self.container_manager.site_containers[self.site_name] = container

        # Verify initial state
        self.assertIsNone(container.last_migration_applied)

        # Mock successful migration
        with patch.object(self.container_manager, 'execute_command_in_container') as mock_execute:
            mock_execute.side_effect = [
                {"success": True, "stdout": "Migration runner available", "stderr": ""},
                {"success": True, "stdout": "", "stderr": ""},
                {"success": True, "stdout": "Init result: True", "stderr": ""},
                {"success": True, "stdout": "Apply result: True", "stderr": ""}
            ]

            result = asyncio.run(
                self.container_manager.apply_database_migrations(self.site_name)
            )

            self.assertTrue(result["success"])
            # Verify metadata was updated
            self.assertIsNotNone(container.last_migration_applied)


if __name__ == "__main__":
    unittest.main()
