"""
Tests for SSL and Security Managers
"""

import json
import os
import shutil
import sqlite3
import sys
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from agent.security.security_manager import SecurityManager
from agent.security.ssl_manager import SSLManager


class TestSSLManager:
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        shutil.rmtree(temp_dir)

    @pytest.fixture
    def ssl_config(self, temp_dir):
        """Create SSL configuration for testing"""
        config = {
            "cert_dir": str(Path(temp_dir) / "ssl"),
            "lets_encrypt": {
                "enabled": True,
                "email": "<EMAIL>",
                "staging": True,
            },
            "self_signed": {
                "enabled": False,
                "country": "US",
                "state": "Test State",
                "locality": "Test City",
                "organization": "Test Org",
                "common_name": "test.local",
            },
            "auto_renewal": {"enabled": True, "days_before_expiry": 30},
        }

        config_path = Path(temp_dir) / "ssl_config.json"
        with open(config_path, "w") as f:
            json.dump(config, f)

        return str(config_path)

    def test_ssl_manager_initialization(self, ssl_config):
        """Test SSL manager initialization"""
        ssl_manager = SSLManager(ssl_config)
        assert ssl_manager.config["lets_encrypt"]["enabled"] is True
        assert ssl_manager.config["self_signed"]["enabled"] is False
        assert Path(ssl_manager.cert_dir).exists()

    @patch("subprocess.run")
    def test_check_certbot_installed(self, mock_run, ssl_config):
        """Test certbot availability check"""
        ssl_manager = SSLManager(ssl_config)

        # Test when certbot is installed
        mock_run.return_value.returncode = 0
        assert ssl_manager._check_certbot() is True

        # Test when certbot is not installed
        mock_run.side_effect = FileNotFoundError()
        assert ssl_manager._check_certbot() is False

    def test_create_openssl_config(self, ssl_config):
        """Test OpenSSL configuration generation"""
        ssl_manager = SSLManager(ssl_config)
        config = ssl_manager._create_openssl_config("test.example.com")

        assert "[req]" in config
        assert "CN = test.example.com" in config
        assert "DNS.1 = test.example.com" in config

    @patch("subprocess.run")
    def test_setup_self_signed_certificate(self, mock_run, ssl_config):
        """Test self-signed certificate creation"""
        ssl_manager = SSLManager(ssl_config)
        ssl_manager.config["self_signed"]["enabled"] = True
        ssl_manager.config["lets_encrypt"]["enabled"] = False

        # Mock successful OpenSSL commands
        mock_run.return_value.returncode = 0
        mock_run.return_value.stdout = ""
        mock_run.return_value.stderr = ""

        # Mock the _check_openssl method to return True
        with patch.object(ssl_manager, "_check_openssl", return_value=True):
            # Mock file operations
            with patch("pathlib.Path.exists", return_value=True):
                with patch("pathlib.Path.unlink"):
                    result = ssl_manager._setup_self_signed("test.example.com")

        assert result["status"] == "success"
        assert "test.example.com" in result["message"]
        assert "fullchain.pem" in result["cert_path"]
        assert "privkey.pem" in result["key_path"]

    def test_copy_certificates(self, ssl_config, temp_dir):
        """Test certificate copying"""
        ssl_manager = SSLManager(ssl_config)

        # Create test certificate files
        source_path = Path(temp_dir) / "source_certs"
        source_path.mkdir()

        cert_file = source_path / "fullchain.pem"
        key_file = source_path / "privkey.pem"

        cert_file.write_text("test certificate content")
        key_file.write_text("test private key content")

        # Copy certificates
        ssl_manager._copy_certificates("test.example.com", source_path)

        # Check if files were copied
        dest_cert = ssl_manager.cert_dir / "test.example.com" / "fullchain.pem"
        dest_key = ssl_manager.cert_dir / "test.example.com" / "privkey.pem"

        assert dest_cert.exists()
        assert dest_key.exists()
        assert dest_cert.read_text() == "test certificate content"
        assert dest_key.read_text() == "test private key content"

    @patch("subprocess.run")
    def test_check_certificate_expiry(self, mock_run, ssl_config, temp_dir):
        """Test certificate expiry check"""
        ssl_manager = SSLManager(ssl_config)

        # Create test certificate
        cert_dir = ssl_manager.cert_dir / "test.example.com"
        cert_dir.mkdir(parents=True)
        cert_file = cert_dir / "fullchain.pem"
        cert_file.write_text("test certificate")

        # Mock OpenSSL command output
        mock_run.return_value.stdout = "notAfter=Dec 31 23:59:59 2024 GMT\n"
        mock_run.return_value.returncode = 0

        result = ssl_manager.check_certificate_expiry("test.example.com")

        assert result["status"] == "success"
        assert "2024-12-31" in result["expiry_date"]
        assert "days_until_expiry" in result
        assert "needs_renewal" in result


class TestSecurityManager:
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for tests"""
        temp_dir = tempfile.mkdtemp()
        yield temp_dir
        # Add retry logic for Windows file cleanup
        import time

        for attempt in range(3):
            try:
                shutil.rmtree(temp_dir)
                break
            except PermissionError:
                if attempt < 2:  # Not the last attempt
                    time.sleep(0.1)  # Wait a bit before retrying
                else:
                    # On last attempt, just log the warning and continue
                    import warnings

                    warnings.warn(f"Could not clean up temp directory: {temp_dir}")

    @pytest.fixture
    def security_config(self, temp_dir):
        """Create security configuration for testing"""
        config = {
            "db_path": str(Path(temp_dir) / "security.db"),
            "rate_limiting": {
                "enabled": True,
                "requests_per_minute": 10,
                "burst_limit": 20,
                "block_duration_minutes": 5,
            },
            "security_headers": {
                "enabled": True,
                "hsts_max_age": 31536000,
                "content_security_policy": "default-src 'self'",
                "x_frame_options": "SAMEORIGIN",
                "x_content_type_options": "nosniff",
                "x_xss_protection": "1; mode=block",
                "referrer_policy": "strict-origin-when-cross-origin",
            },
            "ip_whitelist": ["***********"],
            "ip_blacklist": ["********"],
            "allowed_user_agents": [],
            "blocked_user_agents": ["bot", "crawler"],
            "monitoring": {
                "enabled": True,
                "log_suspicious_activity": True,
                "alert_on_attack": True,
            },
        }

        config_path = Path(temp_dir) / "security_config.json"
        with open(config_path, "w") as f:
            json.dump(config, f)

        return str(config_path)

    def test_security_manager_initialization(self, security_config):
        """Test security manager initialization"""
        security_manager = SecurityManager(security_config)
        try:
            assert security_manager.config["rate_limiting"]["enabled"] is True
            assert Path(security_manager.db_path).exists()

            # Check if tables were created
            conn = sqlite3.connect(security_manager.db_path)
            cursor = conn.cursor()

            cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
            tables = [row[0] for row in cursor.fetchall()]

            assert "security_events" in tables
            assert "rate_limits" in tables
            assert "blocked_ips" in tables

            conn.close()
        finally:
            security_manager.close_connections()

    def test_rate_limit_check_new_ip(self, security_config):
        """Test rate limit check for new IP"""
        security_manager = SecurityManager(security_config)
        try:
            result = security_manager.check_rate_limit("*************")

            assert result["allowed"] is True
            assert result["remaining"] == 9  # 10 - 1
        finally:
            security_manager.close_connections()

    def test_rate_limit_check_existing_ip(self, security_config):
        """Test rate limit check for existing IP"""
        security_manager = SecurityManager(security_config)
        try:
            # Make multiple requests
            for i in range(5):
                result = security_manager.check_rate_limit("*************")
                assert result["allowed"] is True

            # Check remaining requests
            result = security_manager.check_rate_limit("*************")
            assert result["allowed"] is True
            assert result["remaining"] == 4  # 10 - 6
        finally:
            security_manager.close_connections()

    def test_rate_limit_exceeded(self, security_config):
        """Test rate limit exceeded scenario"""
        security_manager = SecurityManager(security_config)
        try:
            # Make requests up to the limit
            for i in range(10):
                result = security_manager.check_rate_limit("*************")
                assert result["allowed"] is True

            # Next request should be blocked
            result = security_manager.check_rate_limit("*************")
            assert result["allowed"] is False
            assert "Rate limit exceeded" in result["reason"]
        finally:
            security_manager.close_connections()

    def test_get_security_headers(self, security_config):
        """Test security headers generation"""
        security_manager = SecurityManager(security_config)
        try:
            headers = security_manager.get_security_headers()

            assert "Strict-Transport-Security" in headers
            assert "Content-Security-Policy" in headers
            assert "X-Frame-Options" in headers
            assert "X-Content-Type-Options" in headers
            assert "X-XSS-Protection" in headers
            assert "Referrer-Policy" in headers
        finally:
            security_manager.close_connections()

    def test_validate_request_allowed(self, security_config):
        """Test request validation for allowed request"""
        security_manager = SecurityManager(security_config)
        try:
            result = security_manager.validate_request(
                "***********",  # Whitelisted IP
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "/api/users",
                "GET",
            )

            assert result["allowed"] is True
            assert result["blocked_reason"] is None
        finally:
            security_manager.close_connections()

    def test_validate_request_blacklisted_ip(self, security_config):
        """Test request validation for blacklisted IP"""
        security_manager = SecurityManager(security_config)
        try:
            result = security_manager.validate_request(
                "********",  # Blacklisted IP
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "/api/users",
                "GET",
            )

            assert result["allowed"] is False
            assert "IP is blacklisted" in result["blocked_reason"]
        finally:
            security_manager.close_connections()

    def test_validate_request_suspicious_user_agent(self, security_config):
        """Test request validation for suspicious user agent"""
        security_manager = SecurityManager(security_config)
        try:
            result = security_manager.validate_request(
                "***********",
                "bot",
                "/api/users",
                "GET",  # Whitelisted IP with suspicious user agent
            )

            assert result["allowed"] is True  # Still allowed but with warning
            assert "Suspicious user agent" in result["warnings"]
        finally:
            security_manager.close_connections()

    def test_validate_request_suspicious_path(self, security_config):
        """Test request validation for suspicious path"""
        security_manager = SecurityManager(security_config)
        try:
            result = security_manager.validate_request(
                "***********",
                "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "/admin",  # Suspicious path
                "GET",
            )

            assert result["allowed"] is True  # Still allowed but with warning
            assert "Suspicious request pattern" in result["warnings"]
        finally:
            security_manager.close_connections()

    def test_block_and_unblock_ip(self, security_config):
        """Test IP blocking and unblocking"""
        security_manager = SecurityManager(security_config)

        try:
            # Block IP
            success = security_manager.block_ip("*************", "Test blocking", 10)
            assert success is True

            # Check if IP is blocked
            result = security_manager.check_rate_limit("*************")
            assert result["allowed"] is False
            assert "IP is blocked" in result["reason"]

            # Unblock IP
            success = security_manager.unblock_ip("*************")
            assert success is True

            # Check if IP is unblocked
            result = security_manager.check_rate_limit("*************")
            assert result["allowed"] is True
        finally:
            # Ensure database connections are closed
            security_manager.close_connections()

    def test_get_security_stats(self, security_config):
        """Test security statistics"""
        security_manager = SecurityManager(security_config)
        try:
            # Generate some activity
            security_manager.check_rate_limit("*************")
            security_manager.validate_request("*************", "bot", "/admin", "GET")

            stats = security_manager.get_security_stats(hours=24)

            assert "total_events" in stats
            assert "events_by_type" in stats
            assert "top_blocked_ips" in stats
            assert "currently_blocked" in stats
            assert stats["total_events"] >= 0
        finally:
            security_manager.close_connections()

    def test_cleanup_old_data(self, security_config):
        """Test cleanup of old data"""
        security_manager = SecurityManager(security_config)
        try:
            # Generate some activity
            security_manager.check_rate_limit("*************")

            # Clean up data older than 1 day
            deleted_count = security_manager.cleanup_old_data(days=1)

            assert deleted_count >= 0
        finally:
            security_manager.close_connections()


if __name__ == "__main__":
    pytest.main([__file__])
