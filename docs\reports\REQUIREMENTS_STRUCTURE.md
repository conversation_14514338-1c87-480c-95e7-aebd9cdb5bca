# AI Coding Agent - Requirements Structure

## Overview

This document describes the new modular requirements structure for the AI Coding Agent project. The dependencies have been split into multiple files to optimize builds, reduce conflicts, and enable selective installation based on service needs.

## Requirements Files

### 1. `requirements.txt` - Core Runtime Dependencies

**Purpose**: Essential dependencies needed for the AI Coding Agent to function.

**Key Categories**:
- Web framework & ASGI server (FastAPI, uvicorn)
- Data layer (SQLAlchemy, Pydantic)
- Caching & messaging (Redis, aiohttp)
- Docker control
- File watching (watchdog)
- AST code generation (astor)
- Scheduling (APScheduler)
- CLI tools (click, typer)
- Core utilities (python-dotenv, python-multipart)

**Usage**:
```bash
pip install -r requirements.txt
```

### 2. `requirements-api.txt` - API-specific Dependencies

**Purpose**: Dependencies needed for API functionality, database operations, and external service integrations.

**Key Categories**:
- Database & ORM (psycopg2-binary, alembic)
- API security & authentication (safety, PyJWT, cryptography)
- Web framework extras (websockets, python-socketio)
- Flask ecosystem (if needed)
- HTTP & networking
- Data processing & serialization
- Monitoring & logging
- Configuration & environment
- File handling
- Git integration
- External services (supabase, etc.)

**Usage**:
```bash
pip install -r requirements.txt -r requirements-api.txt
```

### 3. `requirements-learning.txt` - Learning & AI Dependencies

**Purpose**: Dependencies for machine learning, model training, and AI functionality.

**Key Categories**:
- Core ML libraries (torch, transformers, datasets)
- Model optimization & quantization (safetensors, protobuf)
- Evaluation metrics (rouge_score, nltk, scikit-learn)
- Data processing & analysis (networkx, sympy)
- Progress tracking & monitoring (tqdm, wandb)
- HuggingFace ecosystem (huggingface-hub)
- GPU utilities (GPUtil, pynvml)
- Mathematical computing (numpy, pandas)

**Usage**:
```bash
pip install -r requirements.txt -r requirements-learning.txt
```

### 4. `requirements-dev.txt` - Development Dependencies

**Purpose**: Development tools, testing, code quality, and documentation dependencies.

**Key Categories**:
- Code Quality Tools (black, isort, flake8, mypy)
- Testing (pytest, pytest-cov, pytest-mock)
- Pre-commit hooks (pre-commit, cfgv)
- Documentation (sphinx, sphinx-rtd-theme)
- Performance and profiling (memory-profiler, line-profiler)
- Type checking and validation (types-*)
- Additional development tools (ipython, jupyter)
- Security scanning (safety, pip_audit)
- Code analysis (pylint, mccabe)

**Usage**:
```bash
pip install -r requirements.txt -r requirements-dev.txt
```

## Docker Integration

### Updated Dockerfiles

All Dockerfiles have been updated to use the new requirements structure:

1. **`api/Dockerfile`**: Uses `requirements.txt` + `requirements-api.txt`
2. **`containers/Dockerfile`**: Uses all requirements files for full development
3. **`containers/Dockerfile.learning`**: Uses `requirements.txt` + `requirements-learning.txt`
4. **`trend_monitoring/Dockerfile`**: Uses `requirements.txt` + `requirements-api.txt`

### Example Dockerfile Usage

```dockerfile
# Copy requirements first for better caching
COPY requirements.txt ./requirements.txt
COPY requirements-api.txt ./requirements-api.txt

# Install Python dependencies (core + API-specific)
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt && \
    pip install --no-cache-dir -r requirements-api.txt
```

## Version Pinning Strategy

All packages use version ranges with the format `>=x.y.z,<x.y+1.0` to:
- Ensure compatibility
- Allow for patch updates
- Prevent breaking changes
- Enable reproducible builds

## Benefits

1. **Faster Builds**: Only install what's needed for each service
2. **Reduced Conflicts**: Smaller dependency sets reduce version conflicts
3. **Selective Installation**: Choose dependencies based on service requirements
4. **Better Caching**: Docker layer caching is more effective
5. **Easier Maintenance**: Clear separation of concerns
6. **Flexible Deployment**: Deploy services with minimal dependencies

## Migration Guide

### From Old Structure

1. **Backup existing requirements**:
   ```bash
   cp requirements.txt requirements.txt.backup
   ```

2. **Install new structure**:
   ```bash
   # For core functionality only
   pip install -r requirements.txt

   # For API services
   pip install -r requirements.txt -r requirements-api.txt

   # For learning/AI services
   pip install -r requirements.txt -r requirements-learning.txt

   # For development
   pip install -r requirements.txt -r requirements-dev.txt
   ```

3. **Update Dockerfiles**: Use the new requirements file structure

### Testing

Run the test script to verify the requirements structure:
```bash
python test_requirements.py
```

## Troubleshooting

### Common Issues

1. **Version Conflicts**: Check `pip check` output and adjust version constraints
2. **Missing Dependencies**: Ensure all required packages are in the appropriate requirements file
3. **Docker Build Failures**: Verify requirements file paths in Dockerfiles

### Resolution Steps

1. Identify conflicting packages with `pip check`
2. Update version constraints in the appropriate requirements file
3. Test with `pip install -r requirements.txt -r requirements-*.txt`
4. Update Dockerfiles if needed

## Future Maintenance

1. **Adding New Dependencies**: Place in the most specific requirements file
2. **Version Updates**: Update version ranges in the appropriate file
3. **New Services**: Create service-specific requirements if needed
4. **Regular Audits**: Run `pip check` and `safety check` regularly

## Support

For issues with the requirements structure:
1. Check this documentation
2. Run the test script
3. Review Docker build logs
4. Check for version conflicts with `pip check`
