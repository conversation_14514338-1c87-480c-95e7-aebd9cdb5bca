# Containerized Dev/Test Runner for AI Coding Agent

This guide provides reproducible, Docker-first commands to run tests on Windows and Linux without touching host Python.

## Prerequisites
- Docker Desktop (Windows) or Docker Engine (Linux)
- Docker Compose v2 (docker compose)

## Build and Run Tests

PowerShell (Windows):
```powershell
# From repo root
$ErrorActionPreference = 'Stop'
# Fast tests (default markers skip gpu/win32/slow)
docker compose -f containers/docker-compose.test.yml up --build --abort-on-container-exit --exit-code-from test-runner
```

Bash (Linux/macOS):
```bash
# From repo root
set -euo pipefail
# Fast tests (default markers skip gpu/win32/slow)
docker compose -f containers/docker-compose.test.yml up --build --abort-on-container-exit --exit-code-from test-runner
```

## Override Test Selection

- Run a single test file:
```powershell
$env:PYTEST_ARGS = "-q agent/tests/test_simple.py"
docker compose -f containers/docker-compose.test.yml run --rm test-runner
```
```bash
PYTEST_ARGS="-q agent/tests/test_simple.py" docker compose -f containers/docker-compose.test.yml run --rm test-runner
```

- Run fast tests with coverage:
```powershell
$env:PYTEST_ARGS = "-q --cov=agent --cov-report=term-missing"
docker compose -f containers/docker-compose.test.yml run --rm test-runner
```
```bash
PYTEST_ARGS="-q --cov=agent --cov-report=term-missing" docker compose -f containers/docker-compose.test.yml run --rm test-runner
```

- Include slow tests:
```powershell
$env:PYTEST_ARGS = "-q -m 'not gpu and not win32'"
docker compose -f containers/docker-compose.test.yml run --rm test-runner
```
```bash
PYTEST_ARGS="-q -m 'not gpu and not win32'" docker compose -f containers/docker-compose.test.yml run --rm test-runner
```

## NLTK Corpora Persistence
- NLTK corpora (wordnet, omw, punkt) are stored in a named volume `nltk_data`.
- First run will download missing corpora via `containers/scripts/ensure_nltk_data.py`.

## Database
- A Postgres 16 database is started and health-checked.
- Connection URL inside test-runner: `DATABASE_URL=postgresql+psycopg2://testuser:testpass@postgres:5432/testdb`.
- Override via `.env` or inline env when running compose.

## Ollama Integration
- By default, test-runner uses your host Ollama: `OLLAMA_URL=http://host.docker.internal:11434`.
- To run Ollama in a container, uncomment the `ollama:` service in `containers/docker-compose.test.yml` and set:
```powershell
$env:OLLAMA_URL = "http://ollama:11434"
```
```bash
OLLAMA_URL="http://ollama:11434" docker compose -f containers/docker-compose.test.yml run --rm test-runner
```

## Compose Hygiene
- No secrets inlined. Use `.env` file to override:
```
POSTGRES_USER=devuser
POSTGRES_PASSWORD=devpass
POSTGRES_DB=devdb
POSTGRES_PORT=5433
```
- Large data externalized as volumes: `pgdata`, `nltk_data`, `ollama_models` (if enabled).

## Windows Notes
- Windows-only tests (pywin32) are marked `@pytest.mark.win32` and excluded by default.
- To run them:
```powershell
$env:PYTEST_ARGS = "-q -m win32"
docker compose -f containers/docker-compose.test.yml run --rm test-runner
```

## Troubleshooting
- Port conflict on 11434 (Ollama): change exposed port or use host's Ollama only.
- host.docker.internal not resolving on Linux: use your host IP or run the Ollama container.
- Missing corpora: re-run to trigger downloader, or exec into test-runner and run `python /usr/local/bin/ensure_nltk_data.py`.
- File permission issues on bind mount: ensure your user has rights; on Linux, consider `:delegated` or adjust UID/GID via build args.

## CI Parity
- CI uses the same compose file and image to run tests. See `.github/workflows/docker-test.yml`.

