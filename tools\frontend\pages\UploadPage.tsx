import React, { useState } from 'react';
import { UploadZone } from '../components/UploadZone';
import { UploadForm } from '../components/UploadForm';
import { SecurityReport } from '../components/SecurityReport';
import { SiteList } from '../components/SiteList';
import { FileTree } from '../components/FileTree';
import { ProjectManifest } from '../components/ProjectManifest';

interface UploadState {
  step: 'upload' | 'review' | 'complete';
  uploadedFiles: File[];
  reviewReport: any;
  uploadResult: any;
  error: string | null;
  selectedTab: 'manifest' | 'files' | 'security';
}

export const UploadPage: React.FC = () => {
  const [uploadState, setUploadState] = useState<UploadState>({
    step: 'upload',
    uploadedFiles: [],
    reviewReport: null,
    uploadResult: null,
    error: null,
    selectedTab: 'manifest'
  });

  const handleUpload = (files: File[]) => {
    setUploadState(prev => ({
      ...prev,
      uploadedFiles: files,
      error: null
    }));
  };

  const handleUploadComplete = (result: any) => {
    if (result.status === 'review_required') {
      setUploadState(prev => ({
        ...prev,
        step: 'review',
        reviewReport: result.review_report,
        error: null
      }));
    } else if (result.status === 'success') {
      setUploadState(prev => ({
        ...prev,
        step: 'complete',
        uploadResult: result,
        error: null
      }));
    } else {
      setUploadState(prev => ({
        ...prev,
        error: result.message || 'Upload failed'
      }));
    }
  };

  const handleImportConfirm = async (options: {
    targetName?: string;
    reviewFirst: boolean;
    cleanupAfter: boolean
  }) => {
    try {
      setUploadState(prev => ({ ...prev, error: null }));

      // If we have a review report, proceed with import
      if (uploadState.reviewReport) {
        const response = await fetch('/api/upload-site/confirm', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            upload_path: uploadState.reviewReport.upload_path,
            target_name: options.targetName,
            cleanup_after: options.cleanupAfter
          }),
        });

        const result = await response.json();

        if (result.status === 'success') {
          setUploadState(prev => ({
            ...prev,
            step: 'complete',
            uploadResult: result
          }));
        } else {
          setUploadState(prev => ({
            ...prev,
            error: result.message || 'Import failed'
          }));
        }
      }
    } catch (error) {
      setUploadState(prev => ({
        ...prev,
        error: 'Network error during import'
      }));
    }
  };

  const handleCancelReview = () => {
    setUploadState(prev => ({
      ...prev,
      step: 'upload',
      reviewReport: null
    }));
  };

  const handleSiteSelect = (siteName: string) => {
    // Navigate to editor with the selected site
    window.location.href = `/editor?site=${encodeURIComponent(siteName)}`;
  };

  const handleSitePreview = (siteName: string) => {
    // Open site preview in new tab
    window.open(`/sites/${siteName}`, '_blank');
  };

  const handleSiteValidate = (siteName: string) => {
    // Site validation handled in SiteList component
    console.log(`Site ${siteName} validated`);
  };

  const handleFileSelect = (file: any) => {
    console.log('Selected file:', file);
    // Could open file in editor or show preview
  };

  const handleManifestAction = (action: string, data?: any) => {
    switch (action) {
      case 'preview':
        if (uploadState.uploadResult?.target_path) {
          const siteName = uploadState.uploadResult.target_path.split('/').pop();
          handleSitePreview(siteName);
        }
        break;
      case 'edit':
        if (uploadState.uploadResult?.target_path) {
          const siteName = uploadState.uploadResult.target_path.split('/').pop();
          handleSiteSelect(siteName);
        }
        break;
      case 'validate':
        if (uploadState.uploadResult?.target_path) {
          const siteName = uploadState.uploadResult.target_path.split('/').pop();
          handleSiteValidate(siteName);
        }
        break;
    }
  };

  const resetUpload = () => {
    setUploadState({
      step: 'upload',
      uploadedFiles: [],
      reviewReport: null,
      uploadResult: null,
      error: null,
      selectedTab: 'manifest'
    });
  };

  const getCurrentManifest = () => {
    if (uploadState.step === 'review' && uploadState.reviewReport?.manifest) {
      return uploadState.reviewReport.manifest;
    }
    if (uploadState.step === 'complete' && uploadState.uploadResult?.manifest) {
      return uploadState.uploadResult.manifest;
    }
    return null;
  };

  const getCurrentFileTree = () => {
    const manifest = getCurrentManifest();
    if (manifest?.file_tree) {
      return manifest.file_tree;
    }
    return [];
  };

  return (
    <div className="upload-page">
      <div className="upload-page-header">
        <h1>Import Web Project</h1>
        <p>Upload and import external web projects into your IDE</p>
      </div>

      {uploadState.error && (
        <div className="error-banner">
          <p>❌ {uploadState.error}</p>
          <button onClick={resetUpload} className="reset-button">
            🔄 Start Over
          </button>
        </div>
      )}

      {uploadState.step === 'upload' && (
        <div className="upload-section">
          <UploadZone
            onUpload={handleUpload}
            onUploadComplete={handleUploadComplete}
          />
          {uploadState.uploadedFiles.length > 0 && (
            <UploadForm
              onSubmit={handleImportConfirm}
              disabled={false}
            />
          )}
        </div>
      )}

      {uploadState.step === 'review' && uploadState.reviewReport && (
        <div className="review-section">
          <div className="review-tabs">
            <button
              className={`tab-button ${uploadState.selectedTab === 'manifest' ? 'active' : ''}`}
              onClick={() => setUploadState(prev => ({ ...prev, selectedTab: 'manifest' }))}
            >
              📋 Manifest
            </button>
            <button
              className={`tab-button ${uploadState.selectedTab === 'files' ? 'active' : ''}`}
              onClick={() => setUploadState(prev => ({ ...prev, selectedTab: 'files' }))}
            >
              📁 File Tree
            </button>
            <button
              className={`tab-button ${uploadState.selectedTab === 'security' ? 'active' : ''}`}
              onClick={() => setUploadState(prev => ({ ...prev, selectedTab: 'security' }))}
            >
              🔒 Security
            </button>
          </div>

          <div className="review-content">
            {uploadState.selectedTab === 'manifest' && getCurrentManifest() && (
              <ProjectManifest
                manifest={getCurrentManifest()}
                onAction={handleManifestAction}
              />
            )}

            {uploadState.selectedTab === 'files' && (
              <FileTree
                files={getCurrentFileTree()}
                onFileSelect={handleFileSelect}
                maxDepth={3}
              />
            )}

            {uploadState.selectedTab === 'security' && (
              <SecurityReport
                report={uploadState.reviewReport}
                onProceed={() => handleImportConfirm({
                  targetName: '',
                  reviewFirst: false,
                  cleanupAfter: false
                })}
                onCancel={handleCancelReview}
              />
            )}
          </div>
        </div>
      )}

      {uploadState.step === 'complete' && uploadState.uploadResult && (
        <div className="complete-section">
          <div className="success-message">
            <h2>✅ Import Successful!</h2>
            <p>Your web project has been successfully imported.</p>

            <div className="complete-tabs">
              <button
                className={`tab-button ${uploadState.selectedTab === 'manifest' ? 'active' : ''}`}
                onClick={() => setUploadState(prev => ({ ...prev, selectedTab: 'manifest' }))}
              >
                📋 Project Info
              </button>
              <button
                className={`tab-button ${uploadState.selectedTab === 'files' ? 'active' : ''}`}
                onClick={() => setUploadState(prev => ({ ...prev, selectedTab: 'files' }))}
              >
                📁 File Structure
              </button>
            </div>

            <div className="complete-content">
              {uploadState.selectedTab === 'manifest' && getCurrentManifest() && (
                <ProjectManifest
                  manifest={getCurrentManifest()}
                  onAction={handleManifestAction}
                />
              )}

              {uploadState.selectedTab === 'files' && (
                <FileTree
                  files={getCurrentFileTree()}
                  onFileSelect={handleFileSelect}
                  maxDepth={3}
                />
              )}
            </div>

            <div className="complete-actions">
              <button
                onClick={() => handleSiteSelect(uploadState.uploadResult.target_path?.split('/').pop())}
                className="action-button primary"
              >
                📝 Open in Editor
              </button>
              <button
                onClick={() => handleSitePreview(uploadState.uploadResult.target_path?.split('/').pop())}
                className="action-button secondary"
              >
                🔍 Preview Site
              </button>
              <button
                onClick={resetUpload}
                className="action-button secondary"
              >
                📤 Import Another
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="sites-section">
        <SiteList
          onSiteSelect={handleSiteSelect}
          onSiteValidate={handleSiteValidate}
          onSitePreview={handleSitePreview}
        />
      </div>

      <style jsx>{`
        .upload-page {
          min-height: 100vh;
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          padding: 20px;
        }

        .upload-page-header {
          text-align: center;
          color: white;
          margin-bottom: 40px;
        }

        .upload-page-header h1 {
          margin: 0 0 12px 0;
          font-size: 36px;
          font-weight: 700;
        }

        .upload-page-header p {
          margin: 0;
          font-size: 18px;
          opacity: 0.9;
        }

        .error-banner {
          background: #f8d7da;
          color: #721c24;
          padding: 16px 24px;
          border-radius: 8px;
          margin-bottom: 24px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .reset-button {
          padding: 8px 16px;
          background: #dc3545;
          color: white;
          border: none;
          border-radius: 6px;
          cursor: pointer;
        }

        .upload-section, .review-section, .complete-section {
          margin-bottom: 40px;
        }

        .review-tabs, .complete-tabs {
          display: flex;
          gap: 8px;
          margin-bottom: 20px;
          justify-content: center;
        }

        .tab-button {
          padding: 12px 20px;
          background: rgba(255, 255, 255, 0.2);
          color: white;
          border: none;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.3s ease;
          font-weight: 500;
        }

        .tab-button:hover {
          background: rgba(255, 255, 255, 0.3);
        }

        .tab-button.active {
          background: white;
          color: #333;
        }

        .review-content, .complete-content {
          margin-bottom: 24px;
        }

        .complete-section {
          background: white;
          border-radius: 12px;
          padding: 32px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
          max-width: 1200px;
          margin: 0 auto 40px auto;
        }

        .success-message {
          text-align: center;
        }

        .success-message h2 {
          color: #28a745;
          margin: 0 0 16px 0;
        }

        .complete-actions {
          display: flex;
          gap: 12px;
          justify-content: center;
          flex-wrap: wrap;
          margin-top: 24px;
        }

        .action-button {
          padding: 12px 24px;
          border: none;
          border-radius: 8px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .action-button.primary {
          background: #007bff;
          color: white;
        }

        .action-button.primary:hover {
          background: #0056b3;
          transform: translateY(-2px);
        }

        .action-button.secondary {
          background: #6c757d;
          color: white;
        }

        .action-button.secondary:hover {
          background: #5a6268;
          transform: translateY(-2px);
        }

        .sites-section {
          background: white;
          border-radius: 12px;
          padding: 32px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        @media (max-width: 768px) {
          .upload-page {
            padding: 16px;
          }

          .upload-page-header h1 {
            font-size: 28px;
          }

          .upload-page-header p {
            font-size: 16px;
          }

          .review-tabs, .complete-tabs {
            flex-direction: column;
          }

          .complete-actions {
            flex-direction: column;
          }

          .action-button {
            width: 100%;
          }
        }
      `}</style>
    </div>
  );
};
