#!/usr/bin/env python3
"""
Phase 3.1 CLI/Web Workflow Integration Test
Tests the unified agent.py entrypoint and all command routing functionality.
"""

import asyncio
import json
import sys
import time
from datetime import datetime
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent / "src"))

from agent.core.agents.agent_main import AIAgent
from agent.core.agents.base_agent import Agent<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from agent.core.agents.feedback_manager import FeedbackManager
from agent.core.agents.task_manager import CommandRouter


class Phase31IntegrationTester:
    """Integration tester for Phase 3.1 CLI/Web Workflow"""

    def __init__(self):
        self.test_results = []
        self.agent = None
        self.logger = None
        self.error_handler = None

    async def setup(self):
        """Setup test environment"""
        print("🔧 Setting up Phase 3.1 Integration Test...")

        # Create test directories
        for dir_name in ["logs", "data", "test_reports"]:
            Path(dir_name).mkdir(exist_ok=True)

        # Initialize agent components
        self.logger = AgentLogger(log_level="DEBUG", log_file="logs/test_agent.log")
        self.error_handler = ErrorHandler(self.logger)

        # Initialize agent
        self.agent = AIAgent("config/cli_web_config.json")
        await self.agent.initialize()

        print("✅ Setup completed")

    async def test_agent_initialization(self):
        """Test agent initialization"""
        print("\n🚀 Testing Agent Initialization...")

        try:
            # Test agent status
            status = await self.agent.get_status()

            expected_components = [
                "cms",
                "maintenance",
                "deployment",
                "generator",
                "model_router",
                "database",
            ]

            all_operational = True
            for component in expected_components:
                if status["components"].get(component) != "operational":
                    all_operational = False
                    break

            if all_operational:
                self.test_results.append(
                    {
                        "test_name": "Agent_Initialization",
                        "status": "passed",
                        "details": {"status": status},
                    }
                )
                print("   ✅ Agent initialization successful")
            else:
                self.test_results.append(
                    {
                        "test_name": "Agent_Initialization",
                        "status": "failed",
                        "details": {"status": status},
                    }
                )
                print("   ❌ Agent initialization failed")

        except Exception as e:
            self.test_results.append(
                {
                    "test_name": "Agent_Initialization",
                    "status": "error",
                    "details": {"error": str(e)},
                }
            )
            print(f"   💥 Agent initialization error: {e}")

    async def test_system_commands(self):
        """Test system commands"""
        print("\n⚙️ Testing System Commands...")

        system_commands = ["status", "health", "config"]

        for command in system_commands:
            try:
                result = await self.agent.execute_command(command)

                if result.get("success"):
                    self.test_results.append(
                        {
                            "test_name": f"System_Command_{command}",
                            "status": "passed",
                            "details": {"result": result.get("result")},
                        }
                    )
                    print(f"   ✅ {command} command successful")
                else:
                    self.test_results.append(
                        {
                            "test_name": f"System_Command_{command}",
                            "status": "failed",
                            "details": {"error": result.get("error")},
                        }
                    )
                    print(f"   ❌ {command} command failed")

            except Exception as e:
                self.test_results.append(
                    {
                        "test_name": f"System_Command_{command}",
                        "status": "error",
                        "details": {"error": str(e)},
                    }
                )
                print(f"   💥 {command} command error: {e}")

    async def test_cms_commands(self):
        """Test CMS commands"""
        print("\n📝 Testing CMS Commands...")

        # Test content creation
        try:
            result = await self.agent.execute_command(
                "create-content",
                {
                    "title": "Test Content",
                    "content_type": "page",
                    "content": "<h1>Test Page</h1><p>This is test content.</p>",
                    "metadata": {"author": "Test User", "tags": ["test"]},
                },
            )

            if result.get("success"):
                self.test_results.append(
                    {
                        "test_name": "CMS_Create_Content",
                        "status": "passed",
                        "details": {"result": result.get("result")},
                    }
                )
                print("   ✅ CMS create-content successful")
            else:
                self.test_results.append(
                    {
                        "test_name": "CMS_Create_Content",
                        "status": "failed",
                        "details": {"error": result.get("error")},
                    }
                )
                print("   ❌ CMS create-content failed")

        except Exception as e:
            self.test_results.append(
                {
                    "test_name": "CMS_Create_Content",
                    "status": "error",
                    "details": {"error": str(e)},
                }
            )
            print(f"   💥 CMS create-content error: {e}")

        # Test content listing
        try:
            result = await self.agent.execute_command(
                "list-content", {"content_type": "page", "limit": 10}
            )

            if result.get("success"):
                self.test_results.append(
                    {
                        "test_name": "CMS_List_Content",
                        "status": "passed",
                        "details": {"result": result.get("result")},
                    }
                )
                print("   ✅ CMS list-content successful")
            else:
                self.test_results.append(
                    {
                        "test_name": "CMS_List_Content",
                        "status": "failed",
                        "details": {"error": result.get("error")},
                    }
                )
                print("   ❌ CMS list-content failed")

        except Exception as e:
            self.test_results.append(
                {
                    "test_name": "CMS_List_Content",
                    "status": "error",
                    "details": {"error": str(e)},
                }
            )
            print(f"   💥 CMS list-content error: {e}")

    async def test_maintenance_commands(self):
        """Test maintenance commands"""
        print("\n🔧 Testing Maintenance Commands...")

        # Test maintenance status
        try:
            result = await self.agent.execute_command("maintenance-status")

            if result.get("success"):
                self.test_results.append(
                    {
                        "test_name": "Maintenance_Status",
                        "status": "passed",
                        "details": {"result": result.get("result")},
                    }
                )
                print("   ✅ Maintenance status successful")
            else:
                self.test_results.append(
                    {
                        "test_name": "Maintenance_Status",
                        "status": "failed",
                        "details": {"error": result.get("error")},
                    }
                )
                print("   ❌ Maintenance status failed")

        except Exception as e:
            self.test_results.append(
                {
                    "test_name": "Maintenance_Status",
                    "status": "error",
                    "details": {"error": str(e)},
                }
            )
            print(f"   💥 Maintenance status error: {e}")

    async def test_testing_commands(self):
        """Test testing commands"""
        print("\n🧪 Testing Testing Commands...")

        # Test HTML validation
        try:
            result = await self.agent.execute_command(
                "test-html", {"directory": "test_sites"}
            )

            if result.get("success"):
                self.test_results.append(
                    {
                        "test_name": "Testing_HTML_Validation",
                        "status": "passed",
                        "details": {"result": result.get("result")},
                    }
                )
                print("   ✅ HTML validation test successful")
            else:
                self.test_results.append(
                    {
                        "test_name": "Testing_HTML_Validation",
                        "status": "failed",
                        "details": {"error": result.get("error")},
                    }
                )
                print("   ❌ HTML validation test failed")

        except Exception as e:
            self.test_results.append(
                {
                    "test_name": "Testing_HTML_Validation",
                    "status": "error",
                    "details": {"error": str(e)},
                }
            )
            print(f"   💥 HTML validation test error: {e}")

        # Test CSS validation
        try:
            result = await self.agent.execute_command(
                "test-css", {"directory": "test_sites"}
            )

            if result.get("success"):
                self.test_results.append(
                    {
                        "test_name": "Testing_CSS_Validation",
                        "status": "passed",
                        "details": {"result": result.get("result")},
                    }
                )
                print("   ✅ CSS validation test successful")
            else:
                self.test_results.append(
                    {
                        "test_name": "Testing_CSS_Validation",
                        "status": "failed",
                        "details": {"error": result.get("error")},
                    }
                )
                print("   ❌ CSS validation test failed")

        except Exception as e:
            self.test_results.append(
                {
                    "test_name": "Testing_CSS_Validation",
                    "status": "error",
                    "details": {"error": str(e)},
                }
            )
            print(f"   💥 CSS validation test error: {e}")

    async def test_deployment_commands(self):
        """Test deployment commands"""
        print("\n🚀 Testing Deployment Commands...")

        # Test deployment listing
        try:
            result = await self.agent.execute_command("list-deployments", {"limit": 10})

            if result.get("success"):
                self.test_results.append(
                    {
                        "test_name": "Deployment_List",
                        "status": "passed",
                        "details": {"result": result.get("result")},
                    }
                )
                print("   ✅ Deployment list successful")
            else:
                self.test_results.append(
                    {
                        "test_name": "Deployment_List",
                        "status": "failed",
                        "details": {"error": result.get("error")},
                    }
                )
                print("   ❌ Deployment list failed")

        except Exception as e:
            self.test_results.append(
                {
                    "test_name": "Deployment_List",
                    "status": "error",
                    "details": {"error": str(e)},
                }
            )
            print(f"   💥 Deployment list error: {e}")

    async def test_generator_commands(self):
        """Test generator commands"""
        print("\n🎨 Testing Generator Commands...")

        # Test template listing
        try:
            result = await self.agent.execute_command("list-templates")

            if result.get("success"):
                self.test_results.append(
                    {
                        "test_name": "Generator_List_Templates",
                        "status": "passed",
                        "details": {"result": result.get("result")},
                    }
                )
                print("   ✅ Template listing successful")
            else:
                self.test_results.append(
                    {
                        "test_name": "Generator_List_Templates",
                        "status": "failed",
                        "details": {"error": result.get("error")},
                    }
                )
                print("   ❌ Template listing failed")

        except Exception as e:
            self.test_results.append(
                {
                    "test_name": "Generator_List_Templates",
                    "status": "error",
                    "details": {"error": str(e)},
                }
            )
            print(f"   💥 Template listing error: {e}")

    async def test_error_handling(self):
        """Test error handling"""
        print("\n⚠️ Testing Error Handling...")

        # Test unknown command
        try:
            result = await self.agent.execute_command("unknown-command")

            if not result.get("success") and "COMMAND_ERROR" in str(
                result.get("error", {})
            ):
                self.test_results.append(
                    {
                        "test_name": "Error_Handling_Unknown_Command",
                        "status": "passed",
                        "details": {"error": result.get("error")},
                    }
                )
                print("   ✅ Unknown command error handling successful")
            else:
                self.test_results.append(
                    {
                        "test_name": "Error_Handling_Unknown_Command",
                        "status": "failed",
                        "details": {"result": result},
                    }
                )
                print("   ❌ Unknown command error handling failed")

        except Exception as e:
            self.test_results.append(
                {
                    "test_name": "Error_Handling_Unknown_Command",
                    "status": "error",
                    "details": {"error": str(e)},
                }
            )
            print(f"   💥 Unknown command error handling error: {e}")

        # Test invalid arguments
        try:
            result = await self.agent.execute_command(
                "create-content", {"invalid_arg": "invalid_value"}
            )

            if not result.get("success"):
                self.test_results.append(
                    {
                        "test_name": "Error_Handling_Invalid_Args",
                        "status": "passed",
                        "details": {"error": result.get("error")},
                    }
                )
                print("   ✅ Invalid arguments error handling successful")
            else:
                self.test_results.append(
                    {
                        "test_name": "Error_Handling_Invalid_Args",
                        "status": "failed",
                        "details": {"result": result},
                    }
                )
                print("   ❌ Invalid arguments error handling failed")

        except Exception as e:
            self.test_results.append(
                {
                    "test_name": "Error_Handling_Invalid_Args",
                    "status": "error",
                    "details": {"error": str(e)},
                }
            )
            print(f"   💥 Invalid arguments error handling error: {e}")

    async def test_feedback_system(self):
        """Test feedback system"""
        print("\n📊 Testing Feedback System...")

        try:
            # Execute a command to generate feedback
            result = await self.agent.execute_command("status")

            # Check if feedback was recorded
            history = self.agent.feedback_manager.get_command_history(limit=1)

            if history and len(history) > 0:
                self.test_results.append(
                    {
                        "test_name": "Feedback_System_Recording",
                        "status": "passed",
                        "details": {"history_count": len(history)},
                    }
                )
                print("   ✅ Feedback recording successful")
            else:
                self.test_results.append(
                    {
                        "test_name": "Feedback_System_Recording",
                        "status": "failed",
                        "details": {"history": history},
                    }
                )
                print("   ❌ Feedback recording failed")

        except Exception as e:
            self.test_results.append(
                {
                    "test_name": "Feedback_System_Recording",
                    "status": "error",
                    "details": {"error": str(e)},
                }
            )
            print(f"   💥 Feedback recording error: {e}")

    async def test_logging_system(self):
        """Test logging system"""
        print("\n📝 Testing Logging System...")

        try:
            # Check if log file exists and has content
            log_file = Path("logs/test_agent.log")

            if log_file.exists() and log_file.stat().st_size > 0:
                self.test_results.append(
                    {
                        "test_name": "Logging_System_File",
                        "status": "passed",
                        "details": {"log_size": log_file.stat().st_size},
                    }
                )
                print("   ✅ Logging system file successful")
            else:
                self.test_results.append(
                    {
                        "test_name": "Logging_System_File",
                        "status": "failed",
                        "details": {"log_exists": log_file.exists()},
                    }
                )
                print("   ❌ Logging system file failed")

        except Exception as e:
            self.test_results.append(
                {
                    "test_name": "Logging_System_File",
                    "status": "error",
                    "details": {"error": str(e)},
                }
            )
            print(f"   💥 Logging system file error: {e}")

    async def run_comprehensive_integration_test(self):
        """Run comprehensive integration test"""
        print("🧪 Running Comprehensive Phase 3.1 Integration Test")
        print("=" * 60)

        await self.setup()

        # Run all test categories
        await self.test_agent_initialization()
        await self.test_system_commands()
        await self.test_cms_commands()
        await self.test_maintenance_commands()
        await self.test_testing_commands()
        await self.test_deployment_commands()
        await self.test_generator_commands()
        await self.test_error_handling()
        await self.test_feedback_system()
        await self.test_logging_system()

        # Generate comprehensive report
        print("\n📊 Comprehensive Integration Test Results")
        print("=" * 50)

        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["status"] == "passed"])
        failed_tests = len([r for r in self.test_results if r["status"] == "failed"])
        error_tests = len([r for r in self.test_results if r["status"] == "error"])

        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Errors: {error_tests}")
        print(
            f"Success Rate: {(passed_tests / total_tests * 100):.1f}%"
            if total_tests > 0
            else "N/A"
        )

        # Test breakdown by category
        test_categories = {}
        for result in self.test_results:
            category = result["test_name"].split("_")[0]
            if category not in test_categories:
                test_categories[category] = {
                    "total": 0,
                    "passed": 0,
                    "failed": 0,
                    "error": 0,
                }
            test_categories[category]["total"] += 1
            test_categories[category][result["status"]] += 1

        print(f"\n📋 Test Breakdown by Category:")
        for category, stats in test_categories.items():
            success_rate = (
                (stats["passed"] / stats["total"] * 100) if stats["total"] > 0 else 0
            )
            print(
                f"  {category}: {stats['passed']}/{stats['total']} passed ({success_rate:.1f}%)"
            )

        # Show failed tests
        failed_tests_list = [
            r for r in self.test_results if r["status"] in ["failed", "error"]
        ]
        if failed_tests_list:
            print(f"\n❌ Failed/Error Tests:")
            for result in failed_tests_list:
                print(f"  {result['test_name']}: {result['details']}")

        # Generate HTML report
        self._generate_html_report()

        # Save results to JSON
        results_data = {
            "timestamp": datetime.now().isoformat(),
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": failed_tests,
            "error_tests": error_tests,
            "success_rate": (
                (passed_tests / total_tests * 100) if total_tests > 0 else 0
            ),
            "test_breakdown": test_categories,
            "results": self.test_results,
        }

        with open("test_reports/phase_3_1_integration_results.json", "w") as f:
            json.dump(results_data, f, indent=2)

        print(f"\n📄 Results saved to: test_reports/phase_3_1_integration_results.json")

        print(f"\n🎉 Phase 3.1 Integration Test Completed!")
        print(
            f"   Overall Success Rate: {(passed_tests / total_tests * 100):.1f}%"
            if total_tests > 0
            else "N/A"
        )

        return results_data

    def _generate_html_report(self):
        """Generate HTML test report"""
        total_tests = len(self.test_results)
        passed_tests = len([r for r in self.test_results if r["status"] == "passed"])
        failed_tests = len([r for r in self.test_results if r["status"] == "failed"])
        error_tests = len([r for r in self.test_results if r["status"] == "error"])

        html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Phase 3.1 CLI/Web Workflow Integration Test Report</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .summary {{ background: #f5f5f5; padding: 20px; border-radius: 5px; margin-bottom: 20px; }}
        .test-result {{ margin: 10px 0; padding: 10px; border-left: 4px solid #ccc; }}
        .passed {{ border-left-color: #4CAF50; background: #f1f8e9; }}
        .failed {{ border-left-color: #f44336; background: #ffebee; }}
        .error {{ border-left-color: #9c27b0; background: #f3e5f5; }}
    </style>
</head>
<body>
    <h1>Phase 3.1 CLI/Web Workflow Integration Test Report</h1>
    <div class="summary">
        <h2>Summary</h2>
        <p><strong>Total Tests:</strong> {total_tests}</p>
        <p><strong>Passed:</strong> {passed_tests}</p>
        <p><strong>Failed:</strong> {failed_tests}</p>
        <p><strong>Errors:</strong> {error_tests}</p>
        <p><strong>Success Rate:</strong> {(passed_tests / total_tests * 100):.1f}%</p>
    </div>

    <h2>Test Results</h2>"""

        for result in self.test_results:
            status_class = result["status"]
            html_content += f"""
    <div class="test-result {status_class}">
        <h3>{result['test_name']}</h3>
        <p><strong>Status:</strong> {result['status']}</p>
        <p><strong>Details:</strong> {json.dumps(result['details'], indent=2)}</p>
    </div>"""

        html_content += """
</body>
</html>"""

        with open("test_reports/phase_3_1_integration_report.html", "w") as f:
            f.write(html_content)

        print(
            f"📄 HTML report generated: test_reports/phase_3_1_integration_report.html"
        )


async def main():
    """Main function"""
    tester = Phase31IntegrationTester()
    await tester.run_comprehensive_integration_test()


if __name__ == "__main__":
    asyncio.run(main())
