"""
Test suite for Advanced Code Reviewer

Comprehensive tests for all components of the advanced code review system
including quality analysis, security review, performance analysis, and PR integration.

Phase 20 Implementation - Advanced Code Review
"""

import asyncio
import json

# Add src to path for imports
import sys
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock

import pytest

sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from code_review.advanced_code_reviewer import (
    AdvancedCodeReviewer,
    CodeReviewRequest,
    CodeReviewResponse,
)
from code_review.performance_analyzer import PerformanceAnalyzer, PerformanceBottleneck
from code_review.pr_integration import PRComment, PRIntegration
from code_review.quality_metrics import QualityIssue, QualityMetrics
from code_review.review_dashboard import ReviewDashboard
from code_review.security_analyzer import SecurityAnalyzer, SecurityVulnerability
from code_review.yi_coder_integration import YiCoderIntegration


class TestAdvancedCodeReviewer:
    """Test cases for the main AdvancedCodeReviewer class."""

    @pytest.fixture
    def reviewer(self):
        """Create a test instance of AdvancedCodeReviewer."""
        return AdvancedCodeReviewer()

    @pytest.fixture
    def sample_code(self):
        """Sample code for testing."""
        return """
def calculate_fibonacci(n):
    if n <= 1:
        return n
    return calculate_fibonacci(n-1) + calculate_fibonacci(n-2)

def main():
    result = calculate_fibonacci(10)
    print(f"Fibonacci of 10 is: {result}")

if __name__ == "__main__":
    main()
"""

    @pytest.mark.asyncio
    async def test_initialization(self, reviewer):
        """Test that the reviewer initializes correctly."""
        assert reviewer is not None
        assert reviewer.config is not None
        assert reviewer.yi_coder_integration is not None
        assert reviewer.security_analyzer is not None
        assert reviewer.performance_analyzer is not None
        assert reviewer.quality_metrics is not None
        assert reviewer.pr_integration is not None
        assert reviewer.review_dashboard is not None

    @pytest.mark.asyncio
    async def test_review_code(self, reviewer, sample_code):
        """Test comprehensive code review functionality."""
        request = CodeReviewRequest(
            code=sample_code, language="python", file_path="test_file.py"
        )

        response = await reviewer.review_code(request)

        assert isinstance(response, CodeReviewResponse)
        assert 0.0 <= response.quality_score <= 1.0
        assert 0.0 <= response.security_score <= 1.0
        assert 0.0 <= response.performance_score <= 1.0
        assert 0.0 <= response.overall_score <= 1.0
        assert isinstance(response.issues, list)
        assert isinstance(response.recommendations, list)
        assert isinstance(response.review_summary, str)
        assert response.execution_time >= 0.0

    @pytest.mark.asyncio
    async def test_review_code_with_error_handling(self, reviewer):
        """Test code review with error handling."""
        # Test with invalid code
        request = CodeReviewRequest(
            code="invalid python code {", language="python", file_path="invalid_file.py"
        )

        response = await reviewer.review_code(request)

        assert isinstance(response, CodeReviewResponse)
        assert response.quality_score >= 0.0
        assert response.security_score >= 0.0
        assert response.performance_score >= 0.0

    def test_health_status(self, reviewer):
        """Test health status reporting."""
        health = reviewer.get_health_status()

        assert health["status"] == "healthy"
        assert health["version"] == "1.0.0"
        assert "components" in health
        assert "features_enabled" in health


class TestYiCoderIntegration:
    """Test cases for the YiCoderIntegration class."""

    @pytest.fixture
    def yi_coder(self):
        """Create a test instance of YiCoderIntegration."""
        return YiCoderIntegration()

    @pytest.mark.asyncio
    async def test_initialization(self, yi_coder):
        """Test that the yi-coder integration initializes correctly."""
        assert yi_coder is not None
        assert yi_coder.config is not None
        assert yi_coder.model_loaded is False
        assert yi_coder.model_health == "initializing"

    @pytest.mark.asyncio
    async def test_initialize_model(self, yi_coder):
        """Test model initialization."""
        result = await yi_coder.initialize_model()

        assert result is True
        assert yi_coder.model_loaded is True
        assert yi_coder.model_health == "healthy"

    @pytest.mark.asyncio
    async def test_generate_review_insights(self, yi_coder):
        """Test review insights generation."""
        code = "def test_function(): pass"
        quality_results = {"overall_score": 0.8}
        security_results = {"security_score": 0.9}
        performance_results = {"performance_score": 0.7}

        insights = await yi_coder.generate_review_insights(
            code, "python", quality_results, security_results, performance_results
        )

        assert isinstance(insights, dict)
        assert "insights" in insights
        assert "recommendations" in insights
        assert "confidence" in insights
        assert "model_used" in insights

    @pytest.mark.asyncio
    async def test_generate_review_summary(self, yi_coder):
        """Test review summary generation."""
        code = "def test_function(): pass"
        summary = await yi_coder.generate_review_summary(
            code, "python", 0.8, 0.9, 0.7, 0.8
        )

        assert isinstance(summary, str)
        assert len(summary) > 0

    @pytest.mark.asyncio
    async def test_analyze_code_quality(self, yi_coder):
        """Test code quality analysis."""
        code = "def test_function(): pass"
        analysis = await yi_coder.analyze_code_quality(code, "python")

        assert isinstance(analysis, dict)
        assert "quality_score" in analysis
        assert "maintainability" in analysis
        assert "complexity" in analysis
        assert "readability" in analysis
        assert "issues" in analysis
        assert "recommendations" in analysis
        assert "confidence" in analysis

    def test_health_status(self, yi_coder):
        """Test health status reporting."""
        health = yi_coder.get_health_status()

        assert "status" in health
        assert "model_loaded" in health
        assert "model_name" in health


class TestSecurityAnalyzer:
    """Test cases for the SecurityAnalyzer class."""

    @pytest.fixture
    def security_analyzer(self):
        """Create a test instance of SecurityAnalyzer."""
        return SecurityAnalyzer()

    @pytest.fixture
    def secure_code(self):
        """Sample secure code for testing."""
        return """
def safe_function():
    # This is safe code
    result = "safe"
    return result
"""

    @pytest.fixture
    def vulnerable_code(self):
        """Sample vulnerable code for testing."""
        return """
def vulnerable_function():
    password=os.getenv("PASSWORD")
    api_key=os.getenv("API_KEY")
    query = "SELECT * FROM users WHERE id = " + user_input
    return query
"""

    @pytest.mark.asyncio
    async def test_initialization(self, security_analyzer):
        """Test that the security analyzer initializes correctly."""
        assert security_analyzer is not None
        assert security_analyzer.config is not None
        assert security_analyzer.vulnerability_patterns is not None
        assert security_analyzer.compliance_rules is not None

    @pytest.mark.asyncio
    async def test_analyze_security_secure_code(self, security_analyzer, secure_code):
        """Test security analysis of secure code."""
        result = await security_analyzer.analyze_security(
            secure_code, "python", "secure_file.py"
        )

        assert isinstance(result, dict)
        assert "security_score" in result
        assert "vulnerabilities" in result
        assert "risk_level" in result
        assert "compliance_issues" in result
        assert "recommendations" in result
        assert "confidence" in result
        assert result["security_score"] >= 0.0
        assert result["security_score"] <= 1.0

    @pytest.mark.asyncio
    async def test_analyze_security_vulnerable_code(
        self, security_analyzer, vulnerable_code
    ):
        """Test security analysis of vulnerable code."""
        result = await security_analyzer.analyze_security(
            vulnerable_code, "python", "vulnerable_file.py"
        )

        assert isinstance(result, dict)
        assert "security_score" in result
        assert "vulnerabilities" in result
        assert "risk_level" in result
        assert "compliance_issues" in result
        assert "recommendations" in result
        assert "confidence" in result
        assert result["security_score"] >= 0.0
        assert result["security_score"] <= 1.0

    def test_health_status(self, security_analyzer):
        """Test health status reporting."""
        health = security_analyzer.get_health_status()

        assert health["status"] == "healthy"
        assert "vulnerability_patterns_loaded" in health
        assert "compliance_rules_loaded" in health
        assert "supported_languages" in health


class TestPerformanceAnalyzer:
    """Test cases for the PerformanceAnalyzer class."""

    @pytest.fixture
    def performance_analyzer(self):
        """Create a test instance of PerformanceAnalyzer."""
        return PerformanceAnalyzer()

    @pytest.fixture
    def efficient_code(self):
        """Sample efficient code for testing."""
        return """
def efficient_function():
    result = [i * 2 for i in range(10)]
    return result
"""

    @pytest.fixture
    def inefficient_code(self):
        """Sample inefficient code for testing."""
        return """
def inefficient_function():
    result = []
    for i in range(1000):
        for j in range(1000):
            for k in range(1000):
                result.append(i + j + k)
    return result
"""

    @pytest.mark.asyncio
    async def test_initialization(self, performance_analyzer):
        """Test that the performance analyzer initializes correctly."""
        assert performance_analyzer is not None
        assert performance_analyzer.config is not None
        assert performance_analyzer.performance_patterns is not None
        assert performance_analyzer.optimization_rules is not None

    @pytest.mark.asyncio
    async def test_analyze_performance_efficient_code(
        self, performance_analyzer, efficient_code
    ):
        """Test performance analysis of efficient code."""
        result = await performance_analyzer.analyze_performance(
            efficient_code, "python", "efficient_file.py"
        )

        assert isinstance(result, dict)
        assert "performance_score" in result
        assert "bottlenecks" in result
        assert "optimization_opportunities" in result
        assert "resource_usage" in result
        assert "recommendations" in result
        assert "confidence" in result
        assert result["performance_score"] >= 0.0
        assert result["performance_score"] <= 1.0

    @pytest.mark.asyncio
    async def test_analyze_performance_inefficient_code(
        self, performance_analyzer, inefficient_code
    ):
        """Test performance analysis of inefficient code."""
        result = await performance_analyzer.analyze_performance(
            inefficient_code, "python", "inefficient_file.py"
        )

        assert isinstance(result, dict)
        assert "performance_score" in result
        assert "bottlenecks" in result
        assert "optimization_opportunities" in result
        assert "resource_usage" in result
        assert "recommendations" in result
        assert "confidence" in result
        assert result["performance_score"] >= 0.0
        assert result["performance_score"] <= 1.0

    def test_health_status(self, performance_analyzer):
        """Test health status reporting."""
        health = performance_analyzer.get_health_status()

        assert health["status"] == "healthy"
        assert "performance_patterns_loaded" in health
        assert "optimization_rules_loaded" in health
        assert "supported_languages" in health


class TestQualityMetrics:
    """Test cases for the QualityMetrics class."""

    @pytest.fixture
    def quality_metrics(self):
        """Create a test instance of QualityMetrics."""
        return QualityMetrics()

    @pytest.fixture
    def good_quality_code(self):
        """Sample good quality code for testing."""
        return '''
"""
This is a well-documented function.
"""
def good_function(param1: str, param2: int) -> str:
    """
    A well-documented function with type hints.

    Args:
        param1: A string parameter
        param2: An integer parameter

    Returns:
        A formatted string
    """
    result = f"{param1}: {param2}"
    return result
'''

    @pytest.fixture
    def poor_quality_code(self):
        """Sample poor quality code for testing."""
        return """
def bad_function(x,y,z):
    a=0
    for i in range(1000):
        for j in range(1000):
            for k in range(1000):
                a=a+i+j+k
    return a
"""

    @pytest.mark.asyncio
    async def test_initialization(self, quality_metrics):
        """Test that the quality metrics analyzer initializes correctly."""
        assert quality_metrics is not None
        assert quality_metrics.config is not None
        assert quality_metrics.quality_patterns is not None
        assert quality_metrics.style_rules is not None

    @pytest.mark.asyncio
    async def test_analyze_code_quality_good_code(
        self, quality_metrics, good_quality_code
    ):
        """Test quality analysis of good quality code."""
        result = await quality_metrics.analyze_code_quality(
            good_quality_code, "python", "good_file.py"
        )

        assert isinstance(result, dict)
        assert "overall_score" in result
        assert "maintainability_score" in result
        assert "complexity_score" in result
        assert "readability_score" in result
        assert "issues" in result
        assert "recommendations" in result
        assert "confidence" in result
        assert result["overall_score"] >= 0.0
        assert result["overall_score"] <= 1.0

    @pytest.mark.asyncio
    async def test_analyze_code_quality_poor_code(
        self, quality_metrics, poor_quality_code
    ):
        """Test quality analysis of poor quality code."""
        result = await quality_metrics.analyze_code_quality(
            poor_quality_code, "python", "poor_file.py"
        )

        assert isinstance(result, dict)
        assert "overall_score" in result
        assert "maintainability_score" in result
        assert "complexity_score" in result
        assert "readability_score" in result
        assert "issues" in result
        assert "recommendations" in result
        assert "confidence" in result
        assert result["overall_score"] >= 0.0
        assert result["overall_score"] <= 1.0

    def test_health_status(self, quality_metrics):
        """Test health status reporting."""
        health = quality_metrics.get_health_status()

        assert health["status"] == "healthy"
        assert "quality_patterns_loaded" in health
        assert "style_rules_loaded" in health
        assert "supported_languages" in health


class TestPRIntegration:
    """Test cases for the PRIntegration class."""

    @pytest.fixture
    def pr_integration(self):
        """Create a test instance of PRIntegration."""
        return PRIntegration()

    @pytest.fixture
    def sample_pr_data(self):
        """Sample pull request data for testing."""
        return {
            "id": "123",
            "number": 123,
            "title": "Add new feature",
            "description": "This PR adds a new feature",
            "files": [
                {
                    "path": "src/test.py",
                    "content": "def test_function(): pass",
                    "changes": [
                        {"line_number": 1, "lines_added": 5, "lines_deleted": 0}
                    ],
                }
            ],
        }

    @pytest.mark.asyncio
    async def test_initialization(self, pr_integration):
        """Test that the PR integration initializes correctly."""
        assert pr_integration is not None
        assert pr_integration.config is not None
        assert pr_integration.supported_platforms is not None
        assert len(pr_integration.supported_platforms) > 0

    @pytest.mark.asyncio
    async def test_review_pull_request(self, pr_integration, sample_pr_data):
        """Test pull request review functionality."""
        result = await pr_integration.review_pull_request(sample_pr_data)

        assert isinstance(result, dict)
        assert "pr_id" in result
        assert "overall_score" in result
        assert "quality_score" in result
        assert "security_score" in result
        assert "performance_score" in result
        assert "comments" in result
        assert "approval_recommendation" in result
        assert "summary" in result
        assert "confidence" in result
        assert result["overall_score"] >= 0.0
        assert result["overall_score"] <= 1.0

    def test_health_status(self, pr_integration):
        """Test health status reporting."""
        health = pr_integration.get_health_status()

        assert health["status"] == "healthy"
        assert "supported_platforms" in health
        assert "features" in health


class TestReviewDashboard:
    """Test cases for the ReviewDashboard class."""

    @pytest.fixture
    def review_dashboard(self):
        """Create a test instance of ReviewDashboard."""
        return ReviewDashboard()

    @pytest.fixture
    def sample_review_data(self):
        """Sample review data for testing."""
        return {
            "quality_score": 0.8,
            "security_score": 0.9,
            "performance_score": 0.7,
            "overall_score": 0.8,
            "issues": [],
            "recommendations": ["Add documentation"],
            "total_lines": 100,
        }

    @pytest.mark.asyncio
    async def test_initialization(self, review_dashboard):
        """Test that the review dashboard initializes correctly."""
        assert review_dashboard is not None
        assert review_dashboard.config is not None
        assert review_dashboard.review_history is not None
        assert review_dashboard.project_data is not None

    @pytest.mark.asyncio
    async def test_add_review_result(self, review_dashboard, sample_review_data):
        """Test adding review results to the dashboard."""
        project_id = "test_project"
        result = await review_dashboard.add_review_result(
            project_id, sample_review_data
        )

        assert result is True
        assert len(review_dashboard.review_history) > 0
        assert project_id in review_dashboard.project_data

    @pytest.mark.asyncio
    async def test_get_dashboard_data(self, review_dashboard, sample_review_data):
        """Test getting dashboard data."""
        project_id = "test_project"
        await review_dashboard.add_review_result(project_id, sample_review_data)

        dashboard_data = await review_dashboard.get_dashboard_data(project_id)

        assert isinstance(dashboard_data, dict)
        assert "project_metrics" in dashboard_data
        assert "review_trends" in dashboard_data
        assert "quality_metrics" in dashboard_data
        assert "security_metrics" in dashboard_data
        assert "performance_metrics" in dashboard_data
        assert "recent_reviews" in dashboard_data
        assert "top_issues" in dashboard_data
        assert "recommendations" in dashboard_data
        assert "generated_at" in dashboard_data

    @pytest.mark.asyncio
    async def test_get_project_summary(self, review_dashboard, sample_review_data):
        """Test getting project summary."""
        project_id = "test_project"
        await review_dashboard.add_review_result(project_id, sample_review_data)

        summary = await review_dashboard.get_project_summary(project_id)

        assert isinstance(summary, dict)
        assert "project_id" in summary
        assert "total_reviews" in summary
        assert "average_quality_score" in summary
        assert "average_security_score" in summary
        assert "average_performance_score" in summary

    @pytest.mark.asyncio
    async def test_get_trend_analysis(self, review_dashboard, sample_review_data):
        """Test getting trend analysis."""
        project_id = "test_project"
        await review_dashboard.add_review_result(project_id, sample_review_data)

        trend_analysis = await review_dashboard.get_trend_analysis(project_id, 30)

        assert isinstance(trend_analysis, dict)
        assert "project_id" in trend_analysis
        assert "period_days" in trend_analysis
        assert "total_reviews" in trend_analysis

    def test_health_status(self, review_dashboard):
        """Test health status reporting."""
        health = review_dashboard.get_health_status()

        assert health["status"] == "healthy"
        assert "total_reviews" in health
        assert "total_projects" in health
        assert "features" in health


# Integration tests
class TestIntegration:
    """Integration tests for the complete code review system."""

    @pytest.fixture
    def complete_system(self):
        """Create a complete test system."""
        return AdvancedCodeReviewer()

    @pytest.mark.asyncio
    async def test_end_to_end_review(self, complete_system):
        """Test end-to-end code review process."""
        code = """
def fibonacci(n):
    if n <= 1:
        return n
    return fibonacci(n-1) + fibonacci(n-2)

def main():
    result = fibonacci(10)
    print(f"Result: {result}")

if __name__ == "__main__":
    main()
"""

        request = CodeReviewRequest(
            code=code, language="python", file_path="fibonacci.py"
        )

        response = await complete_system.review_code(request)

        # Verify response structure
        assert isinstance(response, CodeReviewResponse)
        assert 0.0 <= response.quality_score <= 1.0
        assert 0.0 <= response.security_score <= 1.0
        assert 0.0 <= response.performance_score <= 1.0
        assert 0.0 <= response.overall_score <= 1.0
        assert isinstance(response.issues, list)
        assert isinstance(response.recommendations, list)
        assert isinstance(response.review_summary, str)
        assert response.execution_time >= 0.0

    @pytest.mark.asyncio
    async def test_pr_review_integration(self, complete_system):
        """Test pull request review integration."""
        pr_data = {
            "id": "test_pr_123",
            "number": 123,
            "title": "Add new feature",
            "description": "This PR adds a new feature",
            "files": [
                {
                    "path": "src/feature.py",
                    "content": "def new_feature(): return 'feature'",
                    "changes": [
                        {"line_number": 1, "lines_added": 1, "lines_deleted": 0}
                    ],
                }
            ],
        }

        result = await complete_system.review_pull_request(pr_data)

        assert isinstance(result, dict)
        assert "pr_id" in result
        assert "overall_score" in result
        assert "comments" in result
        assert "approval_recommendation" in result
        assert "summary" in result

    @pytest.mark.asyncio
    async def test_dashboard_integration(self, complete_system):
        """Test dashboard integration."""
        project_id = "test_project"

        # Add some review data
        review_data = {
            "quality_score": 0.8,
            "security_score": 0.9,
            "performance_score": 0.7,
            "overall_score": 0.8,
            "issues": [],
            "recommendations": ["Add documentation"],
            "total_lines": 100,
        }

        await complete_system.review_dashboard.add_review_result(
            project_id, review_data
        )

        # Get dashboard data
        dashboard_data = await complete_system.get_review_dashboard(project_id)

        assert isinstance(dashboard_data, dict)
        assert "project_metrics" in dashboard_data
        assert "review_trends" in dashboard_data
        assert "quality_metrics" in dashboard_data
        assert "security_metrics" in dashboard_data
        assert "performance_metrics" in dashboard_data


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
