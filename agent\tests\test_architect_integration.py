"""
Integration tests for Architect<PERSON>gent
Tests the complete workflow from command processing to task execution
"""

import asyncio
from unittest.mock import AsyncMock, patch

import pytest

from agent.core.agents.architect_agent import ArchitectAgent, TaskPriority, TaskStatus


class TestArchitectAgentIntegration:
    """Integration tests for ArchitectAgent"""

    @pytest.fixture
    async def architect_agent(self):
        """Create a test architect agent"""
        agent = ArchitectAgent("config/architect_agent_config.json")
        return agent

    @pytest.mark.asyncio
    async def test_complete_workflow_portfolio_website(self, architect_agent):
        """Test complete workflow for creating a portfolio website"""
        # Test command processing
        result = await architect_agent.process_user_command(
            "Create a simple portfolio website", priority=TaskPriority.MEDIUM
        )

        assert result["success"] is True
        assert "task_id" in result
        assert result["status"] in ["pending", "in_progress", "completed", "failed"]
        assert result["sub_tasks_count"] > 0

        task_id = result["task_id"]

        # Test task status retrieval
        status_result = await architect_agent.get_task_status(task_id)
        assert status_result["success"] is True
        assert status_result["task_id"] == task_id
        assert "sub_tasks" in status_result

        # Test all tasks retrieval
        all_tasks = await architect_agent.get_all_tasks()
        assert all_tasks["success"] is True
        assert "active_tasks" in all_tasks
        assert "completed_tasks" in all_tasks

    @pytest.mark.asyncio
    async def test_complete_workflow_ecommerce_site(self, architect_agent):
        """Test complete workflow for creating an ecommerce site"""
        # Test command processing
        result = await architect_agent.process_user_command(
            "Build an ecommerce website with payment processing",
            priority=TaskPriority.HIGH,
        )

        assert result["success"] is True
        assert "task_id" in result
        assert result["priority"] == "high"

        task_id = result["task_id"]

        # Test task status retrieval
        status_result = await architect_agent.get_task_status(task_id)
        assert status_result["success"] is True
        assert len(status_result["sub_tasks"]) > 0

    @pytest.mark.asyncio
    async def test_task_cancellation(self, architect_agent):
        """Test task cancellation workflow"""
        # Create a task
        result = await architect_agent.process_user_command(
            "Create a blog website", priority=TaskPriority.LOW
        )

        assert result["success"] is True
        task_id = result["task_id"]

        # Cancel the task
        cancel_result = await architect_agent.cancel_task(task_id)
        assert cancel_result["success"] is True
        assert cancel_result["task_id"] == task_id

    @pytest.mark.asyncio
    async def test_multiple_concurrent_tasks(self, architect_agent):
        """Test handling multiple concurrent tasks"""
        # Create multiple tasks
        tasks = []
        for i in range(3):
            result = await architect_agent.process_user_command(
                f"Create website {i+1}", priority=TaskPriority.MEDIUM
            )
            assert result["success"] is True
            tasks.append(result["task_id"])

        # Check all tasks are created
        all_tasks = await architect_agent.get_all_tasks()
        assert all_tasks["success"] is True
        assert len(all_tasks["active_tasks"]) >= 3

    @pytest.mark.asyncio
    async def test_error_handling_invalid_command(self, architect_agent):
        """Test error handling for invalid commands"""
        # Test with empty command
        result = await architect_agent.process_user_command("")
        assert result["success"] is False
        assert "error" in result

    @pytest.mark.asyncio
    async def test_error_handling_nonexistent_task(self, architect_agent):
        """Test error handling for nonexistent task"""
        # Try to get status of nonexistent task
        result = await architect_agent.get_task_status("nonexistent_task")
        assert result["success"] is False
        assert "error" in result

    @pytest.mark.asyncio
    async def test_agent_initialization(self, architect_agent):
        """Test that all specialized agents are properly initialized"""
        # Await the fixture
        agent = await architect_agent
        # Check that all agents are initialized
        assert agent.frontend_agent is not None
        assert agent.backend_agent is not None
        assert agent.container_agent is not None
        assert agent.learning_agent is not None
        assert agent.shell_ops_agent is not None
        assert agent.security_agent is not None
        assert agent.monitoring_agent is not None

    @pytest.mark.asyncio
    async def test_configuration_loading(self, architect_agent):
        """Test that configuration is properly loaded"""
        # Check that configuration is loaded
        assert architect_agent.config is not None
        assert "task_management" in architect_agent.config
        assert "agent_coordination" in architect_agent.config

    @pytest.mark.asyncio
    async def test_task_priority_handling(self, architect_agent):
        """Test different task priority levels"""
        priorities = [
            TaskPriority.LOW,
            TaskPriority.MEDIUM,
            TaskPriority.HIGH,
            TaskPriority.CRITICAL,
        ]

        for priority in priorities:
            result = await architect_agent.process_user_command(
                f"Test {priority.value} priority task", priority=priority
            )
            assert result["success"] is True
            assert result["priority"] == priority.value

    @pytest.mark.asyncio
    async def test_sub_task_dependency_creation(self, architect_agent):
        """Test that sub-tasks are created with proper dependencies"""
        result = await architect_agent.process_user_command(
            "Create a full-stack application with frontend, backend, and containers"
        )

        assert result["success"] is True
        task_id = result["task_id"]

        # Get task status to check sub-tasks
        status_result = await architect_agent.get_task_status(task_id)
        assert status_result["success"] is True

        sub_tasks = status_result["sub_tasks"]
        assert len(sub_tasks) > 0

        # Check that some tasks have dependencies
        has_dependencies = any(
            sub_task.get("dependencies", []) for sub_task in sub_tasks
        )
        assert has_dependencies, "Some sub-tasks should have dependencies"

    @pytest.mark.asyncio
    async def test_agent_shutdown(self, architect_agent):
        """Test proper agent shutdown"""
        # Create a task first
        result = await architect_agent.process_user_command("Test shutdown")
        assert result["success"] is True

        # Shutdown the agent
        await architect_agent.shutdown()

        # Verify shutdown completed without errors
        # (We can't test much more since the agent is shut down)

    @pytest.mark.asyncio
    async def test_task_progress_tracking(self, architect_agent):
        """Test that task progress is properly tracked"""
        result = await architect_agent.process_user_command("Create a simple website")

        assert result["success"] is True
        task_id = result["task_id"]

        # Get initial status
        initial_status = await architect_agent.get_task_status(task_id)
        assert initial_status["success"] is True
        assert "progress_percentage" in initial_status

        # Progress should be tracked (even if 0 initially)
        assert isinstance(initial_status["progress_percentage"], (int, float))

    @pytest.mark.asyncio
    async def test_task_result_summary(self, architect_agent):
        """Test that task results are properly summarized"""
        result = await architect_agent.process_user_command("Create a test website")

        assert result["success"] is True
        task_id = result["task_id"]

        # Wait a bit for task to process
        await asyncio.sleep(1)

        # Get task status
        status_result = await architect_agent.get_task_status(task_id)
        assert status_result["success"] is True

        # Check that result summary exists (even if empty)
        assert "result_summary" in status_result

    @pytest.mark.asyncio
    async def test_complex_command_parsing(self, architect_agent):
        """Test parsing of complex commands"""
        complex_commands = [
            "Build a React ecommerce site with Node.js backend and PostgreSQL database",
            "Create a portfolio website with blog functionality and contact forms",
            "Develop a social media platform with real-time chat and file sharing",
            "Build a project management tool with task tracking and team collaboration",
        ]

        for command in complex_commands:
            result = await architect_agent.process_user_command(command)
            assert result["success"] is True
            assert "task_id" in result
            assert result["sub_tasks_count"] > 0

    @pytest.mark.asyncio
    async def test_task_status_enumeration(self, architect_agent):
        """Test that all task status values are properly handled"""
        # Create a task
        result = await architect_agent.process_user_command("Test status handling")
        assert result["success"] is True
        task_id = result["task_id"]

        # Get status
        status_result = await architect_agent.get_task_status(task_id)
        assert status_result["success"] is True

        # Status should be a valid enum value
        valid_statuses = [status.value for status in TaskStatus]
        assert status_result["status"] in valid_statuses

    @pytest.mark.asyncio
    async def test_sub_task_status_tracking(self, architect_agent):
        """Test that sub-task status is properly tracked"""
        result = await architect_agent.process_user_command("Test sub-task tracking")
        assert result["success"] is True
        task_id = result["task_id"]

        # Get status
        status_result = await architect_agent.get_task_status(task_id)
        assert status_result["success"] is True

        # Check sub-task status
        for sub_task in status_result["sub_tasks"]:
            assert "status" in sub_task
            assert "agent_type" in sub_task
            assert "estimated_duration" in sub_task

    @pytest.mark.asyncio
    async def test_task_creation_with_different_priorities(self, architect_agent):
        """Test task creation with different priority levels"""
        priorities = [
            (TaskPriority.LOW, "low priority task"),
            (TaskPriority.MEDIUM, "medium priority task"),
            (TaskPriority.HIGH, "high priority task"),
            (TaskPriority.CRITICAL, "critical priority task"),
        ]

        for priority, description in priorities:
            result = await architect_agent.process_user_command(
                description, priority=priority
            )
            assert result["success"] is True
            assert result["priority"] == priority.value

    @pytest.mark.asyncio
    async def test_task_metadata_tracking(self, architect_agent):
        """Test that task metadata is properly tracked"""
        result = await architect_agent.process_user_command("Test metadata tracking")
        assert result["success"] is True
        task_id = result["task_id"]

        # Get status
        status_result = await architect_agent.get_task_status(task_id)
        assert status_result["success"] is True

        # Check metadata fields
        assert "created_at" in status_result
        assert "started_at" in status_result
        assert "completed_at" in status_result

        # Created_at should always be present
        assert status_result["created_at"] is not None

    @pytest.mark.asyncio
    async def test_error_recovery(self, architect_agent):
        """Test that the system can recover from errors"""
        # Create a task
        result = await architect_agent.process_user_command("Test error recovery")
        assert result["success"] is True
        task_id = result["task_id"]

        # Try to get status (should work even if task failed)
        status_result = await architect_agent.get_task_status(task_id)
        assert status_result["success"] is True

        # Create another task (system should still work)
        result2 = await architect_agent.process_user_command("Test second task")
        assert result2["success"] is True

    @pytest.mark.asyncio
    async def test_task_id_uniqueness(self, architect_agent):
        """Test that task IDs are unique"""
        task_ids = set()

        for i in range(5):
            result = await architect_agent.process_user_command(f"Task {i}")
            assert result["success"] is True
            task_id = result["task_id"]
            assert task_id not in task_ids
            task_ids.add(task_id)

    @pytest.mark.asyncio
    async def test_concurrent_task_processing(self, architect_agent):
        """Test processing multiple tasks concurrently"""
        # Create multiple tasks concurrently
        tasks = []
        for i in range(3):
            task = asyncio.create_task(
                architect_agent.process_user_command(f"Concurrent task {i}")
            )
            tasks.append(task)

        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks)

        # Check all results
        for result in results:
            assert result["success"] is True
            assert "task_id" in result

    @pytest.mark.asyncio
    async def test_task_capacity_limits(self, architect_agent):
        """Test that task capacity limits are respected"""
        # Create tasks up to capacity limit
        max_tasks = architect_agent.config.get("task_management", {}).get(
            "max_concurrent_tasks", 5
        )

        tasks = []
        for i in range(max_tasks + 2):  # Create more than capacity
            result = await architect_agent.process_user_command(
                f"Capacity test task {i}"
            )
            assert result["success"] is True
            tasks.append(result["task_id"])

        # All tasks should be created (they might be queued)
        assert len(tasks) == max_tasks + 2

    @pytest.mark.asyncio
    async def test_task_timeout_handling(self, architect_agent):
        """Test that task timeouts are handled properly"""
        # Create a task that might timeout
        result = await architect_agent.process_user_command("Test timeout handling")
        assert result["success"] is True
        task_id = result["task_id"]

        # Get status after a short delay
        await asyncio.sleep(0.1)
        status_result = await architect_agent.get_task_status(task_id)
        assert status_result["success"] is True

        # Task should still be accessible even if it timed out
        assert status_result["task_id"] == task_id

    @pytest.mark.asyncio
    async def test_comprehensive_workflow(self, architect_agent):
        """Test a comprehensive workflow with multiple operations"""
        # 1. Create a task
        result = await architect_agent.process_user_command(
            "Comprehensive workflow test"
        )
        assert result["success"] is True
        task_id = result["task_id"]

        # 2. Check initial status
        status1 = await architect_agent.get_task_status(task_id)
        assert status1["success"] is True

        # 3. Create another task
        result2 = await architect_agent.process_user_command(
            "Second comprehensive task"
        )
        assert result2["success"] is True
        task_id2 = result2["task_id"]

        # 4. Check all tasks
        all_tasks = await architect_agent.get_all_tasks()
        assert all_tasks["success"] is True
        assert len(all_tasks["active_tasks"]) >= 2

        # 5. Cancel one task
        cancel_result = await architect_agent.cancel_task(task_id)
        assert cancel_result["success"] is True

        # 6. Check final status
        final_status = await architect_agent.get_task_status(task_id2)
        assert final_status["success"] is True
