#!/usr/bin/env python3
"""
Database Migration API Routes
REST API endpoints for database migration management with rollback capabilities
"""

import asyncio
import json
import logging

# Add project root to path
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List, Optional

from fastapi import APIRouter, BackgroundTasks, Depends, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel, Field

from agent.cli.migration_commands import MigrationCommands
from agent.core.validators.safety_validator import SafetyValidator

project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


logger = logging.getLogger(__name__)

# Create router
router = APIRouter(prefix="/api/migration", tags=["Database Migration"])

# Initialize components
migration_commands = MigrationCommands()
safety_validator = SafetyValidator()


# Pydantic Models
class MigrationStatusResponse(BaseModel):
    success: bool
    status: str
    database_connected: bool
    migration_history: List[Dict[str, Any]]
    pending_migrations: List[Dict[str, Any]]
    backup_status: Dict[str, Any]
    timestamp: str


class MigrationSummaryResponse(BaseModel):
    success: bool
    statistics: Dict[str, Any]
    recent_migrations: List[Dict[str, Any]]
    backup_statistics: Dict[str, Any]
    timestamp: str


class MigrationListResponse(BaseModel):
    success: bool
    all_migrations: List[Dict[str, Any]]
    applied_migrations: List[Dict[str, Any]]
    pending_migrations: List[Dict[str, Any]]
    total_count: int
    applied_count: int
    pending_count: int
    timestamp: str


class MigrationRunRequest(BaseModel):
    migration_name: Optional[str] = Field(None, description="Specific migration to run")
    validate_only: bool = Field(False, description="Only validate, don't run")


class MigrationRunResponse(BaseModel):
    success: bool
    migration_name: str
    backup_created: Optional[str] = None
    result: Dict[str, Any]
    timestamp: str


class MigrationRollbackRequest(BaseModel):
    migration_name: Optional[str] = Field(
        None, description="Specific migration to rollback"
    )


class MigrationRollbackResponse(BaseModel):
    success: bool
    migration_name: str
    backup_created: Optional[str] = None
    result: Dict[str, Any]
    timestamp: str


class MigrationValidateRequest(BaseModel):
    migration_name: str = Field(..., description="Migration name to validate")


class MigrationValidateResponse(BaseModel):
    success: bool
    migration_name: str
    syntax_valid: bool
    dependencies: Dict[str, Any]
    conflicts: List[str]
    can_run: bool
    timestamp: str


class BackupCreateRequest(BaseModel):
    backup_name: Optional[str] = Field(None, description="Custom backup name")


class BackupCreateResponse(BaseModel):
    success: bool
    backup_path: Optional[str] = None
    backup_size: Optional[int] = None
    timestamp: str


class BackupRestoreRequest(BaseModel):
    backup_name: str = Field(..., description="Backup name to restore from")


class BackupRestoreResponse(BaseModel):
    success: bool
    backup_name: str
    pre_restore_backup: Optional[str] = None
    result: Dict[str, Any]
    timestamp: str


class BackupListResponse(BaseModel):
    success: bool
    backups: List[Dict[str, Any]]
    total_count: int
    timestamp: str


class MigrationMetricsResponse(BaseModel):
    success: bool
    performance_data: Dict[str, Any]
    error_rates: Dict[str, Any]
    rollback_statistics: Dict[str, Any]
    timestamp: str


class ExportReportRequest(BaseModel):
    format: str = Field("json", description="Export format (json)")


class ExportReportResponse(BaseModel):
    success: bool
    report_path: Optional[str] = None
    format: str
    timestamp: str


# Container Migration Models
class ContainerMigrationRequest(BaseModel):
    site_name: str = Field(..., description="Site container name")
    migration_config: Optional[Dict[str, Any]] = Field(None, description="Migration configuration")


class ContainerMigrationResponse(BaseModel):
    success: bool
    site_name: str
    container_name: Optional[str] = None
    migration_config: Optional[Dict[str, Any]] = None
    init_output: Optional[str] = None
    apply_output: Optional[str] = None
    error: Optional[str] = None
    timestamp: str


class ContainerCommandRequest(BaseModel):
    site_name: str = Field(..., description="Site container name")
    command: List[str] = Field(..., description="Command to execute")
    working_dir: str = Field("/app", description="Working directory")


class ContainerCommandResponse(BaseModel):
    success: bool
    site_name: str
    container_name: Optional[str] = None
    command: List[str]
    return_code: Optional[int] = None
    stdout: Optional[str] = None
    stderr: Optional[str] = None
    working_dir: str
    error: Optional[str] = None
    timestamp: str


class ContainerMigrationInitRequest(BaseModel):
    site_name: str = Field(..., description="Site container name")
    database_url: Optional[str] = Field(None, description="Database URL")


class ContainerMigrationInitResponse(BaseModel):
    success: bool
    site_name: str
    database_url: str
    container_name: Optional[str] = None
    stdout: Optional[str] = None
    stderr: Optional[str] = None
    error: Optional[str] = None
    timestamp: str


# API Endpoints
@router.get("/status", response_model=MigrationStatusResponse)
async def get_migration_status():
    """Get migration status and health"""
    try:
        result = await migration_commands.migration_status()

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return MigrationStatusResponse(**result)
    except Exception as e:
        logger.error(f"Error getting migration status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/summary", response_model=MigrationSummaryResponse)
async def get_migration_summary():
    """Get migration summary and statistics"""
    try:
        result = await migration_commands.migration_summary()

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return MigrationSummaryResponse(**result)
    except Exception as e:
        logger.error(f"Error getting migration summary: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/list", response_model=MigrationListResponse)
async def list_migrations():
    """List all available migrations"""
    try:
        result = await migration_commands.list_migrations()

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return MigrationListResponse(**result)
    except Exception as e:
        logger.error(f"Error listing migrations: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/run", response_model=MigrationRunResponse)
async def run_migration(
    request: MigrationRunRequest, background_tasks: BackgroundTasks
):
    """Run a specific migration or all pending migrations"""
    try:
        # Validate migration name if provided
        if request.migration_name:
            if not safety_validator.validate_file_path(
                f"migrations/{request.migration_name}"
            ):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid migration name: {request.migration_name}",
                )

        # Run migration
        result = await migration_commands.run_migration(
            migration_name=request.migration_name
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return MigrationRunResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error running migration: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/rollback", response_model=MigrationRollbackResponse)
async def rollback_migration(request: MigrationRollbackRequest):
    """Rollback a specific migration or the last migration"""
    try:
        # Validate migration name if provided
        if request.migration_name:
            if not safety_validator.validate_file_path(
                f"migrations/{request.migration_name}"
            ):
                raise HTTPException(
                    status_code=400,
                    detail=f"Invalid migration name: {request.migration_name}",
                )

        # Run rollback
        result = await migration_commands.rollback_migration(
            migration_name=request.migration_name
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return MigrationRollbackResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error rolling back migration: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/validate", response_model=MigrationValidateResponse)
async def validate_migration(request: MigrationValidateRequest):
    """Validate a migration before running it"""
    try:
        # Validate migration name
        if not safety_validator.validate_file_path(
            f"migrations/{request.migration_name}"
        ):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid migration name: {request.migration_name}",
            )

        result = await migration_commands.validate_migration(
            migration_name=request.migration_name
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return MigrationValidateResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error validating migration: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/backup/create", response_model=BackupCreateResponse)
async def create_backup(request: BackupCreateRequest):
    """Create a database backup"""
    try:
        result = await migration_commands.create_backup(backup_name=request.backup_name)

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return BackupCreateResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating backup: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/backup/restore", response_model=BackupRestoreResponse)
async def restore_backup(request: BackupRestoreRequest):
    """Restore from a database backup"""
    try:
        # Validate backup name
        if not safety_validator.validate_file_path(f"backups/{request.backup_name}"):
            raise HTTPException(
                status_code=400, detail=f"Invalid backup name: {request.backup_name}"
            )

        result = await migration_commands.restore_backup(
            backup_name=request.backup_name
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return BackupRestoreResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error restoring backup: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/backup/list", response_model=BackupListResponse)
async def list_backups():
    """List all available backups"""
    try:
        result = await migration_commands.list_backups()

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return BackupListResponse(**result)
    except Exception as e:
        logger.error(f"Error listing backups: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/metrics", response_model=MigrationMetricsResponse)
async def get_migration_metrics():
    """Get migration performance metrics"""
    try:
        result = await migration_commands.migration_metrics()

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return MigrationMetricsResponse(**result)
    except Exception as e:
        logger.error(f"Error getting migration metrics: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/export", response_model=ExportReportResponse)
async def export_migration_report(request: ExportReportRequest):
    """Export migration report in specified format"""
    try:
        result = await migration_commands.export_migration_report(format=request.format)

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return ExportReportResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error exporting migration report: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/health")
async def health_check():
    """Health check endpoint for migration service"""
    try:
        # Check basic connectivity
        status_result = await migration_commands.migration_status()

        if status_result["success"] and status_result["database_connected"]:
            return {
                "status": "healthy",
                "service": "migration-runner",
                "database_connected": True,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        else:
            return JSONResponse(
                status_code=503,
                content={
                    "status": "unhealthy",
                    "service": "migration-runner",
                    "database_connected": False,
                    "error": status_result.get("error", "Unknown error"),
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                },
            )
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "service": "migration-runner",
                "error": str(e),
                "timestamp": datetime.now(timezone.utc).isoformat(),
            },
        )


# Container Migration Endpoints
@router.post("/container/apply", response_model=ContainerMigrationResponse)
async def apply_container_migrations(request: ContainerMigrationRequest):
    """Apply database migrations within a site container"""
    try:
        # Validate site name
        if not safety_validator.validate_file_path(request.site_name):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid site name: {request.site_name}",
            )

        result = await migration_commands.apply_container_migrations(
            site_name=request.site_name,
            migration_config=request.migration_config
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return ContainerMigrationResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error applying container migrations: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/container/execute", response_model=ContainerCommandResponse)
async def execute_container_command(request: ContainerCommandRequest):
    """Execute a command within a site container"""
    try:
        # Validate site name
        if not safety_validator.validate_file_path(request.site_name):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid site name: {request.site_name}",
            )

        result = await migration_commands.execute_container_command(
            site_name=request.site_name,
            command=request.command,
            working_dir=request.working_dir
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return ContainerCommandResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error executing container command: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/container/init", response_model=ContainerMigrationInitResponse)
async def init_container_migrations(request: ContainerMigrationInitRequest):
    """Initialize migration environment for a site container"""
    try:
        # Validate site name
        if not safety_validator.validate_file_path(request.site_name):
            raise HTTPException(
                status_code=400,
                detail=f"Invalid site name: {request.site_name}",
            )

        result = await migration_commands.init_container_migrations(
            site_name=request.site_name,
            database_url=request.database_url
        )

        if not result["success"]:
            raise HTTPException(status_code=500, detail=result["error"])

        return ContainerMigrationInitResponse(**result)
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error initializing container migrations: {e}")
        raise HTTPException(status_code=500, detail=str(e))
