"""
Unit tests for the enhanced WebsiteGenerator with Phase 2.1 features.
Tests theme customization, asset pipeline, and content metadata functionality.
"""

import json
import shutil
import sys
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, Mock, patch

import pytest

sys.path.append(str(Path(__file__).parent.parent))

from agent.core.website_generator import (
    AssetPipeline,
    ContentMetadata,
    ThemeManager,
    WebsiteGenerator,
)
from db.database_manager import DatabaseManager
from tools.templates.template_manager import TemplateManager


class TestThemeManager:
    """Test cases for ThemeManager class"""

    def setup_method(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.themes_dir = Path(self.temp_dir) / "themes"
        self.theme_manager = ThemeManager(str(self.themes_dir))

    def teardown_method(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_create_theme_success(self):
        """Test successful theme creation"""
        # Create a mock template in the correct location
        template_dir = Path("templates") / "modern"
        template_dir.mkdir(parents=True, exist_ok=True)
        (template_dir / "index.html").write_text("<html>{{title}}</html>")
        (template_dir / "style.css").write_text(":root { --primary-color: #007bff; }")

        customizations = {
            "css": {"primary-color": "#ff6b6b"},
            "html": {"title": "Custom Title"},
        }

        result = self.theme_manager.create_theme(
            "custom-blue", "modern", customizations
        )

        assert result["status"] == "success"
        assert "custom-blue" in self.theme_manager.theme_config
        assert (
            self.theme_manager.theme_config["custom-blue"]["base_template"] == "modern"
        )

    def test_create_theme_duplicate_name(self):
        """Test theme creation with duplicate name"""
        # Create first theme
        template_dir = Path("templates") / "modern"
        template_dir.mkdir(parents=True, exist_ok=True)
        (template_dir / "index.html").write_text("<html></html>")

        self.theme_manager.create_theme("test-theme", "modern", {})

        # Try to create duplicate
        result = self.theme_manager.create_theme("test-theme", "modern", {})

        assert result["status"] == "error"
        assert "already exists" in result["message"]

    def test_get_theme_not_found(self):
        """Test getting non-existent theme"""
        with pytest.raises(ValueError, match="Theme 'nonexistent' not found"):
            self.theme_manager.get_theme("nonexistent")

    def test_apply_css_customizations(self):
        """Test CSS variable replacement"""
        css_content = ":root { --primary-color: #007bff; --secondary-color: #6c757d; }"
        result = self.theme_manager._replace_css_variable(
            css_content, "primary-color", "#ff6b6b"
        )

        assert "--primary-color: #ff6b6b;" in result
        assert "--secondary-color: #6c757d;" in result

    def test_scan_assets(self):
        """Test asset scanning functionality"""
        assets_dir = self.theme_manager.themes_dir / "test-theme" / "assets"
        assets_dir.mkdir(parents=True)

        # Create test assets
        (assets_dir / "image.jpg").write_text("fake image")
        (assets_dir / "style.css").write_text("fake css")
        (assets_dir / "script.js").write_text("fake js")
        (assets_dir / "font.woff2").write_text("fake font")

        assets = self.theme_manager._scan_assets(assets_dir)

        assert "image.jpg" in assets["images"]
        assert "style.css" in assets["styles"]
        assert "script.js" in assets["scripts"]
        assert "font.woff2" in assets["fonts"]


class TestAssetPipeline:
    """Test cases for AssetPipeline class"""

    def setup_method(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.output_dir = Path(self.temp_dir) / "dist"
        self.asset_pipeline = AssetPipeline(str(self.output_dir))

    def teardown_method(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_optimize_assets_no_assets(self):
        """Test asset optimization when no assets exist"""
        source_dir = Path(self.temp_dir) / "source"
        source_dir.mkdir()

        result = self.asset_pipeline.optimize_assets(source_dir, "test-site")

        assert result["status"] == "success"
        assert "No assets to optimize" in result["message"]

    def test_optimize_images(self):
        """Test image optimization"""
        source_dir = Path(self.temp_dir) / "source" / "assets"
        source_dir.mkdir(parents=True)

        # Create test images
        (source_dir / "test.jpg").write_text("fake image")
        (source_dir / "test.png").write_text("fake image")

        results = self.asset_pipeline._optimize_images(source_dir, self.output_dir)

        assert results["processed"] == 2
        assert len(results["errors"]) == 0
        assert (self.output_dir / "test.jpg").exists()
        assert (self.output_dir / "test.png").exists()

    def test_optimize_styles(self):
        """Test CSS optimization"""
        source_dir = Path(self.temp_dir) / "source" / "assets"
        source_dir.mkdir(parents=True)

        # Create test CSS
        (source_dir / "style.css").write_text("body { color: red; }")

        results = self.asset_pipeline._optimize_styles(source_dir, self.output_dir)

        assert results["processed"] == 1
        assert len(results["errors"]) == 0
        assert (self.output_dir / "style.css").exists()

    def test_optimize_scripts(self):
        """Test JavaScript optimization"""
        source_dir = Path(self.temp_dir) / "source" / "assets"
        source_dir.mkdir(parents=True)

        # Create test JS
        (source_dir / "script.js").write_text("console.log('test');")

        results = self.asset_pipeline._optimize_scripts(source_dir, self.output_dir)

        assert results["processed"] == 1
        assert len(results["errors"]) == 0
        assert (self.output_dir / "script.js").exists()


class TestContentMetadata:
    """Test cases for ContentMetadata class"""

    def setup_method(self):
        """Set up test fixtures"""
        self.mock_db_manager = Mock(spec=DatabaseManager)
        self.content_metadata = ContentMetadata(self.mock_db_manager)

    def test_create_content_record_success(self):
        """Test successful content record creation"""
        # Mock database session and manager responses
        mock_session = Mock()
        mock_project = Mock()
        mock_project.id = 1
        mock_content_file = Mock()
        mock_content_file.id = 1

        self.mock_db_manager.create.side_effect = [mock_project, mock_content_file]

        metadata = {"description": "Test site", "content": "<html>Test</html>"}

        result = self.content_metadata.create_content_record(
            mock_session, "test-site", "index", metadata
        )

        assert result["status"] == "success"
        assert result["project_id"] == 1
        assert result["content_id"] == 1

    def test_get_content_metadata_success(self):
        """Test successful content metadata retrieval"""
        mock_session = Mock()
        mock_project = Mock()
        mock_project.id = 1
        mock_project.name = "test-site"
        mock_project.description = "Test site"
        mock_project.created_at.isoformat.return_value = "2024-01-01T00:00:00"

        mock_file = Mock()
        mock_file.id = 1
        mock_file.filename = "index.html"
        mock_file.path = "/index"
        mock_file.language = "html"
        mock_file.created_at.isoformat.return_value = "2024-01-01T00:00:00"

        self.mock_db_manager.get_multi.side_effect = [[mock_project], [mock_file]]

        result = self.content_metadata.get_content_metadata(mock_session, "test-site")

        assert result["status"] == "success"
        assert result["metadata"]["project"]["name"] == "test-site"
        assert len(result["metadata"]["content_files"]) == 1


class TestWebsiteGenerator:
    """Test cases for enhanced WebsiteGenerator class"""

    def setup_method(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.sites_dir = Path(self.temp_dir) / "sites"
        self.templates_dir = Path(self.temp_dir) / "templates"

        # Create mock template manager
        self.mock_template_manager = Mock(spec=TemplateManager)
        self.mock_template_manager.get_template.return_value = {
            "path": str(self.templates_dir / "modern")
        }

        # Create mock database manager
        self.mock_db_manager = Mock(spec=DatabaseManager)

        # Create test template
        template_path = self.templates_dir / "modern"
        template_path.mkdir(parents=True)
        (template_path / "index.html").write_text(
            "<html><head><title>{{title}}</title></head><body>{{description}}</body></html>"
        )
        (template_path / "style.css").write_text("body { color: black; }")

        self.generator = WebsiteGenerator(
            self.mock_template_manager, self.mock_db_manager
        )
        self.generator.sites_dir = self.sites_dir

    def teardown_method(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_create_website_success(self):
        """Test successful website creation with advanced features"""
        site_config = {
            "name": "test-site",
            "template": "modern",
            "title": "Test Website",
            "description": "A test website",
            "author": "Test Author",
            "keywords": "test, website",
        }

        result = self.generator.create_website(site_config)

        assert result["status"] == "success"
        assert "test-site" in result["message"]

        # Check if site directory was created
        site_dir = self.sites_dir / "test-site"
        assert site_dir.exists()
        assert (site_dir / "index.html").exists()
        assert (site_dir / "config.json").exists()

        # Check if content was customized
        index_content = (site_dir / "index.html").read_text()
        assert "Test Website" in index_content
        assert "A test website" in index_content

    def test_create_website_with_theme(self):
        """Test website creation with theme customization"""
        # Create a theme first
        theme_dir = Path(self.temp_dir) / "themes" / "custom-blue"
        theme_dir.mkdir(parents=True)
        (theme_dir / "base").mkdir()
        (theme_dir / "base" / "index.html").write_text("<html>{{title}}</html>")

        self.generator.theme_manager.theme_config["custom-blue"] = {
            "path": str(theme_dir),
            "base_template": "modern",
        }

        site_config = {
            "name": "themed-site",
            "template": "modern",
            "theme": "custom-blue",
            "title": "Themed Website",
            "description": "A themed website",
        }

        result = self.generator.create_website(site_config)

        assert result["status"] == "success"
        assert "themed-site" in result["message"]

    def test_create_website_validation_error(self):
        """Test website creation with validation error"""
        site_config = {
            "name": "test-site",
            # Missing required fields
        }

        result = self.generator.create_website(site_config)

        assert result["status"] == "error"
        assert "Missing required field" in result["message"]

    def test_create_website_duplicate_name(self):
        """Test website creation with duplicate name"""
        # Create first site
        site_dir = self.sites_dir / "test-site"
        site_dir.mkdir(parents=True, exist_ok=True)
        (site_dir / "index.html").write_text("<html></html>")

        site_config = {
            "name": "test-site",
            "template": "modern",
            "title": "Test Website",
            "description": "A test website",
        }

        result = self.generator.create_website(site_config)

        assert result["status"] == "error"
        assert "already exists" in result["message"]

    def test_list_sites(self):
        """Test listing sites with enhanced metadata"""
        # Create a test site
        site_dir = self.sites_dir / "test-site"
        site_dir.mkdir(parents=True, exist_ok=True)
        (site_dir / "index.html").write_text("<html></html>")
        (site_dir / "config.json").write_text(
            json.dumps(
                {
                    "name": "test-site",
                    "title": "Test Website",
                    "description": "A test website",
                }
            )
        )

        sites = self.generator.list_sites()

        assert "test-site" in sites
        assert "statistics" in sites["test-site"]
        assert sites["test-site"]["statistics"]["total_files"] > 0

    def test_delete_site_success(self):
        """Test successful site deletion with cleanup"""
        # Create a test site
        site_dir = self.sites_dir / "test-site"
        site_dir.mkdir(parents=True, exist_ok=True)
        (site_dir / "index.html").write_text("<html></html>")

        # Create optimized assets
        optimized_dir = self.generator.asset_pipeline.output_dir / "test-site"
        optimized_dir.mkdir(parents=True, exist_ok=True)
        (optimized_dir / "test.txt").write_text("test")

        result = self.generator.delete_site("test-site")

        assert result["status"] == "success"
        assert not site_dir.exists()
        assert not optimized_dir.exists()

    def test_delete_site_not_found(self):
        """Test site deletion when site doesn't exist"""
        result = self.generator.delete_site("nonexistent")

        assert result["status"] == "error"
        assert "not found" in result["message"]

    def test_create_theme(self):
        """Test theme creation through generator"""
        import uuid

        theme_name = f"test-theme-{uuid.uuid4().hex[:8]}"
        theme_config = {
            "name": theme_name,
            "base_template": "modern",
            "customizations": {
                "css": {"primary-color": "#ff6b6b"},
                "html": {"title": "Custom Title"},
            },
        }

        result = self.generator.create_theme(theme_config)

        assert result["status"] == "success"
        assert theme_name in self.generator.theme_manager.theme_config

    def test_list_themes(self):
        """Test listing themes"""
        import uuid

        theme_name = f"test-theme-{uuid.uuid4().hex[:8]}"
        # Add a test theme
        self.generator.theme_manager.theme_config[theme_name] = {
            "base_template": "modern",
            "path": "/test/path",
        }

        themes = self.generator.list_themes()

        assert theme_name in themes
        assert themes[theme_name]["base_template"] == "modern"


class TestWebsiteGeneratorIntegration:
    """Integration tests for WebsiteGenerator"""

    def setup_method(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.sites_dir = Path(self.temp_dir) / "sites"
        self.templates_dir = Path(self.temp_dir) / "templates"

        # Create realistic template structure
        template_path = self.templates_dir / "modern"
        template_path.mkdir(parents=True)
        (template_path / "index.html").write_text(
            """
        <!DOCTYPE html>
        <html>
        <head>
            <title>{{title}}</title>
            <meta name="description" content="{{description}}">
            <meta name="author" content="{{author}}">
            <meta name="keywords" content="{{keywords}}">
        </head>
        <body>
            <h1>{{title}}</h1>
            <p>{{description}}</p>
        </body>
        </html>
        """
        )
        (template_path / "style.css").write_text(
            """
        :root {
            --primary-color: #007bff;
            --secondary-color: #6c757d;
        }
        body {
            color: var(--primary-color);
        }
        """
        )
        (template_path / "script.js").write_text("console.log('Hello World');")

        # Create assets directory
        assets_dir = template_path / "assets"
        assets_dir.mkdir()
        (assets_dir / "logo.png").write_text("fake logo")
        (assets_dir / "custom.css").write_text("custom styles")

        self.mock_template_manager = Mock(spec=TemplateManager)
        self.mock_template_manager.get_template.return_value = {
            "path": str(template_path)
        }

        self.generator = WebsiteGenerator(self.mock_template_manager)
        self.generator.sites_dir = self.sites_dir

    def teardown_method(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_full_website_generation_workflow(self):
        """Test complete website generation workflow"""
        site_config = {
            "name": "integration-test-site",
            "template": "modern",
            "title": "Integration Test Website",
            "description": "A comprehensive test website",
            "author": "Integration Test",
            "keywords": "integration, test, website",
        }

        # Create website
        result = self.generator.create_website(site_config)
        assert result["status"] == "success"

        # Verify site structure
        site_dir = self.sites_dir / "integration-test-site"
        assert site_dir.exists()
        assert (site_dir / "index.html").exists()
        assert (site_dir / "style.css").exists()
        assert (site_dir / "script.js").exists()
        assert (site_dir / "config.json").exists()

        # Verify content customization
        index_content = (site_dir / "index.html").read_text()
        assert "Integration Test Website" in index_content
        assert "A comprehensive test website" in index_content
        assert "Integration Test" in index_content
        assert "integration, test, website" in index_content

        # Verify configuration
        config_content = json.loads((site_dir / "config.json").read_text())
        assert config_content["name"] == "integration-test-site"
        assert config_content["generator_version"] == "2.1.0"
        assert config_content["features"]["asset_optimization"] is True

        # Verify asset optimization
        optimized_dir = (
            self.generator.asset_pipeline.output_dir / "integration-test-site"
        )
        assert optimized_dir.exists()
        assert (optimized_dir / "assets" / "logo.png").exists()
        assert (optimized_dir / "assets" / "custom.css").exists()

    def test_theme_customization_workflow(self):
        """Test theme customization workflow"""
        import uuid

        theme_name = f"custom-theme-{uuid.uuid4().hex[:8]}"
        # Create a custom theme
        theme_config = {
            "name": theme_name,
            "base_template": "modern",
            "customizations": {
                "css": {"primary-color": "#ff6b6b"},
                "html": {"title": "Custom Themed Title"},
            },
        }

        theme_result = self.generator.create_theme(theme_config)
        assert theme_result["status"] == "success"

        # Create website with custom theme
        site_config = {
            "name": "themed-integration-site",
            "template": "modern",
            "theme": theme_name,
            "title": "Themed Integration Site",
            "description": "A site with custom theme",
        }

        site_result = self.generator.create_website(site_config)
        assert site_result["status"] == "success"

        # Verify theme was applied
        site_dir = self.sites_dir / "themed-integration-site"
        css_content = (site_dir / "style.css").read_text()
        assert "--primary-color: #ff6b6b;" in css_content


if __name__ == "__main__":
    pytest.main([__file__])
