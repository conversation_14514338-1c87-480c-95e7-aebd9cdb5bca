# Multi-stage build for Model Optimizer with GPU support
FROM python:3.11-slim AS builder
ENV PIP_NO_CACHE_DIR=1
# Set working directory
WORKDIR /app

# Install system dependencies for building
RUN apt-get update && apt-get install -y --no-install-recommends curl git \
 && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY config/requirements.container.txt ./requirements.txt
RUN python -m venv /opt/venv \
 && . /opt/venv/bin/activate \
 && pip install --upgrade pip \
 && pip install --no-cache-dir -r requirements.txt

# Production stage
FROM python:3.11-slim AS runtime
ENV PATH="/opt/venv/bin:$PATH" PYTHONUNBUFFERED=1 PYTHONPATH=/app
# Create non-root user for security
RUN addgroup -r modeloptimizer && adduser -r -g modeloptimizer modeloptimizer \
 && apt-get update && apt-get install -y --no-install-recommends curl git \
 && rm -rf /var/lib/apt/lists/* && apt-get clean

# Set working directory
WORKDIR /app

# Copy Python packages from builder stage
COPY --from=builder /opt/venv /opt/venv

# Create necessary directories
RUN mkdir -p /app/data/model-optimizer \
    /app/logs/model-optimizer \
    /app/models/optimized \
    /app/temp/model-optimizer \
    /app/config \
    /app/cache/model-optimizer

# Set ownership to non-root user
RUN chown -R modeloptimizer:modeloptimizer /app

# Switch to non-root user
USER modeloptimizer

# Set environment variables
ENV MODEL_OPTIMIZER_ENABLED=true
ENV ENVIRONMENT=production
ENV LOG_LEVEL=INFO
ENV PORT=8087
ENV CUDA_VISIBLE_DEVICES=0
ENV NVIDIA_VISIBLE_DEVICES=0

# Expose port
EXPOSE 8087

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8087/health || exit 1

# Create model optimizer service entry point
RUN echo '#!/usr/bin/env python3\nfrom fastapi import FastAPI\nimport uvicorn\napp=FastAPI()\<EMAIL>("/health")\nasync def h():\n    return {"status":"healthy","service":"model_optimizer"}\nif __name__=="__main__":\n uvicorn.run("model_optimizer_service:app",host="0.0.0.0",port=8087,log_level="info")' > /app/model_optimizer_service.py

# Make the script executable
RUN chmod +x /app/model_optimizer_service.py

# Set the entry point
ENTRYPOINT ["python", "/app/model_optimizer_service.py"]
