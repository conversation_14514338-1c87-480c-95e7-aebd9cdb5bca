"""
Unit tests for Phase 2.2: Home Server Hosting
Tests automated deployment, hot-reload, resource monitoring, and home network configuration.
"""

import json
import os
import shutil
import socket
import sys
import tempfile
import time
import unittest
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import Magic<PERSON><PERSON>, <PERSON>ck, patch

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from agent.core.home_server import (
        DeploymentStatus,
        HomeServer,
        HotReloadManager,
        NetworkHealthChecker,
        ServerResource,
    )
    from agent.core.managers import BackupManager, DeploymentManager
    from performance.resource_monitor import ResourceMonitor
except ImportError as e:
    print(f"Warning: Could not import home_server modules: {e}")

    # Create mock classes for testing
    class HomeServer:
        pass

    class ResourceMonitor:
        pass

    class NetworkHealthChecker:
        pass

    class BackupManager:
        pass

    class DeploymentManager:
        pass

    class HotReloadManager:
        pass

    class ServerResource:
        pass

    class DeploymentStatus:
        pass


class TestResourceMonitor(unittest.TestCase):
    """Test ResourceMonitor class"""

    def setUp(self):
        """Set up test fixtures"""
        self.config = {
            "monitoring": {
                "enabled": True,
                "interval_seconds": 30,
                "max_history_size": 100,
            }
        }
        self.monitor = ResourceMonitor(self.config)

    def test_initialization(self):
        """Test ResourceMonitor initialization"""
        self.assertIsNotNone(self.monitor)
        self.assertTrue(self.monitor.monitoring_enabled)
        self.assertEqual(len(self.monitor.metrics_history), 0)

    @patch("psutil.cpu_percent")
    @patch("psutil.virtual_memory")
    @patch("psutil.disk_usage")
    @patch("psutil.net_io_counters")
    def test_collect_metrics(self, mock_net, mock_disk, mock_memory, mock_cpu):
        """Test metrics collection"""
        # Mock psutil responses
        mock_cpu.return_value = 25.5
        mock_memory.return_value = Mock(percent=60.0)
        mock_disk.return_value = Mock(percent=45.0)
        mock_net.return_value = Mock(bytes_sent=1000, bytes_recv=2000)

        metrics = self.monitor._collect_metrics()

        self.assertIsInstance(metrics, ServerResource)
        self.assertEqual(metrics.cpu_percent, 25.5)
        self.assertEqual(metrics.memory_percent, 60.0)
        self.assertEqual(metrics.disk_percent, 45.0)
        self.assertEqual(metrics.network_sent, 1000)
        self.assertEqual(metrics.network_recv, 2000)
        self.assertIsInstance(metrics.timestamp, datetime)

    def test_health_status(self):
        """Test health status calculation"""
        with patch.object(self.monitor, "_collect_metrics") as mock_collect:
            mock_collect.return_value = ServerResource(
                cpu_percent=75.0,
                memory_percent=70.0,
                disk_percent=60.0,
                network_sent=1000,
                network_recv=2000,
                timestamp=datetime.now(),
            )

            health = self.monitor.get_health_status()

            self.assertIn("status", health)
            self.assertIn("cpu_percent", health)
            self.assertIn("memory_percent", health)
            self.assertIn("disk_percent", health)
            self.assertIn("timestamp", health)

    def test_metrics_history_limit(self):
        """Test metrics history size limit"""
        # Add more metrics than the limit
        for i in range(150):
            metrics = ServerResource(
                cpu_percent=float(i),
                memory_percent=float(i),
                disk_percent=float(i),
                network_sent=i,
                network_recv=i,
                timestamp=datetime.now(),
            )
            self.monitor.metrics_history.append(metrics)

        # Should be limited to max_history_size
        self.assertLessEqual(
            len(self.monitor.metrics_history), self.monitor.max_history_size
        )


class TestNetworkHealthChecker(unittest.TestCase):
    """Test NetworkHealthChecker class"""

    def setUp(self):
        """Set up test fixtures"""
        self.config = {"domain": "localhost", "port": 5000, "ssl": {"enabled": False}}
        self.checker = NetworkHealthChecker(self.config)

    @patch("socket.socket")
    def test_check_port_success(self, mock_socket):
        """Test successful port check"""
        mock_socket.return_value.connect_ex.return_value = 0

        result = self.checker._check_port(80)

        self.assertTrue(result["accessible"])
        self.assertEqual(result["port"], 80)
        self.assertIsNone(result["error"])

    @patch("socket.socket")
    def test_check_port_failure(self, mock_socket):
        """Test failed port check"""
        mock_socket.return_value.connect_ex.return_value = 1

        result = self.checker._check_port(9999)

        self.assertFalse(result["accessible"])
        self.assertEqual(result["port"], 9999)
        self.assertIsNotNone(result["error"])

    @patch("socket.gethostbyname")
    def test_check_dns_success(self, mock_gethostbyname):
        """Test successful DNS check"""
        mock_gethostbyname.return_value = "127.0.0.1"

        result = self.checker._check_dns()

        self.assertTrue(result["resolved"])
        self.assertEqual(result["domain"], "localhost")
        self.assertIsNone(result["error"])

    @patch("socket.gethostbyname")
    def test_check_dns_failure(self, mock_gethostbyname):
        """Test failed DNS check"""
        mock_gethostbyname.side_effect = socket.gaierror("Name or service not known")

        result = self.checker._check_dns()

        self.assertFalse(result["resolved"])
        self.assertEqual(result["domain"], "localhost")
        self.assertIsNotNone(result["error"])

    def test_check_local_connectivity(self):
        """Test local connectivity check"""
        with patch.object(self.checker, "_check_port") as mock_port:
            with patch.object(self.checker, "_check_dns") as mock_dns:
                mock_port.return_value = {
                    "status": "ok",
                    "accessible": True,
                    "port": 80,
                    "error": None,
                }
                mock_dns.return_value = {
                    "status": "ok",
                    "resolved": True,
                    "domain": "localhost",
                    "error": None,
                }

                results = self.checker.check_local_connectivity()

                self.assertIn("ports", results)
                self.assertIn("dns", results)
                self.assertIn("ssl", results)
                self.assertIn("overall_status", results)
                self.assertIn("status", results)


class TestBackupManager(unittest.TestCase):
    """Test BackupManager class"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            "backup": {
                "enabled": True,
                "backup_dir": os.path.join(self.temp_dir, "backups"),
                "schedule": "daily",
            },
            "sites_dir": os.path.join(self.temp_dir, "sites"),
        }
        self.backup_manager = BackupManager(self.config)

        # Create test site
        self.test_site_path = Path(self.config["sites_dir"]) / "test-site"
        self.test_site_path.mkdir(parents=True, exist_ok=True)

        # Create test files
        (self.test_site_path / "index.html").write_text("<html>Test</html>")
        (self.test_site_path / "config.json").write_text('{"name": "test"}')

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_initialization(self):
        """Test BackupManager initialization"""
        self.assertIsNotNone(self.backup_manager)
        self.assertTrue(self.backup_manager.backup_enabled)
        self.assertTrue(Path(self.backup_manager.backup_dir).exists())

    def test_create_backup(self):
        """Test backup creation"""
        backup_path = self.backup_manager.create_site_backup(
            self.test_site_path, "test-site"
        )

        self.assertIsNotNone(backup_path)
        self.assertTrue(Path(backup_path).exists())

        # Check backup is a zip file
        backup_file = Path(backup_path)
        self.assertTrue(backup_file.suffix == ".zip")
        self.assertTrue(backup_file.stat().st_size > 0)

        # Check metadata in backup manager
        backups = self.backup_manager.list_backups()
        backup_info = next(
            (b for b in backups if b.get("backup_id") == backup_file.name), None
        )
        self.assertIsNotNone(backup_info)
        self.assertEqual(backup_info["site_name"], "test-site")
        self.assertIn("backup_time", backup_info)
        self.assertEqual(backup_info["backup_type"], "site_zip")
        self.assertIn("size_bytes", backup_info)

    def test_create_backup_site_not_found(self):
        """Test backup creation for non-existent site"""
        non_existent_path = Path(self.temp_dir) / "non-existent-site"
        with self.assertRaises(FileNotFoundError):
            self.backup_manager.create_site_backup(
                non_existent_path, "non-existent-site"
            )

    def test_restore_backup(self):
        """Test backup restoration"""
        # Create backup first
        backup_path = self.backup_manager.create_site_backup(
            self.test_site_path, "test-site"
        )

        # Delete original site
        shutil.rmtree(self.test_site_path)
        self.assertFalse(self.test_site_path.exists())

        # Restore backup
        success = self.backup_manager.restore_site_backup(
            backup_path, self.test_site_path
        )

        self.assertTrue(success)
        self.assertTrue(self.test_site_path.exists())
        self.assertTrue((self.test_site_path / "index.html").exists())
        self.assertTrue((self.test_site_path / "config.json").exists())

    def test_list_backups(self):
        """Test backup listing"""
        # Create multiple backups with unique names
        import uuid

        backup_name1 = f"test-site-{uuid.uuid4().hex[:8]}"
        backup_name2 = f"test-site-{uuid.uuid4().hex[:8]}"
        # Ensure site directories exist
        site_dir1 = Path(self.config["sites_dir"]) / backup_name1
        site_dir2 = Path(self.config["sites_dir"]) / backup_name2
        site_dir1.mkdir(parents=True, exist_ok=True)
        site_dir2.mkdir(parents=True, exist_ok=True)
        (site_dir1 / "index.html").write_text("<html>Backup 1</html>")
        (site_dir2 / "index.html").write_text("<html>Backup 2</html>")
        self.backup_manager.create_site_backup(site_dir1, backup_name1)
        self.backup_manager.create_site_backup(site_dir2, backup_name2)
        backups = self.backup_manager.list_backups()
        self.assertTrue(any(b.get("site_name") == backup_name1 for b in backups))
        self.assertTrue(any(b.get("site_name") == backup_name2 for b in backups))

    def test_cleanup_old_backups(self):
        """Test old backup cleanup"""
        # Create backup
        self.backup_manager.create_site_backup(self.test_site_path, "test-site")

        # Test that cleanup method runs without error
        cleaned_count = self.backup_manager.cleanup_old_backups(keep_days=30)

        # Should return a number (even if 0)
        self.assertIsInstance(cleaned_count, int)
        self.assertGreaterEqual(cleaned_count, 0)


class TestDeploymentManager(unittest.TestCase):
    """Test DeploymentManager class"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            "sites_dir": os.path.join(self.temp_dir, "sites"),
            "deploy_dir": os.path.join(self.temp_dir, "deployments"),
            "deployment": {
                "auto_backup": True,
                "validation_required": True,
                "rollback_on_failure": True,
            },
        }

        # Create mock backup manager
        self.backup_manager = Mock()
        self.backup_manager.create_backup.return_value = "/tmp/backup"
        self.backup_manager.restore_backup.return_value = True

        # Create sites directory
        os.makedirs(os.path.join(self.temp_dir, "sites"), exist_ok=True)
        os.makedirs(os.path.join(self.temp_dir, "deployments"), exist_ok=True)

        self.deployment_manager = DeploymentManager(
            config_dict=self.config, backup_manager=self.backup_manager
        )

        # Create test source
        self.source_path = os.path.join(self.temp_dir, "source")
        os.makedirs(self.source_path)
        (Path(self.source_path) / "index.html").write_text("<html>Test</html>")

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_initialization(self):
        """Test DeploymentManager initialization"""
        self.assertIsNotNone(self.deployment_manager)
        self.assertEqual(len(self.deployment_manager.deployments), 0)

    def test_deploy_site_success(self):
        """Test successful site deployment"""
        deployment = self.deployment_manager.deploy_site_automated(
            "test-site", self.source_path
        )

        self.assertEqual(deployment.status, "success")
        self.assertEqual(deployment.site_name, "test-site")
        self.assertIsNotNone(deployment.deployment_id)
        self.assertIsNotNone(deployment.start_time)
        self.assertIsNotNone(deployment.end_time)
        self.assertIsNone(deployment.error_message)

        # Check deployment was recorded
        self.assertIn(deployment.deployment_id, self.deployment_manager.deployments)

    def test_deploy_site_validation_only(self):
        """Test deployment validation only"""
        deployment = self.deployment_manager.deploy_site_automated(
            "test-site", self.source_path, validate_only=True
        )

        self.assertEqual(deployment.status, "success")
        self.assertIsNotNone(deployment.end_time)

        # Should not create actual deployment
        target_path = Path(self.config["sites_dir"]) / "test-site"
        self.assertFalse(target_path.exists())

    def test_deploy_site_validation_failure(self):
        """Test deployment with validation failure"""
        # Create invalid source (no index.html)
        invalid_source = os.path.join(self.temp_dir, "invalid")
        os.makedirs(invalid_source, exist_ok=True)

        deployment = self.deployment_manager.deploy_site_automated(
            "test-site", invalid_source
        )

        self.assertIn(deployment.status, ["failed", "rolled_back"])
        self.assertIsNotNone(deployment.error_message)

    def test_rollback_deployment(self):
        """Test deployment rollback"""
        # Create deployment first
        deployment = self.deployment_manager.deploy_site_automated(
            "test-site", self.source_path
        )

        # Rollback
        success = self.deployment_manager.rollback_deployment_automated(
            deployment.deployment_id
        )

        self.assertTrue(success)
        self.assertEqual(deployment.status, "rolled_back")

    def test_list_deployments(self):
        """Test deployment listing"""
        # Create multiple deployments
        self.deployment_manager.deploy_site_automated("site1", self.source_path)
        self.deployment_manager.deploy_site_automated("site2", self.source_path)

        # Check internal deployments storage
        self.assertEqual(len(self.deployment_manager.deployments), 2)

        # Check that deployments are stored correctly
        deployment_ids = list(self.deployment_manager.deployments.keys())
        self.assertEqual(len(deployment_ids), 2)

        # Check that both deployments have the correct site names
        site_names = [
            self.deployment_manager.deployments[deploy_id].site_name
            for deploy_id in deployment_ids
        ]
        self.assertIn("site1", site_names)
        self.assertIn("site2", site_names)


class TestHotReloadManager(unittest.TestCase):
    """Test HotReloadManager class"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.config = {
            "sites_dir": os.path.join(self.temp_dir, "sites"),
            "hot_reload": {
                "enabled": True,
                "watch_recursive": True,
                "debounce_seconds": 1.0,
            },
        }

        # Create mock deployment manager
        self.deployment_manager = Mock()

        self.hot_reload_manager = HotReloadManager(self.config, self.deployment_manager)

        # Create test site
        self.test_site_path = Path(self.config["sites_dir"]) / "test-site"
        self.test_site_path.mkdir(parents=True, exist_ok=True)

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_initialization(self):
        """Test HotReloadManager initialization"""
        self.assertIsNotNone(self.hot_reload_manager)
        self.assertEqual(len(self.hot_reload_manager.reload_callbacks), 0)

    def test_add_reload_callback(self):
        """Test adding reload callback"""
        callback = Mock()
        self.hot_reload_manager.add_reload_callback(callback)

        self.assertEqual(len(self.hot_reload_manager.reload_callbacks), 1)
        self.assertIn(callback, self.hot_reload_manager.reload_callbacks)

    def test_watch_new_site(self):
        """Test watching new site"""
        self.hot_reload_manager.watch_new_site("new-site", self.test_site_path)

        # Should add to watched paths
        self.assertIn(str(self.test_site_path), self.hot_reload_manager.watched_paths)
        self.assertIn("new-site", self.hot_reload_manager.watched_sites)


class TestHomeServer(unittest.TestCase):
    """Test HomeServerHosting main class"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.temp_dir, "test_config.json")

        # Create test config
        config = {
            "domain": "localhost",
            "port": 5000,
            "sites_dir": os.path.join(self.temp_dir, "sites"),
            "monitoring": {"enabled": True},
            "backup": {
                "enabled": True,
                "backup_dir": os.path.join(self.temp_dir, "backups"),
            },
        }

        with open(self.config_path, "w") as f:
            json.dump(config, f)

        self.server = HomeServer(self.config_path)

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_initialization(self):
        """Test HomeServerHosting initialization"""
        self.assertIsNotNone(self.server)
        self.assertIsNotNone(self.server.resource_monitor)
        self.assertIsNotNone(self.server.network_checker)
        self.assertIsNotNone(self.server.backup_manager)
        self.assertIsNotNone(self.server.deployment_manager)
        self.assertIsNotNone(self.server.hot_reload_manager)

    def test_get_health_status(self):
        """Test health status retrieval"""
        health = self.server.get_health_status()

        self.assertIn("system_health", health)
        self.assertIn("network", health)
        self.assertIn("network_health", health)
        self.assertIn("pipeline", health)
        self.assertIn("timestamp", health)

    def test_get_site_statistics(self):
        """Test site statistics retrieval"""
        # Create test site in temp directory
        temp_sites_dir = Path(self.temp_dir) / "sites"
        test_site_path = temp_sites_dir / "test-site"
        test_site_path.mkdir(parents=True, exist_ok=True)
        (test_site_path / "index.html").write_text("<html>Test</html>")

        # Temporarily update server config to use temp sites directory
        original_sites_dir = self.server.config.get("deployment", {}).get(
            "sites_dir", "sites"
        )
        self.server.config.setdefault("deployment", {})["sites_dir"] = str(
            temp_sites_dir
        )

        try:
            stats = self.server.get_site_statistics()

            self.assertIn("total_sites", stats)
            self.assertIn("total_size", stats)
            self.assertIn("sites", stats)
            self.assertEqual(stats["total_sites"], 1)
            self.assertEqual(len(stats["sites"]), 1)
            self.assertEqual(stats["sites"][0]["name"], "test-site")
        finally:
            # Restore original config
            self.server.config.setdefault("deployment", {})[
                "sites_dir"
            ] = original_sites_dir


class TestIntegration(unittest.TestCase):
    """Integration tests for Phase 2.2 features"""

    def setUp(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.config_path = os.path.join(self.temp_dir, "integration_config.json")

        # Create comprehensive test config
        config = {
            "domain": "localhost",
            "port": 5000,
            "sites_dir": os.path.join(self.temp_dir, "sites"),
            "monitoring": {"enabled": True, "interval_seconds": 1},
            "backup": {
                "enabled": True,
                "backup_dir": os.path.join(self.temp_dir, "backups"),
            },
            "deployment": {"auto_backup": True, "validation_required": True},
            "hot_reload": {"enabled": True},
        }

        with open(self.config_path, "w") as f:
            json.dump(config, f)

        self.server = HomeServer(self.config_path)

    def tearDown(self):
        """Clean up test fixtures"""
        shutil.rmtree(self.temp_dir, ignore_errors=True)

    def test_full_deployment_workflow(self):
        """Test complete deployment workflow"""
        # Create test source and site
        source_path = os.path.join(self.temp_dir, "source")
        os.makedirs(source_path, exist_ok=True)
        (Path(source_path) / "index.html").write_text("<html>Integration Test</html>")
        (Path(source_path) / "config.json").write_text('{"name": "integration-test"}')
        # Ensure site exists before deployment
        site_dir = os.path.join(self.temp_dir, "sites", "integration-test")
        os.makedirs(site_dir, exist_ok=True)
        (Path(site_dir) / "index.html").write_text("<html>Integration Test</html>")
        (Path(site_dir) / "config.json").write_text('{"name": "integration-test"}')
        # Deploy site (async method)
        import asyncio

        deployment = asyncio.run(
            self.server.deploy_site("integration-test", source_path)
        )
        self.assertIn(
            deployment.get("status", "unknown"), ["success", "failed", "unknown"]
        )

    def test_backup_and_restore_workflow(self):
        """Test backup and restore workflow"""
        # Create test site
        sites_dir = Path(
            self.server.config.get("deployment", {}).get("sites_dir", "sites")
        )
        test_site_path = sites_dir / "backup-test"
        test_site_path.mkdir(parents=True, exist_ok=True)
        (test_site_path / "index.html").write_text("<html>Backup Test</html>")

        # Create backup
        backup_path = self.server.backup_manager.create_site_backup(
            test_site_path, "backup-test"
        )
        self.assertIsNotNone(backup_path)

        # Modify site
        (test_site_path / "index.html").write_text("<html>Modified</html>")

        # Restore backup
        success = self.server.backup_manager.restore_site_backup(
            backup_path, test_site_path
        )
        self.assertTrue(success)

        # Check original content restored
        content = (test_site_path / "index.html").read_text()
        self.assertEqual(content, "<html>Backup Test</html>")


if __name__ == "__main__":
    # Create test runner
    loader = unittest.TestLoader()
    suite = unittest.TestSuite()

    # Add test classes
    test_classes = [
        TestResourceMonitor,
        TestNetworkHealthChecker,
        TestBackupManager,
        TestDeploymentManager,
        TestHotReloadManager,
        TestHomeServer,
        TestIntegration,
    ]

    for test_class in test_classes:
        tests = loader.loadTestsFromTestCase(test_class)
        suite.addTests(tests)

    # Run tests
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)

    # Print summary
    print(f"\n{'='*60}")
    print(f"Phase 2.2 Home Server Hosting Tests Summary:")
    print(f"Tests run: {result.testsRun}")
    print(f"Failures: {len(result.failures)}")
    print(f"Errors: {len(result.errors)}")
    print(
        f"Success rate: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%"
    )
    print(f"{'='*60}")

    if result.failures:
        print("\nFailures:")
        for test, traceback in result.failures:
            print(f"  {test}: {traceback}")

    if result.errors:
        print("\nErrors:")
        for test, traceback in result.errors:
            print(f"  {test}: {traceback}")
