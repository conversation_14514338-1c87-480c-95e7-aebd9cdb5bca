# Supabase Integration - User Guide

## Overview
The AI Coding Agent now includes comprehensive Supabase project management capabilities. This guide will help you set up, configure, and manage your Supabase projects through the agent.

## Table of Contents
1. [Getting Started](#getting-started)
2. [Project Configuration](#project-configuration)
3. [Migration Management](#migration-management)
4. [Schema Management](#schema-management)
5. [CLI Commands](#cli-commands)
6. [Troubleshooting](#troubleshooting)
7. [Best Practices](#best-practices)

## Getting Started

### Prerequisites
- A Supabase project (create one at [supabase.com](https://supabase.com))
- Supabase CLI installed on your system
- Access to your Supabase project credentials

### Quick Setup
1. **Navigate to Supabase Management**: Go to the Supabase page in your AI Coding Agent dashboard
2. **Configure Your Project**: Enter your Supabase project details
3. **Test Connection**: Verify your configuration works
4. **Start Managing**: Create migrations and manage your schema

## Project Configuration

### Setting Up Supabase Configuration

#### Via Web Interface
1. Go to the **Configuration** tab in the Supabase management page
2. Fill in the following details:
   - **Supabase URL**: Your project URL (e.g., `https://your-project.supabase.co`)
   - **Anonymous Key**: Your project's anon/public key
   - **Service Role Key**: Your project's service role key (for admin operations)
   - **Project Reference**: Your project reference ID
   - **Organization ID**: Your Supabase organization ID

3. Click **Test Connection** to verify your configuration
4. Click **Save Configuration** to store your settings

#### Via CLI
```bash
# Link your project to Supabase
supabase link <project_id> <supabase_url> <api_key>

# View current configuration
supabase config <project_id> --show

# Update configuration
supabase config <project_id> --update
```

### Configuration Fields Explained

| Field | Description | Required | Example |
|-------|-------------|----------|---------|
| **Supabase URL** | Your project's URL | Yes | `https://myproject.supabase.co` |
| **Anonymous Key** | Public API key for client operations | Yes | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| **Service Role Key** | Admin API key for server operations | Yes | `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...` |
| **Project Reference** | Unique project identifier | Yes | `myproject-ref` |
| **Organization ID** | Your Supabase organization | Yes | `org-123456` |

### Finding Your Supabase Credentials

1. **Go to your Supabase Dashboard**: [supabase.com/dashboard](https://supabase.com/dashboard)
2. **Select your project**
3. **Navigate to Settings > API**
4. **Copy the required credentials**:
   - Project URL
   - Anon/public key
   - Service role key
   - Project reference (from the URL)

## Migration Management

### Understanding Migrations
Migrations are SQL files that define changes to your database schema. They allow you to:
- Version control your database changes
- Deploy changes safely across environments
- Rollback changes if needed
- Collaborate with team members

### Creating Migrations

#### Via Web Interface
1. Go to the **Migrations** tab
2. Click **Create Migration**
3. Fill in the migration details:
   - **Name**: Descriptive name (e.g., `create_users_table`)
   - **Description**: What this migration does
   - **Version**: Migration version (e.g., `1.0.0`)
   - **SQL Content**: Your SQL statements

4. Use **SQL Templates** to get started quickly:
   - **Create Table**: Basic table creation with RLS
   - **Add Column**: Add columns to existing tables
   - **Create Index**: Add database indexes
   - **Add Foreign Key**: Create relationships
   - **Custom**: Write your own SQL

#### Via CLI
```bash
# Create a new migration
migration create <project_id> <migration_name> --template create_table

# Create with custom SQL
migration create <project_id> <migration_name> --sql "CREATE TABLE users (id SERIAL PRIMARY KEY);"
```

### Migration Templates

#### Create Table Template
```sql
-- Create a new table
CREATE TABLE table_name (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes
CREATE INDEX idx_table_name_created_at ON table_name(created_at);

-- Add RLS (Row Level Security)
ALTER TABLE table_name ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view their own data" ON table_name
    FOR SELECT USING (auth.uid() = user_id);
```

#### Add Column Template
```sql
-- Add a new column to an existing table
ALTER TABLE table_name
ADD COLUMN new_column_name TEXT;

-- Add a column with a default value
ALTER TABLE table_name
ADD COLUMN status TEXT DEFAULT 'active';
```

### Managing Migrations

#### Viewing Migrations
- **Status**: Pending, Applied, Failed, Rolled Back
- **Version**: Migration version number
- **Created Date**: When the migration was created
- **Applied Date**: When the migration was deployed

#### Deploying Migrations
1. Select a **Pending** migration
2. Click **Deploy**
3. Review the deployment status
4. Check for any errors

#### Rolling Back Migrations
1. Select an **Applied** migration
2. Click **Rollback**
3. Confirm the rollback action
4. Monitor the rollback process

### Migration Best Practices

1. **Use Descriptive Names**: `create_users_table` instead of `migration_001`
2. **Write Clear Descriptions**: Explain what the migration does
3. **Test Locally First**: Always test migrations in development
4. **Use Templates**: Leverage built-in templates for common operations
5. **Version Control**: Keep migrations in version control
6. **Backup Before Deploying**: Always backup production data

## Schema Management

### Viewing Your Schema
1. Go to the **Database Schema** tab
2. Click **Sync Schema** to load your current database structure
3. Browse tables and their properties

### Schema Information
For each table, you can see:
- **Table Name**: Name of the table
- **Schema**: Database schema (usually `public`)
- **Columns**: Number of columns in the table
- **Indexes**: Number of indexes on the table

### Synchronizing Schema
- **Manual Sync**: Click "Sync Schema" to refresh
- **Automatic Sync**: Schema syncs when you deploy migrations
- **Type Generation**: Generate TypeScript types from your schema

## CLI Commands

### Supabase Commands

#### Link Project
```bash
supabase link <project_id> <supabase_url> <api_key>
```
Links your project to a Supabase backend.

#### Manage Configuration
```bash
# Show current configuration
supabase config <project_id> --show

# Update configuration
supabase config <project_id> --update

# Delete configuration
supabase config <project_id> --delete
```

### Migration Commands

#### Create Migration
```bash
# Create with template
migration create <project_id> <name> --template create_table

# Create with custom SQL
migration create <project_id> <name> --sql "CREATE TABLE users (id SERIAL PRIMARY KEY);"
```

#### Edit Migration
```bash
migration edit <project_id> <migration_id>
```
Opens the migration in your default editor.

#### Deploy Migration
```bash
# Deploy with dry-run
migration deploy <project_id> <migration_id> --dry-run

# Deploy to production
migration deploy <project_id> <migration_id>
```

#### Rollback Migration
```bash
migration rollback <project_id> <migration_id>
```
Rolls back the specified migration.

### Schema Commands

#### Sync Schema
```bash
# Sync schema
schema sync <project_id>

# Sync and generate TypeScript types
schema sync <project_id> --generate-types
```

## Troubleshooting

### Common Issues

#### Connection Test Fails
**Problem**: "Connection test failed" error
**Solutions**:
1. Verify your Supabase URL is correct
2. Check that your API keys are valid
3. Ensure your project is active in Supabase dashboard
4. Check your internet connection

#### Migration Deployment Fails
**Problem**: Migration deployment fails with SQL errors
**Solutions**:
1. Review the SQL syntax in your migration
2. Check for conflicts with existing schema
3. Verify you have the necessary permissions
4. Test the migration locally first

#### Schema Sync Issues
**Problem**: Schema doesn't sync or shows outdated information
**Solutions**:
1. Click "Sync Schema" to refresh
2. Check your Supabase project is accessible
3. Verify your API keys have read permissions
4. Clear browser cache and try again

#### CLI Command Errors
**Problem**: CLI commands fail or return errors
**Solutions**:
1. Verify Supabase CLI is installed: `supabase --version`
2. Check your project ID is correct
3. Ensure you're in the right directory
4. Check your authentication is valid

### Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| `Project not found` | Invalid project ID | Check your project ID |
| `Unauthorized` | Invalid API keys | Verify your Supabase credentials |
| `Connection timeout` | Network issues | Check your internet connection |
| `SQL syntax error` | Invalid SQL | Review your migration SQL |
| `Table already exists` | Schema conflict | Check existing schema |

### Getting Help

1. **Check the logs**: Look for detailed error messages
2. **Verify configuration**: Double-check your Supabase settings
3. **Test connection**: Use the connection test feature
4. **Review documentation**: Check this guide and API documentation
5. **Contact support**: If issues persist, contact the development team

## Best Practices

### Security
1. **Never commit API keys**: Keep credentials secure
2. **Use environment variables**: Store sensitive data in environment variables
3. **Regular key rotation**: Rotate your Supabase API keys regularly
4. **Principle of least privilege**: Use appropriate API key permissions

### Development Workflow
1. **Local development first**: Always test locally before deploying
2. **Version control migrations**: Keep migrations in your version control
3. **Backup before changes**: Always backup production data
4. **Review migrations**: Have team members review migration changes
5. **Document changes**: Document significant schema changes

### Performance
1. **Optimize indexes**: Add appropriate indexes for your queries
2. **Monitor performance**: Use Supabase dashboard to monitor performance
3. **Batch operations**: Batch related operations when possible
4. **Use RLS policies**: Implement proper Row Level Security

### Collaboration
1. **Team communication**: Communicate migration plans with your team
2. **Migration reviews**: Have team members review migration changes
3. **Environment consistency**: Keep development and production environments consistent
4. **Documentation**: Document your database schema and changes

## Advanced Features

### Custom Migration Templates
You can create custom migration templates for your specific needs:
1. Create a template file in your project
2. Use the template when creating migrations
3. Share templates with your team

### Automated Workflows
Integrate migrations into your CI/CD pipeline:
1. Run migrations automatically on deployment
2. Validate migrations before deployment
3. Rollback on deployment failures

### Schema Versioning
Track schema changes over time:
1. Use semantic versioning for migrations
2. Document breaking changes
3. Plan migration strategies for major changes

---

## Support and Resources

- **Supabase Documentation**: [supabase.com/docs](https://supabase.com/docs)
- **API Reference**: See the API documentation for technical details
- **Community**: Join the Supabase community for help and discussions
- **GitHub Issues**: Report bugs and request features through GitHub

---

**Need Help?** If you encounter issues not covered in this guide, please check the troubleshooting section or contact the development team.
