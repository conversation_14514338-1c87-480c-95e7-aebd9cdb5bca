#!/usr/bin/env python3
"""
Test script to verify the new requirements structure works correctly.
"""

import os
import subprocess
import sys
import tempfile


def test_requirements_file(requirements_file, description):
    """Test a requirements file by attempting to install it in a clean environment."""
    print(f"\n🧪 Testing {description} ({requirements_file})...")

    try:
        # Create a temporary directory for testing
        with tempfile.TemporaryDirectory() as temp_dir:
            # Copy the requirements file to temp directory
            with open(requirements_file, "r") as f:
                content = f.read()

            temp_req_file = os.path.join(temp_dir, "requirements.txt")
            with open(temp_req_file, "w") as f:
                f.write(content)

            # Test the requirements file
            result = subprocess.run(
                [sys.executable, "-m", "pip", "check", "--quiet"],
                capture_output=True,
                text=True,
                cwd=temp_dir,
            )

            if result.returncode == 0:
                print(f"✅ {description} - No conflicts detected")
                return True
            else:
                print(f"❌ {description} - Conflicts detected:")
                print(result.stderr)
                return False

    except Exception as e:
        print(f"❌ {description} - Error: {e}")
        return False


def main():
    """Main test function."""
    print("🚀 Testing AI Coding Agent Requirements Structure")
    print("=" * 60)

    # Test each requirements file
    tests = [
        ("requirements.txt", "Core Runtime Dependencies"),
        ("requirements-api.txt", "API-specific Dependencies"),
        ("requirements-learning.txt", "Learning & AI Dependencies"),
        ("requirements-dev.txt", "Development Dependencies"),
    ]

    results = []
    for req_file, description in tests:
        if os.path.exists(req_file):
            result = test_requirements_file(req_file, description)
            results.append((description, result))
        else:
            print(f"❌ {description} - File not found: {req_file}")
            results.append((description, False))

    # Summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)

    passed = 0
    total = len(results)

    for description, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {description}")
        if result:
            passed += 1

    print(f"\n🎯 Overall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All requirements files are working correctly!")
        return 0
    else:
        print("⚠️  Some requirements files have issues that need to be resolved.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
