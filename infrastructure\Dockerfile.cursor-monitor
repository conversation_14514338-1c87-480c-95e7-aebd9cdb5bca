FROM python:3.11-slim AS builder
ENV PIP_NO_CACHE_DIR=1
WORKDIR /app
RUN apt-get update && apt-get install -y --no-install-recommends build-essential libpq-dev curl \
 && rm -rf /var/lib/apt/lists/*
COPY config/requirements.txt ./requirements.txt
COPY config/requirements-dev.txt ./requirements-dev.txt
RUN python -m venv /opt/venv \
 && . /opt/venv/bin/activate \
 && pip install --upgrade pip \
 && pip install --no-cache-dir -r requirements.txt \
 && pip install --no-cache-dir -r requirements-dev.txt

FROM python:3.11-slim AS runtime
ENV PATH="/opt/venv/bin:$PATH" PYTHONUNBUFFERED=1 PYTHONPATH=/app
WORKDIR /app
RUN apt-get update && apt-get install -y --no-install-recommends \
    curl \
    procps \
    supervisor \
  && rm -rf /var/lib/apt/lists/*
COPY --from=builder /opt/venv /opt/venv
COPY api /app/api
COPY core /app/core
COPY models /app/models
COPY db /app/db
COPY utils /app/utils
COPY security /app/security
COPY config /app/config
COPY scripts /app/scripts
RUN mkdir -p /app/logs /app/data /app/backups /app/tmp
COPY containers/supervisor/cursor-monitor.conf /etc/supervisor/conf.d/cursor-monitor.conf
RUN useradd --create-home --shell /bin/bash cursor_monitor \
 && chown -R cursor_monitor:cursor_monitor /app \
 && chmod -R 755 /app/logs /app/data /app/backups /app/tmp
USER cursor_monitor
EXPOSE 8094
HEALTHCHECK --interval=30s --timeout=15s --start-period=10s --retries=3 CMD curl -f http://localhost:8094/health || exit 1
CMD ["/usr/bin/supervisord", "-c", "/etc/supervisor/conf.d/cursor-monitor.conf"]
