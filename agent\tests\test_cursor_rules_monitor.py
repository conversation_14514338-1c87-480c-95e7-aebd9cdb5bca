#!/usr/bin/env python3
"""
Unit tests for Cursor Rules Monitor PID file functionality
"""

import os
import shutil

# Add project root to path
import sys
import tempfile
import time
from pathlib import Path
from unittest.mock import Magic<PERSON><PERSON>, Mock, patch

import psutil
import pytest

sys.path.insert(0, str(Path(__file__).parent.parent))

from agent.scripts.cursor_rules_monitor import CursorRulesMonitor


class TestCursorRulesMonitorPID:
    """Test cases for Cursor Rules Monitor PID file functionality"""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.fixture
    def monitor(self, temp_dir):
        """Create a CursorRulesMonitor instance for testing"""
        monitor = CursorRulesMonitor()
        return monitor

    def test_check_pid_file_no_file(self, monitor, temp_dir):
        """Test _check_pid_file when no PID file exists"""
        # Test that when no PID file exists, it returns None
        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = temp_dir / "nonexistent.pid"
            result = monitor._check_pid_file()
            assert result is None

    def test_check_pid_file_empty_file(self, monitor, temp_dir):
        """Test _check_pid_file with empty PID file"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Create empty PID file
        try:
            pid_file.write_text("")
        except PermissionError:
            pytest.skip("Cannot create test file due to permissions")

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            result = monitor._check_pid_file()
            assert result is None
            assert not pid_file.exists()  # Should be removed

    def test_check_pid_file_corrupted_file(self, monitor, temp_dir):
        """Test _check_pid_file with corrupted PID file"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Create corrupted PID file
        try:
            pid_file.write_text("invalid_pid")
        except PermissionError:
            pytest.skip("Cannot create test file due to permissions")

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            result = monitor._check_pid_file()
            assert result is None
            assert not pid_file.exists()  # Should be removed

    def test_check_pid_file_stale_pid(self, monitor, temp_dir):
        """Test _check_pid_file with stale PID file"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Create PID file with non-existent PID
        try:
            pid_file.write_text("99999")
        except PermissionError:
            pytest.skip("Cannot create test file due to permissions")

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            with patch("psutil.pid_exists", return_value=False):
                result = monitor._check_pid_file()
                assert result is None
                assert not pid_file.exists()  # Should be removed

    def test_check_pid_file_live_pid(self, monitor, temp_dir):
        """Test _check_pid_file with live PID file"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Create PID file with current PID
        try:
            pid_file.write_text(str(os.getpid()))
        except PermissionError:
            pytest.skip("Cannot create test file due to permissions")

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            with patch("psutil.pid_exists", return_value=True):
                with patch("psutil.Process") as mock_process:
                    mock_process.return_value.cmdline.return_value = [
                        "python",
                        "cursor_rules_monitor.py",
                    ]
                    result = monitor._check_pid_file()
                    assert result == os.getpid()
                    assert pid_file.exists()  # Should remain

    def test_check_pid_file_locked_file(self, monitor, temp_dir):
        """Test _check_pid_file with locked PID file"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Create PID file
        try:
            pid_file.write_text("12345")
        except PermissionError:
            pytest.skip("Cannot create test file due to permissions")

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            with patch("builtins.open", side_effect=PermissionError("File is locked")):
                result = monitor._check_pid_file()
                assert (
                    result == monitor.pid
                )  # Should return current PID when file is locked

    def test_write_pid_file_success(self, monitor, temp_dir):
        """Test _write_pid_file successful write"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Ensure no existing PID file
        if pid_file.exists():
            try:
                pid_file.unlink()
            except PermissionError:
                pass

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            with patch.object(monitor, "_check_pid_file", return_value=None):
                result = monitor._write_pid_file()
                assert result is True
                assert pid_file.exists()
                assert pid_file.read_text().strip() == str(monitor.pid)

    def test_write_pid_file_existing_live_pid(self, monitor, temp_dir):
        """Test _write_pid_file when another instance is running"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            with patch.object(monitor, "_check_pid_file", return_value=54321):
                result = monitor._write_pid_file()
                assert result is False
                # Should not create new PID file when another instance is running
                if pid_file.exists():
                    try:
                        pid_file.unlink()
                    except PermissionError:
                        pass

    def test_write_pid_file_permission_error(self, monitor, temp_dir):
        """Test _write_pid_file with permission error"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            with patch.object(monitor, "_check_pid_file", return_value=None):
                with patch(
                    "builtins.open", side_effect=PermissionError("Access denied")
                ):
                    result = monitor._write_pid_file()
                    assert result is False

    def test_remove_pid_file_exists(self, monitor, temp_dir):
        """Test _remove_pid_file when PID file exists"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Create PID file
        try:
            pid_file.write_text("12345")
        except PermissionError:
            pytest.skip("Cannot create test file due to permissions")

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            monitor._remove_pid_file()
            assert not pid_file.exists()

    def test_remove_pid_file_not_exists(self, monitor, temp_dir):
        """Test _remove_pid_file when PID file doesn't exist"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Ensure PID file doesn't exist
        if pid_file.exists():
            try:
                pid_file.unlink()
            except PermissionError:
                pass

        # Should not raise an error
        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            monitor._remove_pid_file()
            assert not pid_file.exists()


class TestCursorRulesMonitorStatus:
    """Test cases for Cursor Rules Monitor status functionality"""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.fixture
    def monitor(self, temp_dir):
        """Create a CursorRulesMonitor instance for testing"""
        monitor = CursorRulesMonitor()
        return monitor

    def test_get_status_no_pid_file(self, monitor, temp_dir):
        """Test get_status when no PID file exists"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Ensure PID file doesn't exist
        if pid_file.exists():
            try:
                pid_file.unlink()
            except PermissionError:
                pass

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            status = monitor.get_status()
            assert status["is_monitoring"] is False
            assert status["status_message"] == "Monitor not running"

    def test_get_status_live_pid_file(self, monitor, temp_dir):
        """Test get_status when PID file exists and process is alive"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Create PID file with current PID
        try:
            pid_file.write_text(str(os.getpid()))
        except PermissionError:
            pytest.skip("Cannot create test file due to permissions")

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            with patch("psutil.pid_exists", return_value=True):
                with patch("psutil.Process") as mock_process:
                    mock_process.return_value.cmdline.return_value = [
                        "python",
                        "cursor_rules_monitor.py",
                    ]
                    status = monitor.get_status()
                    assert status["is_monitoring"] is True
                    assert (
                        status["status_message"]
                        == f"Monitor running (PID {os.getpid()})"
                    )

    def test_get_status_stale_pid_file(self, monitor, temp_dir):
        """Test get_status when PID file exists but process is dead"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Create PID file with non-existent PID
        try:
            pid_file.write_text("99999")
        except PermissionError:
            pytest.skip("Cannot create test file due to permissions")

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            with patch("psutil.pid_exists", return_value=False):
                status = monitor.get_status()
                assert status["is_monitoring"] is False
                assert (
                    status["status_message"]
                    == "Monitor not running (stale PID removed)"
                )
                assert not pid_file.exists()

    def test_get_status_empty_pid_file(self, monitor, temp_dir):
        """Test get_status when PID file is empty"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Create empty PID file
        try:
            pid_file.write_text("")
        except PermissionError:
            pytest.skip("Cannot create test file due to permissions")

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            status = monitor.get_status()
            assert status["is_monitoring"] is False
            assert status["status_message"] == "Monitor not running (empty PID removed)"
            assert not pid_file.exists()

    def test_get_status_corrupted_pid_file(self, monitor, temp_dir):
        """Test get_status when PID file is corrupted"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Create corrupted PID file
        try:
            pid_file.write_text("invalid_pid")
        except PermissionError:
            pytest.skip("Cannot create test file due to permissions")

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            status = monitor.get_status()
            assert status["is_monitoring"] is False
            assert (
                status["status_message"]
                == "Monitor not running (corrupted PID removed)"
            )
            assert not pid_file.exists()

    def test_get_status_locked_pid_file(self, monitor, temp_dir):
        """Test get_status when PID file is locked/in use"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Create PID file
        try:
            pid_file.write_text("12345")
        except PermissionError:
            pytest.skip("Cannot create test file due to permissions")

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            with patch("builtins.open", side_effect=PermissionError("File is locked")):
                status = monitor.get_status()
                assert status["is_monitoring"] is True
                assert (
                    status["status_message"]
                    == "PID file in use, monitor may still be running"
                )
                assert status["running_pid"] == monitor.pid

    def test_get_status_error_handling(self, monitor, temp_dir):
        """Test get_status error handling"""
        with patch("pathlib.Path", side_effect=Exception("Unexpected error")):
            status = monitor.get_status()
            assert status["is_monitoring"] is False
            assert "Error checking status" in status["status_message"]
            assert status["running_pid"] is None


class TestCursorRulesMonitorStartup:
    """Test cases for Cursor Rules Monitor startup functionality"""

    @pytest.fixture
    def temp_dir(self):
        """Create a temporary directory for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield Path(temp_dir)

    @pytest.fixture
    def monitor(self, temp_dir):
        """Create a CursorRulesMonitor instance for testing"""
        monitor = CursorRulesMonitor()
        return monitor

    def test_start_monitoring_no_pid_file(self, monitor, temp_dir):
        """Test start_monitoring when no PID file exists"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Ensure PID file doesn't exist
        if pid_file.exists():
            try:
                pid_file.unlink()
            except PermissionError:
                pass

        with patch.object(monitor, "_check_pid_file", return_value=None):
            with patch.object(monitor, "_write_pid_file", return_value=True):
                with patch.object(monitor, "_monitoring_loop"):
                    monitor.start_monitoring()
                    assert monitor.is_monitoring is True

    def test_start_monitoring_live_pid_file(self, monitor, temp_dir):
        """Test start_monitoring when another instance is running"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        with patch.object(monitor, "_check_pid_file", return_value=54321):
            monitor.start_monitoring()
            assert monitor.is_monitoring is False
            # Should not create PID file when another instance is running
            if pid_file.exists():
                try:
                    pid_file.unlink()
                except PermissionError:
                    pass

    def test_start_monitoring_pid_file_write_failure(self, monitor, temp_dir):
        """Test start_monitoring when PID file write fails"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        with patch.object(monitor, "_check_pid_file", return_value=None):
            with patch.object(monitor, "_write_pid_file", return_value=False):
                monitor.start_monitoring()
                assert monitor.is_monitoring is False
                # Should not create PID file when write fails
                if pid_file.exists():
                    try:
                        pid_file.unlink()
                    except PermissionError:
                        pass

    def test_stop_monitoring(self, monitor, temp_dir):
        """Test stop_monitoring"""
        pid_file = temp_dir / ".cursor_monitor.pid"

        # Create PID file
        try:
            pid_file.write_text(str(monitor.pid))
        except PermissionError:
            pytest.skip("Cannot create test file due to permissions")

        with patch("pathlib.Path") as mock_path:
            mock_path.return_value = pid_file
            monitor.stop_monitoring()
            assert monitor.is_monitoring is False
            assert not pid_file.exists()
