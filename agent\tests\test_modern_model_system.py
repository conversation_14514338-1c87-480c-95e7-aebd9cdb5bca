#!/usr/bin/env python3
"""
Test script for the modern model configuration system

This script tests:
1. YAML configuration loading
2. Model router initialization
3. Agent model assignment
4. Response generation
5. Fallback mechanisms
"""

import asyncio
import logging
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from agent.models.model_router import ModernModelRouter, get_model_router
from agent.core.agents.frontend_agent import FrontendAgent

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


async def test_yaml_config_loading():
    """Test YAML configuration loading"""
    print("🔍 Testing YAML Configuration Loading...")

    try:
        router = ModernModelRouter()
        print(f"✅ Successfully loaded {len(router.models)} models")
        print(f"✅ Successfully loaded {len(router.agents)} agents")
        print(f"✅ Cloud models enabled: {router.enable_cloud_models}")

        # Test model details
        for model_name, model_config in list(router.models.items())[:3]:
            print(f"   📋 {model_name}: {model_config.get('description', 'No description')}")

        return True
    except Exception as e:
        print(f"❌ Failed to load YAML configuration: {e}")
        return False


async def test_agent_model_assignment():
    """Test agent model assignment"""
    print("\n🤖 Testing Agent Model Assignment...")

    try:
        router = get_model_router()

        # Test FrontendAgent model assignment
        frontend_model = router.get_agent_model("frontend_agent")
        frontend_prompt = router.get_agent_system_prompt("frontend_agent")

        print(f"✅ FrontendAgent model: {frontend_model}")
        print(f"✅ FrontendAgent system prompt: {frontend_prompt[:100]}...")

        # Test other agents
        agents_to_test = ["backend_agent", "container_agent", "learning_agent"]
        for agent_name in agents_to_test:
            model = router.get_agent_model(agent_name)
            if model:
                print(f"   📋 {agent_name}: {model}")
            else:
                print(f"   ⚠️  {agent_name}: No model assigned")

        return True
    except Exception as e:
        print(f"❌ Failed to test agent model assignment: {e}")
        return False


async def test_model_selection():
    """Test model selection logic"""
    print("\n🎯 Testing Model Selection Logic...")

    try:
        router = get_model_router()

        # Test agent-based selection
        model = router.select_model(agent_name="frontend_agent")
        print(f"✅ FrontendAgent selected model: {model}")

        # Test task-based selection
        model = router.select_model(task_type="code_generation")
        print(f"✅ Code generation selected model: {model}")

        # Test complexity-based selection
        model = router.select_model(complexity_hint="high")
        print(f"✅ High complexity selected model: {model}")

        return True
    except Exception as e:
        print(f"❌ Failed to test model selection: {e}")
        return False


async def test_response_generation():
    """Test response generation"""
    print("\n🚀 Testing Response Generation...")

    try:
        router = get_model_router()

        # Test simple prompt with a model we know is available
        prompt = "Generate a simple React button component with TypeScript"

        # First, let's check what models are actually loaded
        print("   📋 Checking available models...")

        # Debug: Check what Ollama list returns
        debug_output = router._debug_ollama_list()
        print(f"   🔍 Debug Ollama list output: {debug_output[:200]}...")

        available_models = []
        for model_name in router.models:
            if router._is_model_loaded_in_ollama(model_name):
                available_models.append(model_name)
                print(f"      ✅ {model_name} is loaded")
            else:
                print(f"      ❌ {model_name} is not loaded")

        if not available_models:
            print("   ⚠️  No models are loaded in Ollama")
            return False

        # Use the first available model
        test_model = available_models[0]
        print(f"   🎯 Using model: {test_model}")

        response = await router.generate_response(
            prompt=prompt,
            model_name=test_model,
            task_type="code_generation"
        )

        if response.get("success", False):
            print(f"✅ Response generated successfully")
            print(f"   📋 Model used: {response.get('model', 'Unknown')}")
            print(f"   ⏱️  Response time: {response.get('response_time', 0):.2f}s")
            content = response.get("content", "")
            print(f"   📄 Content preview: {content[:200]}...")
        else:
            print(f"❌ Response generation failed: {response.get('error', 'Unknown error')}")
            return False

        return True
    except Exception as e:
        print(f"❌ Failed to test response generation: {e}")
        return False


async def test_frontend_agent():
    """Test FrontendAgent with new model system"""
    print("\n🎨 Testing FrontendAgent...")

    try:
        # Initialize FrontendAgent
        agent = FrontendAgent()
        print(f"✅ FrontendAgent initialized with model: {agent.model_name}")

        # Check if the assigned model is loaded
        if not agent.model_router._is_model_loaded_in_ollama(agent.model_name):
            print(f"   ⚠️  Assigned model {agent.model_name} is not loaded, trying fallback...")
            # Try to find an available model
            available_models = []
            for model_name in agent.model_router.models:
                if agent.model_router._is_model_loaded_in_ollama(model_name):
                    available_models.append(model_name)

            if available_models:
                # Use the first available model
                agent.model_name = available_models[0]
                print(f"   🎯 Using fallback model: {agent.model_name}")
            else:
                print("   ❌ No models are loaded in Ollama")
                return False

        # Test task parsing
        task_description = "Create a modern portfolio website with React and Tailwind CSS"

        requirements = await agent._parse_task_requirements(task_description)

        if requirements.get("parsed", False):
            print(f"✅ Task requirements parsed successfully")
            print(f"   📋 Pages: {requirements.get('pages', [])}")
            print(f"   📋 Components: {requirements.get('components', [])}")
        else:
            print(f"❌ Task requirements parsing failed: {requirements.get('error', 'Unknown error')}")
            return False

        return True
    except Exception as e:
        print(f"❌ Failed to test FrontendAgent: {e}")
        return False


async def test_health_monitoring():
    """Test health monitoring"""
    print("\n🏥 Testing Health Monitoring...")

    try:
        router = get_model_router()

        # Get health for all models
        health_info = router.get_all_model_health()

        print(f"✅ Health monitoring working")
        for model_name, health in list(health_info.items())[:3]:
            status = health.get("health", "unknown")
            success_rate = health.get("success_rate", 0)
            print(f"   📋 {model_name}: {status} (success rate: {success_rate:.2f})")

        return True
    except Exception as e:
        print(f"❌ Failed to test health monitoring: {e}")
        return False


async def main():
    """Main test function"""
    print("🚀 Testing Modern Model Configuration System")
    print("=" * 50)

    tests = [
        ("YAML Configuration Loading", test_yaml_config_loading),
        ("Agent Model Assignment", test_agent_model_assignment),
        ("Model Selection Logic", test_model_selection),
        ("Response Generation", test_response_generation),
        ("FrontendAgent Integration", test_frontend_agent),
        ("Health Monitoring", test_health_monitoring),
    ]

    results = []

    for test_name, test_func in tests:
        try:
            result = await test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")

    passed = 0
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {test_name}")
        if result:
            passed += 1

    print(f"\n🎯 Overall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Modern model system is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the configuration and setup.")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
