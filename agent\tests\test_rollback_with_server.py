#!/usr/bin/env python3
"""
Test script for rollback testing with a proper server setup
"""

import asyncio
import json
import shutil
import sys
import tempfile
from pathlib import Path

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agent.core.home_server import RollbackManager


async def create_test_flask_app():
    """Create a simple Flask app for testing"""
    flask_app_content = """
from flask import Flask, jsonify

app = Flask(__name__)

@app.route('/health')
def health():
    return jsonify({"status": "healthy", "service": "test-flask-app"})

@app.route('/ide')
def ide():
    return jsonify({"status": "available", "type": "test-ide"})

@app.route('/')
def home():
    return "<h1>Test Flask App</h1><p>This is a test Flask app for rollback testing.</p>"

if __name__ == '__main__':
    app.run(host='127.0.0.1', port=8000, debug=False)
"""

    # Create a temporary directory for the test Flask app
    temp_dir = Path(tempfile.mkdtemp())

    # Create the Flask app file
    app_file = temp_dir / "app.py"
    with open(app_file, "w") as f:
        f.write(flask_app_content)

    # Create requirements.txt
    requirements_file = temp_dir / "requirements.txt"
    with open(requirements_file, "w") as f:
        f.write("flask==3.0.0\n")

    return temp_dir


async def test_rollback_with_flask():
    """Test rollback functionality with a Flask server"""
    print("🧪 Testing Rollback with Flask Server")
    print("=" * 50)

    try:
        # Create test Flask app
        flask_dir = await create_test_flask_app()
        print(f"✅ Created test Flask app in: {flask_dir}")

        # Create deployment metadata
        deployment_metadata = {
            "site": "test-flask-app",
            "timestamp": "20250725_160000",
            "deployment_path": str(flask_dir),
            "source": str(flask_dir),
        }

        # Initialize test manager
        test_manager = RollbackManager()
        print("✅ RollbackTestManager initialized")

        # Test the deployment
        print("🔄 Testing Flask deployment...")
        result = await test_manager._test_deployment(deployment_metadata)

        print(f"📊 Test Results:")
        print(f"   Overall Status: {result.get('overall_status', 'unknown')}")
        print(f"   Port Used: {result.get('port', 'unknown')}")
        print(f"   Endpoints Tested: {len(result.get('endpoints_tested', []))}")

        if result.get("overall_status") == "passed":
            print("✅ Flask deployment test PASSED")

            # Show endpoint results
            for endpoint_test in result.get("endpoints_tested", []):
                endpoint = endpoint_test.get("endpoint", "unknown")
                status = endpoint_test.get("status", "unknown")
                response_code = endpoint_test.get("response_code", "unknown")
                print(f"   {endpoint}: {status} (HTTP {response_code})")
        else:
            print("❌ Flask deployment test FAILED")
            print(f"   Error: {result.get('error', 'Unknown error')}")

        # Clean up
        try:
            shutil.rmtree(flask_dir)
            print("✅ Cleaned up test Flask app")
        except Exception as e:
            print(f"⚠️  Failed to clean up: {e}")

        return result.get("overall_status") == "passed"

    except Exception as e:
        print(f"❌ Flask rollback test failed: {e}")
        return False


async def main():
    """Main test function"""
    print("🚀 Testing Rollback with Flask Server")
    print("=" * 60)

    success = await test_rollback_with_flask()

    if success:
        print("\n🎉 Flask rollback test PASSED!")
        print("The rollback test system is working correctly with Flask servers.")
    else:
        print("\n❌ Flask rollback test FAILED!")
        print("There may be issues with the rollback test system.")

    return success


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
