#!/usr/bin/env python3
"""
Test script for Robustness Features
Tests recovery, validation, monitoring, and CLI/API functionality.
"""

import asyncio
import json
import logging
import sys
import time
from datetime import datetime
from pathlib import Path

# Configure UTF-8 encoding for Windows
if hasattr(sys.stdout, "reconfigure"):
    sys.stdout.reconfigure(encoding="utf-8", errors="replace")

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class RobustnessFeatureTest:
    """Test suite for robustness features"""

    def __init__(self):
        self.test_results = {}
        self.start_time = time.time()

    async def run_all_tests(self):
        """Run all robustness feature tests"""
        logger.info("🚀 Starting Robustness Features Test Suite")

        tests = [
            ("Recovery System", self.test_recovery_system),
            ("Validation System", self.test_validation_system),
            ("Monitoring System", self.test_monitoring_system),
            ("CLI Commands", self.test_cli_commands),
            ("API Routes", self.test_api_routes),
            ("Integration", self.test_integration),
        ]

        for test_name, test_func in tests:
            try:
                logger.info(f"📋 Running {test_name} test...")
                result = await test_func()
                self.test_results[test_name] = result
                status = "✅ PASSED" if result["success"] else "❌ FAILED"
                logger.info(f"{status} {test_name}: {result['message']}")
            except Exception as e:
                logger.error(f"❌ {test_name} test failed with exception: {e}")
                self.test_results[test_name] = {
                    "success": False,
                    "message": f"Exception: {str(e)}",
                    "error": str(e),
                }

        await self.generate_report()

    async def test_recovery_system(self) -> dict:
        """Test recovery system functionality"""
        try:
            from agent.core.recovery_system import FailureType, get_recovery_system

            recovery_system = get_recovery_system()

            # Test system health
            health = recovery_system.get_system_health()

            # Test failure history
            history = recovery_system.get_failure_history(1)

            # Test manual recovery (simulated)
            success = await recovery_system.manual_recovery(
                FailureType.FILE_CORRUPTION, {"file_path": "test_file.txt"}
            )

            return {
                "success": True,
                "message": "Recovery system functional",
                "details": {
                    "health_status": health.get("status"),
                    "failure_history_count": len(history),
                    "manual_recovery_available": True,
                },
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"Recovery system test failed: {e}",
                "error": str(e),
            }

    async def test_validation_system(self) -> dict:
        """Test validation system functionality"""
        try:
            from agent.core.validation import get_validation_system

            validation_system = get_validation_system()

            # Test system health
            health = validation_system.get_system_health()

            # Test component validation
            result = await validation_system.validate_specific_component("file_system")

            # Test validation history
            history = validation_system.get_validation_history(1)

            return {
                "success": True,
                "message": "Validation system functional",
                "details": {
                    "health_status": health.get("status"),
                    "component_validation_success": result.success,
                    "validation_history_count": len(history),
                },
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"Validation system test failed: {e}",
                "error": str(e),
            }

    async def test_monitoring_system(self) -> dict:
        """Test monitoring system functionality"""
        try:
            from agent.core.monitoring_enhanced import (
                get_metrics_collector,
                get_monitoring_dashboard,
            )

            metrics_collector = get_metrics_collector()
            dashboard = get_monitoring_dashboard()

            # Test current metrics
            current_metrics = metrics_collector.get_current_metrics()

            # Test metrics summary
            summary = metrics_collector.get_metrics_summary(1)

            # Test dashboard data
            dashboard_data = dashboard.get_dashboard_data()

            # Test alerts summary
            alerts_summary = dashboard.get_alerts_summary()

            return {
                "success": True,
                "message": "Monitoring system functional",
                "details": {
                    "current_metrics_available": current_metrics.get("system")
                    is not None,
                    "summary_available": "error" not in summary,
                    "dashboard_available": "error" not in dashboard_data,
                    "alerts_available": "error" not in alerts_summary,
                },
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"Monitoring system test failed: {e}",
                "error": str(e),
            }

    async def test_cli_commands(self) -> dict:
        """Test CLI commands functionality"""
        try:
            # Create a mock agent for testing
            class MockAgent:
                def __init__(self):
                    pass

            from agent.cli.robustness_commands import RobustnessCommands

            commands = RobustnessCommands(MockAgent())

            # Test system validation
            validation_result = await commands.run_system_validation()

            # Test health check
            health_result = await commands.check_system_health()

            # Test monitoring dashboard
            dashboard_result = await commands.get_monitoring_dashboard()

            return {
                "success": True,
                "message": "CLI commands functional",
                "details": {
                    "validation_command": validation_result.get("success", False),
                    "health_command": health_result.get("success", False),
                    "dashboard_command": dashboard_result.get("success", False),
                },
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"CLI commands test failed: {e}",
                "error": str(e),
            }

    async def test_api_routes(self) -> dict:
        """Test API routes functionality"""
        try:
            # Test if API routes can be imported
            from agent.api.robustness_routes import router

            # Check if router has expected endpoints
            routes = [route.path for route in router.routes]
            expected_routes = [
                "/robustness/health",
                "/robustness/validate",
                "/robustness/validate/component",
                "/robustness/recovery/manual",
                "/robustness/recovery/history",
                "/robustness/monitoring/start",
                "/robustness/monitoring/stop",
                "/robustness/monitoring/dashboard",
                "/robustness/monitoring/alerts",
                "/robustness/monitoring/metrics",
                "/robustness/test",
                "/robustness/status",
            ]

            found_routes = [
                route for route in expected_routes if any(route in r for r in routes)
            ]

            return {
                "success": len(found_routes)
                >= len(expected_routes) * 0.8,  # 80% of routes found
                "message": f"API routes available ({len(found_routes)}/{len(expected_routes)})",
                "details": {
                    "total_routes": len(routes),
                    "expected_routes": len(expected_routes),
                    "found_routes": len(found_routes),
                    "missing_routes": len(expected_routes) - len(found_routes),
                },
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"API routes test failed: {e}",
                "error": str(e),
            }

    async def test_integration(self) -> dict:
        """Test integration between robustness systems"""
        try:
            # Test if all systems can work together
            from agent.core.monitoring_enhanced import get_metrics_collector
            from agent.core.recovery_system import get_recovery_system
            from agent.core.validation import get_validation_system

            recovery_system = get_recovery_system()
            validation_system = get_validation_system()
            metrics_collector = get_metrics_collector()

            # Test that all systems are available
            systems_available = all(
                [
                    recovery_system is not None,
                    validation_system is not None,
                    metrics_collector is not None,
                ]
            )

            if not systems_available:
                return {
                    "success": False,
                    "message": "Not all robustness systems are available",
                    "details": {
                        "recovery_system": recovery_system is not None,
                        "validation_system": validation_system is not None,
                        "monitoring_system": metrics_collector is not None,
                    },
                }

            # Test basic functionality of each system
            recovery_health = recovery_system.get_system_health()
            validation_health = validation_system.get_system_health()
            monitoring_metrics = metrics_collector.get_current_metrics()

            return {
                "success": True,
                "message": "All robustness systems integrated successfully",
                "details": {
                    "recovery_health": recovery_health.get("status"),
                    "validation_health": validation_health.get("status"),
                    "monitoring_active": monitoring_metrics.get(
                        "monitoring_active", False
                    ),
                },
            }

        except Exception as e:
            return {
                "success": False,
                "message": f"Integration test failed: {e}",
                "error": str(e),
            }

    async def generate_report(self):
        """Generate test report"""
        end_time = time.time()
        duration = end_time - self.start_time

        # Calculate overall success
        total_tests = len(self.test_results)
        passed_tests = sum(
            1 for result in self.test_results.values() if result.get("success", False)
        )
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        # Generate report
        report = {
            "test_suite": "Robustness Features",
            "timestamp": datetime.now().isoformat(),
            "duration_seconds": round(duration, 2),
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": round(success_rate, 1),
            "results": self.test_results,
        }

        # Save report
        report_file = f"test_reports/robustness_features_test_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        Path(report_file).parent.mkdir(parents=True, exist_ok=True)

        with open(report_file, "w") as f:
            json.dump(report, f, indent=2)

        # Print summary
        logger.info("📊 Test Report Summary:")
        logger.info(f"   Duration: {duration:.2f} seconds")
        logger.info(f"   Total Tests: {total_tests}")
        logger.info(f"   Passed: {passed_tests}")
        logger.info(f"   Failed: {total_tests - passed_tests}")
        logger.info(f"   Success Rate: {success_rate:.1f}%")
        logger.info(f"   Report saved to: {report_file}")

        if success_rate >= 80:
            logger.info("🎉 Robustness Features Test Suite: EXCELLENT")
        elif success_rate >= 60:
            logger.info("✅ Robustness Features Test Suite: GOOD")
        elif success_rate >= 40:
            logger.info("⚠️  Robustness Features Test Suite: NEEDS IMPROVEMENT")
        else:
            logger.info("❌ Robustness Features Test Suite: CRITICAL ISSUES")

        return report


async def main():
    """Main test function"""
    try:
        test_suite = RobustnessFeatureTest()
        await test_suite.run_all_tests()
    except Exception as e:
        logger.error(f"Test suite failed: {e}")
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
