#!/usr/bin/env python3
"""
Tests for ValidationPipeline - Quality gates and validation
"""
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

import pytest

from agent.core.quality.validation_pipeline import (
    ValidationLevel,
    ValidationPipeline,
    ValidationResult,
    ValidationStatus,
    ValidationSuite,
)


@pytest.fixture
def temp_project_dir():
    with tempfile.TemporaryDirectory() as tmpdir:
        project_dir = Path(tmpdir)
        
        # Create basic project structure
        (project_dir / "core").mkdir()
        (project_dir / "tests").mkdir()
        (project_dir / "config").mkdir()
        (project_dir / "logs").mkdir()
        
        # Create some Python files
        (project_dir / "core" / "__init__.py").write_text("")
        (project_dir / "core" / "main.py").write_text("def hello():\n    return 'world'")
        (project_dir / "tests" / "test_main.py").write_text("def test_hello():\n    assert True")
        (project_dir / "README.md").write_text("# Test Project")
        
        yield str(project_dir)


@pytest.fixture
def validation_pipeline(temp_project_dir):
    return ValidationPipeline(
        project_root=temp_project_dir,
        config_path="config/validation_config.json"
    )


def test_validation_result_creation():
    """Test ValidationResult creation and properties"""
    result = ValidationResult(
        check_name="test_check",
        status=ValidationStatus.PASSED,
        level=ValidationLevel.INFO,
        message="Test passed",
        details={"key": "value"},
        duration=1.5
    )
    
    assert result.check_name == "test_check"
    assert result.status == ValidationStatus.PASSED
    assert result.level == ValidationLevel.INFO
    assert result.message == "Test passed"
    assert result.details == {"key": "value"}
    assert result.duration == 1.5


def test_validation_suite_properties():
    """Test ValidationSuite properties and calculations"""
    suite = ValidationSuite(suite_name="test_suite")
    
    # Add some results
    suite.results.append(ValidationResult(
        check_name="check1",
        status=ValidationStatus.PASSED,
        level=ValidationLevel.INFO,
        message="Passed"
    ))
    
    suite.results.append(ValidationResult(
        check_name="check2",
        status=ValidationStatus.FAILED,
        level=ValidationLevel.ERROR,
        message="Failed"
    ))
    
    suite.results.append(ValidationResult(
        check_name="check3",
        status=ValidationStatus.PASSED,
        level=ValidationLevel.WARNING,
        message="Passed with warning"
    ))
    
    assert suite.passed_count == 2
    assert suite.failed_count == 1
    assert suite.overall_status == ValidationStatus.FAILED  # Has error-level failure


def test_validation_suite_overall_status():
    """Test ValidationSuite overall status calculation"""
    suite = ValidationSuite(suite_name="test_suite")
    
    # Empty suite
    assert suite.overall_status == ValidationStatus.PENDING
    
    # All passed
    suite.results.append(ValidationResult(
        check_name="check1",
        status=ValidationStatus.PASSED,
        level=ValidationLevel.INFO,
        message="Passed"
    ))
    assert suite.overall_status == ValidationStatus.PASSED
    
    # Warning-level failure (should still pass)
    suite.results.append(ValidationResult(
        check_name="check2",
        status=ValidationStatus.FAILED,
        level=ValidationLevel.WARNING,
        message="Warning failure"
    ))
    assert suite.overall_status == ValidationStatus.PASSED
    
    # Error-level failure (should fail)
    suite.results.append(ValidationResult(
        check_name="check3",
        status=ValidationStatus.FAILED,
        level=ValidationLevel.ERROR,
        message="Error failure"
    ))
    assert suite.overall_status == ValidationStatus.FAILED


def test_validation_pipeline_initialization(validation_pipeline, temp_project_dir):
    """Test ValidationPipeline initialization"""
    assert validation_pipeline.project_root == Path(temp_project_dir)
    assert validation_pipeline.config is not None
    assert validation_pipeline.artifacts_dir.exists()
    assert validation_pipeline.current_suite is None


def test_validation_pipeline_config_loading(temp_project_dir):
    """Test configuration loading with custom config"""
    config_file = Path(temp_project_dir) / "custom_config.json"
    config_file.write_text('{"test_key": "test_value"}')
    
    pipeline = ValidationPipeline(
        project_root=temp_project_dir,
        config_path=str(config_file)
    )
    
    assert pipeline.config["test_key"] == "test_value"


def test_validation_pipeline_default_config(temp_project_dir):
    """Test default configuration when config file doesn't exist"""
    pipeline = ValidationPipeline(
        project_root=temp_project_dir,
        config_path="nonexistent_config.json"
    )
    
    # Should have default configuration
    assert "lint_checks" in pipeline.config
    assert "security_checks" in pipeline.config
    assert "test_validation" in pipeline.config


@pytest.mark.asyncio
@patch('asyncio.create_subprocess_exec')
async def test_flake8_check_success(mock_subprocess, validation_pipeline):
    """Test successful flake8 check"""
    # Mock successful flake8 execution
    mock_process = AsyncMock()
    mock_process.communicate.return_value = (b"", b"")
    mock_process.returncode = 0
    mock_subprocess.return_value = mock_process
    
    validation_pipeline.current_suite = ValidationSuite("test")
    
    await validation_pipeline._run_flake8()
    
    assert len(validation_pipeline.current_suite.results) == 1
    result = validation_pipeline.current_suite.results[0]
    assert result.check_name == "flake8"
    assert result.status == ValidationStatus.PASSED
    assert result.level == ValidationLevel.INFO


@pytest.mark.asyncio
@patch('asyncio.create_subprocess_exec')
async def test_flake8_check_failure(mock_subprocess, validation_pipeline):
    """Test failed flake8 check"""
    # Mock failed flake8 execution
    mock_process = AsyncMock()
    mock_process.communicate.return_value = (b"./test.py:1:1: E302 expected 2 blank lines", b"")
    mock_process.returncode = 1
    mock_subprocess.return_value = mock_process
    
    validation_pipeline.current_suite = ValidationSuite("test")
    
    await validation_pipeline._run_flake8()
    
    assert len(validation_pipeline.current_suite.results) == 1
    result = validation_pipeline.current_suite.results[0]
    assert result.check_name == "flake8"
    assert result.status == ValidationStatus.FAILED
    assert result.level == ValidationLevel.ERROR


@pytest.mark.asyncio
@patch('asyncio.create_subprocess_exec')
async def test_mypy_check_success(mock_subprocess, validation_pipeline):
    """Test successful mypy check"""
    # Mock successful mypy execution
    mock_process = AsyncMock()
    mock_process.communicate.return_value = (b"Success: no issues found", b"")
    mock_process.returncode = 0
    mock_subprocess.return_value = mock_process
    
    validation_pipeline.current_suite = ValidationSuite("test")
    
    await validation_pipeline._run_mypy()
    
    assert len(validation_pipeline.current_suite.results) == 1
    result = validation_pipeline.current_suite.results[0]
    assert result.check_name == "mypy"
    assert result.status == ValidationStatus.PASSED
    assert result.level == ValidationLevel.INFO


@pytest.mark.asyncio
async def test_project_structure_review(validation_pipeline):
    """Test project structure review"""
    validation_pipeline.current_suite = ValidationSuite("test")
    
    await validation_pipeline._review_project_structure()
    
    assert len(validation_pipeline.current_suite.results) == 1
    result = validation_pipeline.current_suite.results[0]
    assert result.check_name == "project_structure"
    # Should pass because temp_project_dir fixture creates required directories
    assert result.status == ValidationStatus.PASSED


@pytest.mark.asyncio
async def test_code_quality_review(validation_pipeline):
    """Test code quality metrics review"""
    validation_pipeline.current_suite = ValidationSuite("test")
    
    await validation_pipeline._review_code_quality()
    
    assert len(validation_pipeline.current_suite.results) == 1
    result = validation_pipeline.current_suite.results[0]
    assert result.check_name == "code_quality_metrics"
    assert result.status == ValidationStatus.PASSED
    assert "python_files" in result.details
    assert "total_lines" in result.details


@pytest.mark.asyncio
async def test_documentation_review(validation_pipeline):
    """Test documentation review"""
    validation_pipeline.current_suite = ValidationSuite("test")
    
    await validation_pipeline._review_documentation()
    
    assert len(validation_pipeline.current_suite.results) == 1
    result = validation_pipeline.current_suite.results[0]
    assert result.check_name == "documentation"
    # Should pass because temp_project_dir fixture creates README.md
    assert result.status == ValidationStatus.PASSED


@pytest.mark.asyncio
async def test_test_pattern_validation(validation_pipeline):
    """Test test pattern validation"""
    validation_pipeline.current_suite = ValidationSuite("test")
    
    await validation_pipeline._validate_test_patterns()
    
    assert len(validation_pipeline.current_suite.results) == 1
    result = validation_pipeline.current_suite.results[0]
    assert result.check_name == "test_patterns"
    # Should pass because temp_project_dir fixture creates test files
    assert result.status == ValidationStatus.PASSED


@pytest.mark.asyncio
async def test_full_validation_suite(validation_pipeline):
    """Test running a complete validation suite"""
    # Mock subprocess calls to avoid actual tool execution
    with patch('asyncio.create_subprocess_exec') as mock_subprocess:
        # Mock all subprocess calls to return success
        mock_process = AsyncMock()
        mock_process.communicate.return_value = (b"", b"")
        mock_process.returncode = 0
        mock_subprocess.return_value = mock_process
        
        # Mock psutil for memory check
        with patch('psutil.Process') as mock_psutil:
            mock_process_info = MagicMock()
            mock_process_info.memory_info.return_value.rss = 50 * 1024 * 1024  # 50MB
            mock_psutil.return_value = mock_process_info
            
            result = await validation_pipeline.run_validation_suite("full_test")
    
    assert result.suite_name == "full_test"
    assert result.start_time is not None
    assert result.end_time is not None
    assert len(result.results) > 0
    
    # Check that various validation checks were run
    check_names = [r.check_name for r in result.results]
    assert "project_structure" in check_names
    assert "code_quality_metrics" in check_names
    assert "documentation" in check_names


def test_blocking_failure_detection(validation_pipeline):
    """Test blocking failure detection"""
    validation_pipeline.current_suite = ValidationSuite("test")
    
    # Add non-blocking failure
    validation_pipeline.current_suite.results.append(ValidationResult(
        check_name="warning_check",
        status=ValidationStatus.FAILED,
        level=ValidationLevel.WARNING,
        message="Warning failure"
    ))
    
    assert not validation_pipeline.is_blocking_failure()
    
    # Add blocking failure
    validation_pipeline.current_suite.results.append(ValidationResult(
        check_name="error_check",
        status=ValidationStatus.FAILED,
        level=ValidationLevel.ERROR,
        message="Error failure"
    ))
    
    assert validation_pipeline.is_blocking_failure()


def test_validation_summary(validation_pipeline):
    """Test validation summary generation"""
    validation_pipeline.current_suite = ValidationSuite("test")
    
    # Add some results
    validation_pipeline.current_suite.results.append(ValidationResult(
        check_name="check1",
        status=ValidationStatus.PASSED,
        level=ValidationLevel.INFO,
        message="Passed"
    ))
    
    validation_pipeline.current_suite.results.append(ValidationResult(
        check_name="check2",
        status=ValidationStatus.FAILED,
        level=ValidationLevel.ERROR,
        message="Failed"
    ))
    
    summary = validation_pipeline.get_validation_summary()
    
    assert summary["suite_name"] == "test"
    assert summary["passed_count"] == 1
    assert summary["failed_count"] == 1
    assert summary["total_checks"] == 2
    assert summary["blocking_failure"] is True
    assert len(summary["failed_checks"]) == 1
