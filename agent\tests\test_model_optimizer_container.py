#!/usr/bin/env python3
"""
Test Model Optimizer Container
Comprehensive test suite for model optimizer containerization with GPU support
"""

import asyncio
import json
import logging
import subprocess
import time
from pathlib import Path
from typing import Any, Dict, List

import aiohttp

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class ModelOptimizerContainerTester:
    """Test suite for model optimizer container with GPU support"""

    def __init__(self):
        self.base_url = "http://localhost:8087"
        self.timeout = 30
        self.test_results = []
        self.container_name = "ai-coding-model-optimizer"

    def log_test(
        self, test_name: str, passed: bool, message: str, details: Dict[str, Any] = None
    ):
        """Log test result"""
        status = "✅ PASSED" if passed else "❌ FAILED"
        logger.info(f"{status} - {test_name}: {message}")

        self.test_results.append(
            {
                "test": test_name,
                "passed": passed,
                "message": message,
                "details": details or {},
            }
        )

    def test_dockerfile_exists(self) -> bool:
        """Test if Dockerfile.model-optimizer exists"""
        try:
            dockerfile_path = Path("containers/Dockerfile.model-optimizer")
            if dockerfile_path.exists():
                content = dockerfile_path.read_text()

                # Check for required elements
                has_from = "FROM python:3.11-slim" in content
                has_user = "modeloptimizer" in content
                has_healthcheck = "HEALTHCHECK" in content
                has_expose = "EXPOSE 8087" in content
                has_entrypoint = "ENTRYPOINT" in content
                has_gpu_env = "CUDA_VISIBLE_DEVICES" in content

                self.log_test(
                    "Dockerfile.model-optimizer Exists",
                    has_from
                    and has_user
                    and has_healthcheck
                    and has_expose
                    and has_entrypoint
                    and has_gpu_env,
                    "Multi-stage build with GPU support, security, health checks, and proper resource management",
                    {
                        "has_from": has_from,
                        "has_user": has_user,
                        "has_healthcheck": has_healthcheck,
                        "has_expose": has_expose,
                        "has_entrypoint": has_entrypoint,
                        "has_gpu_env": has_gpu_env,
                    },
                )
                return (
                    has_from
                    and has_user
                    and has_healthcheck
                    and has_expose
                    and has_entrypoint
                    and has_gpu_env
                )
            else:
                self.log_test(
                    "Dockerfile.model-optimizer Exists", False, "Dockerfile not found"
                )
                return False
        except Exception as e:
            self.log_test(
                "Dockerfile.model-optimizer Exists", False, f"Error: {str(e)}"
            )
            return False

    def test_docker_compose_integration(self) -> bool:
        """Test docker-compose.yml integration"""
        try:
            compose_path = Path("docker-compose.yml")
            if compose_path.exists():
                content = compose_path.read_text()

                # Find model optimizer service section
                service_section_start = content.find(
                    "  # Model Optimizer with GPU Support"
                )
                if service_section_start != -1:
                    # Find the end of the service section (next service or end of file)
                    next_service_start = content.find("  # ", service_section_start + 1)
                    if next_service_start == -1:
                        service_section = content[service_section_start:]
                    else:
                        service_section = content[
                            service_section_start:next_service_start
                        ]

                    # Look for required elements
                    has_build = "build:" in service_section
                    has_ports = "8087:8087" in service_section
                    has_healthcheck = "healthcheck:" in service_section
                    has_restart = "restart: unless-stopped" in service_section
                    has_volumes = "volumes:" in service_section
                    has_deploy = "deploy:" in service_section
                    has_gpu_env = "NVIDIA_VISIBLE_DEVICES" in service_section
                    has_gpu_devices = "driver: nvidia" in service_section

                    self.log_test(
                        "Docker Compose Integration",
                        has_build
                        and has_ports
                        and has_healthcheck
                        and has_restart
                        and has_volumes
                        and has_deploy
                        and has_gpu_env
                        and has_gpu_devices,
                        "Model optimizer service integrated with GPU support, health checks, and resource limits",
                        {
                            "has_build": has_build,
                            "has_ports": has_ports,
                            "has_healthcheck": has_healthcheck,
                            "has_restart": has_restart,
                            "has_volumes": has_volumes,
                            "has_deploy": has_deploy,
                            "has_gpu_env": has_gpu_env,
                            "has_gpu_devices": has_gpu_devices,
                        },
                    )
                    return (
                        has_build
                        and has_ports
                        and has_healthcheck
                        and has_restart
                        and has_volumes
                        and has_deploy
                        and has_gpu_env
                        and has_gpu_devices
                    )
                else:
                    self.log_test(
                        "Docker Compose Integration",
                        False,
                        "Model optimizer service not found in docker-compose.yml",
                    )
                    return False
            else:
                self.log_test(
                    "Docker Compose Integration", False, "docker-compose.yml not found"
                )
                return False
        except Exception as e:
            self.log_test("Docker Compose Integration", False, f"Error: {str(e)}")
            return False

    def test_model_optimizer_configuration_file(self) -> bool:
        """Test model optimizer configuration file"""
        try:
            config_path = Path("config/model_optimizer_docker_config.json")
            if config_path.exists():
                with open(config_path, "r") as f:
                    config = json.load(f)

                # Check for required sections
                has_service_config = "model_optimizer_service" in config
                has_container_config = "container" in config
                has_dependencies = "dependencies" in config
                has_components = "model_optimizer_components" in config
                has_gpu_config = "gpu_configuration" in config
                has_monitoring = "monitoring" in config
                has_api_endpoints = "api_endpoints" in config

                # Check specific values
                port = config.get("model_optimizer_service", {}).get("port", 0)
                gpu_memory = (
                    config.get("gpu_configuration", {})
                    .get("hardware", {})
                    .get("memory", "")
                )

                self.log_test(
                    "Model Optimizer Configuration File",
                    has_service_config
                    and has_container_config
                    and has_dependencies
                    and has_components
                    and has_gpu_config
                    and has_monitoring
                    and has_api_endpoints
                    and port == 8087
                    and "4GB" in gpu_memory,
                    "Complete configuration structure with GPU support and all required sections",
                    {
                        "has_service_config": has_service_config,
                        "has_container_config": has_container_config,
                        "has_dependencies": has_dependencies,
                        "has_components": has_components,
                        "has_gpu_config": has_gpu_config,
                        "has_monitoring": has_monitoring,
                        "has_api_endpoints": has_api_endpoints,
                        "port": port,
                        "gpu_memory": gpu_memory,
                    },
                )
                return (
                    has_service_config
                    and has_container_config
                    and has_dependencies
                    and has_components
                    and has_gpu_config
                    and has_monitoring
                    and has_api_endpoints
                    and port == 8087
                    and "4GB" in gpu_memory
                )
            else:
                self.log_test(
                    "Model Optimizer Configuration File",
                    False,
                    "Configuration file not found",
                )
                return False
        except Exception as e:
            self.log_test(
                "Model Optimizer Configuration File", False, f"Error: {str(e)}"
            )
            return False

    def test_cli_commands(self) -> bool:
        """Test CLI commands implementation"""
        try:
            cli_path = Path("cli/model_optimizer_commands.py")
            if cli_path.exists():
                with open(cli_path, "r") as f:
                    content = f.read()

                # Check for required methods
                required_methods = [
                    "check_model_optimizer_status",
                    "get_optimization_summary",
                    "get_optimization_jobs",
                    "get_optimization_components",
                    "get_gpu_status",
                    "test_model_optimizer_components",
                    "optimize_model",
                    "analyze_model_performance",
                    "compress_model",
                    "get_optimization_metrics",
                    "export_optimization_data",
                ]

                missing_methods = []
                for method in required_methods:
                    if method not in content:
                        missing_methods.append(method)

                has_command_mapping = "MODEL_OPTIMIZER_COMMANDS" in content

                self.log_test(
                    "CLI Commands",
                    len(missing_methods) == 0 and has_command_mapping,
                    f"CLI commands implemented ({len(required_methods) - len(missing_methods)}/{len(required_methods)})",
                    {
                        "total_methods": len(required_methods),
                        "missing_methods": missing_methods,
                        "has_command_mapping": has_command_mapping,
                    },
                )
                return len(missing_methods) == 0 and has_command_mapping
            else:
                self.log_test("CLI Commands", False, "CLI commands file not found")
                return False
        except Exception as e:
            self.log_test("CLI Commands", False, f"Error: {str(e)}")
            return False

    def test_api_routes(self) -> bool:
        """Test API routes implementation"""
        try:
            api_path = Path("api/model_optimizer_routes.py")
            if api_path.exists():
                with open(api_path, "r") as f:
                    content = f.read()

                # Check for required endpoints
                required_endpoints = [
                    "/status",
                    "/optimization/summary",
                    "/optimization/jobs",
                    "/optimization/components",
                    "/gpu/status",
                    "/optimization/optimize",
                    "/optimization/analyze-performance",
                    "/optimization/compress",
                    "/metrics",
                    "/export",
                    "/health",
                ]

                missing_endpoints = []
                for endpoint in required_endpoints:
                    if endpoint not in content:
                        missing_endpoints.append(endpoint)

                has_router = "APIRouter" in content
                has_pydantic_models = "BaseModel" in content

                self.log_test(
                    "API Routes",
                    len(missing_endpoints) == 0 and has_router and has_pydantic_models,
                    f"API routes implemented ({len(required_endpoints) - len(missing_endpoints)}/{len(required_endpoints)})",
                    {
                        "total_endpoints": len(required_endpoints),
                        "missing_endpoints": missing_endpoints,
                        "has_router": has_router,
                        "has_pydantic_models": has_pydantic_models,
                    },
                )
                return (
                    len(missing_endpoints) == 0 and has_router and has_pydantic_models
                )
            else:
                self.log_test("API Routes", False, "API routes file not found")
                return False
        except Exception as e:
            self.log_test("API Routes", False, f"Error: {str(e)}")
            return False

    def test_resource_limits(self) -> bool:
        """Test resource limits configuration"""
        try:
            compose_path = Path("docker-compose.yml")
            if compose_path.exists():
                content = compose_path.read_text()

                # Find model optimizer service section
                service_section_start = content.find(
                    "  # Model Optimizer with GPU Support"
                )
                if service_section_start != -1:
                    # Find the end of the service section
                    next_service_start = content.find("  # ", service_section_start + 1)
                    if next_service_start == -1:
                        service_section = content[service_section_start:]
                    else:
                        service_section = content[
                            service_section_start:next_service_start
                        ]

                    # Check for resource limits
                    has_cpu_limit = "cpus: '2.0'" in service_section
                    has_memory_limit = "memory: 4G" in service_section
                    has_deploy_section = "deploy:" in service_section
                    has_healthcheck = "healthcheck:" in service_section
                    has_restart_policy = "restart: unless-stopped" in service_section
                    has_volumes = "volumes:" in service_section
                    has_gpu_memory = "memory: 4GB" in service_section

                    self.log_test(
                        "Resource Limits",
                        has_cpu_limit
                        and has_memory_limit
                        and has_deploy_section
                        and has_healthcheck
                        and has_restart_policy
                        and has_volumes
                        and has_gpu_memory,
                        "Resource limits properly configured with GPU support",
                        {
                            "has_cpu_limit": has_cpu_limit,
                            "has_memory_limit": has_memory_limit,
                            "has_deploy_section": has_deploy_section,
                            "has_healthcheck": has_healthcheck,
                            "has_restart_policy": has_restart_policy,
                            "has_volumes": has_volumes,
                            "has_gpu_memory": has_gpu_memory,
                        },
                    )
                    return (
                        has_cpu_limit
                        and has_memory_limit
                        and has_deploy_section
                        and has_healthcheck
                        and has_restart_policy
                        and has_volumes
                        and has_gpu_memory
                    )
                else:
                    self.log_test(
                        "Resource Limits", False, "Model optimizer service not found"
                    )
                    return False
            else:
                self.log_test("Resource Limits", False, "docker-compose.yml not found")
                return False
        except Exception as e:
            self.log_test("Resource Limits", False, f"Error: {str(e)}")
            return False

    def test_service_discovery(self) -> bool:
        """Test service discovery configuration"""
        try:
            compose_path = Path("docker-compose.yml")
            if compose_path.exists():
                content = compose_path.read_text()

                # Find model optimizer service section
                service_section_start = content.find(
                    "  # Model Optimizer with GPU Support"
                )
                if service_section_start != -1:
                    # Find the end of the service section
                    next_service_start = content.find("  # ", service_section_start + 1)
                    if next_service_start == -1:
                        service_section = content[service_section_start:]
                    else:
                        service_section = content[
                            service_section_start:next_service_start
                        ]

                    # Check for network and dependencies
                    has_network = "ai-coding-network" in service_section
                    has_dependencies = "depends_on:" in service_section
                    has_api_dependency = "api:" in service_section
                    has_db_dependency = "db:" in service_section
                    has_redis_dependency = "redis:" in service_section
                    has_ollama_dependency = "ollama:" in service_section

                    self.log_test(
                        "Service Discovery",
                        has_network
                        and has_dependencies
                        and has_api_dependency
                        and has_db_dependency
                        and has_redis_dependency
                        and has_ollama_dependency,
                        "Service discovery properly configured with network and dependencies",
                        {
                            "has_network": has_network,
                            "has_dependencies": has_dependencies,
                            "has_api_dependency": has_api_dependency,
                            "has_db_dependency": has_db_dependency,
                            "has_redis_dependency": has_redis_dependency,
                            "has_ollama_dependency": has_ollama_dependency,
                        },
                    )
                    return (
                        has_network
                        and has_dependencies
                        and has_api_dependency
                        and has_db_dependency
                        and has_redis_dependency
                        and has_ollama_dependency
                    )
                else:
                    self.log_test(
                        "Service Discovery", False, "Model optimizer service not found"
                    )
                    return False
            else:
                self.log_test(
                    "Service Discovery", False, "docker-compose.yml not found"
                )
                return False
        except Exception as e:
            self.log_test("Service Discovery", False, f"Error: {str(e)}")
            return False

    async def test_container_build(self) -> bool:
        """Test container build"""
        try:
            # Clean up any existing container
            subprocess.run(["docker", "stop", self.container_name], capture_output=True)
            subprocess.run(["docker", "rm", self.container_name], capture_output=True)

            # Build the container
            result = subprocess.run(
                [
                    "docker",
                    "build",
                    "-f",
                    "containers/Dockerfile.model-optimizer",
                    "-t",
                    "ai-coding-model-optimizer:test",
                    ".",
                ],
                capture_output=True,
                text=True,
            )

            build_success = result.returncode == 0

            self.log_test(
                "Container Build",
                build_success,
                (
                    "Container built successfully"
                    if build_success
                    else f"Build failed: {result.stderr}"
                ),
                {
                    "return_code": result.returncode,
                    "stdout_lines": len(result.stdout.split("\n")),
                    "stderr_lines": len(result.stderr.split("\n")),
                },
            )
            return build_success
        except Exception as e:
            self.log_test("Container Build", False, f"Error: {str(e)}")
            return False

    async def test_container_startup(self) -> bool:
        """Test container startup and health check"""
        try:
            # Clean up any existing container
            subprocess.run(["docker", "stop", self.container_name], capture_output=True)
            subprocess.run(["docker", "rm", self.container_name], capture_output=True)

            # Start the container
            subprocess.run(
                [
                    "docker",
                    "run",
                    "-d",
                    "--name",
                    self.container_name,
                    "-p",
                    "8087:8087",
                    "--network",
                    "ai-coding-network",
                    "ai-coding-model-optimizer:test",
                ],
                capture_output=True,
            )

            # Wait for container to start
            await asyncio.sleep(15)

            # Check container status
            status_result = subprocess.run(
                [
                    "docker",
                    "ps",
                    "--filter",
                    f"name={self.container_name}",
                    "--format",
                    "{{.Status}}",
                ],
                capture_output=True,
                text=True,
            )

            container_running = "Up" in status_result.stdout

            # Test health check
            health_result = subprocess.run(
                [
                    "docker",
                    "inspect",
                    self.container_name,
                    "--format",
                    "{{.State.Health.Status}}",
                ],
                capture_output=True,
                text=True,
            )

            health_status = health_result.stdout.strip()
            health_ok = "healthy" in health_status

            # Test API health endpoint
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.get(
                        f"{self.base_url}/health", timeout=10
                    ) as response:
                        api_health_ok = response.status == 200
            except:
                api_health_ok = False

            self.log_test(
                "Container Startup and Health Check",
                container_running and health_ok and api_health_ok,
                (
                    f"Container started and health check passed"
                    if container_running and health_ok and api_health_ok
                    else f"Container status: {container_running}, Health: {health_ok}, API: {api_health_ok}"
                ),
                {
                    "container_status": (
                        "running" if container_running else "not running"
                    ),
                    "health_status": health_status,
                    "api_health": "ok" if api_health_ok else "failed",
                },
            )
            return container_running and health_ok and api_health_ok
        except Exception as e:
            self.log_test(
                "Container Startup and Health Check", False, f"Error: {str(e)}"
            )
            return False

    async def test_api_endpoints(self) -> bool:
        """Test API endpoints"""
        try:
            # Start a fresh container for API testing
            subprocess.run(["docker", "stop", self.container_name], capture_output=True)
            subprocess.run(["docker", "rm", self.container_name], capture_output=True)

            subprocess.run(
                [
                    "docker",
                    "run",
                    "-d",
                    "--name",
                    self.container_name,
                    "-p",
                    "8087:8087",
                    "--network",
                    "ai-coding-network",
                    "ai-coding-model-optimizer:test",
                ],
                capture_output=True,
            )

            # Wait for container to be ready
            await asyncio.sleep(15)

            endpoints_to_test = [
                "/health",
                "/status",
                "/optimization/summary",
                "/optimization/jobs",
                "/optimization/components",
                "/gpu/status",
            ]

            successful_endpoints = 0
            endpoint_results = {}

            async with aiohttp.ClientSession() as session:
                for endpoint in endpoints_to_test:
                    try:
                        async with session.get(
                            f"{self.base_url}{endpoint}", timeout=10
                        ) as response:
                            if response.status == 200:
                                successful_endpoints += 1
                                endpoint_results[endpoint] = {
                                    "status_code": response.status,
                                    "success": True,
                                }
                            else:
                                endpoint_results[endpoint] = {
                                    "status_code": response.status,
                                    "success": False,
                                }
                    except Exception as e:
                        logger.warning(f"Failed to test endpoint {endpoint}: {str(e)}")
                        endpoint_results[endpoint] = {
                            "status_code": 0,
                            "success": False,
                        }

            # Clean up test container
            subprocess.run(["docker", "stop", self.container_name], capture_output=True)
            subprocess.run(["docker", "rm", self.container_name], capture_output=True)

            all_endpoints_ok = successful_endpoints == len(endpoints_to_test)

            self.log_test(
                "API Endpoints",
                all_endpoints_ok,
                f"API endpoints tested ({successful_endpoints}/{len(endpoints_to_test)} successful)",
                {
                    "successful_endpoints": successful_endpoints,
                    "total_endpoints": len(endpoints_to_test),
                    "endpoint_results": endpoint_results,
                },
            )
            return all_endpoints_ok
        except Exception as e:
            self.log_test("API Endpoints", False, f"Error: {str(e)}")
            return False

    async def run_all_tests(self):
        """Run all tests"""
        logger.info("🧪 Starting Model Optimizer Container Tests...")
        logger.info("=" * 60)

        # Run tests
        tests = [
            ("Dockerfile.model-optimizer Exists", self.test_dockerfile_exists),
            ("Docker Compose Integration", self.test_docker_compose_integration),
            (
                "Model Optimizer Configuration File",
                self.test_model_optimizer_configuration_file,
            ),
            ("CLI Commands", self.test_cli_commands),
            ("API Routes", self.test_api_routes),
            ("Resource Limits", self.test_resource_limits),
            ("Service Discovery", self.test_service_discovery),
            ("Container Build", self.test_container_build),
            ("Container Startup and Health Check", self.test_container_startup),
            ("API Endpoints", self.test_api_endpoints),
        ]

        for test_name, test_func in tests:
            try:
                if asyncio.iscoroutinefunction(test_func):
                    await test_func()
                else:
                    test_func()
            except Exception as e:
                self.log_test(test_name, False, f"Test failed with exception: {str(e)}")

        # Print summary
        logger.info("=" * 60)
        logger.info("📊 TEST SUMMARY")
        logger.info("=" * 60)

        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["passed"])
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        logger.info(f"Total Tests: {total_tests}")
        logger.info(f"Passed: {passed_tests} ✅")
        logger.info(f"Failed: {failed_tests} ❌")
        logger.info(f"Success Rate: {success_rate:.1f}%")

        # Save detailed results
        results_file = "test_results_model_optimizer_container.json"
        with open(results_file, "w") as f:
            json.dump(
                {
                    "test_summary": {
                        "total_tests": total_tests,
                        "passed_tests": passed_tests,
                        "failed_tests": failed_tests,
                        "success_rate": success_rate,
                    },
                    "test_results": self.test_results,
                },
                f,
                indent=2,
            )

        logger.info(f"📄 Detailed results saved to: {results_file}")

        if success_rate == 100:
            logger.info("🎉 All model optimizer container tests passed!")
        else:
            logger.warning(
                f"⚠️ {failed_tests} test(s) failed. Please review the results."
            )

        return success_rate == 100


async def main():
    """Main test runner"""
    tester = ModelOptimizerContainerTester()
    success = await tester.run_all_tests()
    return success


if __name__ == "__main__":
    asyncio.run(main())
