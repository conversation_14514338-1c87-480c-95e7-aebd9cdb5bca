#!/usr/bin/env python3
"""
Container Migration Demo
Demonstrates how to apply database migrations via SiteContainerManager
"""

import asyncio
import json
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from agent.core.site_container_manager import SiteContainerManager, SiteContainer, ContainerStatus
from datetime import datetime


async def demo_container_migrations():
    """Demonstrate container migration functionality"""
    
    print("🚀 Container Migration Demo")
    print("=" * 50)
    
    # Initialize SiteContainerManager
    container_manager = SiteContainerManager()
    site_name = "demo_site"
    
    print(f"\n📦 Managing migrations for site: {site_name}")
    
    # Create a mock container (in real usage, this would be created via create_site_container)
    mock_container = SiteContainer(
        site_name=site_name,
        container_name=f"site-{site_name}",
        port=8080,
        status=ContainerStatus.RUNNING,  # Assume container is running
        image_name=f"ai-coding-site-{site_name}",
        created_at=datetime.now()
    )
    
    container_manager.site_containers[site_name] = mock_container
    print(f"✅ Container registered: {mock_container.container_name}")
    
    # Demo 1: Execute a simple command in container
    print(f"\n🔧 Demo 1: Execute command in container")
    
    # This would normally execute in the actual container
    # For demo purposes, we'll show the command structure
    test_command = ["python", "-c", "print('Hello from container!')"]
    print(f"Command to execute: {' '.join(test_command)}")
    
    # In real usage:
    # result = await container_manager.execute_command_in_container(
    #     site_name, test_command, "/app"
    # )
    
    print("✅ Command structure validated")
    
    # Demo 2: Apply database migrations
    print(f"\n🗄️  Demo 2: Apply database migrations")
    
    migration_config = {
        "database_url": f"sqlite:///app/data/{site_name}.db",
        "migrations_dir": "/app/migrations",
        "python_path": "python"
    }
    
    print(f"Migration config:")
    print(json.dumps(migration_config, indent=2))
    
    # In real usage with running container:
    # result = await container_manager.apply_database_migrations(
    #     site_name, migration_config
    # )
    
    print("✅ Migration configuration prepared")
    
    # Demo 3: Show API usage
    print(f"\n🌐 Demo 3: API Usage Examples")
    
    print("To apply migrations via API:")
    print("POST /api/migration/container/apply")
    print(json.dumps({
        "site_name": site_name,
        "migration_config": migration_config
    }, indent=2))
    
    print("\nTo execute command via API:")
    print("POST /api/migration/container/execute")
    print(json.dumps({
        "site_name": site_name,
        "command": ["python", "--version"],
        "working_dir": "/app"
    }, indent=2))
    
    print("\nTo initialize migrations via API:")
    print("POST /api/migration/container/init")
    print(json.dumps({
        "site_name": site_name,
        "database_url": f"sqlite:///app/data/{site_name}.db"
    }, indent=2))
    
    # Demo 4: Show CLI usage
    print(f"\n💻 Demo 4: CLI Usage Examples")
    
    print("CLI commands for container migrations:")
    print(f"# Apply migrations to container")
    print(f"ai-cli migration apply-container-migrations --site-name {site_name}")
    
    print(f"\n# Execute command in container")
    print(f"ai-cli migration execute-container-command --site-name {site_name} --command 'python --version'")
    
    print(f"\n# Initialize migrations in container")
    print(f"ai-cli migration init-container-migrations --site-name {site_name}")
    
    # Demo 5: Show workflow
    print(f"\n🔄 Demo 5: Complete Workflow")
    
    workflow_steps = [
        "1. Create site container via SiteContainerManager",
        "2. Start the container",
        "3. Initialize migration environment in container",
        "4. Generate migrations (if needed)",
        "5. Apply migrations to container database",
        "6. Verify migration status",
        "7. Monitor container health"
    ]
    
    print("Complete migration workflow:")
    for step in workflow_steps:
        print(f"   {step}")
    
    print(f"\n✅ Container migration demo completed!")
    print("=" * 50)
    
    return {
        "success": True,
        "site_name": site_name,
        "container_name": mock_container.container_name,
        "migration_config": migration_config
    }


async def demo_real_migration_runner():
    """Demo the actual migration runner functionality"""
    
    print("\n🔧 Bonus: Real Migration Runner Demo")
    print("-" * 40)
    
    import tempfile
    from agent.core.db.migration_runner import MigrationRunner
    
    # Create temporary project
    temp_dir = tempfile.mkdtemp()
    site_name = "demo_migration_site"
    
    try:
        # Initialize migration runner
        migration_runner = MigrationRunner(
            project_name=site_name,
            projects_root=temp_dir
        )
        
        print(f"✅ Migration runner initialized for: {site_name}")
        
        # Initialize migrations
        init_result = migration_runner.init_migrations()
        print(f"✅ Migrations initialized: {init_result.success}")
        
        # Generate a migration
        gen_result = migration_runner.generate_migration("Demo migration", auto_generate=False)
        print(f"✅ Migration generated: {gen_result.success}")
        if gen_result.revision:
            print(f"   Revision: {gen_result.revision}")
        
        # Apply migrations
        apply_result = migration_runner.apply_migrations()
        print(f"✅ Migrations applied: {apply_result.success}")
        
        # Get database info
        db_info = migration_runner.get_database_info()
        print(f"✅ Database info: {db_info.get('table_count', 0)} tables")
        
        return True
        
    except Exception as e:
        print(f"❌ Error in migration demo: {e}")
        return False
        
    finally:
        # Cleanup
        import shutil
        try:
            shutil.rmtree(temp_dir)
        except:
            pass


async def main():
    """Run the complete demo"""
    
    # Run container migration demo
    demo_result = await demo_container_migrations()
    
    # Run real migration runner demo
    migration_result = await demo_real_migration_runner()
    
    print(f"\n🎯 Demo Summary")
    print("-" * 30)
    print(f"Container demo: {'✅ Success' if demo_result['success'] else '❌ Failed'}")
    print(f"Migration demo: {'✅ Success' if migration_result else '❌ Failed'}")
    
    if demo_result['success'] and migration_result:
        print(f"\n🎉 All demos completed successfully!")
        print(f"Site: {demo_result['site_name']}")
        print(f"Container: {demo_result['container_name']}")
    
    return demo_result['success'] and migration_result


if __name__ == "__main__":
    # Run the demo
    result = asyncio.run(main())
    
    print(f"\n{'🎉 Demo completed successfully!' if result else '❌ Demo failed'}")
    exit(0 if result else 1)
