"""
Simple Tests for ArchitectAgent Bonus Features
Basic verification that bonus features are implemented and working
"""

import asyncio
from datetime import datetime, timedelta

import pytest

from agent.core.agents.architect_agent import ArchitectAgent, TaskPriority
from agent.core.learning_integration import LearningIntegration, TaskOutcome
from agent.core.retry_manager import ClarificationType, clarification_manager, retry_manager
from agent.core.task_queue import TaskPriority as QueueTaskPriority
from agent.core.task_queue import task_queue_manager


@pytest.mark.asyncio
async def test_bonus_features_implementation():
    """Test that all bonus features are implemented and accessible"""

    # Create agent
    agent = ArchitectAgent("config/architect_agent_config.json")

    try:
        # Test 1: Deferred task scheduling
        scheduled_at = datetime.now() + timedelta(minutes=5)
        result = await agent.schedule_deferred_task(
            user_command="Test deferred task",
            scheduled_at=scheduled_at,
            priority=TaskPriority.MEDIUM,
        )
        assert result["success"] is True
        assert "task_id" in result
        assert "queue_task_id" in result

        # Test 2: Queue status
        status = await agent.get_queue_status()
        assert "queued_tasks" in status
        assert "running_tasks" in status
        assert "completed_tasks" in status

        # Test 3: Pending clarifications
        clarifications = await agent.get_pending_clarifications()
        assert clarifications["success"] is True
        assert "clarifications" in clarifications
        assert "count" in clarifications

        # Test 4: Learning insights
        insights = await agent.get_learning_insights()
        assert isinstance(insights, dict)

        # Test 5: Pattern recommendations
        recommendations = await agent.get_pattern_recommendations("Create a website")
        assert recommendations["success"] is True
        assert "recommendations" in recommendations

        # Test 6: Learn from feedback
        feedback_result = await agent.learn_from_feedback(
            "test_task_123", {"satisfaction": 5, "feedback": "Great work!"}
        )
        assert feedback_result["success"] is True

        # Test 7: Retry manager
        should_retry = await retry_manager.should_retry(
            "test_task", "frontend", Exception("Connection timeout")
        )
        assert isinstance(should_retry, bool)

        # Test 8: Clarification manager
        request_id = await clarification_manager.request_clarification(
            task_id="test_task",
            clarification_type=ClarificationType.MISSING_INFORMATION,
            message="Test clarification",
            timeout_minutes=30,
        )
        assert isinstance(request_id, str)

        # Test 9: Task queue manager
        queue_task_id = await task_queue_manager.add_task(
            name="Test Task",
            description="Test description",
            executor_func=lambda: None,
            priority=QueueTaskPriority.MEDIUM,
        )
        assert isinstance(queue_task_id, str)

        # Test 10: Learning integration
        outcome = TaskOutcome(
            task_id="test_task_456",
            task_type="frontend",
            success=True,
            duration_minutes=10.0,
            agent_used="frontend",
            completion_time=datetime.now(),
        )
        await agent.learning_integration.record_task_outcome(outcome)

        print("✅ All bonus features are implemented and working!")

    finally:
        await agent.shutdown()


@pytest.mark.asyncio
async def test_bonus_features_cli_commands():
    """Test that CLI commands for bonus features work"""

    # Test CLI command structure
    from agent.cli.bonus_features_commands import BonusFeaturesCommands

    agent = ArchitectAgent("config/architect_agent_config.json")
    commands = BonusFeaturesCommands(agent)

    # Test scheduling command
    result = await commands.schedule_task("Test command", 0, "medium")
    assert isinstance(result, dict)

    # Test queue status command
    status = await commands.get_queue_status()
    assert isinstance(status, dict)

    # Test clarifications command
    clarifications = await commands.get_clarifications()
    assert isinstance(clarifications, dict)

    # Test insights command
    insights = await commands.get_learning_insights()
    assert isinstance(insights, dict)

    # Test recommendations command
    recommendations = await commands.get_recommendations("Create a website")
    assert isinstance(recommendations, dict)

    # Test feedback command
    feedback_result = await commands.provide_feedback("test_task", 5, "Great work!")
    assert isinstance(feedback_result, dict)

    await agent.shutdown()

    print("✅ All CLI commands for bonus features are working!")


if __name__ == "__main__":
    asyncio.run(test_bonus_features_implementation())
    asyncio.run(test_bonus_features_cli_commands())
    print("🎉 All bonus features tests passed!")
