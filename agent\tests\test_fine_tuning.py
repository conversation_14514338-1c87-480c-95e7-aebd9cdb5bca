#!/usr/bin/env python3
"""
Test script for the fine-tuning pipeline
This script demonstrates how to use the fine-tuning system with sample data
"""

import json
import os
import subprocess
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))

from docs.fine_tuning.data_preprocessor import DataPreprocessor
from docs.fine_tuning.evaluator import EvaluationConfig, ModelEvaluator
from docs.fine_tuning.trainer import FineTuningConfig, FineTuningTrainer


def create_test_dataset():
    """Create a small test dataset for demonstration"""
    dataset_path = Path("data/fine_tuning/test_dataset.jsonl")
    dataset_path.parent.mkdir(parents=True, exist_ok=True)

    test_data = [
        {
            "type": "code_generation",
            "prompt": "Write a Python function to add two numbers",
            "generated_code": "def add_numbers(a, b):\n    return a + b",
        },
        {
            "type": "code_generation",
            "prompt": "How do I create a list in Python?",
            "generated_code": "my_list = [1, 2, 3, 4, 5]",
        },
        {
            "type": "code_review",
            "code": "for i in range(10):\n    print(i)",
            "review": "This is a simple for loop that prints numbers 0-9. It's correct and follows Python conventions.",
        },
        {
            "type": "code_generation",
            "prompt": "Show me how to define a class in Python",
            "generated_code": "class MyClass:\n    def __init__(self):\n        self.value = 42",
        },
        {
            "type": "code_generation",
            "prompt": "How do I handle exceptions in Python?",
            "generated_code": "try:\n    # code that might raise an exception\n    pass\nexcept Exception as e:\n    print(f'Error: {e}')",
        },
    ]

    with open(dataset_path, "w") as f:
        for item in test_data:
            f.write(json.dumps(item) + "\n")

    print(f"Created test dataset at {dataset_path}")
    return str(dataset_path)


def test_data_preprocessing():
    """Test the data preprocessing step"""
    print("\n=== Testing Data Preprocessing ===")

    dataset_path = create_test_dataset()

    preprocessor = DataPreprocessor()
    results = preprocessor.prepare_for_training(
        input_file=dataset_path, output_dir="data/fine_tuning/test_processed"
    )

    print("Preprocessing results:")
    for key, value in results.items():
        print(f"  {key}: {value}")

    return results


def test_training_config():
    """Test training configuration creation"""
    print("\n=== Testing Training Configuration ===")

    config = FineTuningConfig(
        model_name="microsoft/DialoGPT-medium",
        train_file="data/fine_tuning/test_processed/train.jsonl",
        val_file="data/fine_tuning/test_processed/val.jsonl",
        output_dir="models/test_fine_tuned",
        num_epochs=1,  # Quick test
        batch_size=1,
        max_length=128,
        use_lora=True,
        lora_r=8,
        logging_steps=1,
    )

    print("Training configuration:")
    for attr in dir(config):
        if not attr.startswith("_"):
            value = getattr(config, attr)
            if not callable(value):
                print(f"  {attr}: {value}")

    return config


def test_evaluation_config():
    """Test evaluation configuration creation"""
    print("\n=== Testing Evaluation Configuration ===")

    config = EvaluationConfig(
        model_path="models/test_fine_tuned",
        test_file="data/fine_tuning/test_processed/test.jsonl",
        max_samples=5,
        metrics=["bleu", "rouge"],
        output_dir="evaluation_test_results",
    )

    print("Evaluation configuration:")
    for attr in dir(config):
        if not attr.startswith("_"):
            value = getattr(config, attr)
            if not callable(value):
                print(f"  {attr}: {value}")

    return config


def test_pipeline_cli():
    """Test the pipeline CLI commands"""
    print("\n=== Testing Pipeline CLI ===")

    # Test help command
    print("Testing CLI help...")
    result = subprocess.run(
        [sys.executable, "src/fine_tuning/pipeline.py", "--help"],
        capture_output=True,
        text=True,
    )

    if result.returncode == 0:
        print("✓ CLI help command works")
    else:
        print("✗ CLI help command failed")
        print(result.stderr)

    # Test individual commands help
    commands = ["preprocess", "train", "evaluate", "run"]
    for cmd in commands:
        result = subprocess.run(
            [sys.executable, "src/fine_tuning/pipeline.py", cmd, "--help"],
            capture_output=True,
            text=True,
        )

        if result.returncode == 0:
            print(f"✓ {cmd} command help works")
        else:
            print(f"✗ {cmd} command help failed")


def main():
    """Run all tests"""
    print("AI Coding Agent Fine-tuning System Test")
    print("=" * 50)

    # Check if required directories exist
    required_dirs = ["data/fine_tuning", "models", "evaluation_results"]

    for dir_path in required_dirs:
        Path(dir_path).mkdir(parents=True, exist_ok=True)

    try:
        # Test 1: Data preprocessing
        preprocessing_results = test_data_preprocessing()

        # Test 2: Training config
        training_config = test_training_config()

        # Test 3: Evaluation config
        evaluation_config = test_evaluation_config()

        # Test 4: CLI
        test_pipeline_cli()

        print("\n" + "=" * 50)
        print("✓ All tests completed successfully!")
        print("\nNext steps:")
        print("1. Install dependencies: pip install -r requirements-fine-tuning.txt")
        print(
            "2. Run preprocessing: python src/fine_tuning/pipeline.py preprocess --input data/fine_tuning/sample_dataset.jsonl --output data/fine_tuning/processed"
        )
        print(
            "3. Run training: python src/fine_tuning/pipeline.py train --train-file data/fine_tuning/processed/train.jsonl --val-file data/fine_tuning/processed/val.jsonl --output-dir models/my_model"
        )
        print(
            "4. Run evaluation: python src/fine_tuning/pipeline.py evaluate --model-path models/my_model --test-file data/fine_tuning/processed/test.jsonl"
        )
        print(
            "5. Run complete pipeline: python src/fine_tuning/pipeline.py run --config config/fine_tuning_config.json"
        )

    except Exception as e:
        print(f"\n✗ Test failed: {e}")
        import traceback

        traceback.print_exc()


if __name__ == "__main__":
    main()
