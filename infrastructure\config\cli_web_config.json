{"cli": {"command_structure": {"prefix": "ai-agent", "subcommands": {"cms": {"description": "Content Management System operations", "commands": ["create-content", "list-content", "update-content", "delete-content", "generate-content"]}, "maintenance": {"description": "Maintenance engine operations", "commands": ["check-links", "update-dependencies", "approve-updates", "maintenance-status", "run-maintenance"]}, "testing": {"description": "Testing harness operations", "commands": ["test-html", "test-css", "test-playwright", "test-deployment", "test-suite", "test-results"]}, "deployment": {"description": "Deployment operations", "commands": ["deploy", "rollback", "deployment-status", "list-deployments"]}, "generator": {"description": "Website generator operations", "commands": ["generate-site", "list-templates", "customize-theme"]}, "ssl": {"description": "SSL certificate operations", "commands": ["ssl-status", "ssl-renew", "ssl-install"]}, "security": {"description": "Security operations", "commands": ["security-scan", "security-audit", "security-status"]}, "system": {"description": "System operations", "commands": ["status", "health", "config", "logs"]}}}, "interactive_mode": {"enabled": true, "prompt": "🤖 > ", "history_file": "data/command_history.json", "max_history": 1000, "auto_complete": true, "syntax_highlighting": true}, "output_format": {"default": "json", "formats": ["json", "yaml", "table", "text"], "pretty_print": true, "color_output": true}}, "api": {"endpoints": {"base_url": "/api/v1", "endpoints": {"cms": {"base": "/cms", "routes": {"create_content": {"path": "/content", "method": "POST", "description": "Create new content"}, "list_content": {"path": "/content", "method": "GET", "description": "List content"}, "get_content": {"path": "/content/{content_id}", "method": "GET", "description": "Get specific content"}, "update_content": {"path": "/content/{content_id}", "method": "PUT", "description": "Update content"}, "delete_content": {"path": "/content/{content_id}", "method": "DELETE", "description": "Delete content"}, "generate_content": {"path": "/content/generate", "method": "POST", "description": "Generate AI content"}}}, "maintenance": {"base": "/maintenance", "routes": {"check_links": {"path": "/links/check", "method": "POST", "description": "Check for broken links"}, "update_dependencies": {"path": "/dependencies/update", "method": "POST", "description": "Update dependencies"}, "approve_updates": {"path": "/updates/approve", "method": "POST", "description": "Approve updates"}, "get_status": {"path": "/status", "method": "GET", "description": "Get maintenance status"}, "run_maintenance": {"path": "/run", "method": "POST", "description": "Run maintenance cycle"}}}, "testing": {"base": "/testing", "routes": {"test_html": {"path": "/html", "method": "POST", "description": "Run HTML validation tests"}, "test_css": {"path": "/css", "method": "POST", "description": "Run CSS validation tests"}, "test_playwright": {"path": "/playwright", "method": "POST", "description": "Run Playwright tests"}, "test_deployment": {"path": "/deployment", "method": "POST", "description": "Run deployment tests"}, "test_suite": {"path": "/suite", "method": "POST", "description": "Run comprehensive test suite"}, "get_results": {"path": "/results", "method": "GET", "description": "Get test results"}}}, "deployment": {"base": "/deployment", "routes": {"deploy": {"path": "/deploy", "method": "POST", "description": "Deploy a site"}, "rollback": {"path": "/rollback", "method": "POST", "description": "Rollback deployment"}, "get_status": {"path": "/status/{deployment_id}", "method": "GET", "description": "Get deployment status"}, "list_deployments": {"path": "/list", "method": "GET", "description": "List deployments"}}}, "generator": {"base": "/generator", "routes": {"generate_site": {"path": "/generate", "method": "POST", "description": "Generate a website"}, "list_templates": {"path": "/templates", "method": "GET", "description": "List available templates"}, "customize_theme": {"path": "/theme", "method": "POST", "description": "Customize theme"}}}, "ssl": {"base": "/ssl", "routes": {"check_status": {"path": "/status/{domain}", "method": "GET", "description": "Check SSL status"}, "renew_certificate": {"path": "/renew", "method": "POST", "description": "Renew SSL certificate"}, "install_certificate": {"path": "/install", "method": "POST", "description": "Install SSL certificate"}}}, "security": {"base": "/security", "routes": {"scan": {"path": "/scan", "method": "POST", "description": "Run security scan"}, "audit": {"path": "/audit", "method": "POST", "description": "Run security audit"}, "get_status": {"path": "/status", "method": "GET", "description": "Get security status"}}}, "system": {"base": "/system", "routes": {"get_status": {"path": "/status", "method": "GET", "description": "Get system status"}, "get_health": {"path": "/health", "method": "GET", "description": "Get system health"}, "get_config": {"path": "/config", "method": "GET", "description": "Get system configuration"}, "get_logs": {"path": "/logs", "method": "GET", "description": "Get system logs"}}}}}, "authentication": {"enabled": true, "methods": ["api_key", "jwt", "oauth2"], "api_key_header": "X-API-Key", "jwt_secret": "your-jwt-secret-here", "jwt_expiry": 3600, "rate_limiting": {"enabled": true, "requests_per_minute": 100, "burst_limit": 20}}, "cors": {"enabled": true, "allowed_origins": ["*"], "allowed_methods": ["GET", "POST", "PUT", "DELETE", "OPTIONS"], "allowed_headers": ["Content-Type", "Authorization", "X-API-Key"]}}, "error_codes": {"AGENT_ERROR": {"code": "AGENT_ERROR", "message": "General agent error", "http_status": 500}, "CONFIG_ERROR": {"code": "CONFIG_ERROR", "message": "Configuration error", "http_status": 500}, "DATABASE_ERROR": {"code": "DATABASE_ERROR", "message": "Database operation error", "http_status": 500}, "DEPLOYMENT_ERROR": {"code": "DEPLOYMENT_ERROR", "message": "Deployment operation error", "http_status": 500}, "CMS_ERROR": {"code": "CMS_ERROR", "message": "Content management error", "http_status": 500}, "MAINTENANCE_ERROR": {"code": "MAINTENANCE_ERROR", "message": "Maintenance operation error", "http_status": 500}, "TESTING_ERROR": {"code": "TESTING_ERROR", "message": "Testing operation error", "http_status": 500}, "SSL_ERROR": {"code": "SSL_ERROR", "message": "SSL certificate error", "http_status": 500}, "SECURITY_ERROR": {"code": "SECURITY_ERROR", "message": "Security operation error", "http_status": 500}, "MODEL_ERROR": {"code": "MODEL_ERROR", "message": "AI model error", "http_status": 500}, "VALIDATION_ERROR": {"code": "VALIDATION_ERROR", "message": "Data validation error", "http_status": 400}, "NETWORK_ERROR": {"code": "NETWORK_ERROR", "message": "Network connectivity error", "http_status": 503}, "PERMISSION_ERROR": {"code": "PERMISSION_ERROR", "message": "Permission or access error", "http_status": 403}, "NOT_FOUND_ERROR": {"code": "NOT_FOUND_ERROR", "message": "Resource not found", "http_status": 404}, "COMMAND_ERROR": {"code": "COMMAND_ERROR", "message": "Unknown or invalid command", "http_status": 400}, "INIT_ERROR": {"code": "INIT_ERROR", "message": "Initialization error", "http_status": 500}}, "logging": {"level": "INFO", "file": "logs/agent.log", "max_file_size": "10MB", "backup_count": 5, "format": "%(asctime)s - %(name)s - %(levelname)s - %(message)s", "date_format": "%Y-%m-%d %H:%M:%S", "handlers": {"file": {"enabled": true, "filename": "logs/agent.log", "max_bytes": 10485760, "backup_count": 5}, "console": {"enabled": true, "level": "INFO"}, "error_file": {"enabled": true, "filename": "logs/errors.log", "level": "ERROR"}}, "performance_logging": {"enabled": true, "slow_query_threshold": 1.0, "log_sql_queries": false}}, "feedback": {"enabled": true, "storage": {"feedback_file": "data/feedback.json", "history_file": "data/command_history.json", "max_history_size": 1000}, "collection": {"command_execution": true, "user_feedback": true, "error_reporting": true, "performance_metrics": true}}, "security": {"input_validation": {"enabled": true, "max_input_length": 10000, "sanitize_html": true, "validate_json": true}, "rate_limiting": {"enabled": true, "requests_per_minute": 100, "burst_limit": 20}, "authentication": {"require_auth": false, "api_key_required": false, "session_timeout": 3600}}, "performance": {"caching": {"enabled": true, "cache_ttl": 300, "max_cache_size": 100}, "async_operations": {"enabled": true, "max_concurrent_operations": 10, "timeout": 30}, "database": {"connection_pool_size": 10, "query_timeout": 30}}}