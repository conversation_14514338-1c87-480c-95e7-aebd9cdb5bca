#!/usr/bin/env python3
"""
Test script to verify security implementations are working
"""

import os
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent))


def test_mfa_manager():
    """Test MFA Manager implementation"""
    print("🔐 Testing MFA Manager...")

    try:
        from agent.security.mfa_manager import MFAManager

        # Test configuration
        config = {
            "mfa_db_path": "database/test_mfa.db",
            "mfa_issuer": "AI Coding Agent Test",
        }

        # Initialize MFA manager
        mfa_manager = MFAManager(config)

        # Test user ID
        test_user_id = 12345

        # Test getting MFA status
        status = mfa_manager.get_user_mfa_status(test_user_id)
        print(f"  ✅ MFA Status: {status}")

        # Test TOTP setup
        setup_result = mfa_manager.setup_totp(test_user_id)
        print(f"  ✅ TOTP Setup: {setup_result.get('success', False)}")

        # Test TOTP verification (with dummy token)
        verification = mfa_manager.verify_totp(test_user_id, "123456")
        print(f"  ✅ TOTP Verification: {verification}")

        return True

    except Exception as e:
        print(f"  ❌ MFA Manager test failed: {e}")
        return False


def test_oauth2_manager():
    """Test OAuth2 Manager implementation"""
    print("🔗 Testing OAuth2 Manager...")

    try:
        from agent.security.oauth2_manager import OAuth2Manager

        # Test configuration
        config = {
            "oauth2_db_path": "database/test_oauth2.db",
            "providers": {
                "google": {
                    "client_id": "test_client_id",
                    "client_secret": "test_client_secret",
                    "redirect_uri": "http://localhost:3000/auth/callback",
                }
            },
        }

        # Initialize OAuth2 manager
        oauth2_manager = OAuth2Manager(config)

        # Test getting authorization URL
        auth_url = oauth2_manager.get_authorization_url("google", "test_state")
        print(f"  ✅ Authorization URL: {auth_url is not None}")

        # Test provider status
        status = oauth2_manager.get_provider_status()
        print(f"  ✅ Provider Status: {status}")

        return True

    except Exception as e:
        print(f"  ❌ OAuth2 Manager test failed: {e}")
        return False


def test_audit_logger():
    """Test Advanced Audit Logger implementation"""
    print("📝 Testing Advanced Audit Logger...")

    try:
        from agent.security.audit_logger import AdvancedAuditLogger

        # Test configuration
        config = {"audit_db_path": "database/test_audit.db", "audit_retention_days": 30}

        # Initialize audit logger
        audit_logger = AdvancedAuditLogger(config)

        # Test logging an event
        event_details = {
            "action": "login_attempt",
            "source": "web_interface",
            "target_resource": "user_authentication",
            "outcome": "success",
        }

        success = audit_logger.log_event(
            event_type="authentication",
            user_id=12345,
            details=event_details,
            ip_address="*************",
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
        )
        print(f"  ✅ Event Logging: {success}")

        # Test getting audit trail
        audit_trail = audit_logger.get_audit_trail(user_id=12345, limit=10)
        print(f"  ✅ Audit Trail: {len(audit_trail)} events retrieved")

        # Test getting statistics
        stats = audit_logger.get_audit_statistics()
        print(f"  ✅ Audit Statistics: {stats.get('total_events', 0)} total events")

        return True

    except Exception as e:
        print(f"  ❌ Audit Logger test failed: {e}")
        return False


def test_compliance_checker():
    """Test Compliance Checker implementation"""
    print("📋 Testing Compliance Checker...")

    try:
        from agent.security.compliance_checker import ComplianceChecker

        # Test configuration
        config = {
            "compliance_db_path": "database/test_compliance.db",
            "frameworks": ["gdpr", "soc2", "iso27001"],
        }

        # Initialize compliance checker
        compliance_checker = ComplianceChecker(config)

        # Test GDPR compliance check
        gdpr_result = compliance_checker.check_gdpr_compliance()
        print(
            f"  ✅ GDPR Compliance: {gdpr_result.get('compliant', False)} (score: {gdpr_result.get('score', 0)})"
        )

        # Test SOC2 compliance check
        soc2_result = compliance_checker.check_soc2_compliance()
        print(
            f"  ✅ SOC2 Compliance: {soc2_result.get('compliant', False)} (score: {soc2_result.get('score', 0)})"
        )

        # Test ISO27001 compliance check
        iso_result = compliance_checker.check_iso27001_compliance()
        print(
            f"  ✅ ISO27001 Compliance: {iso_result.get('compliant', False)} (score: {iso_result.get('score', 0)})"
        )

        # Test comprehensive report
        report = compliance_checker.generate_compliance_report()
        print(f"  ✅ Compliance Report: {report.get('overall_score', 0)} overall score")

        return True

    except Exception as e:
        print(f"  ❌ Compliance Checker test failed: {e}")
        return False


def test_threat_detector():
    """Test Threat Detector implementation"""
    print("🛡️ Testing Threat Detector...")

    try:
        from agent.security.threat_detector import ThreatDetector

        # Test configuration
        config = {
            "threat_db_path": "database/test_threats.db",
            "threshold_scores": {
                "low": 0.3,
                "medium": 0.6,
                "high": 0.8,
                "critical": 0.9,
            },
        }

        # Initialize threat detector
        threat_detector = ThreatDetector(config)

        # Test normal request analysis
        normal_request = {
            "user_id": 12345,
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "path": "/api/users/profile",
            "method": "GET",
            "body": "",
            "headers": {"Content-Type": "application/json"},
        }

        normal_analysis = threat_detector.analyze_request(normal_request)
        print(
            f"  ✅ Normal Request Analysis: {normal_analysis.get('threat_score', 0)} threat score"
        )

        # Test malicious request analysis (SQL injection attempt)
        malicious_request = {
            "user_id": 12345,
            "ip_address": "*************",
            "user_agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            "path": "/api/users",
            "method": "POST",
            "body": "SELECT * FROM users WHERE id = 1 OR 1=1",
            "headers": {"Content-Type": "application/json"},
        }

        malicious_analysis = threat_detector.analyze_request(malicious_request)
        print(
            f"  ✅ Malicious Request Analysis: {malicious_analysis.get('threat_score', 0)} threat score"
        )

        # Test anomaly detection
        anomalies = threat_detector.detect_anomalies(12345)
        print(f"  ✅ Anomaly Detection: {len(anomalies)} anomalies detected")

        # Test threat statistics
        stats = threat_detector.get_threat_statistics()
        print(f"  ✅ Threat Statistics: {stats.get('total_threats', 0)} total threats")

        return True

    except Exception as e:
        print(f"  ❌ Threat Detector test failed: {e}")
        return False


def test_security_manager_integration():
    """Test Security Manager integration with all components"""
    print("🔒 Testing Security Manager Integration...")

    try:
        from agent.security.security_manager import SecurityManager

        # Test configuration
        config = {
            "database_path": "database/test_security.db",
            "mfa_db_path": "database/test_mfa.db",
            "oauth2_db_path": "database/test_oauth2.db",
            "audit_db_path": "database/test_audit.db",
            "compliance_db_path": "database/test_compliance.db",
            "threat_db_path": "database/test_threats.db",
        }

        # Initialize security manager
        security_manager = SecurityManager(config)

        # Test security status
        status = security_manager.get_security_status()
        print(f"  ✅ Security Status: {status.get('status', 'unknown')}")

        # Test security headers
        headers = security_manager.get_security_headers()
        print(f"  ✅ Security Headers: {len(headers)} headers generated")

        # Test request validation
        validation = security_manager.validate_request(
            ip_address="*************",
            user_agent="Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
            path="/api/test",
            method="GET",
        )
        print(f"  ✅ Request Validation: {validation.get('valid', False)}")

        return True

    except Exception as e:
        print(f"  ❌ Security Manager integration test failed: {e}")
        return False


def cleanup_test_databases():
    """Clean up test databases"""
    print("🧹 Cleaning up test databases...")

    test_dbs = [
        "database/test_mfa.db",
        "database/test_oauth2.db",
        "database/test_audit.db",
        "database/test_compliance.db",
        "database/test_threats.db",
        "database/test_security.db",
    ]

    for db_path in test_dbs:
        try:
            if os.path.exists(db_path):
                os.remove(db_path)
                print(f"  ✅ Removed {db_path}")
        except Exception as e:
            print(f"  ⚠️ Could not remove {db_path}: {e}")


def main():
    """Run all security implementation tests"""
    print("🚀 Starting Security Implementation Tests...")
    print("=" * 60)

    test_results = []

    # Run individual component tests
    test_results.append(("MFA Manager", test_mfa_manager()))
    test_results.append(("OAuth2 Manager", test_oauth2_manager()))
    test_results.append(("Audit Logger", test_audit_logger()))
    test_results.append(("Compliance Checker", test_compliance_checker()))
    test_results.append(("Threat Detector", test_threat_detector()))
    test_results.append(
        ("Security Manager Integration", test_security_manager_integration())
    )

    # Print results summary
    print("\n" + "=" * 60)
    print("📊 Test Results Summary:")
    print("=" * 60)

    passed = 0
    total = len(test_results)

    for test_name, result in test_results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{test_name:<30} {status}")
        if result:
            passed += 1

    print("=" * 60)
    print(f"Overall Result: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All security implementations are working correctly!")
        return True
    else:
        print("⚠️ Some security implementations need attention.")
        return False


if __name__ == "__main__":
    try:
        success = main()
        if success:
            print(
                "\n✅ TODO-002: Complete Security Manager Implementations - COMPLETED!"
            )
        else:
            print("\n❌ TODO-002: Some issues remain to be fixed.")
    except Exception as e:
        print(f"\n💥 Test execution failed: {e}")
        sys.exit(1)
