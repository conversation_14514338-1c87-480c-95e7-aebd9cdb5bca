#!/usr/bin/env python3
"""
Test Threat Detection Container
Comprehensive test suite for the containerized threat detection engine

Tests:
- Container build and startup
- Health check functionality
- API endpoints
- Threat detection components integration
- Resource limits and monitoring
- Data persistence
- Service discovery
"""

import asyncio
import json
import subprocess
import sys
import time
from pathlib import Path
from typing import Any, Dict, List

import aiohttp
import docker

# Add project root to path for imports
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


class ThreatDetectionContainerTester:
    """Test suite for threat detection container"""

    def __init__(self):
        self.test_results = []
        self.total_tests = 0
        self.passed_tests = 0
        self.failed_tests = 0
        self.container_name = "ai-coding-threat-detection"
        self.service_url = "http://localhost:8085"
        self.docker_client = docker.from_env()

    def log_test(
        self, test_name: str, success: bool, message: str = "", details: Dict = None
    ):
        """Log test result"""
        self.total_tests += 1
        if success:
            self.passed_tests += 1
            status = "✅ PASSED"
        else:
            self.failed_tests += 1
            status = "❌ FAILED"

        result = {
            "test": test_name,
            "status": status,
            "success": success,
            "message": message,
            "details": details or {},
        }

        self.test_results.append(result)
        print(f"{status} {test_name}: {message}")

        if details:
            print(f"   Details: {json.dumps(details, indent=2)}")

    def test_dockerfile_exists(self):
        """Test that Dockerfile.threat-detection exists"""
        try:
            dockerfile_path = Path("containers/Dockerfile.threat-detection")
            exists = dockerfile_path.exists()

            if exists:
                content = dockerfile_path.read_text()
                has_multi_stage = "FROM python:3.11-slim as builder" in content
                has_production = "FROM python:3.11-slim as production" in content
                has_healthcheck = "HEALTHCHECK" in content
                has_non_root = (
                    "useradd -r -g threatdetection threatdetection" in content
                )

                self.log_test(
                    "Dockerfile.threat-detection Exists",
                    True,
                    "Dockerfile.threat-detection found with proper structure",
                    {
                        "file_size": len(content),
                        "has_multi_stage": has_multi_stage,
                        "has_production": has_production,
                        "has_healthcheck": has_healthcheck,
                        "has_non_root": has_non_root,
                    },
                )
            else:
                self.log_test(
                    "Dockerfile.threat-detection Exists",
                    False,
                    "Dockerfile.threat-detection not found",
                )
        except Exception as e:
            self.log_test(
                "Dockerfile.threat-detection Exists",
                False,
                f"Error checking Dockerfile: {str(e)}",
            )

    def test_docker_compose_integration(self):
        """Test that threat detection service is in docker-compose.yml"""
        try:
            compose_path = Path("docker-compose.yml")
            if compose_path.exists():
                content = compose_path.read_text()

                has_threat_detection_service = "threat-detection:" in content
                has_port_mapping = "8085:8085" in content
                has_healthcheck = "healthcheck:" in content and "8085/health" in content
                has_resource_limits = (
                    "cpus: '1.0'" in content and "memory: 2G" in content
                )
                has_dependencies = "depends_on:" in content

                self.log_test(
                    "Docker Compose Integration",
                    has_threat_detection_service and has_port_mapping,
                    "Threat detection service integrated in docker-compose.yml",
                    {
                        "has_threat_detection_service": has_threat_detection_service,
                        "has_port_mapping": has_port_mapping,
                        "has_healthcheck": has_healthcheck,
                        "has_resource_limits": has_resource_limits,
                        "has_dependencies": has_dependencies,
                    },
                )
            else:
                self.log_test(
                    "Docker Compose Integration", False, "docker-compose.yml not found"
                )
        except Exception as e:
            self.log_test(
                "Docker Compose Integration",
                False,
                f"Error checking docker-compose.yml: {str(e)}",
            )

    def test_configuration_file(self):
        """Test threat detection configuration file"""
        try:
            config_path = Path("config/threat_detection_docker_config.json")
            if config_path.exists():
                with open(config_path, "r") as f:
                    config = json.load(f)

                has_service_config = "threat_detection_service" in config
                has_container_config = "container" in config
                has_dependencies = "dependencies" in config
                has_components = "threat_detection_components" in config
                has_monitoring = "monitoring" in config

                self.log_test(
                    "Threat Detection Configuration File",
                    has_service_config and has_container_config,
                    "Threat detection configuration file properly structured",
                    {
                        "has_service_config": has_service_config,
                        "has_container_config": has_container_config,
                        "has_dependencies": has_dependencies,
                        "has_components": has_components,
                        "has_monitoring": has_monitoring,
                        "port": config.get("threat_detection_service", {}).get(
                            "port", "not_found"
                        ),
                    },
                )
            else:
                self.log_test(
                    "Threat Detection Configuration File",
                    False,
                    "Threat detection configuration file not found",
                )
        except Exception as e:
            self.log_test(
                "Threat Detection Configuration File",
                False,
                f"Error checking configuration: {str(e)}",
            )

    async def test_container_build(self):
        """Test container build process"""
        try:
            # Check if container can be built
            result = subprocess.run(
                [
                    "docker",
                    "build",
                    "-f",
                    "containers/Dockerfile.threat-detection",
                    "-t",
                    "ai-coding-threat-detection:test",
                    ".",
                ],
                capture_output=True,
                text=True,
                timeout=300,  # 5 minute timeout
            )

            success = result.returncode == 0
            self.log_test(
                "Container Build",
                success,
                (
                    "Threat detection container built successfully"
                    if success
                    else "Container build failed"
                ),
                {
                    "return_code": result.returncode,
                    "stdout_lines": len(result.stdout.split("\n")),
                    "stderr_lines": (
                        len(result.stderr.split("\n")) if result.stderr else 0
                    ),
                },
            )
        except subprocess.TimeoutExpired:
            self.log_test("Container Build", False, "Container build timed out")
        except Exception as e:
            self.log_test(
                "Container Build", False, f"Error during container build: {str(e)}"
            )

    async def test_container_startup(self):
        """Test container startup and health check"""
        try:
            # Start the container
            container = self.docker_client.containers.run(
                "ai-coding-threat-detection:test",
                name=f"{self.container_name}-test",
                ports={"8085/tcp": 8085},
                detach=True,
                remove=True,
            )

            # Wait for container to start
            time.sleep(10)

            # Check container status
            container.reload()
            is_running = container.status == "running"

            if is_running:
                # Test health check
                try:
                    async with aiohttp.ClientSession() as session:
                        async with session.get(
                            f"{self.service_url}/health", timeout=10
                        ) as response:
                            health_success = response.status == 200
                            health_data = (
                                await response.json() if health_success else {}
                            )

                            self.log_test(
                                "Container Startup and Health Check",
                                health_success,
                                "Container started and health check passed",
                                {
                                    "container_status": container.status,
                                    "health_status": health_data.get(
                                        "status", "unknown"
                                    ),
                                    "response_code": response.status,
                                },
                            )
                except Exception as e:
                    self.log_test(
                        "Container Startup and Health Check",
                        False,
                        f"Health check failed: {str(e)}",
                        {"container_status": container.status, "error": str(e)},
                    )
            else:
                self.log_test(
                    "Container Startup and Health Check",
                    False,
                    "Container failed to start",
                    {
                        "container_status": container.status,
                        "logs": container.logs().decode()[-500:],  # Last 500 chars
                    },
                )

            # Clean up
            try:
                container.stop(timeout=10)
            except:
                pass

        except Exception as e:
            self.log_test(
                "Container Startup and Health Check",
                False,
                f"Error during container startup test: {str(e)}",
            )

    async def test_api_endpoints(self):
        """Test threat detection service API endpoints"""
        try:
            # Start container for API testing
            container = self.docker_client.containers.run(
                "ai-coding-threat-detection:test",
                name=f"{self.container_name}-api-test",
                ports={"8085/tcp": 8085},
                detach=True,
                remove=True,
            )

            # Wait for startup
            time.sleep(15)

            async with aiohttp.ClientSession() as session:
                endpoints_to_test = [
                    ("/health", "GET"),
                    ("/status", "GET"),
                    ("/threats/summary", "GET"),
                    ("/threats/alerts", "GET"),
                    ("/threats/components", "GET"),
                ]

                successful_endpoints = 0
                endpoint_results = {}

                for endpoint, method in endpoints_to_test:
                    try:
                        if method == "GET":
                            async with session.get(
                                f"{self.service_url}{endpoint}", timeout=10
                            ) as response:
                                success = response.status in [
                                    200,
                                    404,
                                    503,
                                ]  # Accept various status codes
                                endpoint_results[endpoint] = {
                                    "status_code": response.status,
                                    "success": success,
                                }
                                if success:
                                    successful_endpoints += 1
                    except Exception as e:
                        endpoint_results[endpoint] = {"error": str(e), "success": False}

                self.log_test(
                    "API Endpoints",
                    successful_endpoints >= 3,  # At least 3 endpoints should work
                    f"API endpoints tested ({successful_endpoints}/{len(endpoints_to_test)} successful)",
                    {
                        "successful_endpoints": successful_endpoints,
                        "total_endpoints": len(endpoints_to_test),
                        "endpoint_results": endpoint_results,
                    },
                )

            # Clean up
            try:
                container.stop(timeout=10)
            except:
                pass

        except Exception as e:
            self.log_test(
                "API Endpoints", False, f"Error testing API endpoints: {str(e)}"
            )

    def test_cli_commands(self):
        """Test CLI commands for threat detection service"""
        try:
            cli_path = Path("cli/threat_detection_commands.py")
            if cli_path.exists():
                content = cli_path.read_text()

                required_methods = [
                    "check_threat_detection_status",
                    "get_threats_summary",
                    "get_threat_alerts",
                    "get_threat_components",
                    "test_threat_detection_components",
                    "optimize_threat_detection",
                    "export_threat_data",
                    "import_threat_data",
                    "get_approved_threat_models",
                    "validate_threat_model",
                ]

                missing_methods = []
                for method in required_methods:
                    if method not in content:
                        missing_methods.append(method)

                success = len(missing_methods) == 0
                self.log_test(
                    "CLI Commands",
                    success,
                    f"CLI commands implemented ({len(required_methods) - len(missing_methods)}/{len(required_methods)})",
                    {
                        "total_methods": len(required_methods),
                        "missing_methods": missing_methods,
                        "has_command_mapping": "THREAT_DETECTION_COMMANDS" in content,
                    },
                )
            else:
                self.log_test("CLI Commands", False, "CLI commands file not found")
        except Exception as e:
            self.log_test(
                "CLI Commands", False, f"Error checking CLI commands: {str(e)}"
            )

    def test_api_routes(self):
        """Test API routes for threat detection service"""
        try:
            routes_path = Path("api/threat_detection_routes.py")
            if routes_path.exists():
                content = routes_path.read_text()

                required_endpoints = [
                    "/status",
                    "/threats/summary",
                    "/threats/alerts",
                    "/threats/components",
                    "/optimize",
                    "/export",
                    "/import",
                    "/approved-models",
                    "/validate-model",
                    "/health",
                ]

                missing_endpoints = []
                for endpoint in required_endpoints:
                    if endpoint not in content:
                        missing_endpoints.append(endpoint)

                success = len(missing_endpoints) == 0
                self.log_test(
                    "API Routes",
                    success,
                    f"API routes implemented ({len(required_endpoints) - len(missing_endpoints)}/{len(required_endpoints)})",
                    {
                        "total_endpoints": len(required_endpoints),
                        "missing_endpoints": missing_endpoints,
                        "has_router": "APIRouter" in content,
                        "has_pydantic_models": "BaseModel" in content,
                    },
                )
            else:
                self.log_test("API Routes", False, "API routes file not found")
        except Exception as e:
            self.log_test("API Routes", False, f"Error checking API routes: {str(e)}")

    def test_resource_limits(self):
        """Test resource limits configuration"""
        try:
            compose_path = Path("docker-compose.yml")
            if compose_path.exists():
                content = compose_path.read_text()

                # Find threat detection service section
                threat_detection_section_start = content.find(
                    "  # Threat Detection Engine"
                )
                if threat_detection_section_start != -1:
                    # Find the end of the threat detection service section (next service or end of file)
                    next_service_start = content.find(
                        "  # ", threat_detection_section_start + 1
                    )
                    if next_service_start == -1:
                        threat_detection_section = content[
                            threat_detection_section_start:
                        ]
                    else:
                        threat_detection_section = content[
                            threat_detection_section_start:next_service_start
                        ]

                    # Look for resource limits in the deploy section
                    has_cpu_limit = "cpus: '1.0'" in threat_detection_section
                    has_memory_limit = "memory: 2G" in threat_detection_section
                    has_healthcheck = "healthcheck:" in threat_detection_section
                    has_restart_policy = (
                        "restart: unless-stopped" in threat_detection_section
                    )
                    has_volumes = "volumes:" in threat_detection_section
                    has_deploy_section = "deploy:" in threat_detection_section

                    self.log_test(
                        "Resource Limits",
                        has_cpu_limit and has_memory_limit and has_deploy_section,
                        "Resource limits properly configured",
                        {
                            "has_cpu_limit": has_cpu_limit,
                            "has_memory_limit": has_memory_limit,
                            "has_deploy_section": has_deploy_section,
                            "has_healthcheck": has_healthcheck,
                            "has_restart_policy": has_restart_policy,
                            "has_volumes": has_volumes,
                        },
                    )
                else:
                    self.log_test(
                        "Resource Limits",
                        False,
                        "Threat detection service section not found in docker-compose.yml",
                    )
            else:
                self.log_test("Resource Limits", False, "docker-compose.yml not found")
        except Exception as e:
            self.log_test(
                "Resource Limits", False, f"Error checking resource limits: {str(e)}"
            )

    def test_service_discovery(self):
        """Test service discovery configuration"""
        try:
            compose_path = Path("docker-compose.yml")
            if compose_path.exists():
                content = compose_path.read_text()

                # Find threat detection service section
                threat_detection_section_start = content.find(
                    "  # Threat Detection Engine"
                )
                if threat_detection_section_start != -1:
                    # Find the end of the threat detection service section (next service or end of file)
                    next_service_start = content.find(
                        "  # ", threat_detection_section_start + 1
                    )
                    if next_service_start == -1:
                        threat_detection_section = content[
                            threat_detection_section_start:
                        ]
                    else:
                        threat_detection_section = content[
                            threat_detection_section_start:next_service_start
                        ]

                    has_network = "ai-coding-network" in threat_detection_section
                    has_dependencies = "depends_on:" in threat_detection_section
                    has_api_dependency = "api:" in threat_detection_section
                    has_db_dependency = "db:" in threat_detection_section
                    has_redis_dependency = "redis:" in threat_detection_section

                    self.log_test(
                        "Service Discovery",
                        has_network and has_dependencies,
                        "Service discovery properly configured",
                        {
                            "has_network": has_network,
                            "has_dependencies": has_dependencies,
                            "has_api_dependency": has_api_dependency,
                            "has_db_dependency": has_db_dependency,
                            "has_redis_dependency": has_redis_dependency,
                        },
                    )
                else:
                    self.log_test(
                        "Service Discovery",
                        False,
                        "Threat detection service section not found in docker-compose.yml",
                    )
            else:
                self.log_test(
                    "Service Discovery", False, "docker-compose.yml not found"
                )
        except Exception as e:
            self.log_test(
                "Service Discovery",
                False,
                f"Error checking service discovery: {str(e)}",
            )

    async def run_all_tests(self):
        """Run all tests"""
        print("🧪 Starting Threat Detection Container Tests...")
        print("=" * 60)

        # Run all test methods
        test_methods = [
            self.test_dockerfile_exists,
            self.test_docker_compose_integration,
            self.test_configuration_file,
            self.test_cli_commands,
            self.test_api_routes,
            self.test_resource_limits,
            self.test_service_discovery,
        ]

        # Run synchronous tests
        for test_method in test_methods:
            try:
                test_method()
            except Exception as e:
                self.log_test(
                    test_method.__name__,
                    False,
                    f"Test method failed with exception: {str(e)}",
                )

        # Run asynchronous tests
        async_test_methods = [
            self.test_container_build,
            self.test_container_startup,
            self.test_api_endpoints,
        ]

        for test_method in async_test_methods:
            try:
                await test_method()
            except Exception as e:
                self.log_test(
                    test_method.__name__,
                    False,
                    f"Async test method failed with exception: {str(e)}",
                )

        # Print summary
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)
        print(f"Total Tests: {self.total_tests}")
        print(f"Passed: {self.passed_tests} ✅")
        print(f"Failed: {self.failed_tests} ❌")
        print(f"Success Rate: {(self.passed_tests / self.total_tests * 100):.1f}%")

        # Save detailed results
        results = {
            "timestamp": time.time(),
            "total_tests": self.total_tests,
            "passed_tests": self.passed_tests,
            "failed_tests": self.failed_tests,
            "success_rate": self.passed_tests / self.total_tests * 100,
            "test_results": self.test_results,
        }

        # Save to file
        results_file = Path("test_results_threat_detection_container.json")
        with open(results_file, "w") as f:
            json.dump(results, f, indent=2)

        print(f"\n📄 Detailed results saved to: {results_file}")

        return self.passed_tests == self.total_tests


async def main():
    """Main test runner"""
    tester = ThreatDetectionContainerTester()
    success = await tester.run_all_tests()

    if success:
        print("\n🎉 All threat detection container tests passed!")
        sys.exit(0)
    else:
        print(
            "\n⚠️  Some threat detection container tests failed. Check the results above."
        )
        sys.exit(1)


if __name__ == "__main__":
    asyncio.run(main())
