# 🔧 Troubleshooting Guide

This guide helps you diagnose and resolve common issues with the AI Coding Agent Docker setup and monitoring system.

## 🚨 **Common Issues and Solutions**

### **Docker Issues**

#### **Issue: Docker Desktop Not Running**
```
Error: Cannot connect to the Docker daemon
```

**Solution:**
1. Start Docker Desktop
2. Wait for <PERSON><PERSON> to fully initialize
3. Verify with: `docker --version`
4. Check Docker status: `python cli.py check-docker-status`

#### **Issue: Port Already in Use**
```
Error: Port 8080 is already in use
```

**Solution:**
1. Check port usage: `python cli.py list-ports`
2. Release port: `python cli.py release-port <site_name>`
3. Or use auto-allocation: `python cli.py create-site-container <site> --port auto`

#### **Issue: Docker Build Fails**
```
Error: Failed to build Docker image
```

**Solution:**
1. Check Docker daemon is running
2. Verify site directory exists: `python cli.py list-sites`
3. Check Dockerfile syntax in generated files
4. Clear Docker cache: `docker system prune -f`
5. Retry with verbose output

### **Virtual Environment Issues**

#### **Issue: Virtual Environment Not Activated**
```
Error: Module not found or command not recognized
```

**Solution:**
```bash
# Windows PowerShell
.\.venv\Scripts\Activate.ps1

# Unix/macOS
source .venv/bin/activate

# Verify activation
python -c "import sys; print(sys.prefix)"
```

#### **Issue: Missing Dependencies**
```
Error: ModuleNotFoundError: No module named 'docker'
```

**Solution:**
```bash
# Activate venv first
.\.venv\Scripts\Activate.ps1

# Install dependencies
pip install -r requirements.txt

# Verify installation
python -c "import docker; print('Docker module available')"
```

### **Monitoring System Issues**

#### **Issue: Monitoring Dashboard Not Accessible**
```
Error: Cannot connect to http://localhost:8090
```

**Solution:**
1. Check monitoring status: `python cli.py get-monitoring-status`
2. Start monitoring: `python cli.py start-monitoring`
3. Verify port is not blocked by firewall
4. Check for port conflicts: `netstat -an | findstr 8090`

#### **Issue: No Metrics Being Collected**
```
Warning: No container metrics available
```

**Solution:**
1. Verify containers are running: `docker ps`
2. Check monitoring system: `python cli.py get-monitoring-status`
3. Restart monitoring: `python cli.py stop-monitoring && python cli.py start-monitoring`
4. Verify Docker API access

#### **Issue: Health Checks Failing**
```
Error: Health check timeout for container
```

**Solution:**
1. Check container status: `docker ps`
2. Verify container is responding: `curl http://localhost:<port>`
3. Check container logs: `docker logs <container_name>`
4. Adjust health check timeout in configuration

### **AI Integration Issues**

#### **Issue: Ollama Not Available**
```
Warning: AI analysis not available - Ollama not running
```

**Solution:**
1. Install Ollama: Download from [ollama.ai](https://ollama.ai)
2. Start Ollama service
3. Pull required model: `ollama pull deepseek-coder:1.3b`
4. Test availability: `python cli.py test-ai-models`

#### **Issue: AI Model Not Found**
```
Error: Model 'deepseek-coder:1.3b' not found
```

**Solution:**
1. List available models: `ollama list`
2. Pull missing model: `ollama pull deepseek-coder:1.3b`
3. Use available model: `python cli.py ai-analyze-site <site> --model <available_model>`

### **Site Creation Issues**

#### **Issue: Site Directory Not Found**
```
Error: Site directory 'my-site' does not exist
```

**Solution:**
1. Check available sites: `python cli.py list-sites`
2. Verify site path exists in `sites/` directory
3. Create site directory structure
4. Ensure proper file permissions

#### **Issue: Framework Detection Failed**
```
Warning: Could not detect site framework
```

**Solution:**
1. Ensure `package.json` exists for Node.js projects
2. Add framework dependencies to package.json
3. Create proper project structure (src/, public/, etc.)
4. Use manual framework specification if needed

### **Performance Issues**

#### **Issue: High Memory Usage**
```
Alert: Container memory usage above 90%
```

**Solution:**
1. Check container metrics: `python cli.py get-container-performance <container>`
2. Increase memory limits in docker-compose.yml
3. Optimize application code
4. Consider container scaling

#### **Issue: Slow Container Startup**
```
Warning: Container taking long time to start
```

**Solution:**
1. Check Docker image size
2. Optimize Dockerfile (use multi-stage builds)
3. Reduce dependency installation time
4. Use image caching strategies

## 🔍 **Diagnostic Commands**

### **System Health Check**
```bash
# Complete system validation
python cli.py validate-environment

# Check Docker connectivity
python cli.py check-docker-status

# Verify monitoring system
python cli.py get-monitoring-status

# Test AI models
python cli.py test-ai-models
```

### **Container Diagnostics**
```bash
# List all containers
python cli.py list-containers

# Check specific container performance
python cli.py get-container-performance <container_name>

# View container logs
docker logs <container_name>

# Inspect container configuration
docker inspect <container_name>
```

### **Port and Network Diagnostics**
```bash
# Check port allocations
python cli.py list-ports

# Get available ports
python cli.py get-available-ports

# Test network connectivity
docker network ls
docker network inspect ai-coding-network
```

## 📊 **Log Analysis**

### **Important Log Locations**
- **Application logs**: `logs/`
- **Container logs**: `docker logs <container_name>`
- **Monitoring logs**: `logs/cursor_rules_monitor.log`
- **Health check logs**: `data/health_checks.json`
- **Metrics logs**: `data/container_metrics.json`

### **Log Analysis Commands**
```bash
# View recent application logs
tail -f logs/app.log

# Check monitoring system logs
tail -f logs/cursor_rules_monitor.log

# View container logs with timestamps
docker logs -t <container_name>

# Follow container logs in real-time
docker logs -f <container_name>
```

## 🚨 **Emergency Procedures**

### **Complete System Reset**
```bash
# Stop all monitoring
python cli.py stop-monitoring

# Stop all containers
docker stop $(docker ps -q)

# Remove all containers
docker rm $(docker ps -aq)

# Clean Docker system
docker system prune -af

# Restart monitoring
python cli.py start-monitoring
```

### **Monitoring System Recovery**
```bash
# Stop monitoring
python cli.py stop-monitoring

# Clear monitoring data
rm -rf data/container_metrics.json data/health_checks.json data/alerts.json

# Restart monitoring
python cli.py start-monitoring --port 8090
```

### **Port Conflict Resolution**
```bash
# Release all ports
python cli.py reset-config

# Check for conflicts
netstat -an | findstr ":80"

# Restart with clean state
python cli.py start-monitoring
```

## 📞 **Getting Help**

### **Built-in Help**
```bash
# General help
python cli.py help

# Command-specific help
python cli.py help <command_name>

# System information
python cli.py get-system-info
```

### **Debug Mode**
```bash
# Enable verbose logging
export LOG_LEVEL=DEBUG

# Run commands with detailed output
python cli.py <command> --verbose

# Check cursor rules compliance
python scripts/cursor_rules_monitor.py --status
```

### **Community Resources**
- **Documentation**: Check `docs/` directory
- **Examples**: Review `examples/` directory
- **Configuration**: See `config/` directory
- **Scripts**: Utility scripts in `scripts/` directory

## 🔧 **Advanced Troubleshooting**

### **Docker Network Issues**
```bash
# Recreate Docker network
docker network rm ai-coding-network
docker network create ai-coding-network

# Check network connectivity
docker run --rm --network ai-coding-network alpine ping <container_name>
```

### **Permission Issues**
```bash
# Fix file permissions
chmod +x scripts/*.py
chmod 600 .env*

# Fix directory permissions
chmod 755 sites/ containers/ logs/ data/
```

### **Database Issues**
```bash
# Reset monitoring database
rm -f data/*.json

# Restart with clean state
python cli.py start-monitoring
```

---

**Remember**: Always activate your virtual environment before running any commands, and ensure Docker Desktop is running before attempting container operations.
