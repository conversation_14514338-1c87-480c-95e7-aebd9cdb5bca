#!/usr/bin/env python3
"""
Tests for AST-Based Advanced Code Generator

This module provides comprehensive tests for the AST code generation system including:
1. AST manipulation and transformation
2. Code generation from AST nodes
3. AST merging and integration
4. Refactoring capabilities
5. Multi-language support
6. Template-based generation
"""

import ast
import asyncio
import json
import os
import tempfile
from pathlib import Path
from typing import Any, Dict, List

import pytest

from agent.core.code_generation.ast_code_generator import (
    ASTCodeGenerator,
    CodeLanguage,
    GeneratedCode,
    GenerationContext,
    GenerationStrategy,
)
from agent.core.code_generation.ast_manipulator import ASTManipulator
from agent.core.code_generation.code_analyzer import CodeAnalyzer
from agent.core.code_generation.language_support.javascript_support import (
    JavaScriptASTSupport,
)
from agent.core.code_generation.language_support.jsx_support import JSXASTSupport
from agent.core.code_generation.language_support.python_support import PythonASTSupport
from agent.core.code_generation.language_support.tsx_support import TSXASTSupport
from agent.core.code_generation.language_support.typescript_support import (
    TypeScriptASTSupport,
)
from agent.core.code_generation.pattern_matcher import CodePatternMatcher


class TestASTCodeGenerator:
    """Test cases for ASTCodeGenerator"""

    @pytest.fixture
    def ast_generator(self):
        """Create AST code generator instance"""
        generator = ASTCodeGenerator()
        return generator

    @pytest.fixture
    def python_config(self):
        """Python language configuration"""
        return {
            "features": {
                "type_hints": True,
                "async_await": True,
                "dataclasses": True,
                "enums": True,
                "docstrings": True,
                "error_handling": True,
            },
            "conventions": {
                "naming": "snake_case",
                "max_line_length": 88,
                "import_style": "absolute",
            },
        }

    @pytest.fixture
    def typescript_config(self):
        """TypeScript language configuration"""
        return {
            "features": {
                "strict_mode": True,
                "interfaces": True,
                "generics": True,
                "async_await": True,
                "decorators": True,
            },
            "conventions": {
                "naming": "camelCase",
                "max_line_length": 100,
                "import_style": "relative",
            },
        }

    @pytest.mark.asyncio
    async def test_ast_generator_initialization(self, ast_generator):
        """Test AST code generator initialization"""
        assert ast_generator is not None
        assert ast_generator.ast_manipulator is not None
        assert ast_generator.code_analyzer is not None
        assert ast_generator.pattern_matcher is not None
        assert ast_generator.generation_history == []
        assert ast_generator.template_cache == {}
        assert ast_generator.pattern_cache == {}

    @pytest.mark.asyncio
    async def test_generate_python_code(self, ast_generator, python_config):
        """Test Python code generation"""
        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="test_module.py",
            requirements=["Create a function that calculates fibonacci numbers"],
            constraints=["Use type hints", "Include docstring"],
            conventions=python_config["conventions"],
        )

        result = await ast_generator.generate_code(context, GenerationStrategy.HYBRID)

        assert result is not None
        assert isinstance(result, GeneratedCode)
        assert result.language == CodeLanguage.PYTHON
        assert result.code is not None
        assert len(result.code) > 0
        assert "def fibonacci" in result.code
        assert "-> int" in result.code  # Type hints
        assert '"""' in result.code  # Docstring

    @pytest.mark.asyncio
    async def test_generate_typescript_code(self, ast_generator, typescript_config):
        """Test TypeScript code generation"""
        context = GenerationContext(
            language=CodeLanguage.TYPESCRIPT,
            target_file="test_module.ts",
            requirements=["Create a function that calculates fibonacci numbers"],
            constraints=["Use TypeScript types", "Include JSDoc"],
            conventions=typescript_config["conventions"],
        )

        result = await ast_generator.generate_code(context, GenerationStrategy.HYBRID)

        assert result is not None
        assert isinstance(result, GeneratedCode)
        assert result.language == CodeLanguage.TYPESCRIPT
        assert result.code is not None
        assert len(result.code) > 0
        assert "function fibonacci" in result.code
        assert ": number" in result.code  # TypeScript types

    @pytest.mark.asyncio
    async def test_generate_javascript_code(self, ast_generator):
        """Test JavaScript code generation"""
        context = GenerationContext(
            language=CodeLanguage.JAVASCRIPT,
            target_file="test_module.js",
            requirements=["Create a function that calculates fibonacci numbers"],
            constraints=["Use ES6 syntax", "Include JSDoc"],
            conventions={"naming": "camelCase", "max_line_length": 100},
        )

        result = await ast_generator.generate_code(context, GenerationStrategy.HYBRID)

        assert result is not None
        assert isinstance(result, GeneratedCode)
        assert result.language == CodeLanguage.JAVASCRIPT
        assert result.code is not None
        assert len(result.code) > 0
        assert "function fibonacci" in result.code

    @pytest.mark.asyncio
    async def test_generate_jsx_code(self, ast_generator):
        """Test JSX code generation"""
        context = GenerationContext(
            language=CodeLanguage.JSX,
            target_file="test_component.jsx",
            requirements=["Create a React component that displays a counter"],
            constraints=["Use functional component", "Include useState hook"],
            conventions={"naming": "PascalCase", "max_line_length": 100},
        )

        result = await ast_generator.generate_code(context, GenerationStrategy.HYBRID)

        assert result is not None
        assert isinstance(result, GeneratedCode)
        assert result.language == CodeLanguage.JSX
        assert result.code is not None
        assert len(result.code) > 0
        assert "import React" in result.code
        assert "useState" in result.code

    @pytest.mark.asyncio
    async def test_generate_tsx_code(self, ast_generator, typescript_config):
        """Test TSX code generation"""
        context = GenerationContext(
            language=CodeLanguage.TSX,
            target_file="test_component.tsx",
            requirements=["Create a React component that displays a counter"],
            constraints=[
                "Use TypeScript",
                "Include useState hook",
                "Add props interface",
            ],
            conventions=typescript_config["conventions"],
        )

        result = await ast_generator.generate_code(context, GenerationStrategy.HYBRID)

        assert result is not None
        assert isinstance(result, GeneratedCode)
        assert result.language == CodeLanguage.TSX
        assert result.code is not None
        assert len(result.code) > 0
        assert "import React" in result.code
        assert "useState" in result.code
        assert "interface" in result.code

    @pytest.mark.asyncio
    async def test_code_merging(self, ast_generator):
        """Test code merging functionality"""
        existing_code = """
import math

def square(x):
    return x * x
"""

        new_code = """
def cube(x):
    return x * x * x
"""

        merged_code = await ast_generator.merge_code(
            existing_code, new_code, CodeLanguage.PYTHON
        )

        assert merged_code is not None
        assert "def square" in merged_code
        assert "def cube" in merged_code
        assert "import math" in merged_code

    @pytest.mark.asyncio
    async def test_code_refactoring(self, ast_generator):
        """Test code refactoring functionality"""
        code = """
def calculate_area(width, height):
    return width * height

def calculate_perimeter(width, height):
    return 2 * (width + height)
"""

        refactoring_rules = [
            {"type": "extract", "target": "calculate_area", "new_name": "get_area"}
        ]

        refactored_code = await ast_generator.refactor_code(
            code, refactoring_rules, CodeLanguage.PYTHON
        )

        assert refactored_code is not None
        assert "def get_area" in refactored_code
        assert "def calculate_perimeter" in refactored_code

    @pytest.mark.asyncio
    async def test_generation_metrics(self, ast_generator):
        """Test generation metrics collection"""
        # Generate some code first
        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="test_metrics.py",
            requirements=["Create a simple function"],
            constraints=[],
        )

        await ast_generator.generate_code(context)

        metrics = await ast_generator.get_generation_metrics()

        assert metrics is not None
        assert "total_generations" in metrics
        assert "successful_generations" in metrics
        assert "failed_generations" in metrics
        assert "average_generation_time" in metrics
        assert "language_usage" in metrics
        assert metrics["total_generations"] > 0

    @pytest.mark.asyncio
    async def test_template_based_generation(self, ast_generator):
        """Test template-based code generation"""
        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="test_template.py",
            requirements=["Create a class with methods"],
            constraints=["Use template-based generation"],
        )

        result = await ast_generator.generate_code(
            context, GenerationStrategy.TEMPLATE_BASED
        )

        assert result is not None
        assert result.strategy_used == GenerationStrategy.TEMPLATE_BASED
        assert result.code is not None
        assert len(result.code) > 0

    @pytest.mark.asyncio
    async def test_ast_manipulation_generation(self, ast_generator):
        """Test AST manipulation-based code generation"""
        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="test_ast_manipulation.py",
            requirements=["Create a function with AST manipulation"],
            constraints=["Use AST manipulation strategy"],
        )

        result = await ast_generator.generate_code(
            context, GenerationStrategy.AST_MANIPULATION
        )

        assert result is not None
        assert result.strategy_used == GenerationStrategy.AST_MANIPULATION
        assert result.code is not None
        assert len(result.code) > 0

    @pytest.mark.asyncio
    async def test_pattern_based_generation(self, ast_generator):
        """Test pattern-based code generation"""
        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="test_pattern.py",
            requirements=["Create a singleton pattern"],
            constraints=["Use pattern-based generation"],
        )

        result = await ast_generator.generate_code(
            context, GenerationStrategy.PATTERN_BASED
        )

        assert result is not None
        assert result.strategy_used == GenerationStrategy.PATTERN_BASED
        assert result.code is not None
        assert len(result.code) > 0

    @pytest.mark.asyncio
    async def test_hybrid_generation(self, ast_generator):
        """Test hybrid code generation"""
        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="test_hybrid.py",
            requirements=["Create a complex function with multiple features"],
            constraints=["Use hybrid generation strategy"],
        )

        result = await ast_generator.generate_code(context, GenerationStrategy.HYBRID)

        assert result is not None
        assert result.strategy_used == GenerationStrategy.HYBRID
        assert result.code is not None
        assert len(result.code) > 0

    @pytest.mark.asyncio
    async def test_error_handling(self, ast_generator):
        """Test error handling in code generation"""
        # Test with invalid requirements
        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="test_error.py",
            requirements=["Invalid requirement that should cause error"],
            constraints=["Invalid constraint"],
        )

        try:
            result = await ast_generator.generate_code(context)
            # Should not reach here for invalid requirements
            assert False, "Expected error for invalid requirements"
        except Exception as e:
            # Expected error
            assert str(e) is not None

    @pytest.mark.asyncio
    async def test_language_specific_features(self, ast_generator):
        """Test language-specific feature generation"""
        # Test Python-specific features
        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="test_python_features.py",
            requirements=["Create a dataclass with type hints"],
            constraints=["Use Python-specific features"],
        )

        result = await ast_generator.generate_code(context)

        assert result is not None
        assert result.code is not None
        assert "@dataclass" in result.code or "class" in result.code

        # Test TypeScript-specific features
        context = GenerationContext(
            language=CodeLanguage.TYPESCRIPT,
            target_file="test_typescript_features.ts",
            requirements=["Create an interface with generics"],
            constraints=["Use TypeScript-specific features"],
        )

        result = await ast_generator.generate_code(context)

        assert result is not None
        assert result.code is not None
        assert "interface" in result.code or "function" in result.code

    @pytest.mark.asyncio
    async def test_convention_application(self, ast_generator):
        """Test convention application in generated code"""
        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="test_conventions.py",
            requirements=["Create functions with different naming conventions"],
            constraints=["Apply naming conventions"],
            conventions={"naming": "snake_case", "max_line_length": 88},
        )

        result = await ast_generator.generate_code(context)

        assert result is not None
        assert result.code is not None

        # Check that function names follow snake_case
        lines = result.code.split("\n")
        for line in lines:
            if line.strip().startswith("def "):
                func_name = line.split("def ")[1].split("(")[0]
                assert (
                    "_" in func_name or func_name.islower()
                ), f"Function name {func_name} should be snake_case"

    @pytest.mark.asyncio
    async def test_complex_generation_scenario(self, ast_generator):
        """Test complex code generation scenario"""
        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="test_complex.py",
            requirements=[
                "Create a class for user management",
                "Include CRUD operations",
                "Add validation methods",
                "Include error handling",
                "Add type hints and docstrings",
            ],
            constraints=[
                "Use async/await",
                "Include proper error handling",
                "Follow PEP 8 conventions",
                "Add comprehensive docstrings",
            ],
            imports=["from typing import List, Dict, Optional"],
            dependencies=["pydantic", "sqlalchemy"],
        )

        result = await ast_generator.generate_code(context)

        assert result is not None
        assert result.code is not None
        assert len(result.code) > 100  # Should be substantial code

        # Check for key elements
        assert "class" in result.code
        assert "async def" in result.code
        assert "from typing import" in result.code
        assert '"""' in result.code  # Docstrings
        assert "try:" in result.code or "except" in result.code  # Error handling

    @pytest.mark.asyncio
    async def test_performance_benchmark(self, ast_generator):
        """Test performance of code generation"""
        import time

        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="test_performance.py",
            requirements=["Create a large module with many functions and classes"],
            constraints=["Optimize for performance"],
        )

        start_time = time.time()
        result = await ast_generator.generate_code(context)
        end_time = time.time()

        generation_time = end_time - start_time

        assert result is not None
        assert generation_time < 30.0  # Should complete within 30 seconds
        assert result.generation_time > 0
        assert result.generation_time < 30.0

    @pytest.mark.asyncio
    async def test_multi_language_generation(self, ast_generator):
        """Test generation across multiple languages"""
        languages = [
            CodeLanguage.PYTHON,
            CodeLanguage.TYPESCRIPT,
            CodeLanguage.JAVASCRIPT,
            CodeLanguage.JSX,
            CodeLanguage.TSX,
        ]

        for language in languages:
            context = GenerationContext(
                language=language,
                target_file=f"test_{language.value}.{language.value}",
                requirements=["Create a simple function"],
                constraints=[],
            )

            result = await ast_generator.generate_code(context)

            assert result is not None
            assert result.language == language
            assert result.code is not None
            assert len(result.code) > 0

    @pytest.mark.asyncio
    async def test_generation_history(self, ast_generator):
        """Test generation history tracking"""
        # Generate multiple pieces of code
        for i in range(3):
            context = GenerationContext(
                language=CodeLanguage.PYTHON,
                target_file=f"test_history_{i}.py",
                requirements=[f"Create function number {i}"],
                constraints=[],
            )
            await ast_generator.generate_code(context)

        # Check history
        assert len(ast_generator.generation_history) == 3

        for i, history_item in enumerate(ast_generator.generation_history):
            assert history_item is not None
            assert history_item.language == CodeLanguage.PYTHON
            assert history_item.code is not None
            assert f"test_history_{i}.py" in history_item.metadata.get(
                "target_file", ""
            )

    @pytest.mark.asyncio
    async def test_cleanup_functionality(self, ast_generator):
        """Test cleanup functionality"""
        # Generate some code to populate caches
        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="test_cleanup.py",
            requirements=["Create a simple function"],
            constraints=[],
        )
        await ast_generator.generate_code(context)

        # Verify caches are populated
        assert (
            len(ast_generator.template_cache) > 0
            or len(ast_generator.pattern_cache) > 0
        )

        # Cleanup
        await ast_generator.cleanup()

        # Verify caches are cleared
        assert len(ast_generator.template_cache) == 0
        assert len(ast_generator.pattern_cache) == 0


class TestLanguageSupport:
    """Test cases for language-specific AST support"""

    @pytest.mark.asyncio
    async def test_python_support(self, python_config):
        """Test Python AST support"""
        support = PythonASTSupport(python_config)

        # Test base AST creation
        ast_tree = await support.create_base_ast()
        assert isinstance(ast_tree, ast.Module)
        assert len(ast_tree.body) == 0

        # Test code parsing
        code = "def test(): pass"
        parsed_ast = await support.parse_code(code)
        assert isinstance(parsed_ast, ast.Module)
        assert len(parsed_ast.body) == 1

        # Test AST to code conversion
        converted_code = await support.ast_to_code(parsed_ast)
        assert "def test" in converted_code

        # Test function addition
        modified_ast = await support.add_function(ast_tree, "new_function", [], "pass")
        assert len(modified_ast.body) == 1

        # Test validation
        assert await support.validate_ast(modified_ast) is True

        await support.cleanup()

    @pytest.mark.asyncio
    async def test_typescript_support(self, typescript_config):
        """Test TypeScript AST support"""
        support = TypeScriptASTSupport(typescript_config)

        # Test base AST creation
        ast_tree = await support.create_base_ast()
        assert isinstance(ast_tree, ast.Module)

        # Test code parsing
        code = "function test() {}"
        parsed_ast = await support.parse_code(code)
        assert isinstance(parsed_ast, ast.Module)

        # Test AST to code conversion
        converted_code = await support.ast_to_code(parsed_ast)
        assert "function test" in converted_code

        # Test function addition
        modified_ast = await support.add_function(
            ast_tree, "newFunction", [], "return null;"
        )
        assert len(modified_ast.body) == 1

        # Test validation
        assert await support.validate_ast(modified_ast) is True

        await support.cleanup()

    @pytest.mark.asyncio
    async def test_javascript_support(self):
        """Test JavaScript AST support"""
        config = {
            "features": {"es6_modules": True},
            "conventions": {"naming": "camelCase"},
        }
        support = JavaScriptASTSupport(config)

        # Test base AST creation
        ast_tree = await support.create_base_ast()
        assert isinstance(ast_tree, ast.Module)

        # Test code parsing
        code = "function test() {}"
        parsed_ast = await support.parse_code(code)
        assert isinstance(parsed_ast, ast.Module)

        # Test AST to code conversion
        converted_code = await support.ast_to_code(parsed_ast)
        assert "function test" in converted_code

        await support.cleanup()

    @pytest.mark.asyncio
    async def test_jsx_support(self):
        """Test JSX AST support"""
        config = {
            "features": {"functional_components": True},
            "conventions": {"naming": "PascalCase"},
        }
        support = JSXASTSupport(config)

        # Test base AST creation
        ast_tree = await support.create_base_ast()
        assert isinstance(ast_tree, ast.Module)
        assert len(ast_tree.body) > 0  # Should have React import

        # Test React component addition
        modified_ast = await support.add_react_component(
            ast_tree, "TestComponent", ["title"], ["Hello World"]
        )
        assert len(modified_ast.body) > 1

        # Test hook addition
        modified_ast = await support.add_hook(modified_ast, "useState", "state", ["0"])
        assert len(modified_ast.body) > 2

        await support.cleanup()

    @pytest.mark.asyncio
    async def test_tsx_support(self, typescript_config):
        """Test TSX AST support"""
        support = TSXASTSupport(typescript_config)

        # Test base AST creation
        ast_tree = await support.create_base_ast()
        assert isinstance(ast_tree, ast.Module)
        assert len(ast_tree.body) > 0  # Should have React imports

        # Test React component addition with TypeScript
        modified_ast = await support.add_react_component(
            ast_tree, "TestComponent", "TestProps", ["title"], ["Hello World"]
        )
        assert len(modified_ast.body) > 1

        # Test interface addition
        modified_ast = await support.add_props_interface(
            modified_ast, "TestProps", [{"name": "title", "type": "string"}]
        )
        assert len(modified_ast.body) > 2

        await support.cleanup()


class TestIntegration:
    """Integration tests for the complete AST generation system"""

    @pytest.mark.asyncio
    async def test_full_generation_workflow(self):
        """Test complete code generation workflow"""
        generator = ASTCodeGenerator()

        # Create a complex generation context
        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="integration_test.py",
            requirements=[
                "Create a web API with FastAPI",
                "Include user authentication",
                "Add database models",
                "Include CRUD operations",
                "Add input validation",
                "Include error handling",
            ],
            constraints=[
                "Use async/await",
                "Follow REST API conventions",
                "Include proper documentation",
                "Add type hints",
            ],
            imports=[
                "from fastapi import FastAPI, HTTPException, Depends",
                "from pydantic import BaseModel",
                "from sqlalchemy import create_engine, Column, Integer, String",
                "from typing import List, Optional",
            ],
            dependencies=["fastapi", "pydantic", "sqlalchemy", "uvicorn"],
        )

        # Generate code
        result = await generator.generate_code(context, GenerationStrategy.HYBRID)

        # Verify result
        assert result is not None
        assert result.code is not None
        assert len(result.code) > 200  # Should be substantial

        # Check for key components
        assert "class" in result.code
        assert "async def" in result.code
        assert "FastAPI" in result.code
        assert "BaseModel" in result.code
        assert "from fastapi import" in result.code
        assert "from pydantic import" in result.code

        # Check quality metrics
        assert result.complexity_score > 0
        assert result.quality_score > 0

        await generator.cleanup()

    @pytest.mark.asyncio
    async def test_multi_file_generation(self):
        """Test generation of multiple related files"""
        generator = ASTCodeGenerator()

        # Generate backend API
        api_context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="api/main.py",
            requirements=["Create FastAPI application with user endpoints"],
            constraints=["Use async/await", "Include validation"],
        )

        api_result = await generator.generate_code(api_context)

        # Generate frontend component
        frontend_context = GenerationContext(
            language=CodeLanguage.TSX,
            target_file="components/UserList.tsx",
            requirements=["Create React component to display users"],
            constraints=["Use TypeScript", "Include useState hook"],
        )

        frontend_result = await generator.generate_code(frontend_context)

        # Verify both results
        assert api_result is not None
        assert frontend_result is not None
        assert api_result.code is not None
        assert frontend_result.code is not None

        # Check for integration points
        assert "FastAPI" in api_result.code
        assert "React" in frontend_result.code
        assert "useState" in frontend_result.code

        await generator.cleanup()

    @pytest.mark.asyncio
    async def test_error_recovery(self):
        """Test error recovery and fallback mechanisms"""
        generator = ASTCodeGenerator()

        # Test with problematic requirements
        context = GenerationContext(
            language=CodeLanguage.PYTHON,
            target_file="error_test.py",
            requirements=["Create invalid syntax that should be handled gracefully"],
            constraints=["Handle errors gracefully"],
        )

        try:
            result = await generator.generate_code(context)
            # Should handle gracefully and still produce some code
            assert result is not None
            assert result.code is not None
        except Exception as e:
            # Should provide meaningful error message
            assert str(e) is not None
            assert len(str(e)) > 0

        await generator.cleanup()


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v", "--tb=short"])
