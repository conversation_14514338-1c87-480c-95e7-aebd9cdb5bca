import React, { useState, useEffect, useRef, useCallback, memo } from 'react';

interface LivePreviewProps {
  siteName: string;
  onClose?: () => void;
}

interface PreviewInfo {
  url: string;
  type: 'static' | 'flask' | 'react' | 'node' | 'unknown';
  status: 'loading' | 'running' | 'stopped' | 'error';
  port?: number;
  message?: string;
}

const LivePreview = memo<LivePreviewProps>(({ siteName, onClose }) => {
  const [previewInfo, setPreviewInfo] = useState<PreviewInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isStarting, setIsStarting] = useState(false);
  const [refreshKey, setRefreshKey] = useState(0);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // Load preview information
  useEffect(() => {
    const loadPreviewInfo = async () => {
      try {
        setIsLoading(true);
        setError(null);

        const response = await fetch(`/api/sites/${siteName}/preview`);

        if (!response.ok) {
          throw new Error(`Failed to load preview: ${response.statusText}`);
        }

        const data = await response.json();

        if (data.status === 'success') {
          setPreviewInfo({
            url: data.preview_url || '',
            type: data.site_type || 'unknown',
            status: data.status || 'stopped',
            port: data.port,
            message: data.message
          });
        } else {
          throw new Error(data.message || 'Failed to load preview');
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Unknown error');
      } finally {
        setIsLoading(false);
      }
    };

    if (siteName) {
      loadPreviewInfo();
    }
  }, [siteName, refreshKey]);

  // Start preview server
  const handleStartPreview = useCallback(async () => {
    try {
      setIsStarting(true);
      setError(null);

      const response = await fetch(`/api/sites/${siteName}/preview/start`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to start preview: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.status === 'success') {
        // Refresh preview info
        setRefreshKey(prev => prev + 1);
      } else {
        throw new Error(data.message || 'Failed to start preview');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Unknown error');
    } finally {
      setIsStarting(false);
    }
  }, [siteName, setRefreshKey, setError, setIsStarting]);

  // Stop preview server
  const handleStopPreview = useCallback(async () => {
    try {
      const response = await fetch(`/api/sites/${siteName}/preview/stop`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        }
      });

      if (response.ok) {
        setRefreshKey(prev => prev + 1);
      }
    } catch (err) {
      console.error('Failed to stop preview:', err);
    }
  }, [siteName, setRefreshKey]);

  // Refresh iframe
  const handleRefresh = useCallback(() => {
    if (iframeRef.current) {
      iframeRef.current.src = iframeRef.current.src;
    }
  }, [iframeRef]);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'running': return 'text-green-600 bg-green-100';
      case 'loading': return 'text-yellow-600 bg-yellow-100';
      case 'stopped': return 'text-gray-600 bg-gray-100';
      case 'error': return 'text-red-600 bg-red-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  // Get type icon
  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'react':
        return (
          <svg className="w-4 h-4 text-blue-500" fill="currentColor" viewBox="0 0 20 20">
            <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
            <path fillRule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clipRule="evenodd" />
          </svg>
        );
      case 'flask':
        return (
          <svg className="w-4 h-4 text-green-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
          </svg>
        );
      case 'static':
        return (
          <svg className="w-4 h-4 text-purple-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
          </svg>
        );
      default:
        return (
          <svg className="w-4 h-4 text-gray-500" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
          </svg>
        );
    }
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2">Loading preview...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-4 bg-red-50 border border-red-200 rounded-lg">
        <div className="flex items-center">
          <svg className="w-5 h-5 text-red-400 mr-2" fill="currentColor" viewBox="0 0 20 20">
            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
          </svg>
          <span className="text-red-800 font-medium">Preview Error</span>
        </div>
        <p className="text-red-600 mt-1">{error}</p>
        <button
          onClick={() => setRefreshKey(prev => prev + 1)}
          className="mt-2 px-3 py-1 bg-red-100 text-red-700 rounded hover:bg-red-200"
        >
          Retry
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-white">
      {/* Preview Header */}
      <div className="flex items-center justify-between p-3 bg-gray-50 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="flex items-center">
            <svg className="w-4 h-4 text-gray-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
            </svg>
            <span className="font-medium text-gray-700">Live Preview</span>
          </div>

          {previewInfo && (
            <>
              <div className="flex items-center space-x-2">
                {getTypeIcon(previewInfo.type)}
                <span className="text-sm text-gray-600 capitalize">{previewInfo.type}</span>
              </div>

              <span className={`px-2 py-1 text-xs rounded ${getStatusColor(previewInfo.status)}`}>
                {previewInfo.status}
              </span>

              {previewInfo.port && (
                <span className="text-xs text-gray-500">
                  Port: {previewInfo.port}
                </span>
              )}
            </>
          )}
        </div>

        <div className="flex items-center space-x-2">
          {previewInfo && (
            <>
              {previewInfo.status === 'stopped' && (
                <button
                  onClick={handleStartPreview}
                  disabled={isStarting}
                  className={`px-3 py-1 text-sm rounded ${
                    isStarting
                      ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      : 'bg-green-500 text-white hover:bg-green-600'
                  }`}
                >
                  {isStarting ? 'Starting...' : 'Start Preview'}
                </button>
              )}

              {previewInfo.status === 'running' && (
                <>
                  <button
                    onClick={handleRefresh}
                    className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600"
                  >
                    Refresh
                  </button>
                  <button
                    onClick={handleStopPreview}
                    className="px-3 py-1 text-sm bg-red-500 text-white rounded hover:bg-red-600"
                  >
                    Stop
                  </button>
                </>
              )}
            </>
          )}

          {onClose && (
            <button
              onClick={onClose}
              className="p-1 text-gray-400 hover:text-gray-600"
            >
              <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          )}
        </div>
      </div>

      {/* Preview Content */}
      <div className="flex-1 min-h-0">
        {previewInfo?.status === 'running' && previewInfo.url ? (
          <iframe
            ref={iframeRef}
            src={previewInfo.url}
            className="w-full h-full border-0"
            title={`Preview of ${siteName}`}
            sandbox="allow-scripts allow-same-origin allow-forms allow-popups"
          />
        ) : (
          <div className="flex flex-col items-center justify-center h-full bg-gray-50">
            {previewInfo?.status === 'stopped' ? (
              <div className="text-center">
                <svg className="w-16 h-16 text-gray-300 mx-auto mb-4" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM9.555 7.168A1 1 0 008 8v4a1 1 0 001.555.832l3-2a1 1 0 000-1.664l-3-2z" clipRule="evenodd" />
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">Preview Not Running</h3>
                <p className="text-gray-500 mb-4">
                  Start the preview server to see your site in action
                </p>
                <button
                  onClick={handleStartPreview}
                  disabled={isStarting}
                  className={`px-4 py-2 rounded ${
                    isStarting
                      ? 'bg-gray-200 text-gray-500 cursor-not-allowed'
                      : 'bg-blue-500 text-white hover:bg-blue-600'
                  }`}
                >
                  {isStarting ? 'Starting...' : 'Start Preview'}
                </button>
              </div>
            ) : (
              <div className="text-center">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                <p className="text-gray-500">Loading preview...</p>
              </div>
            )}
          </div>
        )}
      </div>

      {/* Preview Info */}
      {previewInfo?.message && (
        <div className="p-3 bg-blue-50 border-t border-blue-200">
          <div className="flex items-start">
            <svg className="w-4 h-4 text-blue-400 mr-2 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
            </svg>
            <p className="text-sm text-blue-700">{previewInfo.message}</p>
          </div>
        </div>
      )}
    </div>
  );
});

export default LivePreview;
