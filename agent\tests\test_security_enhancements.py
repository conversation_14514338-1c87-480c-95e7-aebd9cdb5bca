#!/usr/bin/env python3
"""
Comprehensive Security Enhancements Test Suite
Tests subprocess safety, path validation, and directory confinement features
"""

import asyncio
import json
import os
import sys
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agent.core.validators.safety_validator import SafetyValidator
from agent.core.validators.subprocess_safety_manager import SubprocessSafetyManager


class SecurityEnhancementsTester:
    """Comprehensive tester for security enhancements"""

    def __init__(self):
        self.test_results = []
        self.subprocess_manager = SubprocessSafetyManager()
        self.test_site_name = "test-security-site"
        self.test_site_path = Path("sites") / self.test_site_name

    async def run_all_tests(self) -> Dict[str, Any]:
        """Run all security enhancement tests"""
        print("🔒 Testing Security Enhancements...")
        print("=" * 60)

        # Create test site directory
        self.test_site_path.mkdir(parents=True, exist_ok=True)

        # Run test categories
        self._test_path_validation()
        self._test_directory_confinement()
        self._test_subprocess_safety()
        self._test_command_validation()
        self._test_shell_injection_prevention()
        self._test_upload_path_validation()
        await self._test_subprocess_manager_integration()

        # Cleanup
        self._cleanup_test_site()

        # Generate report
        return self._generate_report()

    def _test_path_validation(self):
        """Test enhanced path validation"""
        print("\n🔍 Testing Path Validation...")

        test_cases = [
            # Valid paths
            ("sites/test-site", True, "Valid site path"),
            ("sites/test-site/index.html", True, "Valid file path"),
            ("uploads/test-file.zip", True, "Valid upload path"),
            # Invalid paths - path traversal
            ("sites/../config/config.json", False, "Path traversal attempt"),
            ("sites/test-site/../../scripts/test.py", False, "Path traversal attempt"),
            ("sites/test-site/..\\..\\core\\agent.py", False, "Windows path traversal"),
            # Invalid paths - forbidden directories
            ("src/main.py", False, "Forbidden src directory"),
            ("scripts/test.py", False, "Forbidden scripts directory"),
            ("config/settings.json", False, "Forbidden config directory"),
            ("core/agent.py", False, "Forbidden core directory"),
            ("api/routes.py", False, "Forbidden api directory"),
            # Invalid paths - dangerous characters
            ("sites/test-site/file<>.txt", False, "Dangerous characters"),
            ("sites/test-site/file|.txt", False, "Dangerous characters"),
            ("sites/test-site/file*.txt", False, "Dangerous characters"),
            # Invalid paths - too long
            ("sites/" + "a" * 300, False, "Path too long"),
        ]

        for path, should_pass, description in test_cases:
            try:
                result = SafetyValidator.validate_file_path(path)
                if should_pass and result:
                    self._add_result("PASSED", f"Path Validation: {description}")
                elif not should_pass:
                    self._add_result(
                        "FAILED", f"Path Validation: {description} - Should have failed"
                    )
                else:
                    self._add_result(
                        "FAILED", f"Path Validation: {description} - Unexpected result"
                    )
            except ValueError as e:
                if not should_pass:
                    self._add_result(
                        "PASSED",
                        f"Path Validation: {description} - Correctly rejected: {str(e)}",
                    )
                else:
                    self._add_result(
                        "FAILED",
                        f"Path Validation: {description} - Should have passed: {str(e)}",
                    )

    def _test_directory_confinement(self):
        """Test directory confinement enforcement"""
        print("\n🔍 Testing Directory Confinement...")

        test_cases = [
            # Valid directories
            ("sites/test-site", True, "Valid site directory"),
            ("uploads", True, "Valid upload directory"),
            # Invalid directories - outside allowed paths
            ("/tmp/test", False, "Absolute path outside allowed areas"),
            ("C:/Windows/System32", False, "Windows system directory"),
            ("/etc/passwd", False, "System file"),
            ("/home/<USER>/.ssh", False, "User SSH directory"),
            # Invalid directories - forbidden paths
            ("src", False, "Forbidden src directory"),
            ("scripts", False, "Forbidden scripts directory"),
            ("config", False, "Forbidden config directory"),
            ("core", False, "Forbidden core directory"),
        ]

        for path, should_pass, description in test_cases:
            try:
                result = SafetyValidator.enforce_directory_confinement(path)
                if should_pass and result:
                    self._add_result("PASSED", f"Directory Confinement: {description}")
                elif not should_pass:
                    self._add_result(
                        "FAILED",
                        f"Directory Confinement: {description} - Should have failed",
                    )
                else:
                    self._add_result(
                        "FAILED",
                        f"Directory Confinement: {description} - Unexpected result",
                    )
            except ValueError as e:
                if not should_pass:
                    self._add_result(
                        "PASSED",
                        f"Directory Confinement: {description} - Correctly rejected: {str(e)}",
                    )
                else:
                    self._add_result(
                        "FAILED",
                        f"Directory Confinement: {description} - Should have passed: {str(e)}",
                    )

    def _test_subprocess_safety(self):
        """Test subprocess safety validation"""
        print("\n🔍 Testing Subprocess Safety...")

        test_cases = [
            # Valid commands
            ("npm install", True, "Valid npm command"),
            ("yarn build", True, "Valid yarn command"),
            ("node server.js", True, "Valid node command"),
            ("python -m pip install requests", True, "Valid python command"),
            ("git status", True, "Valid git command"),
            # Invalid commands - forbidden
            ("rm -rf /", False, "Forbidden rm command"),
            ("del /s /q C:\\", False, "Forbidden del command"),
            ("format C:", False, "Forbidden format command"),
            ("fdisk /dev/sda", False, "Forbidden fdisk command"),
            ("shutdown -h now", False, "Forbidden shutdown command"),
            ("reboot", False, "Forbidden reboot command"),
            # Invalid commands - not in whitelist
            ("curl http://example.com", False, "Command not in whitelist"),
            ("wget http://example.com", False, "Command not in whitelist"),
            ("ls -la", False, "Command not in whitelist"),
            ("cat /etc/passwd", False, "Command not in whitelist"),
        ]

        for command, should_pass, description in test_cases:
            try:
                result = SafetyValidator.validate_command(command)
                if should_pass and result:
                    self._add_result("PASSED", f"Subprocess Safety: {description}")
                elif not should_pass:
                    self._add_result(
                        "FAILED",
                        f"Subprocess Safety: {description} - Should have failed",
                    )
                else:
                    self._add_result(
                        "FAILED",
                        f"Subprocess Safety: {description} - Unexpected result",
                    )
            except ValueError as e:
                if not should_pass:
                    self._add_result(
                        "PASSED",
                        f"Subprocess Safety: {description} - Correctly rejected: {str(e)}",
                    )
                else:
                    self._add_result(
                        "FAILED",
                        f"Subprocess Safety: {description} - Should have passed: {str(e)}",
                    )

    def _test_command_validation(self):
        """Test command validation through subprocess manager"""
        print("\n🔍 Testing Command Validation...")

        test_cases = [
            # Valid commands
            ("npm install", True, "Valid npm command"),
            ("yarn build", True, "Valid yarn command"),
            # Invalid commands
            ("rm -rf /", False, "Forbidden rm command"),
            ("curl http://example.com", False, "Command not in whitelist"),
        ]

        for command, should_pass, description in test_cases:
            result = self.subprocess_manager.validate_command_safety(command)
            if result == should_pass:
                self._add_result("PASSED", f"Command Validation: {description}")
            else:
                self._add_result(
                    "FAILED",
                    f"Command Validation: {description} - Expected {should_pass}, got {result}",
                )

    def _test_shell_injection_prevention(self):
        """Test shell injection prevention"""
        print("\n🔍 Testing Shell Injection Prevention...")

        injection_attempts = [
            ("npm install && rm -rf /", "Command chaining"),
            ("npm install || echo 'hacked'", "Command chaining"),
            ("npm install; rm -rf /", "Command separator"),
            ("npm install | cat /etc/passwd", "Pipe redirection"),
            ("npm install > /tmp/hack", "Output redirection"),
            ("npm install < /etc/passwd", "Input redirection"),
            ("npm install & rm -rf /", "Background execution"),
            ("npm install # rm -rf /", "Comment injection"),
            ("npm install `rm -rf /`", "Backtick substitution"),
            ("npm install $(rm -rf /)", "Command substitution"),
            ("npm install {rm,-rf,/}", "Brace expansion"),
            ("npm install $PATH", "Variable expansion"),
        ]

        for command, description in injection_attempts:
            try:
                self.subprocess_manager._check_shell_injection(command)
                self._add_result(
                    "FAILED",
                    f"Shell Injection: {description} - Should have been detected",
                )
            except ValueError as e:
                self._add_result(
                    "PASSED",
                    f"Shell Injection: {description} - Correctly detected: {str(e)}",
                )

    def _test_upload_path_validation(self):
        """Test upload path validation"""
        print("\n🔍 Testing Upload Path Validation...")

        test_cases = [
            # Valid upload paths
            ("uploads/test-file.zip", True, "Valid upload path"),
            ("uploads/site-backup.tar.gz", True, "Valid upload path"),
            # Invalid upload paths
            ("sites/test-file.zip", False, "Upload path outside uploads directory"),
            ("uploads/../config/config.json", False, "Path traversal from uploads"),
            ("uploads/test-file<>.zip", False, "Dangerous characters in upload path"),
        ]

        for path, should_pass, description in test_cases:
            try:
                result = SafetyValidator.validate_upload_path(path)
                if should_pass and result:
                    self._add_result("PASSED", f"Upload Path Validation: {description}")
                elif not should_pass:
                    self._add_result(
                        "FAILED",
                        f"Upload Path Validation: {description} - Should have failed",
                    )
                else:
                    self._add_result(
                        "FAILED",
                        f"Upload Path Validation: {description} - Unexpected result",
                    )
            except ValueError as e:
                if not should_pass:
                    self._add_result(
                        "PASSED",
                        f"Upload Path Validation: {description} - Correctly rejected: {str(e)}",
                    )
                else:
                    self._add_result(
                        "FAILED",
                        f"Upload Path Validation: {description} - Should have passed: {str(e)}",
                    )

    async def _test_subprocess_manager_integration(self):
        """Test subprocess manager integration"""
        print("\n🔍 Testing Subprocess Manager Integration...")

        # Test valid command execution
        try:
            result = await self.subprocess_manager.run_safe_command(
                command="echo 'test'", cwd=str(self.test_site_path), timeout=10
            )

            if result["success"] and "test" in result["stdout"]:
                self._add_result(
                    "PASSED", "Subprocess Manager: Valid command execution"
                )
            else:
                self._add_result(
                    "FAILED", f"Subprocess Manager: Valid command failed - {result}"
                )

        except Exception as e:
            self._add_result(
                "FAILED", f"Subprocess Manager: Valid command exception - {str(e)}"
            )

        # Test invalid command rejection
        try:
            result = await self.subprocess_manager.run_safe_command(
                command="rm -rf /", cwd=str(self.test_site_path), timeout=10
            )
            self._add_result(
                "FAILED",
                "Subprocess Manager: Invalid command should have been rejected",
            )
        except Exception as e:
            self._add_result(
                "PASSED",
                f"Subprocess Manager: Invalid command correctly rejected - {str(e)}",
            )

        # Test process status
        status = self.subprocess_manager.get_process_status()
        if isinstance(status, dict) and "active_processes" in status:
            self._add_result("PASSED", "Subprocess Manager: Process status tracking")
        else:
            self._add_result(
                "FAILED", "Subprocess Manager: Process status tracking failed"
            )

    def _add_result(self, status: str, message: str):
        """Add test result"""
        self.test_results.append(
            {
                "status": status,
                "message": message,
                "timestamp": datetime.now(timezone.utc).isoformat(),
            }
        )

        # Print result
        emoji = "✅" if status == "PASSED" else "❌"
        print(f"   {emoji} {message}")

    def _cleanup_test_site(self):
        """Clean up test site directory"""
        try:
            if self.test_site_path.exists():
                import shutil

                shutil.rmtree(self.test_site_path)
        except Exception as e:
            print(f"⚠️ Warning: Could not cleanup test site: {e}")

    def _generate_report(self) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        passed = sum(1 for result in self.test_results if result["status"] == "PASSED")
        failed = sum(1 for result in self.test_results if result["status"] == "FAILED")
        total = len(self.test_results)
        success_rate = (passed / total * 100) if total > 0 else 0

        report = {
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "total_tests": total,
            "passed_tests": passed,
            "failed_tests": failed,
            "success_rate": round(success_rate, 2),
            "test_results": self.test_results,
            "summary": {
                "path_validation": sum(
                    1
                    for r in self.test_results
                    if "Path Validation" in r["message"] and r["status"] == "PASSED"
                ),
                "directory_confinement": sum(
                    1
                    for r in self.test_results
                    if "Directory Confinement" in r["message"]
                    and r["status"] == "PASSED"
                ),
                "subprocess_safety": sum(
                    1
                    for r in self.test_results
                    if "Subprocess Safety" in r["message"] and r["status"] == "PASSED"
                ),
                "command_validation": sum(
                    1
                    for r in self.test_results
                    if "Command Validation" in r["message"] and r["status"] == "PASSED"
                ),
                "shell_injection": sum(
                    1
                    for r in self.test_results
                    if "Shell Injection" in r["message"] and r["status"] == "PASSED"
                ),
                "upload_validation": sum(
                    1
                    for r in self.test_results
                    if "Upload Path Validation" in r["message"]
                    and r["status"] == "PASSED"
                ),
                "subprocess_manager": sum(
                    1
                    for r in self.test_results
                    if "Subprocess Manager" in r["message"] and r["status"] == "PASSED"
                ),
            },
        }

        return report


async def main():
    """Main test execution"""
    print("🔒 SECURITY ENHANCEMENTS TEST SUITE")
    print("=" * 60)

    tester = SecurityEnhancementsTester()
    report = await tester.run_all_tests()

    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST SUMMARY")
    print("=" * 60)
    print(f"Total Tests: {report['total_tests']}")
    print(f"Passed: {report['passed_tests']}")
    print(f"Failed: {report['failed_tests']}")
    print(f"Success Rate: {report['success_rate']}%")

    print("\n📋 DETAILED BREAKDOWN:")
    for category, count in report["summary"].items():
        print(f"   {category.replace('_', ' ').title()}: {count} tests passed")

    # Save results
    results_file = project_root / "test_results_security_enhancements.json"
    with open(results_file, "w") as f:
        json.dump(report, f, indent=2)

    print(f"\n📄 Results saved to: {results_file}")

    # Return success/failure
    if report["success_rate"] >= 95:
        print("\n🎉 Security enhancements test suite PASSED!")
        return True
    else:
        print("\n❌ Security enhancements test suite FAILED!")
        return False


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
