#!/usr/bin/env python3
"""
Performance tests for AI Coding Agent orchestration components
"""
import asyncio
import time
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest

# Import components for performance testing
from agent.core.context.shared_context_manager import SharedContextManager
from agent.core.cursor_rules_enforcer import CursorRulesEnforcer


@pytest.fixture
def context_manager():
    return SharedContextManager(storage_dir="data/test_context", max_sessions=10)


@pytest.fixture
def enforcer():
    return CursorRulesEnforcer()


def test_context_manager_creation_performance(benchmark, context_manager):
    """Benchmark context manager creation"""
    def create_context_manager():
        return SharedContextManager(storage_dir="data/test_context", max_sessions=10)
    
    result = benchmark(create_context_manager)
    assert result is not None


def test_context_session_creation_performance(benchmark, context_manager):
    """Benchmark context session creation"""
    def create_session():
        return context_manager.create_session(
            f"test_session_{time.time()}",
            {"test": "metadata"}
        )
    
    result = benchmark(create_session)
    assert result is not None


def test_context_set_performance(benchmark, context_manager):
    """Benchmark context value setting"""
    session_id = "perf_test_session"
    context_manager.create_session(session_id)
    
    def set_context():
        return context_manager.set_context(
            session_id=session_id,
            key=f"test_key_{time.time()}",
            value={"data": "test_value", "timestamp": time.time()},
            agent_id="perf_test_agent"
        )
    
    result = benchmark(set_context)
    assert result is True


def test_context_get_performance(benchmark, context_manager):
    """Benchmark context value retrieval"""
    session_id = "perf_test_session"
    context_manager.create_session(session_id)
    
    # Pre-populate with test data
    for i in range(100):
        context_manager.set_context(
            session_id=session_id,
            key=f"test_key_{i}",
            value=f"test_value_{i}",
            agent_id="perf_test_agent"
        )
    
    def get_context():
        return context_manager.get_context(
            session_id=session_id,
            key="test_key_50"
        )
    
    result = benchmark(get_context)
    assert result == "test_value_50"


def test_cursor_rules_enforcer_creation_performance(benchmark):
    """Benchmark cursor rules enforcer creation"""
    def create_enforcer():
        return CursorRulesEnforcer()
    
    result = benchmark(create_enforcer)
    assert result is not None


def test_cursor_rules_compliance_check_performance(benchmark, enforcer):
    """Benchmark compliance checking"""
    def check_compliance():
        return enforcer.check_compliance()
    
    result = benchmark(check_compliance)
    assert "compliance_score" in result


def test_file_cleanup_check_performance(benchmark, enforcer):
    """Benchmark file cleanup checking"""
    def check_file_cleanup():
        return enforcer._check_file_cleanup()
    
    result = benchmark(check_file_cleanup)
    assert "status" in result


@pytest.mark.asyncio
async def test_async_context_operations_performance(context_manager):
    """Test performance of async context operations"""
    session_id = "async_perf_test_session"
    context_manager.create_session(session_id)
    
    start_time = time.time()
    
    # Simulate concurrent context operations
    tasks = []
    for i in range(50):
        # Set context
        task = asyncio.create_task(
            asyncio.to_thread(
                context_manager.set_context,
                session_id=session_id,
                key=f"async_key_{i}",
                value=f"async_value_{i}",
                agent_id="async_agent"
            )
        )
        tasks.append(task)
    
    # Wait for all operations to complete
    results = await asyncio.gather(*tasks)
    
    end_time = time.time()
    duration = end_time - start_time
    
    # All operations should succeed
    assert all(results)
    
    # Should complete within reasonable time (adjust threshold as needed)
    assert duration < 5.0, f"Async operations took too long: {duration:.2f}s"
    
    print(f"Async context operations completed in {duration:.2f}s")


def test_memory_usage_context_manager(context_manager):
    """Test memory usage of context manager with large datasets"""
    try:
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        session_id = "memory_test_session"
        context_manager.create_session(session_id)
        
        # Add large amount of data
        for i in range(1000):
            large_data = {
                "id": i,
                "data": "x" * 1000,  # 1KB of data
                "metadata": {
                    "timestamp": time.time(),
                    "index": i,
                    "tags": [f"tag_{j}" for j in range(10)]
                }
            }
            context_manager.set_context(
                session_id=session_id,
                key=f"large_key_{i}",
                value=large_data,
                agent_id="memory_test_agent"
            )
        
        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory
        
        print(f"Memory usage increased by {memory_increase:.2f} MB for 1000 entries")
        
        # Memory increase should be reasonable (adjust threshold as needed)
        assert memory_increase < 100, f"Memory usage too high: {memory_increase:.2f} MB"
        
    except ImportError:
        pytest.skip("psutil not available for memory testing")


def test_concurrent_access_performance(context_manager):
    """Test performance under concurrent access"""
    import threading
    import queue
    
    session_id = "concurrent_test_session"
    context_manager.create_session(session_id)
    
    results_queue = queue.Queue()
    num_threads = 10
    operations_per_thread = 50
    
    def worker(thread_id):
        thread_results = []
        for i in range(operations_per_thread):
            start_time = time.time()
            
            # Set operation
            success = context_manager.set_context(
                session_id=session_id,
                key=f"thread_{thread_id}_key_{i}",
                value=f"thread_{thread_id}_value_{i}",
                agent_id=f"thread_{thread_id}_agent"
            )
            
            # Get operation
            value = context_manager.get_context(
                session_id=session_id,
                key=f"thread_{thread_id}_key_{i}"
            )
            
            end_time = time.time()
            thread_results.append({
                "success": success,
                "value_correct": value == f"thread_{thread_id}_value_{i}",
                "duration": end_time - start_time
            })
        
        results_queue.put(thread_results)
    
    # Start threads
    threads = []
    start_time = time.time()
    
    for thread_id in range(num_threads):
        thread = threading.Thread(target=worker, args=(thread_id,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    end_time = time.time()
    total_duration = end_time - start_time
    
    # Collect results
    all_results = []
    while not results_queue.empty():
        all_results.extend(results_queue.get())
    
    # Verify results
    assert len(all_results) == num_threads * operations_per_thread
    
    successful_operations = sum(1 for r in all_results if r["success"] and r["value_correct"])
    success_rate = successful_operations / len(all_results)
    
    avg_operation_time = sum(r["duration"] for r in all_results) / len(all_results)
    
    print(f"Concurrent test: {success_rate:.2%} success rate, {avg_operation_time:.4f}s avg operation time")
    print(f"Total duration: {total_duration:.2f}s for {len(all_results)} operations")
    
    # Should have high success rate
    assert success_rate > 0.95, f"Success rate too low: {success_rate:.2%}"
    
    # Should complete within reasonable time
    assert total_duration < 30, f"Concurrent operations took too long: {total_duration:.2f}s"


def test_cleanup_performance(benchmark, enforcer):
    """Benchmark cleanup operations performance"""
    def run_cleanup():
        return enforcer.auto_fix_cleanup_issues(dry_run=True)
    
    result = benchmark(run_cleanup)
    assert "success" in result


@pytest.mark.asyncio
async def test_monitoring_hooks_performance():
    """Test performance of monitoring hooks"""
    try:
        from agent.core.container_monitor_hooks import container_monitor_hooks, monitor_container_performance
        
        start_time = time.time()
        
        # Simulate monitoring multiple containers
        tasks = []
        for i in range(10):
            task = asyncio.create_task(
                monitor_container_performance(f"test_container_{i}")
            )
            tasks.append(task)
        
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"Monitoring {len(tasks)} containers took {duration:.2f}s")
        
        # Should complete within reasonable time
        assert duration < 10, f"Monitoring took too long: {duration:.2f}s"
        
        # All monitoring operations should return metrics
        assert all(result is not None for result in results)
        
    except ImportError:
        pytest.skip("Container monitoring hooks not available")


def test_basic_performance_without_benchmark():
    """Run basic performance tests without pytest-benchmark"""
    print("Running basic performance tests...")
    
    # Test context manager performance
    context_manager = SharedContextManager(storage_dir="data/test_context")
    
    start_time = time.time()
    session = context_manager.create_session("perf_test", {"test": "data"})
    creation_time = time.time() - start_time
    print(f"Context session creation: {creation_time:.4f}s")
    
    start_time = time.time()
    context_manager.set_context("perf_test", "test_key", "test_value", "test_agent")
    set_time = time.time() - start_time
    print(f"Context set operation: {set_time:.4f}s")
    
    start_time = time.time()
    value = context_manager.get_context("perf_test", "test_key")
    get_time = time.time() - start_time
    print(f"Context get operation: {get_time:.4f}s")
    
    # Test enforcer performance
    enforcer = CursorRulesEnforcer()
    
    start_time = time.time()
    compliance = enforcer.check_compliance()
    compliance_time = time.time() - start_time
    print(f"Compliance check: {compliance_time:.4f}s")
    
    print("Basic performance tests completed!")
    
    # Basic assertions
    assert creation_time < 1.0, "Context creation too slow"
    assert set_time < 0.1, "Context set too slow"
    assert get_time < 0.1, "Context get too slow"
    assert compliance_time < 5.0, "Compliance check too slow"


if __name__ == "__main__":
    test_basic_performance_without_benchmark()
