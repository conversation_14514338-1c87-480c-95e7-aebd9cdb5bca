/**
 * Learning Integration Tests
 * Tests for the learning system integration components
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { LearningDashboard } from '../components/learning/LearningDashboard';
import { LearningCard } from '../components/learning/LearningCard';
import { LearningAnalytics } from '../components/learning/LearningAnalytics';
import { LearningStatus } from '../components/learning/LearningStatus';
import { LearningRecommendations } from '../components/learning/LearningRecommendations';
import { useLearningStore } from '../store/learningStore';

// Mock the learning store
jest.mock('../store/learningStore', () => ({
  useLearningStore: jest.fn(),
}));

const mockUseLearningStore = useLearningStore as jest.MockedFunction<typeof useLearningStore>;

describe('Learning Integration', () => {
  const mockStoreData = {
    status: {
      success: true,
      message: 'OK',
      data: {
        status: 'active',
        active_models: 5,
        total_patterns: 1250,
        last_updated: '2025-01-19T10:00:00Z',
        performance_metrics: {
          accuracy: 0.85,
          response_time: 1.2,
          user_satisfaction: 0.78,
        },
      },
    },
    summary: {
      success: true,
      message: 'OK',
      summary: {
        total_patterns_learned: 1250,
        user_preferences: 89,
        performance_insights: 156,
        recommendations_generated: 23,
        last_learning_cycle: '2025-01-19T09:30:00Z',
        system_health: 'healthy',
      },
    },
    recommendations: [
      {
        id: '1',
        type: 'performance',
        title: 'Optimize Response Time',
        description: 'Consider implementing caching to improve response times.',
        confidence: 0.92,
        priority: 'high' as const,
        category: 'performance',
        created_at: '2025-01-19T10:00:00Z',
      },
      {
        id: '2',
        type: 'learning',
        title: 'New Pattern Detected',
        description: 'A new coding pattern has been identified.',
        confidence: 0.78,
        priority: 'medium' as const,
        category: 'patterns',
        created_at: '2025-01-19T09:45:00Z',
      },
    ],
    loading: false,
    error: null,
    lastUpdate: new Date('2025-01-19T10:00:00Z'),
    refreshAll: jest.fn(),
    refreshStatus: jest.fn(),
    refreshSummary: jest.fn(),
    refreshRecommendations: jest.fn(),
    learnCodePattern: jest.fn(),
    learnUserPreference: jest.fn(),
    recordLearningEvent: jest.fn(),
    learnPerformanceInsight: jest.fn(),
    runEnhancementCycle: jest.fn(),
    enableEnhancement: jest.fn(),
    disableEnhancement: jest.fn(),
    getAnalytics: jest.fn(() => ({
      totalPatterns: 1250,
      userPreferences: 89,
      performanceInsights: 156,
      recommendationsGenerated: 23,
      systemHealth: 'healthy',
      activeModels: 5,
      accuracy: 0.85,
      responseTime: 1.2,
      userSatisfaction: 0.78,
      highPriorityRecommendations: 1,
      mediumPriorityRecommendations: 1,
      lowPriorityRecommendations: 0,
    })),
  };

  beforeEach(() => {
    mockUseLearningStore.mockReturnValue(mockStoreData);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('LearningDashboard', () => {
    it('renders the learning dashboard with all components', () => {
      render(<LearningDashboard />);

      expect(screen.getByText('Learning Dashboard')).toBeInTheDocument();
      expect(screen.getByText('Monitor and manage the AI learning system performance and insights')).toBeInTheDocument();
      // Use more specific selectors to avoid conflicts
      expect(screen.getAllByText('Active Models')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Total Patterns')[0]).toBeInTheDocument();
      expect(screen.getAllByText('System Health')[0]).toBeInTheDocument();
    });

    it('displays loading state when loading', () => {
      mockUseLearningStore.mockReturnValue({
        ...mockStoreData,
        loading: true,
        status: null,
        summary: null,
      });

      render(<LearningDashboard />);

      expect(screen.getByTestId('loading-spinner')).toBeInTheDocument();
    });

    it('displays error state when there is an error', () => {
      mockUseLearningStore.mockReturnValue({
        ...mockStoreData,
        error: 'Failed to load learning data',
      });

      render(<LearningDashboard />);

      expect(screen.getByText('Error loading learning data')).toBeInTheDocument();
      expect(screen.getByText('Failed to load learning data')).toBeInTheDocument();
      expect(screen.getByText('Retry')).toBeInTheDocument();
    });
  });

  describe('LearningCard', () => {
    it('renders a learning card with correct data', () => {
      render(
        <LearningCard
          title="Test Card"
          value={42}
          icon="status"
          color="blue"
        />
      );

      expect(screen.getByText('Test Card')).toBeInTheDocument();
      expect(screen.getByText('42')).toBeInTheDocument();
    });

    it('renders a learning card with string value', () => {
      render(
        <LearningCard
          title="Status"
          value="Active"
          icon="status"
          color="green"
        />
      );

      expect(screen.getByText('Status')).toBeInTheDocument();
      expect(screen.getByText('Active')).toBeInTheDocument();
    });
  });

  describe('LearningAnalytics', () => {
    it('renders learning analytics with performance metrics', () => {
      render(<LearningAnalytics />);

      expect(screen.getByText('Learning Analytics')).toBeInTheDocument();
      expect(screen.getByText('Performance Metrics')).toBeInTheDocument();
      expect(screen.getByText('Accuracy')).toBeInTheDocument();
      expect(screen.getByText('85.0%')).toBeInTheDocument();
      expect(screen.getByText('Response Time')).toBeInTheDocument();
      expect(screen.getByText('1.20s')).toBeInTheDocument();
      expect(screen.getByText('User Satisfaction')).toBeInTheDocument();
      expect(screen.getByText('78.0%')).toBeInTheDocument();
    });

    it('renders learning statistics', () => {
      render(<LearningAnalytics />);

      expect(screen.getByText('Learning Statistics')).toBeInTheDocument();
      expect(screen.getByText('Total Patterns')).toBeInTheDocument();
      expect(screen.getByText('1,250')).toBeInTheDocument();
      expect(screen.getByText('User Preferences')).toBeInTheDocument();
      expect(screen.getByText('89')).toBeInTheDocument();
    });
  });

  describe('LearningStatus', () => {
    it('renders system status information', () => {
      render(<LearningStatus />);

      // Use more specific selectors to avoid conflicts
      expect(screen.getAllByText('System Status')[0]).toBeInTheDocument();
      expect(screen.getAllByText('Active Models')[0]).toBeInTheDocument();
      expect(screen.getByText('5')).toBeInTheDocument();
      expect(screen.getAllByText('Total Patterns')[0]).toBeInTheDocument();
      expect(screen.getByText('1250')).toBeInTheDocument();
    });

    it('renders performance metrics when available', () => {
      render(<LearningStatus />);

      expect(screen.getByText('Performance Metrics')).toBeInTheDocument();
      expect(screen.getByText('Accuracy')).toBeInTheDocument();
      expect(screen.getByText('85.0%')).toBeInTheDocument();
      expect(screen.getByText('Response Time')).toBeInTheDocument();
      expect(screen.getByText('1.20s')).toBeInTheDocument();
    });

    it('handles run enhancement cycle button click', async () => {
      render(<LearningStatus />);

      const runButton = screen.getByText('Run Enhancement Cycle');
      fireEvent.click(runButton);

      await waitFor(() => {
        expect(mockStoreData.runEnhancementCycle).toHaveBeenCalled();
      });
    });
  });

  describe('LearningRecommendations', () => {
    it('renders recommendations list', () => {
      render(<LearningRecommendations />);

      expect(screen.getByText('Learning Recommendations')).toBeInTheDocument();
      expect(screen.getByText('Optimize Response Time')).toBeInTheDocument();
      expect(screen.getByText('New Pattern Detected')).toBeInTheDocument();
    });

    it('filters recommendations by priority', () => {
      render(<LearningRecommendations />);

      const highPriorityButton = screen.getByText('High');
      fireEvent.click(highPriorityButton);

      expect(screen.getByText('Optimize Response Time')).toBeInTheDocument();
      expect(screen.queryByText('New Pattern Detected')).not.toBeInTheDocument();
    });

    it('displays recommendation details', () => {
      render(<LearningRecommendations />);

      expect(screen.getByText('HIGH')).toBeInTheDocument();
      expect(screen.getByText('performance')).toBeInTheDocument();
      expect(screen.getByText('Confidence: 92.0%')).toBeInTheDocument();
      expect(screen.getByText('Category: performance')).toBeInTheDocument();
    });
  });
});
