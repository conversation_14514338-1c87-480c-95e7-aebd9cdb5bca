#!/usr/bin/env python3
"""
Tests for orchestration CLI commands
"""
import json
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest
from click.testing import CliRunner

from agent.cli.orchestration_commands import orchestration


@pytest.fixture
def cli_runner():
    return CliRunner()


@pytest.fixture
def temp_project_dir():
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir


def test_project_create_command(cli_runner, temp_project_dir):
    """Test project create command"""
    with cli_runner.isolated_filesystem():
        # Create data directory
        Path("data/projects").mkdir(parents=True, exist_ok=True)
        
        result = cli_runner.invoke(orchestration, [
            'project', 'create', 'test_project',
            '--title', 'Test Project',
            '--description', 'A test project'
        ])
        
        assert result.exit_code == 0
        assert "✅ Created project: test_project" in result.output
        assert "Title: Test Project" in result.output


def test_project_list_command(cli_runner):
    """Test project list command"""
    with cli_runner.isolated_filesystem():
        # Create mock project structure
        project_dir = Path("data/projects/test_project")
        project_dir.mkdir(parents=True, exist_ok=True)
        
        # Create mock roadmap
        roadmap_file = project_dir / "roadmap.json"
        roadmap_data = {
            "id": "test_roadmap",
            "title": "Test Project",
            "description": "Test description",
            "phases": []
        }
        roadmap_file.write_text(json.dumps(roadmap_data))
        
        # Create mock state
        state_file = project_dir / "state.json"
        state_data = {
            "project_id": "test_project",
            "current_phase": "planning",
            "current_step": "",
            "current_task": "",
            "last_updated": "2024-01-01T00:00:00"
        }
        state_file.write_text(json.dumps(state_data))
        
        result = cli_runner.invoke(orchestration, ['project', 'list'])
        
        assert result.exit_code == 0
        assert "📋 Projects:" in result.output
        assert "test_project" in result.output


def test_project_status_command(cli_runner):
    """Test project status command"""
    with cli_runner.isolated_filesystem():
        # Create mock project structure
        project_dir = Path("data/projects/test_project")
        project_dir.mkdir(parents=True, exist_ok=True)
        
        # Create mock state
        state_file = project_dir / "state.json"
        state_data = {
            "project_id": "test_project",
            "current_phase": "development",
            "current_step": "backend",
            "current_task": "api_setup",
            "last_updated": "2024-01-01T00:00:00"
        }
        state_file.write_text(json.dumps(state_data))
        
        result = cli_runner.invoke(orchestration, ['project', 'status', 'test_project'])
        
        assert result.exit_code == 0
        assert "📊 Project Status: test_project" in result.output
        assert "Current Phase: development" in result.output
        assert "Current Step: backend" in result.output


def test_project_status_not_found(cli_runner):
    """Test project status command with non-existent project"""
    with cli_runner.isolated_filesystem():
        result = cli_runner.invoke(orchestration, ['project', 'status', 'nonexistent'])
        
        assert result.exit_code == 1
        assert "❌ Project not found: nonexistent" in result.output


@patch('cli.orchestration_commands.ValidationPipeline')
def test_validation_run_command(mock_pipeline_class, cli_runner):
    """Test validation run command"""
    # Mock validation pipeline
    mock_pipeline = MagicMock()
    mock_pipeline_class.return_value = mock_pipeline
    
    # Mock validation result
    mock_result = MagicMock()
    mock_result.overall_status.value = "passed"
    mock_result.passed_count = 5
    mock_result.failed_count = 0
    mock_result.duration = 2.5
    mock_result.results = []
    
    async def mock_run_validation(suite_name):
        return mock_result
    
    mock_pipeline.run_validation_suite = mock_run_validation
    mock_pipeline.get_validation_summary.return_value = {"blocking_failure": False}
    
    result = cli_runner.invoke(orchestration, ['validation', 'run'])
    
    assert result.exit_code == 0
    assert "🔍 Validation Results: default" in result.output
    assert "Overall Status: passed" in result.output
    assert "Passed: 5" in result.output
    assert "Failed: 0" in result.output


@patch('cli.orchestration_commands.ValidationPipeline')
def test_validation_summary_command(mock_pipeline_class, cli_runner):
    """Test validation summary command"""
    # Mock validation pipeline
    mock_pipeline = MagicMock()
    mock_pipeline_class.return_value = mock_pipeline
    
    mock_pipeline.get_validation_summary.return_value = {
        "suite_name": "test_suite",
        "overall_status": "passed",
        "passed_count": 8,
        "failed_count": 2,
        "total_checks": 10,
        "duration": 3.2,
        "blocking_failure": False
    }
    
    result = cli_runner.invoke(orchestration, ['validation', 'summary'])
    
    assert result.exit_code == 0
    assert "📊 Validation Summary:" in result.output
    assert "Suite: test_suite" in result.output
    assert "Status: passed" in result.output


@patch('cli.orchestration_commands.SharedContextManager')
def test_context_create_session_command(mock_context_class, cli_runner):
    """Test context create-session command"""
    # Mock context manager
    mock_context = MagicMock()
    mock_context_class.return_value = mock_context
    
    mock_session = MagicMock()
    mock_session.created_at = "2024-01-01T00:00:00"
    mock_session.metadata = {"test": "data"}
    mock_context.create_session.return_value = mock_session
    
    result = cli_runner.invoke(orchestration, [
        'context', 'create-session', 'test_session',
        '--metadata', '{"test": "data"}'
    ])
    
    assert result.exit_code == 0
    assert "✅ Created context session: test_session" in result.output


@patch('cli.orchestration_commands.SharedContextManager')
def test_context_set_command(mock_context_class, cli_runner):
    """Test context set command"""
    # Mock context manager
    mock_context = MagicMock()
    mock_context_class.return_value = mock_context
    mock_context.set_context.return_value = True
    
    result = cli_runner.invoke(orchestration, [
        'context', 'set', 'test_session', 'test_key', 'test_value',
        '--agent-id', 'test_agent',
        '--tags', 'tag1,tag2',
        '--ttl', '3600'
    ])
    
    assert result.exit_code == 0
    assert "✅ Set context (session): test_key = test_value" in result.output


@patch('cli.orchestration_commands.SharedContextManager')
def test_context_get_command(mock_context_class, cli_runner):
    """Test context get command"""
    # Mock context manager
    mock_context = MagicMock()
    mock_context_class.return_value = mock_context
    mock_context.get_context.return_value = "test_value"
    
    result = cli_runner.invoke(orchestration, [
        'context', 'get', 'test_session', 'test_key'
    ])
    
    assert result.exit_code == 0
    assert "test_value" in result.output


@patch('cli.orchestration_commands.SharedContextManager')
def test_context_search_command(mock_context_class, cli_runner):
    """Test context search command"""
    # Mock context manager
    mock_context = MagicMock()
    mock_context_class.return_value = mock_context
    
    # Mock search results
    mock_entry = MagicMock()
    mock_entry.key = "test_key"
    mock_entry.agent_id = "test_agent"
    mock_entry.value = "test_value"
    mock_entry.tags = {"tag1", "tag2"}
    mock_entry.timestamp = "2024-01-01T00:00:00"
    
    mock_context.search_context.return_value = [mock_entry]
    
    result = cli_runner.invoke(orchestration, [
        'context', 'search', 'test_session',
        '--tags', 'tag1,tag2',
        '--agent-id', 'test_agent'
    ])
    
    assert result.exit_code == 0
    assert "🔍 Found 1 context entries:" in result.output
    assert "test_key" in result.output


@patch('cli.orchestration_commands.CursorRulesEnforcer')
def test_cleanup_auto_fix_command(mock_enforcer_class, cli_runner):
    """Test cleanup auto-fix command"""
    # Mock enforcer
    mock_enforcer = MagicMock()
    mock_enforcer_class.return_value = mock_enforcer
    
    mock_enforcer.auto_fix_cleanup_issues.return_value = {
        "success": True,
        "message": "Cleanup completed successfully",
        "actions_taken": [
            {
                "action": "remove_obsolete",
                "status": "success",
                "message": "Removed obsolete file test.bak"
            }
        ],
        "errors": []
    }
    
    result = cli_runner.invoke(orchestration, ['cleanup', 'auto-fix'])
    
    assert result.exit_code == 0
    assert "🧹 Cleanup Auto-Fix (EXECUTION)" in result.output
    assert "✅ Cleanup completed successfully" in result.output


@patch('cli.orchestration_commands.CursorRulesEnforcer')
def test_cleanup_check_command(mock_enforcer_class, cli_runner):
    """Test cleanup check command"""
    # Mock enforcer
    mock_enforcer = MagicMock()
    mock_enforcer_class.return_value = mock_enforcer
    
    mock_enforcer._check_file_cleanup.return_value = {
        "status": "failed",
        "duplicates": 2,
        "obsolete_files": 3,
        "legacy_files": 1,
        "temp_files": 5,
        "cleanup_actions": [
            {
                "type": "remove_obsolete",
                "description": "Remove obsolete backup files"
            }
        ]
    }
    
    result = cli_runner.invoke(orchestration, ['cleanup', 'check'])
    
    assert result.exit_code == 0
    assert "🔍 Cleanup Check Results" in result.output
    assert "⚠️ Cleanup issues detected:" in result.output
    assert "Duplicates: 2" in result.output


@patch('cli.orchestration_commands.CursorRulesEnforcer')
@patch('cli.orchestration_commands.SharedContextManager')
@patch('cli.orchestration_commands.ValidationPipeline')
def test_monitor_status_command(mock_pipeline_class, mock_context_class, mock_enforcer_class, cli_runner):
    """Test monitor status command"""
    # Mock enforcer
    mock_enforcer = MagicMock()
    mock_enforcer_class.return_value = mock_enforcer
    mock_enforcer.check_compliance.return_value = {
        "compliance_score": 85.5,
        "violations": [
            {"rule": "test_rule", "severity": "error", "message": "Test violation"}
        ]
    }
    
    # Mock context manager
    mock_context = MagicMock()
    mock_context_class.return_value = mock_context
    mock_context.get_global_summary.return_value = {
        "total_sessions": 3,
        "global_entries": 10
    }
    
    # Mock validation pipeline
    mock_pipeline = MagicMock()
    mock_pipeline_class.return_value = mock_pipeline
    mock_pipeline.get_validation_summary.return_value = {
        "status": "completed",
        "overall_status": "passed"
    }
    
    result = cli_runner.invoke(orchestration, ['monitor', 'status'])
    
    assert result.exit_code == 0
    assert "📊 System Status" in result.output
    assert "Compliance Score: 85.5%" in result.output
    assert "Active Sessions: 3" in result.output


def test_orchestration_help(cli_runner):
    """Test main orchestration help"""
    result = cli_runner.invoke(orchestration, ['--help'])
    
    assert result.exit_code == 0
    assert "AI Coding Agent Project Orchestration Commands" in result.output
    assert "project" in result.output
    assert "validation" in result.output
    assert "context" in result.output
    assert "cleanup" in result.output
    assert "monitor" in result.output


def test_project_help(cli_runner):
    """Test project subcommand help"""
    result = cli_runner.invoke(orchestration, ['project', '--help'])
    
    assert result.exit_code == 0
    assert "Project management commands" in result.output
    assert "create" in result.output
    assert "list" in result.output
    assert "status" in result.output
