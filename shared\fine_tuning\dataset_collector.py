import json
import logging
import os
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

logger = logging.getLogger(__name__)


class DatasetCollector:
    """Collects and manages datasets for model fine-tuning"""

    def __init__(self, data_dir: str = "data/fine_tuning"):
        self.data_dir = Path(data_dir)
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.datasets_file = self.data_dir / "datasets.json"
        self.datasets = self._load_datasets()

    def _load_datasets(self) -> Dict[str, Any]:
        """Load existing datasets from file"""
        if self.datasets_file.exists():
            try:
                with open(self.datasets_file, "r") as f:
                    return json.load(f)
            except Exception as e:
                logger.warning(f"Could not load datasets file: {e}")
                return {}
        return {}

    def _save_datasets(self) -> None:
        """Save datasets to file"""
        try:
            with open(self.datasets_file, "w") as f:
                json.dump(self.datasets, f, indent=2)
        except Exception as e:
            logger.error(f"Could not save datasets file: {e}")

    def add_interaction(
        self, prompt: str, response: str, model: str, rating: Optional[int] = None
    ) -> None:
        """Add a prompt-response interaction to the dataset"""
        if model not in self.datasets:
            self.datasets[model] = []

        interaction = {
            "prompt": prompt,
            "response": response,
            "timestamp": datetime.now().isoformat(),
            "rating": rating,
        }

        self.datasets[model].append(interaction)
        self._save_datasets()
        logger.info(f"Added interaction to {model} dataset")

    def add_code_review_data(
        self, code: str, review: str, language: str, model: str
    ) -> None:
        """Add code review data to the dataset"""
        if model not in self.datasets:
            self.datasets[model] = []

        review_data = {
            "code": code,
            "review": review,
            "language": language,
            "type": "code_review",
            "timestamp": datetime.now().isoformat(),
        }

        self.datasets[model].append(review_data)
        self._save_datasets()
        logger.info(f"Added code review data to {model} dataset")

    def add_code_generation_data(
        self, prompt: str, generated_code: str, language: str, model: str
    ) -> None:
        """Add code generation data to the dataset"""
        if model not in self.datasets:
            self.datasets[model] = []

        generation_data = {
            "prompt": prompt,
            "generated_code": generated_code,
            "language": language,
            "type": "code_generation",
            "timestamp": datetime.now().isoformat(),
        }

        self.datasets[model].append(generation_data)
        self._save_datasets()
        logger.info(f"Added code generation data to {model} dataset")

    def get_dataset(self, model: str) -> List[Dict[str, Any]]:
        """Get all data for a specific model"""
        return self.datasets.get(model, [])

    def get_all_datasets(self) -> Dict[str, List[Dict[str, Any]]]:
        """Get all datasets"""
        return self.datasets

    def save_dataset_to_file(self, model: str, filepath: Optional[str] = None) -> str:
        """Save a specific model's dataset to a JSONL file"""
        if not filepath:
            filepath = str(self.data_dir / f"{model}_dataset.jsonl")

        dataset = self.get_dataset(model)
        try:
            with open(filepath, "w") as f:
                for item in dataset:
                    f.write(json.dumps(item) + "\n")
            logger.info(f"Saved {len(dataset)} items to {filepath}")
            return filepath
        except Exception as e:
            logger.error(f"Could not save dataset to file: {e}")
            raise

    def load_dataset_from_file(self, filepath: str, model: str) -> None:
        """Load dataset from a JSONL file"""
        try:
            with open(filepath, "r") as f:
                lines = f.readlines()

            if model not in self.datasets:
                self.datasets[model] = []

            for line in lines:
                item = json.loads(line.strip())
                self.datasets[model].append(item)

            self._save_datasets()
            logger.info(f"Loaded {len(lines)} items from {filepath} to {model} dataset")
        except Exception as e:
            logger.error(f"Could not load dataset from file: {e}")
            raise


# Global instance
dataset_collector = DatasetCollector()

if __name__ == "__main__":
    # Example usage
    collector = DatasetCollector()

    # Add some sample data
    collector.add_interaction(
        "Write a Python function to calculate fibonacci numbers",
        "Here's a Python function to calculate fibonacci numbers:\n\ndef fibonacci(n):\n    if n <= 1:\n        return n\n    else:\n        return fibonacci(n-1) + fibonacci(n-2)",
        "deepseek-coder:1.3b",
        5,
    )

    collector.add_code_generation_data(
        "Create a React component for a todo list",
        "import React, { useState } from 'react';\n\nconst TodoList = () => {\n  const [todos, setTodos] = useState([]);\n  const [inputValue, setInputValue] = useState('');\n\n  const addTodo = () => {\n    if (inputValue.trim() !== '') {\n      setTodos([...todos, { id: Date.now(), text: inputValue, completed: false }]);\n      setInputValue('');\n    }\n  };\n\n  const toggleTodo = (id) => {\n    setTodos(todos.map(todo => \n      todo.id === id ? { ...todo, completed: !todo.completed } : todo\n    ));\n  };\n\n  return (\n    <div>\n      <h1>Todo List</h1>\n      <input \n        type=\"text\" \n        value={inputValue} \n        onChange={(e) => setInputValue(e.target.value)}\n        onKeyPress={(e) => e.key === 'Enter' && addTodo()}\n      />\n      <button onClick={addTodo}>Add Todo</button>\n      <ul>\n        {todos.map(todo => (\n          <li \n            key={todo.id} \n            onClick={() => toggleTodo(todo.id)}\n            style={{ textDecoration: todo.completed ? 'line-through' : 'none' }}\n          >\n            {todo.text}\n          </li>\n        ))}\n      </ul>\n    </div>\n  );\n};\n\nexport default TodoList;",
        "JavaScript/React",
        "deepseek-coder:1.3b",
    )

    # Save dataset to file
    collector.save_dataset_to_file("deepseek-coder:1.3b")
