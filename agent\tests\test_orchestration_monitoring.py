#!/usr/bin/env python3
"""
Tests for orchestration monitoring and metrics system
"""
import time
from unittest.mock import MagicMock, patch

import pytest

from agent.monitoring.orchestration_metrics import (
    ArchitectMetrics,
    ComplianceMetrics,
    ContainerMetrics,
    MetricsCollector,
    MonitoringDashboard,
    PerformanceMetrics,
    RoutingMetrics,
    export_prometheus_metrics,
    get_metrics_summary,
    metrics_collector,
    monitoring_dashboard,
    record_architect_project_start,
    record_compliance_check,
    record_container_operation,
    record_routing_decision,
)


@pytest.fixture
def fresh_metrics_collector():
    return MetricsCollector()


@pytest.fixture
def architect_metrics():
    return ArchitectMetrics()


@pytest.fixture
def routing_metrics():
    return RoutingMetrics()


@pytest.fixture
def compliance_metrics():
    return ComplianceMetrics()


@pytest.fixture
def container_metrics():
    return ContainerMetrics()


@pytest.fixture
def performance_metrics():
    return PerformanceMetrics()


def test_metrics_collector_initialization(fresh_metrics_collector):
    """Test metrics collector initialization"""
    assert len(fresh_metrics_collector.counters) == 0
    assert len(fresh_metrics_collector.gauges) == 0
    assert len(fresh_metrics_collector.histograms) == 0
    assert fresh_metrics_collector.start_time is not None


def test_metrics_collector_counter_operations(fresh_metrics_collector):
    """Test counter operations"""
    # Test basic increment
    fresh_metrics_collector.increment_counter("test_counter")
    assert fresh_metrics_collector.counters["test_counter"] == 1
    
    # Test increment with value
    fresh_metrics_collector.increment_counter("test_counter", 5)
    assert fresh_metrics_collector.counters["test_counter"] == 6
    
    # Test increment with labels
    fresh_metrics_collector.increment_counter("test_counter", labels={"type": "test"})
    assert fresh_metrics_collector.counters["test_counter{type=test}"] == 1


def test_metrics_collector_gauge_operations(fresh_metrics_collector):
    """Test gauge operations"""
    # Test setting gauge
    fresh_metrics_collector.set_gauge("test_gauge", 42.5)
    assert fresh_metrics_collector.gauges["test_gauge"] == 42.5
    
    # Test gauge with labels
    fresh_metrics_collector.set_gauge("test_gauge", 100.0, labels={"component": "test"})
    assert fresh_metrics_collector.gauges["test_gauge{component=test}"] == 100.0


def test_metrics_collector_histogram_operations(fresh_metrics_collector):
    """Test histogram operations"""
    # Record multiple values
    values = [1.0, 2.5, 3.2, 1.8, 4.1]
    for value in values:
        fresh_metrics_collector.record_histogram("test_histogram", value)
    
    assert len(fresh_metrics_collector.histograms["test_histogram"]) == 5
    
    # Test histogram summaries
    summaries = fresh_metrics_collector._get_histogram_summaries()
    assert "test_histogram" in summaries
    
    summary = summaries["test_histogram"]
    assert summary["count"] == 5
    assert summary["min"] == 1.0
    assert summary["max"] == 4.1
    assert summary["mean"] == sum(values) / len(values)


def test_architect_metrics(architect_metrics):
    """Test architect metrics functionality"""
    # Test project start recording
    architect_metrics.record_project_start("project_1", "roadmap")
    assert architect_metrics.projects_started == 1
    assert architect_metrics.roadmaps_created == 1
    
    architect_metrics.record_project_start("project_2", "inference")
    assert architect_metrics.projects_started == 2
    assert architect_metrics.roadmaps_inferred == 1
    
    # Test project completion
    architect_metrics.record_project_completion("project_1", 120.5)
    assert architect_metrics.projects_completed == 1
    assert len(architect_metrics.decision_times) == 1
    
    # Test phase transition
    architect_metrics.record_phase_transition("project_1", "planning", "development")
    assert architect_metrics.phase_transitions == 1
    assert architect_metrics.state_transitions["planning->development"] == 1
    
    # Test task delegation
    architect_metrics.record_task_delegation("task_1", "backend_agent", 0.5)
    assert architect_metrics.task_delegations == 1
    
    # Test summary
    summary = architect_metrics.get_summary()
    assert summary["projects_started"] == 2
    assert summary["projects_completed"] == 1
    assert summary["completion_rate"] == 0.5


def test_routing_metrics(routing_metrics):
    """Test routing metrics functionality"""
    # Test routing decision recording
    routing_metrics.record_routing_decision("api_task", "backend_agent", "ai_powered", 0.3)
    assert routing_metrics.routing_decisions == 1
    assert routing_metrics.agent_selections["backend_agent"] == 1
    assert routing_metrics.routing_strategies["ai_powered"] == 1
    
    # Test routing failure
    routing_metrics.record_routing_failure("complex_task", "No suitable agent found")
    assert routing_metrics.routing_failures == 1
    
    # Test fallback activation
    routing_metrics.record_fallback_activation("primary_agent", "fallback_agent")
    assert routing_metrics.fallback_activations == 1
    
    # Test summary
    summary = routing_metrics.get_summary()
    assert summary["total_routing_decisions"] == 1
    assert summary["routing_failures"] == 1
    assert summary["failure_rate"] == 1.0


def test_compliance_metrics(compliance_metrics):
    """Test compliance metrics functionality"""
    # Test compliance check recording
    compliance_metrics.record_compliance_check(85.5, 3)
    assert compliance_metrics.compliance_checks == 1
    assert len(compliance_metrics.compliance_scores) == 1
    assert compliance_metrics.violations_detected == 3
    
    # Test auto-fix recording
    compliance_metrics.record_auto_fix(5, 4)
    assert compliance_metrics.auto_fixes_attempted == 5
    assert compliance_metrics.auto_fixes_successful == 4
    
    # Test validation run
    compliance_metrics.record_validation_run(True, 2.5)
    assert compliance_metrics.validation_runs == 1
    assert compliance_metrics.validation_failures == 0
    
    compliance_metrics.record_validation_run(False, 3.0)
    assert compliance_metrics.validation_runs == 2
    assert compliance_metrics.validation_failures == 1
    
    # Test security scan
    compliance_metrics.record_security_scan(2)
    assert compliance_metrics.security_scans == 1
    assert compliance_metrics.security_issues == 2
    
    # Test summary
    summary = compliance_metrics.get_summary()
    assert summary["compliance_checks"] == 1
    assert summary["avg_compliance_score"] == 85.5
    assert summary["auto_fix_success_rate"] == 0.8
    assert summary["validation_pass_rate"] == 0.5


def test_container_metrics(container_metrics):
    """Test container metrics functionality"""
    # Test container operation recording
    container_metrics.record_container_operation("create", True, 5.2)
    assert container_metrics.containers_created == 1
    assert container_metrics.container_failures == 0
    
    container_metrics.record_container_operation("start", False, 2.1)
    assert container_metrics.containers_started == 1
    assert container_metrics.container_failures == 1
    
    # Test Docker-First violation
    container_metrics.record_docker_first_violation("host_execution")
    assert container_metrics.docker_first_violations == 1
    
    # Test security validation
    container_metrics.record_security_validation(True, 95.0)
    assert container_metrics.security_validations == 1
    assert container_metrics.security_violations == 0
    
    container_metrics.record_security_validation(False, 65.0)
    assert container_metrics.security_validations == 2
    assert container_metrics.security_violations == 1
    
    # Test health check
    container_metrics.record_health_check("test_container", True)
    assert container_metrics.health_checks == 1
    assert container_metrics.health_failures == 0
    
    # Test summary
    summary = container_metrics.get_summary()
    assert summary["containers_created"] == 1
    assert summary["container_failures"] == 1
    assert summary["docker_first_violations"] == 1
    assert summary["security_pass_rate"] == 0.5


def test_performance_metrics(performance_metrics):
    """Test performance metrics functionality"""
    # Test response time recording
    performance_metrics.record_response_time("api_call", 1.5)
    assert len(performance_metrics.response_times) == 1
    
    # Test resource usage recording
    performance_metrics.record_resource_usage(512.0, 45.2)
    assert len(performance_metrics.memory_usage_samples) == 1
    assert len(performance_metrics.cpu_usage_samples) == 1
    
    # Test concurrent operations
    performance_metrics.record_concurrent_operation_start()
    assert performance_metrics.concurrent_operations == 1
    assert performance_metrics.peak_concurrent_operations == 1
    
    performance_metrics.record_concurrent_operation_start()
    assert performance_metrics.concurrent_operations == 2
    assert performance_metrics.peak_concurrent_operations == 2
    
    performance_metrics.record_concurrent_operation_end()
    assert performance_metrics.concurrent_operations == 1
    
    # Test error recording
    performance_metrics.record_error("timeout_error")
    assert performance_metrics.error_rates["timeout_error"] == 1
    
    # Test throughput recording
    performance_metrics.record_throughput(25.5)
    assert len(performance_metrics.throughput_samples) == 1
    
    # Test summary
    summary = performance_metrics.get_summary()
    assert summary["avg_response_time_seconds"] == 1.5
    assert summary["avg_memory_usage_mb"] == 512.0
    assert summary["current_concurrent_operations"] == 1
    assert summary["peak_concurrent_operations"] == 2


def test_monitoring_dashboard(fresh_metrics_collector):
    """Test monitoring dashboard functionality"""
    dashboard = MonitoringDashboard(fresh_metrics_collector)
    
    # Add some test metrics
    fresh_metrics_collector.compliance_metrics.record_compliance_check(75.0, 5)
    fresh_metrics_collector.performance_metrics.record_response_time("test", 6.0)
    fresh_metrics_collector.container_metrics.record_container_operation("create", False, 1.0)
    
    # Test alert checking
    alerts = dashboard.check_alerts()
    
    # Should have alerts for low compliance score, high response time, and container failure
    assert len(alerts) >= 2
    
    alert_types = [alert["type"] for alert in alerts]
    assert "compliance_score_low" in alert_types
    assert "response_time_high" in alert_types
    
    # Test dashboard data generation
    dashboard_data = dashboard.generate_dashboard_data()
    
    assert "timestamp" in dashboard_data
    assert "overview" in dashboard_data
    assert "architect_progress" in dashboard_data
    assert "routing_performance" in dashboard_data
    assert "quality_metrics" in dashboard_data
    assert "container_status" in dashboard_data
    assert "performance_stats" in dashboard_data
    assert "alerts" in dashboard_data
    
    # Check alert summary
    assert dashboard_data["alerts"]["active_alerts"] >= 2


def test_prometheus_export(fresh_metrics_collector):
    """Test Prometheus format export"""
    # Add some test metrics
    fresh_metrics_collector.increment_counter("test_counter", 5)
    fresh_metrics_collector.set_gauge("test_gauge", 42.0)
    fresh_metrics_collector.record_histogram("test_histogram", 1.5)
    fresh_metrics_collector.record_histogram("test_histogram", 2.5)
    
    # Export to Prometheus format
    prometheus_output = fresh_metrics_collector.export_prometheus_format()
    
    assert "orchestration_uptime_seconds" in prometheus_output
    assert "test_counter" in prometheus_output
    assert "test_gauge" in prometheus_output
    assert "test_histogram" in prometheus_output
    assert "# TYPE" in prometheus_output
    assert "# HELP" in prometheus_output


def test_global_metrics_functions():
    """Test global metrics convenience functions"""
    # Test architect project start
    record_architect_project_start("test_project", "roadmap")
    
    # Test routing decision
    record_routing_decision("test_task", "test_agent", "simple", 0.5)
    
    # Test compliance check
    record_compliance_check(90.0, 2)
    
    # Test container operation
    record_container_operation("create", True, 3.0)
    
    # Test metrics summary
    summary = get_metrics_summary()
    assert "uptime_seconds" in summary
    assert "architect_metrics" in summary
    assert "routing_metrics" in summary
    assert "compliance_metrics" in summary
    assert "container_metrics" in summary
    
    # Test Prometheus export
    prometheus_output = export_prometheus_metrics()
    assert isinstance(prometheus_output, str)
    assert len(prometheus_output) > 0


def test_integration_hooks():
    """Test integration hooks for existing components"""
    from agent.monitoring.orchestration_metrics import (
        integrate_with_architect_agent,
        integrate_with_container_manager,
        integrate_with_cursor_rules_monitor,
    )
    
    # Test architect agent hooks
    architect_hooks = integrate_with_architect_agent()
    assert "on_project_start" in architect_hooks
    assert "on_phase_transition" in architect_hooks
    assert "on_task_delegation" in architect_hooks
    
    # Test container manager hooks
    container_hooks = integrate_with_container_manager()
    assert "on_container_operation" in container_hooks
    assert "on_docker_first_violation" in container_hooks
    assert "on_security_validation" in container_hooks
    
    # Test cursor rules monitor hooks
    cursor_hooks = integrate_with_cursor_rules_monitor()
    assert "on_compliance_check" in cursor_hooks
    assert "on_auto_fix" in cursor_hooks
    assert "on_validation_run" in cursor_hooks


def test_metrics_persistence_and_cleanup(fresh_metrics_collector):
    """Test metrics persistence and cleanup behavior"""
    # Add many metrics to test cleanup
    for i in range(1000):
        fresh_metrics_collector.increment_counter(f"test_counter_{i % 10}")
        fresh_metrics_collector.record_histogram("test_histogram", float(i))
    
    # Should handle large number of metrics
    assert len(fresh_metrics_collector.counters) <= 10  # Due to modulo
    assert len(fresh_metrics_collector.histograms["test_histogram"]) == 1000
    
    # Test histogram summary with large dataset
    summaries = fresh_metrics_collector._get_histogram_summaries()
    histogram_summary = summaries["test_histogram"]
    
    assert histogram_summary["count"] == 1000
    assert histogram_summary["min"] == 0.0
    assert histogram_summary["max"] == 999.0
    assert histogram_summary["p50"] is not None
    assert histogram_summary["p95"] is not None


def test_concurrent_metrics_collection(fresh_metrics_collector):
    """Test concurrent metrics collection"""
    import threading
    import time
    
    def worker(worker_id):
        for i in range(100):
            fresh_metrics_collector.increment_counter("concurrent_counter")
            fresh_metrics_collector.record_histogram("concurrent_histogram", float(i))
            time.sleep(0.001)  # Small delay to simulate real work
    
    # Start multiple threads
    threads = []
    for worker_id in range(5):
        thread = threading.Thread(target=worker, args=(worker_id,))
        threads.append(thread)
        thread.start()
    
    # Wait for all threads to complete
    for thread in threads:
        thread.join()
    
    # Verify metrics were collected correctly
    assert fresh_metrics_collector.counters["concurrent_counter"] == 500  # 5 workers * 100 increments
    assert len(fresh_metrics_collector.histograms["concurrent_histogram"]) == 500


def test_alert_threshold_configuration():
    """Test alert threshold configuration"""
    dashboard = MonitoringDashboard(MetricsCollector())
    
    # Test default thresholds
    assert dashboard.alert_thresholds["compliance_score_min"] == 80.0
    assert dashboard.alert_thresholds["response_time_max"] == 5.0
    
    # Test threshold modification
    dashboard.alert_thresholds["compliance_score_min"] = 90.0
    dashboard.alert_thresholds["custom_threshold"] = 100.0
    
    assert dashboard.alert_thresholds["compliance_score_min"] == 90.0
    assert dashboard.alert_thresholds["custom_threshold"] == 100.0


if __name__ == "__main__":
    # Run basic monitoring tests
    print("Running monitoring system tests...")
    
    # Test basic functionality
    collector = MetricsCollector()
    collector.increment_counter("test_counter", 5)
    collector.set_gauge("test_gauge", 42.0)
    collector.record_histogram("test_histogram", 1.5)
    
    summary = collector.get_metrics_summary()
    print(f"Metrics summary: {len(summary)} categories")
    
    # Test dashboard
    dashboard = MonitoringDashboard(collector)
    dashboard_data = dashboard.generate_dashboard_data()
    print(f"Dashboard data generated with {len(dashboard_data)} sections")
    
    # Test Prometheus export
    prometheus_output = collector.export_prometheus_format()
    print(f"Prometheus export: {len(prometheus_output)} characters")
    
    print("Monitoring system tests completed!")
