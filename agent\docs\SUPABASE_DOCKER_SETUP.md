# 🔐 Supabase Configuration for Docker Environment

Guide for configuring Supabase authentication and database with the AI Coding Agent Docker setup.

## 📋 Prerequisites

1. **Supabase Account**: Create an account at [supabase.com](https://supabase.com)
2. **Supabase Project**: Create a new project in your Supabase dashboard
3. **Docker Environment**: Ensure Docker is running and the AI Coding Agent is set up

## 🔧 Getting Your Supabase Credentials

### 1. Project Settings
1. Go to your Supabase project dashboard
2. Navigate to **Settings** → **API**
3. Copy the following values:
   - **Project URL** (e.g., `https://your-project-id.supabase.co`)
   - **Anon/Public Key** (starts with `eyJ...`)
   - **Service Role Key** (starts with `eyJ...`)

### 2. Database Connection
1. Go to **Settings** → **Database**
2. Find the **Connection string** section
3. Copy the **URI** connection string
4. Replace `[YOUR-PASSWORD]` with your database password

## ⚙️ Environment Configuration

### 1. Copy Environment Template
```bash
# Copy the environment template
cp config/env.example .env

# Edit the .env file
notepad .env  # Windows
nano .env     # Linux/Mac
```

### 2. Update Supabase Configuration
Replace the placeholder values in your `.env` file:

```bash
# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
SUPABASE_URL=https://your-actual-project-id.supabase.co
SUPABASE_ANON_KEY=your_actual_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_actual_service_role_key_here

# Database connection (if using Supabase as primary database)
SUPABASE_DB_URL=*******************************************************************************/postgres

# Frontend Supabase configuration
NEXT_PUBLIC_SUPABASE_URL=https://your-actual-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_actual_anon_key_here
```

## 🗄️ Database Configuration Options

### Option 1: Use Supabase as Primary Database
If you want to use Supabase PostgreSQL instead of the local PostgreSQL container:

```bash
# In your .env file, update the DATABASE_URL
DATABASE_URL=************************************************************************/postgres

# Disable the local PostgreSQL container
# Comment out or remove the 'db' service from docker-compose.yml
```

### Option 2: Use Local PostgreSQL (Recommended for Development)
Keep the local PostgreSQL container and use Supabase only for authentication:

```bash
# Keep the default DATABASE_URL for local PostgreSQL
DATABASE_URL=******************************************************/ai_coding_agent

# Supabase is used only for auth and storage
SUPABASE_AUTH_ENABLED=true
SUPABASE_STORAGE_ENABLED=true
```

## 🔐 Authentication Setup

### 1. Configure Auth Settings
In your `.env` file:

```bash
# Development settings
SUPABASE_AUTH_ENABLED=true
SUPABASE_AUTH_REDIRECT_URL=http://localhost:3000/auth/callback
SUPABASE_AUTH_COOKIE_SECURE=false

# Production settings (uncomment when deploying)
# SUPABASE_AUTH_COOKIE_SECURE=true
# SUPABASE_AUTH_REDIRECT_URL=https://yourdomain.com/auth/callback
```

### 2. Set Up Auth Providers
In your Supabase dashboard:

1. Go to **Authentication** → **Providers**
2. Enable the providers you want to use:
   - **Email** (recommended)
   - **Google** (optional)
   - **GitHub** (optional)
   - **Discord** (optional)

### 3. Configure Email Templates
1. Go to **Authentication** → **Email Templates**
2. Customize the email templates for:
   - **Confirm signup**
   - **Reset password**
   - **Magic link**

## 📁 Storage Configuration

### 1. Create Storage Bucket
1. Go to **Storage** in your Supabase dashboard
2. Click **Create a new bucket**
3. Name it `ai-coding-agent-files`
4. Set it as **Public** or **Private** based on your needs

### 2. Configure Storage Settings
In your `.env` file:

```bash
SUPABASE_STORAGE_BUCKET=ai-coding-agent-files
SUPABASE_STORAGE_ENABLED=true
```

### 3. Set Up Storage Policies
In your Supabase dashboard:

1. Go to **Storage** → **Policies**
2. Create policies for your bucket:
   - **Read access** for authenticated users
   - **Write access** for authenticated users
   - **Delete access** for file owners

## 🔄 Realtime Configuration

### 1. Enable Realtime
In your `.env` file:

```bash
SUPABASE_REALTIME_ENABLED=true
SUPABASE_REALTIME_CHANNELS=chat,notifications,file-updates
```

### 2. Configure Realtime Tables
In your Supabase dashboard:

1. Go to **Database** → **Tables**
2. Enable realtime for tables that need live updates:
   - `chat_messages`
   - `notifications`
   - `file_updates`

## 🚀 Starting the Application

### 1. Development Mode
```bash
# Start with Supabase configuration
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d

# Check logs
docker-compose logs -f api frontend
```

### 2. Production Mode
```bash
# Start production with Supabase
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

# Verify services
docker-compose ps
```

## 🧪 Testing Supabase Integration

### 1. Test Authentication
1. Open your application: `http://localhost:3000`
2. Try to sign up with a new account
3. Verify email confirmation works
4. Test login functionality

### 2. Test Storage
1. Upload a file through the application
2. Verify it appears in your Supabase storage bucket
3. Test file download functionality

### 3. Test Realtime
1. Open multiple browser tabs
2. Send a chat message in one tab
3. Verify it appears in real-time in other tabs

## 🔧 Troubleshooting

### Common Issues

#### 1. **Authentication Not Working**
```bash
# Check environment variables
docker-compose exec api env | grep SUPABASE

# Verify Supabase URL and keys
docker-compose logs api | grep -i supabase
```

#### 2. **Database Connection Issues**
```bash
# Test database connection
docker-compose exec api python -c "
import os
import psycopg2
try:
    conn = psycopg2.connect(os.getenv('DATABASE_URL'))
    print('Database connection successful')
    conn.close()
except Exception as e:
    print(f'Database connection failed: {e}')
"
```

#### 3. **Storage Upload Failures**
```bash
# Check storage configuration
docker-compose exec api python -c "
import os
print(f'Storage enabled: {os.getenv(\"SUPABASE_STORAGE_ENABLED\")}')
print(f'Storage bucket: {os.getenv(\"SUPABASE_STORAGE_BUCKET\")}')
"
```

### Debug Commands
```bash
# View Supabase-related logs
docker-compose logs api | grep -i supabase
docker-compose logs frontend | grep -i supabase

# Check environment variables in containers
docker-compose exec api env | grep SUPABASE
docker-compose exec frontend env | grep SUPABASE

# Test Supabase connection
docker-compose exec api python -c "
from supabase import create_client
import os
try:
    supabase = create_client(os.getenv('SUPABASE_URL'), os.getenv('SUPABASE_ANON_KEY'))
    print('Supabase connection successful')
except Exception as e:
    print(f'Supabase connection failed: {e}')
"
```

## 🔒 Security Best Practices

### 1. Environment Variables
- **Never commit** your `.env` file to version control
- Use **strong, unique passwords** for database connections
- **Rotate keys** regularly

### 2. Supabase Security
- **Enable Row Level Security (RLS)** on all tables
- **Create proper policies** for data access
- **Use service role key** only for server-side operations
- **Use anon key** for client-side operations

### 3. Production Deployment
- **Enable HTTPS** for all communications
- **Set secure cookies** in production
- **Use environment-specific** configurations
- **Monitor authentication** logs

## 📚 Additional Resources

- [Supabase Documentation](https://supabase.com/docs)
- [Supabase JavaScript Client](https://supabase.com/docs/reference/javascript)
- [Supabase Python Client](https://supabase.com/docs/reference/python)
- [Authentication Guide](https://supabase.com/docs/guides/auth)
- [Storage Guide](https://supabase.com/docs/guides/storage)
- [Realtime Guide](https://supabase.com/docs/guides/realtime)

---

**🎉 Your Supabase integration is now configured!** The AI Coding Agent will use Supabase for authentication, storage, and real-time features while maintaining the flexibility to use either Supabase or local PostgreSQL for the main database.
