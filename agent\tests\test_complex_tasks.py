"""
Tests for Complex Tasks Module

Comprehensive test suite for the complex tasks system including
task management, architecture design, system integration, performance
optimization, problem solving, resource monitoring, quality assurance,
and progress tracking.
"""

import asyncio
import json
from datetime import datetime, timedelta
from pathlib import Path
from unittest.mock import AsyncMock, Mock, patch

import pytest

from complex_tasks import (
    ArchitectureDesigner,
    ComplexProblemSolver,
    ComplexTask,
    ComplexTaskManager,
    PerformanceOptimizer,
    ProgressReport,
    ProgressTracker,
    QualityAssurance,
    QualityMetrics,
    ResourceAllocation,
    ResourceMonitor,
    SystemIntegrator,
    TaskComplexity,
    TaskStatus,
    TaskType,
)
from agent.models.model_manager import ModelProvider


class TestComplexTaskManager:
    """Test the main complex task manager"""

    @pytest.fixture
    def config(self):
        """Test configuration"""
        return {
            "max_concurrent_tasks": 2,
            "starcoder2": {
                "model_name": "starcoder2:3b",
                "temperature": 0.1,
                "max_tokens": 4096,
            },
        }

    @pytest.fixture
    def task_manager(self, config):
        """Task manager instance"""
        return ComplexTaskManager(config)

    @pytest.fixture
    def sample_task_data(self):
        """Sample task data"""
        return {
            "title": "Test Architecture Design",
            "description": "Design a microservices architecture for a web application",
            "task_type": "architecture_design",
            "complexity": "complex",
            "resource_allocation": {
                "cpu_cores": 4,
                "memory_gb": 8,
                "gpu_required": False,
                "estimated_duration_hours": 12.0,
            },
            "requirements": ["Scalability", "High availability", "Security"],
            "constraints": ["Budget limit", "Time constraint"],
            "priority": 8,
            "tags": ["architecture", "microservices", "web"],
        }

    @pytest.mark.asyncio
    async def test_create_task(self, task_manager, sample_task_data):
        """Test task creation"""
        task = await task_manager.create_task(sample_task_data)

        assert task.task_id is not None
        assert task.title == sample_task_data["title"]
        assert task.description == sample_task_data["description"]
        assert task.task_type == TaskType.ARCHITECTURE_DESIGN
        assert task.complexity == TaskComplexity.COMPLEX
        assert task.status == TaskStatus.PENDING
        assert task.priority == 8
        assert "architecture" in task.tags

    @pytest.mark.asyncio
    async def test_execute_task(self, task_manager, sample_task_data):
        """Test task execution"""
        task = await task_manager.create_task(sample_task_data)

        with patch.object(
            task_manager.architecture_designer, "design_architecture"
        ) as mock_design:
            mock_design.return_value = {
                "architecture_design": {
                    "components": ["frontend", "backend", "database"]
                },
                "quality_metrics": QualityMetrics(code_quality_score=90.0),
            }

            result = await task_manager.execute_task(task.task_id)

            assert result is not None
            assert "architecture_design" in result
            assert "quality_metrics" in result

    @pytest.mark.asyncio
    async def test_list_tasks(self, task_manager, sample_task_data):
        """Test task listing"""
        task1 = await task_manager.create_task(sample_task_data)

        # Create another task with different type
        task_data2 = sample_task_data.copy()
        task_data2["title"] = "Test Performance Optimization"
        task_data2["task_type"] = "performance_optimization"
        task2 = await task_manager.create_task(task_data2)

        # List all tasks
        all_tasks = await task_manager.list_tasks()
        assert len(all_tasks) == 2

        # List by type
        arch_tasks = await task_manager.list_tasks(
            task_type=TaskType.ARCHITECTURE_DESIGN
        )
        assert len(arch_tasks) == 1
        assert arch_tasks[0]["title"] == "Test Architecture Design"

    @pytest.mark.asyncio
    async def test_get_task_analytics(self, task_manager, sample_task_data):
        """Test task analytics"""
        task = await task_manager.create_task(sample_task_data)

        analytics = await task_manager.get_task_analytics()

        assert analytics["total_tasks"] == 1
        assert analytics["completed_tasks"] == 0
        assert analytics["running_tasks"] == 0
        assert "tasks_by_type" in analytics
        assert "tasks_by_complexity" in analytics

    @pytest.mark.asyncio
    async def test_save_and_load_tasks(self, task_manager, sample_task_data, tmp_path):
        """Test saving and loading tasks"""
        task = await task_manager.create_task(sample_task_data)

        # Save tasks
        save_path = tmp_path / "tasks.json"
        await task_manager.save_tasks(str(save_path))

        # Create new manager and load tasks
        new_manager = ComplexTaskManager(task_manager.config)
        await new_manager.load_tasks(str(save_path))

        assert len(new_manager.tasks) == 1
        loaded_task = list(new_manager.tasks.values())[0]
        assert loaded_task.title == task.title


class TestArchitectureDesigner:
    """Test the architecture designer"""

    @pytest.fixture
    def config(self):
        """Test configuration"""
        return {
            "architecture_design": {
                "patterns": {
                    "microservices": {"complexity": "high"},
                    "layered_architecture": {"complexity": "medium"},
                }
            }
        }

    @pytest.fixture
    def model_manager(self):
        """Mock model manager"""
        mock_manager = Mock()
        mock_manager.generate = AsyncMock(return_value="Mocked architecture design")
        return mock_manager

    @pytest.fixture
    def designer(self, config, model_manager):
        """Architecture designer instance"""
        return ArchitectureDesigner(config, model_manager)

    @pytest.fixture
    def sample_task(self):
        """Sample complex task"""
        return ComplexTask(
            task_id="test-arch-task",
            title="Test Architecture",
            description="Design a scalable web architecture",
            task_type=TaskType.ARCHITECTURE_DESIGN,
            complexity=TaskComplexity.COMPLEX,
            resource_allocation=ResourceAllocation(cpu_cores=4, memory_gb=8),
        )

    @pytest.mark.asyncio
    async def test_design_architecture(self, designer, sample_task):
        """Test architecture design"""
        result = await designer.design_architecture(sample_task)

        assert "architecture_design" in result
        assert "analysis" in result
        assert "validation" in result
        assert "quality_metrics" in result
        assert "deliverables" in result
        assert "recommendations" in result

    def test_load_design_patterns(self, designer):
        """Test design patterns loading"""
        patterns = designer.design_patterns

        assert "microservices" in patterns
        assert "layered_architecture" in patterns
        assert "event_driven" in patterns
        assert "domain_driven_design" in patterns
        assert "clean_architecture" in patterns

    def test_load_technology_stacks(self, designer):
        """Test technology stacks loading"""
        stacks = designer.technology_stacks

        assert "web_development" in stacks
        assert "mobile_development" in stacks
        assert "data_science" in stacks

        web_stack = stacks["web_development"]
        assert "frontend" in web_stack
        assert "backend" in web_stack
        assert "database" in web_stack


class TestSystemIntegrator:
    """Test the system integrator"""

    @pytest.fixture
    def config(self):
        """Test configuration"""
        return {
            "system_integration": {
                "patterns": {
                    "api_gateway": {"complexity": "medium"},
                    "event_driven": {"complexity": "high"},
                }
            }
        }

    @pytest.fixture
    def model_manager(self):
        """Mock model manager"""
        mock_manager = Mock()
        mock_manager.generate = AsyncMock(return_value="Mocked integration design")
        return mock_manager

    @pytest.fixture
    def integrator(self, config, model_manager):
        """System integrator instance"""
        return SystemIntegrator(config, model_manager)

    @pytest.fixture
    def sample_task(self):
        """Sample complex task"""
        return ComplexTask(
            task_id="test-integration-task",
            title="Test System Integration",
            description="Integrate multiple services",
            task_type=TaskType.SYSTEM_INTEGRATION,
            complexity=TaskComplexity.COMPLEX,
            resource_allocation=ResourceAllocation(cpu_cores=4, memory_gb=8),
        )

    @pytest.mark.asyncio
    async def test_integrate_system(self, integrator, sample_task):
        """Test system integration"""
        result = await integrator.integrate_system(sample_task)

        assert "integration_design" in result
        assert "integration_code" in result
        assert "integration_tests" in result
        assert "analysis" in result
        assert "validation" in result
        assert "quality_metrics" in result
        assert "deliverables" in result
        assert "recommendations" in result

    def test_load_integration_patterns(self, integrator):
        """Test integration patterns loading"""
        patterns = integrator.integration_patterns

        assert "api_gateway" in patterns
        assert "event_driven" in patterns
        assert "message_queue" in patterns
        assert "service_mesh" in patterns
        assert "data_pipeline" in patterns

    def test_load_api_standards(self, integrator):
        """Test API standards loading"""
        standards = integrator.api_standards

        assert "rest" in standards
        assert "graphql" in standards
        assert "grpc" in standards

        rest_standard = standards["rest"]
        assert "methods" in rest_standard
        assert "status_codes" in rest_standard
        assert "best_practices" in rest_standard


class TestPerformanceOptimizer:
    """Test the performance optimizer"""

    @pytest.fixture
    def config(self):
        """Test configuration"""
        return {
            "performance_optimization": {
                "patterns": {
                    "caching": {"types": ["memory_cache", "redis_cache"]},
                    "load_balancing": {"types": ["round_robin", "least_connections"]},
                }
            }
        }

    @pytest.fixture
    def model_manager(self):
        """Mock model manager"""
        mock_manager = Mock()
        mock_manager.generate = AsyncMock(return_value="Mocked optimization design")
        return mock_manager

    @pytest.fixture
    def optimizer(self, config, model_manager):
        """Performance optimizer instance"""
        return PerformanceOptimizer(config, model_manager)

    @pytest.fixture
    def sample_task(self):
        """Sample complex task"""
        return ComplexTask(
            task_id="test-optimization-task",
            title="Test Performance Optimization",
            description="Optimize application performance",
            task_type=TaskType.PERFORMANCE_OPTIMIZATION,
            complexity=TaskComplexity.COMPLEX,
            resource_allocation=ResourceAllocation(cpu_cores=4, memory_gb=8),
        )

    @pytest.mark.asyncio
    async def test_optimize_performance(self, optimizer, sample_task):
        """Test performance optimization"""
        result = await optimizer.optimize_performance(sample_task)

        assert "performance_analysis" in result
        assert "bottlenecks" in result
        assert "optimization_strategies" in result
        assert "optimizations" in result
        assert "improvements" in result
        assert "validation" in result
        assert "quality_metrics" in result
        assert "deliverables" in result
        assert "recommendations" in result

    def test_load_optimization_patterns(self, optimizer):
        """Test optimization patterns loading"""
        patterns = optimizer.optimization_patterns

        assert "caching" in patterns
        assert "load_balancing" in patterns
        assert "database_optimization" in patterns
        assert "algorithm_optimization" in patterns
        assert "memory_optimization" in patterns

    def test_load_performance_metrics(self, optimizer):
        """Test performance metrics loading"""
        metrics = optimizer.performance_metrics

        assert "response_time" in metrics
        assert "throughput" in metrics
        assert "cpu_usage" in metrics
        assert "memory_usage" in metrics
        assert "error_rate" in metrics


class TestComplexProblemSolver:
    """Test the complex problem solver"""

    @pytest.fixture
    def config(self):
        """Test configuration"""
        return {
            "complex_problem_solving": {
                "patterns": {
                    "optimization": {
                        "types": ["linear_programming", "dynamic_programming"]
                    },
                    "search": {"types": ["binary_search", "depth_first"]},
                }
            }
        }

    @pytest.fixture
    def model_manager(self):
        """Mock model manager"""
        mock_manager = Mock()
        mock_manager.generate = AsyncMock(return_value="Mocked problem solution")
        return mock_manager

    @pytest.fixture
    def solver(self, config, model_manager):
        """Complex problem solver instance"""
        return ComplexProblemSolver(config, model_manager)

    @pytest.fixture
    def sample_task(self):
        """Sample complex task"""
        return ComplexTask(
            task_id="test-problem-task",
            title="Test Complex Problem Solving",
            description="Solve a complex optimization problem",
            task_type=TaskType.COMPLEX_PROBLEM_SOLVING,
            complexity=TaskComplexity.COMPLEX,
            resource_allocation=ResourceAllocation(cpu_cores=4, memory_gb=8),
        )

    @pytest.mark.asyncio
    async def test_solve_problem(self, solver, sample_task):
        """Test problem solving"""
        result = await solver.solve_problem(sample_task)

        assert "problem_analysis" in result
        assert "problem_classification" in result
        assert "solution_approach" in result
        assert "solution_implementation" in result
        assert "solution_validation" in result
        assert "solution_optimization" in result
        assert "quality_metrics" in result
        assert "deliverables" in result
        assert "recommendations" in result

    def test_load_problem_patterns(self, solver):
        """Test problem patterns loading"""
        patterns = solver.problem_patterns

        assert "optimization" in patterns
        assert "search" in patterns
        assert "classification" in patterns
        assert "clustering" in patterns
        assert "prediction" in patterns

    def test_load_solution_strategies(self, solver):
        """Test solution strategies loading"""
        strategies = solver.solution_strategies

        assert "divide_and_conquer" in strategies
        assert "dynamic_programming" in strategies
        assert "greedy_algorithm" in strategies
        assert "backtracking" in strategies
        assert "heuristic_search" in strategies


class TestResourceMonitor:
    """Test the resource monitor"""

    @pytest.fixture
    def config(self):
        """Test configuration"""
        return {
            "monitoring_interval": 5,
            "history_size": 100,
            "alert_thresholds": {
                "cpu_usage": 90.0,
                "memory_usage": 85.0,
                "disk_usage": 90.0,
                "network_latency": 100.0,
            },
        }

    @pytest.fixture
    def monitor(self, config):
        """Resource monitor instance"""
        return ResourceMonitor(config)

    @pytest.mark.asyncio
    async def test_start_stop_monitoring(self, monitor):
        """Test monitoring start/stop"""
        await monitor.start_monitoring()
        assert monitor.is_monitoring is True

        await monitor.stop_monitoring()
        assert monitor.is_monitoring is False

    @pytest.mark.asyncio
    async def test_track_progress(self, monitor):
        """Test progress tracking"""
        progress_report = ProgressReport(
            task_id="test-task", progress_percentage=50.0, current_phase="Testing"
        )

        await monitor.track_progress(progress_report)

        assert "test-task" in monitor.tasks_progress
        assert len(monitor.tasks_progress["test-task"]) == 1
        assert monitor.tasks_progress["test-task"][0].progress_percentage == 50.0

    @pytest.mark.asyncio
    async def test_check_availability(self, monitor):
        """Test resource availability checking"""
        allocation = ResourceAllocation(cpu_cores=2, memory_gb=4, storage_gb=10)

        # Mock current usage to be low
        with patch.object(monitor, "get_current_usage") as mock_usage:
            mock_usage.return_value = {
                "cpu": {"count": 8, "usage_percent": 20.0},
                "memory": {"available_gb": 16.0},
                "disk": {"free_gb": 100.0},
            }

            available = await monitor.check_availability(allocation)
            assert available is True

    @pytest.mark.asyncio
    async def test_get_usage_statistics(self, monitor):
        """Test usage statistics"""
        # Add some mock history data
        monitor.cpu_history = [
            {
                "timestamp": datetime.now(),
                "usage_percent": 50.0,
                "count": 4,
                "frequency_mhz": 2000,
            }
        ]
        monitor.memory_history = [
            {
                "timestamp": datetime.now(),
                "total_gb": 16.0,
                "used_gb": 8.0,
                "usage_percent": 50.0,
            }
        ]

        stats = await monitor.get_usage_statistics()

        assert "cpu" in stats
        assert "memory" in stats
        assert stats["cpu"]["average"] == 50.0
        assert stats["memory"]["average"] == 50.0


class TestQualityAssurance:
    """Test the quality assurance system"""

    @pytest.fixture
    def config(self):
        """Test configuration"""
        return {
            "quality_thresholds": {
                "code_quality": 80.0,
                "performance": 85.0,
                "test_coverage": 90.0,
                "security": 90.0,
                "documentation": 85.0,
                "user_satisfaction": 80.0,
            }
        }

    @pytest.fixture
    def qa(self, config):
        """Quality assurance instance"""
        return QualityAssurance(config)

    @pytest.fixture
    def sample_task(self):
        """Sample complex task"""
        return ComplexTask(
            task_id="test-qa-task",
            title="Test Quality Assurance",
            description="Test quality assessment",
            task_type=TaskType.ARCHITECTURE_DESIGN,
            complexity=TaskComplexity.COMPLEX,
            resource_allocation=ResourceAllocation(cpu_cores=4, memory_gb=8),
        )

    @pytest.fixture
    def sample_deliverables(self):
        """Sample deliverables"""
        return {
            "architecture_design": {"components": ["frontend", "backend"]},
            "documentation": {"api_docs": "comprehensive", "user_guide": "clear"},
            "tests": {"unit_tests": "comprehensive", "integration_tests": "adequate"},
        }

    @pytest.mark.asyncio
    async def test_assess_quality(self, qa, sample_task, sample_deliverables):
        """Test quality assessment"""
        result = await qa.assess_quality(sample_task, sample_deliverables)

        assert "overall_score" in result
        assert "code_quality" in result
        assert "performance" in result
        assert "security" in result
        assert "test_coverage" in result
        assert "documentation" in result
        assert "user_experience" in result
        assert "recommendations" in result
        assert "quality_metrics" in result

    @pytest.mark.asyncio
    async def test_run_automated_tests(self, qa, sample_task, sample_deliverables):
        """Test automated test execution"""
        result = await qa.run_automated_tests(sample_task, sample_deliverables)

        assert "unit_tests" in result
        assert "integration_tests" in result
        assert "performance_tests" in result
        assert "security_tests" in result

    @pytest.mark.asyncio
    async def test_validate_deliverables(self, qa, sample_task, sample_deliverables):
        """Test deliverable validation"""
        result = await qa.validate_deliverables(sample_task, sample_deliverables)

        # Check that validation results are returned for the deliverables that exist
        assert "documentation" in result
        assert "tests" in result
        # Note: "code" is not in sample_deliverables, so it won't be in the result

    def test_load_quality_checks(self, qa):
        """Test quality checks loading"""
        checks = qa.quality_checks

        assert "code_quality" in checks
        assert "performance" in checks
        assert "security" in checks
        assert "test_coverage" in checks
        assert "documentation" in checks

    def test_load_test_frameworks(self, qa):
        """Test test frameworks loading"""
        frameworks = qa.test_frameworks

        assert "unit_testing" in frameworks
        assert "integration_testing" in frameworks
        assert "performance_testing" in frameworks
        assert "security_testing" in frameworks


class TestProgressTracker:
    """Test the progress tracker"""

    @pytest.fixture
    def config(self):
        """Test configuration"""
        return {"tracking_interval_seconds": 60, "history_size": 1000}

    @pytest.fixture
    def tracker(self, config):
        """Progress tracker instance"""
        return ProgressTracker(config)

    @pytest.fixture
    def sample_task(self):
        """Sample complex task"""
        return ComplexTask(
            task_id="test-progress-task",
            title="Test Progress Tracking",
            description="Test progress tracking functionality",
            task_type=TaskType.ARCHITECTURE_DESIGN,
            complexity=TaskComplexity.COMPLEX,
            resource_allocation=ResourceAllocation(cpu_cores=4, memory_gb=8),
        )

    @pytest.mark.asyncio
    async def test_start_stop_tracking(self, tracker):
        """Test tracking start/stop"""
        await tracker.start_tracking()
        assert tracker.is_tracking is True

        await tracker.stop_tracking()
        assert tracker.is_tracking is False

    @pytest.mark.asyncio
    async def test_track_progress(self, tracker):
        """Test progress tracking"""
        progress_report = ProgressReport(
            task_id="test-task",
            progress_percentage=75.0,
            current_phase="Implementation",
        )

        await tracker.track_progress(progress_report)

        assert "test-task" in tracker.tasks_progress
        assert len(tracker.tasks_progress["test-task"]) == 1
        assert tracker.tasks_progress["test-task"][0].progress_percentage == 75.0

    @pytest.mark.asyncio
    async def test_add_milestone(self, tracker):
        """Test milestone addition"""
        milestone = {
            "name": "Design Complete",
            "description": "Architecture design completed",
            "due_date": (datetime.now() + timedelta(days=7)).isoformat(),
        }

        await tracker.add_milestone("test-task", milestone)

        assert "test-task" in tracker.milestones
        assert len(tracker.milestones["test-task"]) == 1
        assert tracker.milestones["test-task"][0]["name"] == "Design Complete"

    @pytest.mark.asyncio
    async def test_add_risk(self, tracker):
        """Test risk addition"""
        risk = {
            "description": "Resource constraint risk",
            "severity": "medium",
            "probability": 0.3,
            "impact": "high",
        }

        await tracker.add_risk("test-task", risk)

        assert "test-task" in tracker.risks
        assert len(tracker.risks["test-task"]) == 1
        assert (
            tracker.risks["test-task"][0]["description"] == "Resource constraint risk"
        )

    @pytest.mark.asyncio
    async def test_get_task_progress(self, tracker):
        """Test getting task progress"""
        # Add some progress data
        progress_report = ProgressReport(
            task_id="test-task", progress_percentage=60.0, current_phase="Testing"
        )
        await tracker.track_progress(progress_report)

        # Add milestone
        milestone = {"name": "Milestone 1", "description": "Test milestone"}
        await tracker.add_milestone("test-task", milestone)

        progress = await tracker.get_task_progress("test-task")

        assert progress["task_id"] == "test-task"
        assert "latest_progress" in progress
        assert "progress_history" in progress
        assert "progress_trend" in progress
        assert "milestones" in progress
        assert "risks" in progress

    @pytest.mark.asyncio
    async def test_get_progress_summary(self, tracker):
        """Test progress summary"""
        # Add some progress data
        progress_report = ProgressReport(
            task_id="test-task", progress_percentage=80.0, current_phase="Final Testing"
        )
        await tracker.track_progress(progress_report)

        summary = await tracker.get_progress_summary()

        assert summary["total_tasks"] == 1
        assert "active_tasks" in summary
        assert "completed_tasks" in summary
        assert "total_milestones" in summary
        assert "total_risks" in summary


class TestDataModels:
    """Test the data models"""

    def test_complex_task_creation(self):
        """Test ComplexTask creation"""
        task = ComplexTask(
            task_id="test-task",
            title="Test Task",
            description="Test task description",
            task_type=TaskType.ARCHITECTURE_DESIGN,
            complexity=TaskComplexity.COMPLEX,
            resource_allocation=ResourceAllocation(cpu_cores=4, memory_gb=8),
        )

        assert task.task_id == "test-task"
        assert task.title == "Test Task"
        assert task.task_type == TaskType.ARCHITECTURE_DESIGN
        assert task.complexity == TaskComplexity.COMPLEX
        assert task.status == TaskStatus.PENDING

    def test_resource_allocation(self):
        """Test ResourceAllocation"""
        allocation = ResourceAllocation(
            cpu_cores=8,
            memory_gb=16,
            gpu_required=True,
            gpu_memory_gb=8,
            storage_gb=100,
            network_bandwidth_mbps=1000,
            estimated_duration_hours=24.0,
            max_concurrent_tasks=2,
            priority_level=8,
        )

        assert allocation.cpu_cores == 8
        assert allocation.memory_gb == 16
        assert allocation.gpu_required is True
        assert allocation.gpu_memory_gb == 8
        assert allocation.priority_level == 8

    def test_quality_metrics(self):
        """Test QualityMetrics"""
        metrics = QualityMetrics(
            code_quality_score=90.0,
            performance_improvement=85.0,
            test_coverage=95.0,
            complexity_reduction=80.0,
            maintainability_score=88.0,
            security_score=92.0,
            documentation_quality=87.0,
            user_satisfaction=90.0,
            bugs_found=5,
            bugs_fixed=4,
            review_comments=10,
            review_approvals=2,
        )

        assert metrics.code_quality_score == 90.0
        assert metrics.performance_improvement == 85.0
        assert metrics.test_coverage == 95.0
        assert metrics.calculate_overall_score() > 0

    def test_progress_report(self):
        """Test ProgressReport"""
        report = ProgressReport(
            task_id="test-task",
            progress_percentage=75.0,
            current_phase="Implementation",
            milestones_completed=["Design", "Planning"],
            milestones_pending=["Testing", "Deployment"],
            issues_encountered=["Resource constraint"],
            solutions_implemented=["Resource optimization"],
        )

        assert report.task_id == "test-task"
        assert report.progress_percentage == 75.0
        assert report.current_phase == "Implementation"
        assert len(report.milestones_completed) == 2
        assert len(report.milestones_pending) == 2

    def test_task_serialization(self):
        """Test task serialization to/from dict"""
        task = ComplexTask(
            task_id="test-task",
            title="Test Task",
            description="Test task description",
            task_type=TaskType.ARCHITECTURE_DESIGN,
            complexity=TaskComplexity.COMPLEX,
            resource_allocation=ResourceAllocation(cpu_cores=4, memory_gb=8),
        )

        # Convert to dict
        task_dict = task.to_dict()

        # Convert back to task
        restored_task = ComplexTask.from_dict(task_dict)

        assert restored_task.task_id == task.task_id
        assert restored_task.title == task.title
        assert restored_task.task_type == task.task_type
        assert restored_task.complexity == task.complexity

    def test_quality_metrics_serialization(self):
        """Test quality metrics serialization"""
        metrics = QualityMetrics(
            code_quality_score=90.0, performance_improvement=85.0, test_coverage=95.0
        )

        # Convert to dict
        metrics_dict = metrics.to_dict()

        # Convert back to metrics
        restored_metrics = QualityMetrics.from_dict(metrics_dict)

        assert restored_metrics.code_quality_score == metrics.code_quality_score
        assert (
            restored_metrics.performance_improvement == metrics.performance_improvement
        )
        assert restored_metrics.test_coverage == metrics.test_coverage


class TestIntegration:
    """Integration tests for the complex tasks system"""

    @pytest.fixture
    def config(self):
        """Test configuration"""
        return {
            "max_concurrent_tasks": 2,
            "starcoder2": {
                "model_name": "starcoder2:3b",
                "temperature": 0.1,
                "max_tokens": 4096,
            },
        }

    @pytest.fixture
    def task_manager(self, config):
        """Task manager instance"""
        return ComplexTaskManager(config)

    @pytest.mark.asyncio
    async def test_full_workflow(self, task_manager):
        """Test full complex task workflow"""
        # Create task
        task_data = {
            "title": "Full Workflow Test",
            "description": "Test the complete complex task workflow",
            "task_type": "architecture_design",
            "complexity": "complex",
            "resource_allocation": {
                "cpu_cores": 4,
                "memory_gb": 8,
                "gpu_required": False,
                "estimated_duration_hours": 12.0,
            },
            "requirements": ["Scalability", "Security"],
            "priority": 8,
        }

        task = await task_manager.create_task(task_data)
        assert task.task_id is not None

        # Execute task
        with patch.object(
            task_manager.architecture_designer, "design_architecture"
        ) as mock_design:
            mock_design.return_value = {
                "architecture_design": {"components": ["frontend", "backend"]},
                "quality_metrics": QualityMetrics(code_quality_score=90.0),
            }

            result = await task_manager.execute_task(task.task_id)
            assert result is not None

        # Get task status
        status = await task_manager.get_task_status(task.task_id)
        assert status["task"]["task_id"] == task.task_id

        # Get analytics
        analytics = await task_manager.get_task_analytics()
        assert analytics["total_tasks"] == 1

    @pytest.mark.asyncio
    async def test_batch_execution(self, task_manager):
        """Test batch task execution"""
        # Create multiple tasks
        tasks = []
        for i in range(3):
            task_data = {
                "title": f"Batch Task {i}",
                "description": f"Test batch task {i}",
                "task_type": "performance_optimization",
                "complexity": "moderate",
                "resource_allocation": {
                    "cpu_cores": 2,
                    "memory_gb": 4,
                    "estimated_duration_hours": 4.0,
                },
            }
            task = await task_manager.create_task(task_data)
            tasks.append(task)

        # Execute batch
        task_ids = [task.task_id for task in tasks]

        with patch.object(
            task_manager.performance_optimizer, "optimize_performance"
        ) as mock_optimize:
            mock_optimize.return_value = {
                "performance_analysis": {"score": 85.0},
                "quality_metrics": QualityMetrics(performance_improvement=85.0),
            }

            results = await task_manager.execute_tasks_batch(task_ids)
            assert len(results) == 3
