#!/usr/bin/env python3
"""
AgentPerformanceTracker - Tracks historical performance metrics for agents

This module provides comprehensive performance tracking for agents including:
1. Success/failure rates
2. Task completion times
3. Error patterns and types
4. Performance trends over time
5. Agent-specific metrics
6. Learning from task outcomes
"""

import asyncio
import json
import logging
import statistics
import time
from collections import defaultdict, deque
from dataclasses import asdict, dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from pathlib import Path
from typing import Any, Dict, List, Optional, Tuple

logger = logging.getLogger(__name__)


class TaskOutcome(Enum):
    """Task outcome types"""

    SUCCESS = "success"
    FAILURE = "failure"
    TIMEOUT = "timeout"
    CANCELLED = "cancelled"
    PARTIAL_SUCCESS = "partial_success"


@dataclass
class PerformanceMetric:
    """Represents a performance metric for an agent"""

    agent_id: str
    metric_type: str
    value: float
    timestamp: datetime
    task_id: Optional[str] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class AgentPerformance:
    """Represents overall performance for an agent"""

    agent_id: str
    agent_type: str
    total_tasks: int = 0
    successful_tasks: int = 0
    failed_tasks: int = 0
    average_duration: float = 0.0
    success_rate: float = 0.0
    error_rate: float = 0.0
    last_updated: datetime = field(default_factory=datetime.now)
    performance_trend: List[float] = field(default_factory=list)
    recent_performance: deque = field(default_factory=lambda: deque(maxlen=100))


class AgentPerformanceTracker:
    """
    Tracks and analyzes agent performance metrics for intelligent routing decisions.
    """

    def __init__(self, config_path: str = "config/performance_tracker_config.json"):
        self.config = self._load_config(config_path)
        self.agent_performances: Dict[str, AgentPerformance] = {}
        self.performance_history: Dict[str, deque] = defaultdict(
            lambda: deque(maxlen=1000)
        )
        self.metrics_history: Dict[str, deque] = defaultdict(lambda: deque(maxlen=500))

        # Performance thresholds
        self.success_rate_threshold = self.config.get("thresholds", {}).get(
            "success_rate", 0.7
        )
        self.duration_threshold = self.config.get("thresholds", {}).get(
            "average_duration", 300
        )

        logger.info("AgentPerformanceTracker initialized")

    def _load_config(self, config_path: str) -> Dict[str, Any]:
        """Load configuration from JSON file"""
        try:
            with open(config_path, "r") as f:
                config = json.load(f)
            logger.info(f"Loaded performance tracker config from {config_path}")
            return config
        except FileNotFoundError:
            logger.warning(f"Config file {config_path} not found, using defaults")
            return self._get_default_config()
        except json.JSONDecodeError as e:
            logger.error(f"Invalid JSON in config file {config_path}: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """Get default configuration"""
        return {
            "tracking": {
                "enabled": True,
                "history_size": 1000,
                "cleanup_interval_hours": 24,
                "metrics_retention_days": 30,
            },
            "thresholds": {
                "success_rate": 0.7,
                "average_duration": 300,
                "error_rate": 0.3,
                "performance_degradation": 0.2,
            },
            "learning": {
                "enabled": True,
                "learning_rate": 0.1,
                "update_interval_minutes": 30,
                "trend_analysis": True,
            },
            "metrics": {
                "track_success_rate": True,
                "track_duration": True,
                "track_error_patterns": True,
                "track_trends": True,
                "track_specializations": True,
            },
        }

    async def register_agent(self, agent_id: str, agent_type: str) -> bool:
        """Register a new agent for performance tracking"""
        try:
            if agent_id not in self.agent_performances:
                self.agent_performances[agent_id] = AgentPerformance(
                    agent_id=agent_id, agent_type=agent_type
                )
                logger.info(
                    f"Registered agent {agent_id} ({agent_type}) for performance tracking"
                )
            return True
        except Exception as e:
            logger.error(f"Failed to register agent {agent_id}: {e}")
            return False

    async def record_task_outcome(
        self,
        agent_id: str,
        task_id: str,
        success: bool,
        duration: Optional[int] = None,
        error: Optional[str] = None,
    ) -> None:
        """Record the outcome of a task for an agent"""
        try:
            if agent_id not in self.agent_performances:
                await self.register_agent(agent_id, "unknown")

            performance = self.agent_performances[agent_id]

            # Update basic metrics
            performance.total_tasks += 1
            if success:
                performance.successful_tasks += 1
            else:
                performance.failed_tasks += 1

            # Update duration metrics
            if duration is not None:
                performance.recent_performance.append(duration)
                if len(performance.recent_performance) > 0:
                    performance.average_duration = statistics.mean(
                        performance.recent_performance
                    )

            # Update success rate
            performance.success_rate = (
                performance.successful_tasks / performance.total_tasks
            )
            performance.error_rate = performance.failed_tasks / performance.total_tasks

            # Update timestamp
            performance.last_updated = datetime.now()

            # Store detailed metric
            metric = PerformanceMetric(
                agent_id=agent_id,
                metric_type="task_outcome",
                value=1.0 if success else 0.0,
                timestamp=datetime.now(),
                task_id=task_id,
                metadata={
                    "success": success,
                    "duration": duration,
                    "error": error,
                    "outcome": TaskOutcome.SUCCESS if success else TaskOutcome.FAILURE,
                },
            )

            self.metrics_history[agent_id].append(metric)

            # Update performance trend
            self._update_performance_trend(agent_id)

            logger.debug(
                f"Recorded task outcome for {agent_id}: success={success}, duration={duration}"
            )

        except Exception as e:
            logger.error(f"Failed to record task outcome for {agent_id}: {e}")

    def _update_performance_trend(self, agent_id: str) -> None:
        """Update performance trend for an agent"""
        try:
            performance = self.agent_performances.get(agent_id)
            if not performance:
                return

            # Calculate recent performance (last 10 tasks)
            recent_metrics = list(performance.recent_performance)[-10:]
            if recent_metrics:
                recent_avg = statistics.mean(recent_metrics)
                performance.performance_trend.append(recent_avg)

                # Keep only last 50 trend points
                if len(performance.performance_trend) > 50:
                    performance.performance_trend = performance.performance_trend[-50:]

        except Exception as e:
            logger.error(f"Failed to update performance trend for {agent_id}: {e}")

    async def get_agent_performance(self, agent_id: str) -> Dict[str, Any]:
        """Get comprehensive performance metrics for an agent"""
        try:
            performance = self.agent_performances.get(agent_id)
            if not performance:
                return {}

            # Calculate additional metrics
            metrics = {
                "agent_id": agent_id,
                "agent_type": performance.agent_type,
                "total_tasks": performance.total_tasks,
                "successful_tasks": performance.successful_tasks,
                "failed_tasks": performance.failed_tasks,
                "success_rate": performance.success_rate,
                "error_rate": performance.error_rate,
                "average_duration": performance.average_duration,
                "last_updated": performance.last_updated.isoformat(),
                "performance_trend": performance.performance_trend[
                    -10:
                ],  # Last 10 points
                "recent_performance": list(performance.recent_performance)[
                    -20:
                ],  # Last 20 tasks
                "performance_score": self._calculate_performance_score(performance),
            }

            # Add trend analysis
            if len(performance.performance_trend) >= 2:
                metrics["trend_direction"] = self._analyze_trend(
                    performance.performance_trend
                )
                metrics["trend_stability"] = self._calculate_trend_stability(
                    performance.performance_trend
                )

            return metrics

        except Exception as e:
            logger.error(f"Failed to get performance for {agent_id}: {e}")
            return {}

    def _calculate_performance_score(self, performance: AgentPerformance) -> float:
        """Calculate overall performance score for an agent"""
        try:
            # Base score from success rate
            base_score = performance.success_rate

            # Duration penalty (longer durations reduce score)
            duration_penalty = 0.0
            if performance.average_duration > 0:
                # Normalize duration (0-1 scale, lower is better)
                normalized_duration = min(
                    performance.average_duration / self.duration_threshold, 1.0
                )
                duration_penalty = (1.0 - normalized_duration) * 0.3

            # Trend bonus/penalty
            trend_bonus = 0.0
            if len(performance.performance_trend) >= 2:
                recent_trend = performance.performance_trend[-5:]
                if len(recent_trend) >= 2:
                    trend_direction = self._analyze_trend(recent_trend)
                    if trend_direction == "improving":
                        trend_bonus = 0.1
                    elif trend_direction == "declining":
                        trend_bonus = -0.1

            # Calculate final score
            final_score = base_score + duration_penalty + trend_bonus
            return max(0.0, min(1.0, final_score))

        except Exception as e:
            logger.error(f"Failed to calculate performance score: {e}")
            return 0.5

    def _analyze_trend(self, trend_data: List[float]) -> str:
        """Analyze performance trend direction"""
        try:
            if len(trend_data) < 2:
                return "stable"

            # Calculate trend using linear regression
            x = list(range(len(trend_data)))
            y = trend_data

            n = len(x)
            if n < 2:
                return "stable"

            # Calculate slope
            sum_x = sum(x)
            sum_y = sum(y)
            sum_xy = sum(x[i] * y[i] for i in range(n))
            sum_x2 = sum(x[i] ** 2 for i in range(n))

            slope = (n * sum_xy - sum_x * sum_y) / (n * sum_x2 - sum_x**2)

            # Determine trend direction
            if slope > 0.01:  # Positive slope threshold
                return "improving"
            elif slope < -0.01:  # Negative slope threshold
                return "declining"
            else:
                return "stable"

        except Exception as e:
            logger.error(f"Failed to analyze trend: {e}")
            return "stable"

    def _calculate_trend_stability(self, trend_data: List[float]) -> float:
        """Calculate trend stability (lower values = more stable)"""
        try:
            if len(trend_data) < 2:
                return 0.0

            # Calculate coefficient of variation
            mean_val = statistics.mean(trend_data)
            if mean_val == 0:
                return 0.0

            std_val = statistics.stdev(trend_data)
            cv = std_val / mean_val

            return cv

        except Exception as e:
            logger.error(f"Failed to calculate trend stability: {e}")
            return 0.0

    async def get_agent_score(self, agent_id: str) -> float:
        """Get a normalized performance score (0.0 to 1.0) for an agent"""
        try:
            performance = self.agent_performances.get(agent_id)
            if not performance:
                return 0.5  # Default score for unknown agents

            return self._calculate_performance_score(performance)

        except Exception as e:
            logger.error(f"Failed to get agent score for {agent_id}: {e}")
            return 0.5

    async def get_top_performing_agents(self, limit: int = 5) -> List[Dict[str, Any]]:
        """Get top performing agents based on performance scores"""
        try:
            agent_scores = []

            for agent_id, performance in self.agent_performances.items():
                score = await self.get_agent_score(agent_id)
                agent_scores.append(
                    {
                        "agent_id": agent_id,
                        "agent_type": performance.agent_type,
                        "performance_score": score,
                        "success_rate": performance.success_rate,
                        "total_tasks": performance.total_tasks,
                    }
                )

            # Sort by performance score
            agent_scores.sort(key=lambda x: x["performance_score"], reverse=True)
            return agent_scores[:limit]

        except Exception as e:
            logger.error(f"Failed to get top performing agents: {e}")
            return []

    async def get_performance_insights(self) -> Dict[str, Any]:
        """Get overall performance insights across all agents"""
        try:
            insights = {
                "total_agents": len(self.agent_performances),
                "average_success_rate": 0.0,
                "average_duration": 0.0,
                "best_performing_agent": None,
                "worst_performing_agent": None,
                "performance_distribution": {
                    "excellent": 0,
                    "good": 0,
                    "average": 0,
                    "poor": 0,
                },
            }

            if not self.agent_performances:
                return insights

            # Calculate averages
            success_rates = [p.success_rate for p in self.agent_performances.values()]
            durations = [
                p.average_duration
                for p in self.agent_performances.values()
                if p.average_duration > 0
            ]

            insights["average_success_rate"] = (
                statistics.mean(success_rates) if success_rates else 0.0
            )
            insights["average_duration"] = (
                statistics.mean(durations) if durations else 0.0
            )

            # Find best and worst performers
            agent_scores = []
            for agent_id, performance in self.agent_performances.items():
                score = await self.get_agent_score(agent_id)
                agent_scores.append((agent_id, score))

            if agent_scores:
                agent_scores.sort(key=lambda x: x[1], reverse=True)
                insights["best_performing_agent"] = {
                    "agent_id": agent_scores[0][0],
                    "score": agent_scores[0][1],
                }
                insights["worst_performing_agent"] = {
                    "agent_id": agent_scores[-1][0],
                    "score": agent_scores[-1][1],
                }

            # Performance distribution
            for agent_id, performance in self.agent_performances.items():
                score = await self.get_agent_score(agent_id)
                if score >= 0.8:
                    insights["performance_distribution"]["excellent"] += 1
                elif score >= 0.6:
                    insights["performance_distribution"]["good"] += 1
                elif score >= 0.4:
                    insights["performance_distribution"]["average"] += 1
                else:
                    insights["performance_distribution"]["poor"] += 1

            return insights

        except Exception as e:
            logger.error(f"Failed to get performance insights: {e}")
            return {}

    async def cleanup(self) -> None:
        """Cleanup old performance data"""
        try:
            cutoff_time = datetime.now() - timedelta(
                days=self.config.get("tracking", {}).get("metrics_retention_days", 30)
            )

            # Cleanup metrics history
            for agent_id in list(self.metrics_history.keys()):
                self.metrics_history[agent_id] = deque(
                    [
                        m
                        for m in self.metrics_history[agent_id]
                        if m.timestamp > cutoff_time
                    ],
                    maxlen=1000,
                )

            logger.info("Performance tracker cleanup completed")

        except Exception as e:
            logger.error(f"Failed to cleanup performance tracker: {e}")
