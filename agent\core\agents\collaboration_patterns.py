import logging
from dataclasses import dataclass
from enum import Enum
from typing import Any, Callable, Dict, List, Optional

from agent.core.agents.collaboration_manager import (
    Collaboration,
    CollaborationPattern,
    CollaborationTask,
)

logger = logging.getLogger(__name__)


@dataclass
class PatternTemplate:
    """Template for creating collaboration patterns."""

    name: str
    pattern_type: CollaborationPattern
    description: str
    required_agents: List[str]
    optional_agents: Optional[List[str]] = None
    default_config: Optional[Dict[str, Any]] = None
    task_templates: Optional[List[Dict[str, Any]]] = None

    def __post_init__(self):
        if self.optional_agents is None:
            self.optional_agents = []
        if self.default_config is None:
            self.default_config = {}
        if self.task_templates is None:
            self.task_templates = []


class CollaborationPatternLibrary:
    """Library of predefined collaboration patterns for common scenarios."""

    def __init__(self):
        self.patterns: Dict[str, PatternTemplate] = {}
        self._initialize_default_patterns()
        logger.info("CollaborationPatternLibrary initialized with default patterns")

    def _initialize_default_patterns(self):
        """Initialize default collaboration patterns."""

        # Website Creation Pattern - Sequential
        self.patterns["website_creation"] = PatternTemplate(
            name="Website Creation",
            pattern_type=CollaborationPattern.SEQUENTIAL,
            description="Complete website creation from requirements to deployment",
            required_agents=["frontend", "backend", "container"],
            optional_agents=["security", "shell_ops"],
            default_config={
                "timeout_seconds": 600,
                "max_parallel_tasks": 1,
                "auto_rollback": True,
            },
            task_templates=[
                {
                    "task_type": "analyze_requirements",
                    "agent_id": "frontend",
                    "priority": 1,
                    "dependencies": [],
                },
                {
                    "task_type": "generate_backend_api",
                    "agent_id": "backend",
                    "priority": 2,
                    "dependencies": ["analyze_requirements"],
                },
                {
                    "task_type": "generate_frontend_components",
                    "agent_id": "frontend",
                    "priority": 3,
                    "dependencies": ["generate_backend_api"],
                },
                {
                    "task_type": "create_container",
                    "agent_id": "container",
                    "priority": 4,
                    "dependencies": ["generate_frontend_components"],
                },
                {
                    "task_type": "security_review",
                    "agent_id": "security",
                    "priority": 5,
                    "dependencies": ["create_container"],
                    "optional": True,
                },
            ],
        )

        # Code Review Pattern - Parallel
        self.patterns["code_review"] = PatternTemplate(
            name="Code Review",
            pattern_type=CollaborationPattern.PARALLEL,
            description="Parallel code review by multiple specialized agents",
            required_agents=["security", "frontend", "backend"],
            optional_agents=["learning"],
            default_config={
                "timeout_seconds": 300,
                "max_parallel_tasks": 3,
                "require_consensus": True,
            },
            task_templates=[
                {
                    "task_type": "security_analysis",
                    "agent_id": "security",
                    "priority": 1,
                    "dependencies": [],
                },
                {
                    "task_type": "frontend_review",
                    "agent_id": "frontend",
                    "priority": 1,
                    "dependencies": [],
                },
                {
                    "task_type": "backend_review",
                    "agent_id": "backend",
                    "priority": 1,
                    "dependencies": [],
                },
                {
                    "task_type": "learning_analysis",
                    "agent_id": "learning",
                    "priority": 2,
                    "dependencies": [
                        "security_analysis",
                        "frontend_review",
                        "backend_review",
                    ],
                    "optional": True,
                },
            ],
        )

        # Bug Fix Pattern - Pipeline
        self.patterns["bug_fix"] = PatternTemplate(
            name="Bug Fix Pipeline",
            pattern_type=CollaborationPattern.PIPELINE,
            description="Pipeline for analyzing, fixing, and testing bugs",
            required_agents=["learning", "frontend", "backend"],
            optional_agents=["security", "shell_ops"],
            default_config={
                "timeout_seconds": 400,
                "max_parallel_tasks": 1,
                "auto_test": True,
            },
            task_templates=[
                {
                    "task_type": "analyze_bug",
                    "agent_id": "learning",
                    "priority": 1,
                    "dependencies": [],
                },
                {
                    "task_type": "generate_fix",
                    "agent_id": "frontend",  # Will be determined dynamically
                    "priority": 2,
                    "dependencies": ["analyze_bug"],
                },
                {
                    "task_type": "test_fix",
                    "agent_id": "shell_ops",
                    "priority": 3,
                    "dependencies": ["generate_fix"],
                    "optional": True,
                },
                {
                    "task_type": "security_check",
                    "agent_id": "security",
                    "priority": 4,
                    "dependencies": ["test_fix"],
                    "optional": True,
                },
            ],
        )

        # Feature Enhancement Pattern - Hierarchical
        self.patterns["feature_enhancement"] = PatternTemplate(
            name="Feature Enhancement",
            pattern_type=CollaborationPattern.HIERARCHICAL,
            description="Hierarchical approach to adding new features",
            required_agents=["learning", "frontend", "backend", "container"],
            optional_agents=["security", "shell_ops"],
            default_config={
                "timeout_seconds": 800,
                "max_parallel_tasks": 2,
                "staged_deployment": True,
            },
            task_templates=[
                {
                    "task_type": "analyze_requirements",
                    "agent_id": "learning",
                    "priority": 1,
                    "dependencies": [],
                    "level": 0,
                },
                {
                    "task_type": "design_backend_changes",
                    "agent_id": "backend",
                    "priority": 1,
                    "dependencies": ["analyze_requirements"],
                    "level": 1,
                },
                {
                    "task_type": "design_frontend_changes",
                    "agent_id": "frontend",
                    "priority": 1,
                    "dependencies": ["analyze_requirements"],
                    "level": 1,
                },
                {
                    "task_type": "implement_backend",
                    "agent_id": "backend",
                    "priority": 2,
                    "dependencies": ["design_backend_changes"],
                    "level": 2,
                },
                {
                    "task_type": "implement_frontend",
                    "agent_id": "frontend",
                    "priority": 2,
                    "dependencies": ["design_frontend_changes", "implement_backend"],
                    "level": 2,
                },
                {
                    "task_type": "update_container",
                    "agent_id": "container",
                    "priority": 3,
                    "dependencies": ["implement_backend", "implement_frontend"],
                    "level": 3,
                },
            ],
        )

        # Performance Optimization Pattern - Competitive
        self.patterns["performance_optimization"] = PatternTemplate(
            name="Performance Optimization",
            pattern_type=CollaborationPattern.COMPETITIVE,
            description="Multiple agents compete to find best optimization approach",
            required_agents=["frontend", "backend"],
            optional_agents=["learning", "shell_ops"],
            default_config={
                "timeout_seconds": 300,
                "max_parallel_tasks": 4,
                "select_best": True,
                "benchmark_required": True,
            },
            task_templates=[
                {
                    "task_type": "optimize_frontend",
                    "agent_id": "frontend",
                    "priority": 1,
                    "dependencies": [],
                },
                {
                    "task_type": "optimize_backend",
                    "agent_id": "backend",
                    "priority": 1,
                    "dependencies": [],
                },
                {
                    "task_type": "optimize_database",
                    "agent_id": "backend",
                    "priority": 1,
                    "dependencies": [],
                },
                {
                    "task_type": "optimize_algorithms",
                    "agent_id": "learning",
                    "priority": 1,
                    "dependencies": [],
                    "optional": True,
                },
            ],
        )

        # Quick Fix Pattern - Sequential (Fast)
        self.patterns["quick_fix"] = PatternTemplate(
            name="Quick Fix",
            pattern_type=CollaborationPattern.SEQUENTIAL,
            description="Fast sequential fix for urgent issues",
            required_agents=["learning"],
            optional_agents=["frontend", "backend", "security"],
            default_config={
                "timeout_seconds": 120,
                "max_parallel_tasks": 1,
                "fast_mode": True,
            },
            task_templates=[
                {
                    "task_type": "quick_analysis",
                    "agent_id": "learning",
                    "priority": 1,
                    "dependencies": [],
                },
                {
                    "task_type": "apply_fix",
                    "agent_id": "frontend",  # Will be determined dynamically
                    "priority": 2,
                    "dependencies": ["quick_analysis"],
                },
            ],
        )

        # Full Stack Development Pattern - Pipeline
        self.patterns["full_stack_development"] = PatternTemplate(
            name="Full Stack Development",
            pattern_type=CollaborationPattern.PIPELINE,
            description="Complete full-stack development pipeline",
            required_agents=["backend", "frontend", "container", "shell_ops"],
            optional_agents=["security", "learning"],
            default_config={
                "timeout_seconds": 900,
                "max_parallel_tasks": 2,
                "comprehensive_testing": True,
            },
            task_templates=[
                {
                    "task_type": "design_architecture",
                    "agent_id": "backend",
                    "priority": 1,
                    "dependencies": [],
                },
                {
                    "task_type": "implement_backend_core",
                    "agent_id": "backend",
                    "priority": 2,
                    "dependencies": ["design_architecture"],
                },
                {
                    "task_type": "implement_frontend_core",
                    "agent_id": "frontend",
                    "priority": 3,
                    "dependencies": ["implement_backend_core"],
                },
                {
                    "task_type": "setup_deployment",
                    "agent_id": "container",
                    "priority": 4,
                    "dependencies": ["implement_frontend_core"],
                },
                {
                    "task_type": "run_tests",
                    "agent_id": "shell_ops",
                    "priority": 5,
                    "dependencies": ["setup_deployment"],
                },
                {
                    "task_type": "security_audit",
                    "agent_id": "security",
                    "priority": 6,
                    "dependencies": ["run_tests"],
                    "optional": True,
                },
            ],
        )

    def get_pattern(self, pattern_name: str) -> Optional[PatternTemplate]:
        """Get a pattern template by name."""
        return self.patterns.get(pattern_name)

    def list_patterns(self) -> List[str]:
        """List all available pattern names."""
        return list(self.patterns.keys())

    def get_patterns_for_agents(self, available_agents: List[str]) -> List[str]:
        """Get patterns that can be executed with available agents."""
        compatible_patterns = []

        for pattern_name, pattern in self.patterns.items():
            # Check if all required agents are available
            if all(agent in available_agents for agent in pattern.required_agents):
                compatible_patterns.append(pattern_name)

        return compatible_patterns

    def create_collaboration_from_pattern(
        self,
        pattern_name: str,
        collaboration_name: str,
        task_data: Dict[str, Any],
        agent_mapping: Optional[Dict[str, str]] = None,
    ) -> Optional[Collaboration]:
        """Create a collaboration from a pattern template."""
        pattern = self.get_pattern(pattern_name)
        if not pattern:
            logger.error(f"Pattern '{pattern_name}' not found")
            return None

        # Create collaboration
        # Merge pattern default config into kwargs safely
        default_cfg = pattern.default_config or {}
        if not isinstance(default_cfg, dict):
            default_cfg = {}

        collaboration = Collaboration(
            id=f"{pattern_name}_{collaboration_name}",
            name=collaboration_name,
            pattern=pattern.pattern_type,
            **default_cfg,
        )

        # Add shared context from pattern
        collaboration.shared_context.update(task_data)

        # Create tasks from templates
        templates = pattern.task_templates or []
        for task_template in templates:
            # Skip optional tasks if agent not available
            if task_template.get("optional", False):
                candidate_id = (
                    agent_mapping.get(task_template["agent_id"], task_template["agent_id"]) if agent_mapping else task_template["agent_id"]
                )
                if agent_mapping and candidate_id not in agent_mapping.values():
                    continue

            # Create task
            task_id = f"{collaboration.id}_{task_template['task_type']}"
            mapped_id = (
                agent_mapping.get(task_template["agent_id"], task_template["agent_id"]) if agent_mapping else task_template["agent_id"]
            )
            agent_id = str(mapped_id) if mapped_id is not None else str(task_template["agent_id"])

            task = CollaborationTask(
                id=task_id,
                agent_id=agent_id,
                task_type=task_template["task_type"],
                task_data=task_data.copy(),
                dependencies=[
                    f"{collaboration.id}_{dep}"
                    for dep in task_template.get("dependencies", [])
                ],
                priority=task_template.get("priority", 1),
            )

            collaboration.add_task(task)

        logger.info(
            f"Created collaboration '{collaboration_name}' from pattern '{pattern_name}'"
        )
        return collaboration

    def add_custom_pattern(self, pattern: PatternTemplate) -> None:
        """Add a custom pattern to the library."""
        self.patterns[pattern.name.lower().replace(" ", "_")] = pattern
        logger.info(f"Added custom pattern: {pattern.name}")

    def validate_pattern(self, pattern: PatternTemplate) -> List[str]:
        """Validate a pattern template and return any issues."""
        issues = []

        if not pattern.name:
            issues.append("Pattern name is required")

        if not pattern.required_agents:
            issues.append("At least one required agent must be specified")

        if not pattern.task_templates:
            issues.append("At least one task template must be specified")

        # Validate task dependencies
        templates = pattern.task_templates or []
        task_ids = {template["task_type"] for template in templates}
        for task_template in templates:
            for dep in task_template.get("dependencies", []):
                if dep not in task_ids:
                    issues.append(
                        f"Task '{task_template['task_type']}' has invalid dependency '{dep}'"
                    )

        # Check for circular dependencies
        if self._has_circular_dependencies(templates):
            issues.append("Pattern has circular dependencies")

        return issues

    def _has_circular_dependencies(self, task_templates: List[Dict[str, Any]]) -> bool:
        """Check if task templates have circular dependencies."""
        # Build dependency graph
        graph = {}
        for template in task_templates:
            task_id = template["task_type"]
            dependencies = template.get("dependencies", [])
            graph[task_id] = dependencies

        # DFS to detect cycles
        def has_cycle(node, visited, rec_stack):
            visited.add(node)
            rec_stack.add(node)

            for neighbor in graph.get(node, []):
                if neighbor not in visited:
                    if has_cycle(neighbor, visited, rec_stack):
                        return True
                elif neighbor in rec_stack:
                    return True

            rec_stack.remove(node)
            return False

        visited = set()
        for node in graph:
            if node not in visited:
                if has_cycle(node, visited, set()):
                    return True

        return False

    def get_pattern_recommendations(
        self, task_description: str, available_agents: List[str]
    ) -> List[Dict[str, Any]]:
        """Get pattern recommendations based on task description and available agents."""
        recommendations = []

        # Simple keyword-based matching (could be enhanced with ML)
        keywords = task_description.lower().split()

        for pattern_name, pattern in self.patterns.items():
            score = 0

            # Check agent compatibility
            if all(agent in available_agents for agent in pattern.required_agents):
                score += 10
            else:
                continue  # Skip if required agents not available

            # Check keyword matches
            pattern_keywords = (pattern.name + " " + pattern.description).lower()
            for keyword in keywords:
                if keyword in pattern_keywords:
                    score += 1

            # Bonus for simpler patterns when fewer agents available
            if len(available_agents) <= 3 and len(pattern.required_agents) <= 2:
                score += 2

            # Bonus for comprehensive patterns when many agents available
            if len(available_agents) >= 5 and len(pattern.required_agents) >= 3:
                score += 2

            if score > 10:  # Minimum threshold
                recommendations.append(
                    {
                        "pattern_name": pattern_name,
                        "pattern": pattern,
                        "score": score,
                        "reason": f"Compatible with {len(pattern.required_agents)} required agents",
                    }
                )

        # Sort by score
        recommendations.sort(key=lambda x: x["score"], reverse=True)
        return recommendations[:5]  # Return top 5

    def export_patterns(self, file_path: str) -> None:
        """Export patterns to a JSON file."""
        try:
            import json
            from pathlib import Path

            export_data = {}
            for name, pattern in self.patterns.items():
                export_data[name] = {
                    "name": pattern.name,
                    "pattern_type": pattern.pattern_type.value,
                    "description": pattern.description,
                    "required_agents": pattern.required_agents,
                    "optional_agents": pattern.optional_agents,
                    "default_config": pattern.default_config,
                    "task_templates": pattern.task_templates,
                }

            Path(file_path).parent.mkdir(parents=True, exist_ok=True)
            with open(file_path, "w") as f:
                json.dump(export_data, f, indent=2)

            logger.info(f"Exported {len(self.patterns)} patterns to {file_path}")
        except Exception as e:
            logger.error(f"Failed to export patterns: {e}")

    def import_patterns(self, file_path: str) -> int:
        """Import patterns from a JSON file."""
        try:
            import json
            from pathlib import Path

            if not Path(file_path).exists():
                logger.error(f"Pattern file not found: {file_path}")
                return 0

            with open(file_path, "r") as f:
                import_data = json.load(f)

            imported_count = 0
            for name, pattern_data in import_data.items():
                try:
                    pattern = PatternTemplate(
                        name=pattern_data["name"],
                        pattern_type=CollaborationPattern(pattern_data["pattern_type"]),
                        description=pattern_data["description"],
                        required_agents=pattern_data["required_agents"],
                        optional_agents=pattern_data.get("optional_agents", []),
                        default_config=pattern_data.get("default_config", {}),
                        task_templates=pattern_data.get("task_templates", []),
                    )

                    # Validate pattern
                    issues = self.validate_pattern(pattern)
                    if issues:
                        logger.warning(f"Pattern '{name}' has issues: {issues}")
                        continue

                    self.patterns[name] = pattern
                    imported_count += 1
                except Exception as e:
                    logger.error(f"Failed to import pattern '{name}': {e}")

            logger.info(f"Imported {imported_count} patterns from {file_path}")
            return imported_count
        except Exception as e:
            logger.error(f"Failed to import patterns: {e}")
            return 0
