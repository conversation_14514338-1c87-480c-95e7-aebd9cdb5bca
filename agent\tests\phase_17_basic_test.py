#!/usr/bin/env python3
"""
Phase 17: Basic Performance Test
Quick test of core performance optimization features.
"""

import json
import logging
import sys
from datetime import datetime, timezone
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from performance.performance_manager import PerformanceManager

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


def main():
    """Test basic Phase 17 functionality."""
    try:
        logger.info("Testing Phase 17: Performance Optimization - Basic Functionality")

        # Initialize performance manager
        performance_manager = PerformanceManager("config/performance_config.json")

        # Test 1: Cache functionality
        logger.info("Testing cache functionality...")
        cache_key = "test_data"
        cache_value = {
            "test": "value",
            "timestamp": datetime.now(timezone.utc).isoformat(),
        }

        # Set cache
        set_success = performance_manager.set_cache(cache_key, cache_value)
        logger.info(f"Cache set: {set_success}")

        # Get cache
        retrieved_value = performance_manager.get_cache(cache_key)
        cache_working = retrieved_value == cache_value
        logger.info(f"Cache get: {cache_working}")

        # Get cache stats
        cache_stats = performance_manager.get_cache_stats()
        logger.info(f"Cache stats: {cache_stats.get('enabled', False)}")

        # Test 2: Resource monitoring
        logger.info("Testing resource monitoring...")
        resource_usage = performance_manager.get_resource_usage()
        resource_working = "cpu" in resource_usage
        logger.info(f"Resource monitoring: {resource_working}")

        # Test 3: Performance analytics
        logger.info("Testing performance analytics...")
        performance_manager.analytics.add_metric("test_metric", 100.0)
        analysis = performance_manager.analyze_performance(1)
        analytics_working = "metrics" in analysis
        logger.info(f"Performance analytics: {analytics_working}")

        # Test 4: Bundle optimization
        logger.info("Testing bundle optimization...")
        bundle_analysis = performance_manager.analyze_bundle_sizes()
        bundle_working = "bundle_sizes" in bundle_analysis
        logger.info(f"Bundle optimization: {bundle_working}")

        # Test 5: Performance status
        logger.info("Testing performance status...")
        status = performance_manager.get_performance_status()
        status_working = "enabled" in status
        logger.info(f"Performance status: {status_working}")

        # Generate results
        results = {
            "phase": "Phase 17: Performance Optimization - Basic Test",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "tests": {
                "cache_functionality": {
                    "success": set_success and cache_working,
                    "details": f"Set: {set_success}, Get: {cache_working}",
                },
                "resource_monitoring": {
                    "success": resource_working,
                    "details": f"Resource data available: {resource_working}",
                },
                "performance_analytics": {
                    "success": analytics_working,
                    "details": f"Analytics working: {analytics_working}",
                },
                "bundle_optimization": {
                    "success": bundle_working,
                    "details": f"Bundle analysis working: {bundle_working}",
                },
                "performance_status": {
                    "success": status_working,
                    "details": f"Status available: {status_working}",
                },
            },
        }

        # Calculate summary
        tests = results["tests"]
        total_tests = len(tests)
        passed_tests = sum(1 for test in tests.values() if test.get("success", False))
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0

        results["summary"] = {
            "total_tests": total_tests,
            "passed_tests": passed_tests,
            "failed_tests": total_tests - passed_tests,
            "success_rate": success_rate,
            "status": (
                "PASSED"
                if success_rate == 100
                else "PARTIAL" if success_rate >= 80 else "FAILED"
            ),
        }

        # Save results
        results_file = "test_reports/phase_17_basic_results.json"
        Path("test_reports").mkdir(exist_ok=True)

        with open(results_file, "w") as f:
            json.dump(results, f, indent=2)

        logger.info(
            f"Basic test completed: {passed_tests}/{total_tests} tests passed ({success_rate:.1f}% success rate)"
        )
        logger.info(f"Results saved to {results_file}")

        # Print summary
        print(f"\nPhase 17 Basic Performance Test Results:")
        print(f"Total Tests: {total_tests}")
        print(f"Passed Tests: {passed_tests}")
        print(f"Success Rate: {success_rate:.1f}%")
        print(f"Status: {results['summary']['status']}")

        return success_rate == 100

    except Exception as e:
        logger.error(f"Error in basic test: {e}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
