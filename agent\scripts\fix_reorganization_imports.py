#!/usr/bin/env python3
"""
Import Path Updater for Directory Reorganization

This script updates all import statements to reflect the new 6-directory structure:
- /agent/ - Core AI agent system
- /projects/ - User project files  
- /infrastructure/ - Infrastructure & deployment
- /shared/ - Shared resources
- /tools/ - Development tools
- /docs/ - Documentation & reports

Handles both absolute and relative imports that reference the old structure.
"""

import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple

# Directory mapping from old to new structure
DIRECTORY_MAPPING = {
    # Old paths -> New paths
    "core": "agent/core",
    "security": "agent/security", 
    "models": "agent/models",
    "monitoring": "agent/monitoring",
    "database": "agent/database",
    "utils": "agent/utils",
    "scripts": "agent/scripts",
    "api": "agent/api",
    "cli": "agent/cli",
    "tests": "agent/tests",
    "config": "agent/config",
    "validation": "agent/validation",
    "learning": "agent/learning",
    "frontend": "agent/frontend",
    "dashboard": "agent/dashboard",
    
    # Infrastructure
    "containers": "infrastructure/containers",
    "k8s": "infrastructure/k8s", 
    "nginx": "infrastructure/nginx",
    "ssl": "infrastructure/ssl",
    "ollama": "infrastructure/ollama",
    
    # Shared resources
    "data": "shared/data",
    "logs": "shared/logs",
    "cache": "shared/cache",
    "temp": "shared/temp",
    "uploads": "shared/uploads",
    
    # Tools
    "templates": "tools/templates",
    "migrations": "tools/migrations",
    "build": "tools/build",
    "dist": "tools/dist",
    "public": "tools/public",
    "error_analysis": "tools/error_analysis",
    "external_libs": "tools/external_libs",
    "model_versioning": "tools/model_versioning",
    "cypress": "tools/cypress",
    
    # Documentation
    "reports": "docs/reports",
    "test_reports": "docs/test_reports", 
    "test_logs": "docs/test_logs",
    "test_screenshots": "docs/test_screenshots",
    "evaluation_results": "docs/evaluation_results",
    "export": "docs/export",
    "exports": "docs/exports",
    "fine_tuning": "docs/fine_tuning",
    
    # Projects
    "sites": "projects/sites",
    "backups": "projects/backups",
    "content": "projects/content", 
    "themes": "projects/themes",
    "deployments": "projects/deployments"
}

def find_python_files(root_dir: str = ".") -> List[Path]:
    """Find all Python files in the project."""
    python_files = []
    for root, dirs, files in os.walk(root_dir):
        # Skip virtual environments and cache directories
        dirs[:] = [
            d for d in dirs if d not in {".venv", "__pycache__", ".git", "node_modules", ".pytest_cache"}
        ]
        
        for file in files:
            if file.endswith(".py"):
                python_files.append(Path(root) / file)
    return python_files

def update_import_path(import_path: str) -> str:
    """Update an import path based on the directory mapping."""
    # Split the import path into parts
    parts = import_path.split(".")
    
    # Check if the first part matches any old directory
    if parts[0] in DIRECTORY_MAPPING:
        new_path = DIRECTORY_MAPPING[parts[0]]
        new_parts = new_path.split("/") + parts[1:]
        return ".".join(new_parts)
    
    # Check for multi-level mappings (e.g., "core.managers" -> "agent.core.managers")
    for old_path, new_path in DIRECTORY_MAPPING.items():
        if import_path.startswith(old_path + ".") or import_path == old_path:
            return import_path.replace(old_path, new_path.replace("/", "."), 1)
    
    return import_path

def fix_imports_in_file(file_path: Path) -> List[str]:
    """Fix import statements in a single file."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        original_content = content
        changes = []
        
        # Pattern 1: from module import ...
        pattern1 = r"from\s+([a-zA-Z_][a-zA-Z0-9_./]*)\s+import"
        matches1 = re.finditer(pattern1, content)
        for match in matches1:
            old_module = match.group(1)
            new_module = update_import_path(old_module)
            if new_module != old_module:
                old_import = match.group(0)
                new_import = old_import.replace(old_module, new_module)
                content = content.replace(old_import, new_import)
                changes.append(f"  {old_import} -> {new_import}")
        
        # Pattern 2: import module
        pattern2 = r"import\s+([a-zA-Z_][a-zA-Z0-9_./]*)"
        matches2 = re.finditer(pattern2, content)
        for match in matches2:
            old_module = match.group(1)
            new_module = update_import_path(old_module)
            if new_module != old_module:
                old_import = match.group(0)
                new_import = old_import.replace(old_module, new_module)
                content = content.replace(old_import, new_import)
                changes.append(f"  {old_import} -> {new_import}")
        
        # Write back if changes were made
        if content != original_content:
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            return changes
        
        return []
    
    except Exception as e:
        print(f"Error processing {file_path}: {e}")
        return []

def main():
    """Main function to fix all import paths."""
    print("🔍 Finding Python files...")
    python_files = find_python_files()
    print(f"Found {len(python_files)} Python files")
    
    total_changes = 0
    files_with_changes = 0
    
    print("\n🔧 Updating import paths...")
    for file_path in python_files:
        changes = fix_imports_in_file(file_path)
        if changes:
            print(f"\n📝 {file_path}:")
            for change in changes:
                print(change)
            total_changes += len(changes)
            files_with_changes += 1
    
    print(f"\n✅ Import path update complete!")
    print(f"  Files modified: {files_with_changes}")
    print(f"  Total changes: {total_changes}")
    
    if files_with_changes > 0:
        print(f"\n💡 Next steps:")
        print(f"  1. Test the updated imports")
        print(f"  2. Run tests to ensure functionality")
        print(f"  3. Commit the changes")

if __name__ == "__main__":
    main()
