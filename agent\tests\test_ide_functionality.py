#!/usr/bin/env python3
"""
IDE Functionality Test Script
Verifies that the IDE-style interface is fully functional with all components.
"""

import asyncio
import json
import sys
from pathlib import Path
from typing import Any, Dict, List


class IDEFunctionalityTester:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.test_results = []
        self.overall_success = True

    def log_test(self, test_name: str, success: bool, message: str):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}: {message}")
        self.test_results.append(
            {"test": test_name, "success": success, "message": message}
        )
        if not success:
            self.overall_success = False

    def test_ide_components_exist(self) -> bool:
        """Test that all IDE components exist"""
        try:
            required_components = [
                "components/ide/IDELayout.tsx",
                "components/ide/ChatPanel.tsx",
                "components/ide/CodeEditor.tsx",
                "components/ide/Toolbar.tsx",
                "components/ide/StatusBar.tsx",
                "components/ide/PreviewPanel.tsx",
                "components/ide/ModelHealthPanel.tsx",
                "components/ide/DocumentationPanel.tsx",
            ]

            missing_components = []
            for component in required_components:
                if not (self.project_root / component).exists():
                    missing_components.append(component)

            if missing_components:
                self.log_test(
                    "IDE Components", False, f"Missing components: {missing_components}"
                )
                return False

            self.log_test("IDE Components", True, "All IDE components present")
            return True

        except Exception as e:
            self.log_test("IDE Components", False, f"Error: {str(e)}")
            return False

    def test_ide_services_exist(self) -> bool:
        """Test that all required services exist"""
        try:
            required_services = [
                "services/FileManager.ts",
                "services/AIService.ts",
                "services/ModelHealthMonitor.ts",
                "services/DeploymentService.ts",
                "services/SSLService.ts",
                "services/MaintenanceService.ts",
                "services/PerformanceService.ts",
                "services/TestingService.ts",
                "services/HelpService.ts",
            ]

            missing_services = []
            for service in required_services:
                if not (self.project_root / service).exists():
                    missing_services.append(service)

            if missing_services:
                self.log_test(
                    "IDE Services", False, f"Missing services: {missing_services}"
                )
                return False

            self.log_test("IDE Services", True, "All IDE services present")
            return True

        except Exception as e:
            self.log_test("IDE Services", False, f"Error: {str(e)}")
            return False

    def test_ide_page_exists(self) -> bool:
        """Test that IDE page exists and is properly configured"""
        try:
            ide_page = self.project_root / "pages/ide.tsx"
            if not ide_page.exists():
                self.log_test("IDE Page", False, "IDE page not found")
                return False

            # Check that IDE page imports IDELayout
            with open(ide_page, "r") as f:
                content = f.read()
                if "IDELayout" not in content:
                    self.log_test(
                        "IDE Page", False, "IDE page does not import IDELayout"
                    )
                    return False

            self.log_test("IDE Page", True, "IDE page properly configured")
            return True

        except Exception as e:
            self.log_test("IDE Page", False, f"Error: {str(e)}")
            return False

    def test_navigation_integration(self) -> bool:
        """Test that IDE is properly integrated into navigation"""
        try:
            sidebar = self.project_root / "components/layout/Sidebar.tsx"
            if not sidebar.exists():
                self.log_test(
                    "Navigation Integration", False, "Sidebar component not found"
                )
                return False

            with open(sidebar, "r") as f:
                content = f.read()
                if "/ide" not in content:
                    self.log_test(
                        "Navigation Integration",
                        False,
                        "IDE route not found in navigation",
                    )
                    return False

            self.log_test(
                "Navigation Integration",
                True,
                "IDE properly integrated into navigation",
            )
            return True

        except Exception as e:
            self.log_test("Navigation Integration", False, f"Error: {str(e)}")
            return False

    def test_app_routing(self) -> bool:
        """Test that app routing is properly configured"""
        try:
            app_file = self.project_root / "pages/_app.tsx"
            if not app_file.exists():
                self.log_test("App Routing", False, "_app.tsx not found")
                return False

            with open(app_file, "r") as f:
                content = f.read()
                if "/ide" not in content:
                    self.log_test(
                        "App Routing", False, "IDE route not configured in _app.tsx"
                    )
                    return False

            self.log_test("App Routing", True, "App routing properly configured")
            return True

        except Exception as e:
            self.log_test("App Routing", False, f"Error: {str(e)}")
            return False

    def test_index_redirect(self) -> bool:
        """Test that index page redirects to IDE"""
        try:
            index_file = self.project_root / "pages/index.tsx"
            if not index_file.exists():
                self.log_test("Index Redirect", False, "index.tsx not found")
                return False

            with open(index_file, "r") as f:
                content = f.read()
                if "/ide" not in content:
                    self.log_test(
                        "Index Redirect", False, "Index page does not redirect to IDE"
                    )
                    return False

            self.log_test(
                "Index Redirect", True, "Index page properly redirects to IDE"
            )
            return True

        except Exception as e:
            self.log_test("Index Redirect", False, f"Error: {str(e)}")
            return False

    def test_ide_layout_structure(self) -> bool:
        """Test that IDELayout has proper structure"""
        try:
            layout_file = self.project_root / "components/ide/IDELayout.tsx"
            if not layout_file.exists():
                self.log_test("IDE Layout Structure", False, "IDELayout.tsx not found")
                return False

            with open(layout_file, "r") as f:
                content = f.read()

                # Check for required components
                required_components = [
                    "IDESidebarUnified",
                    "ChatPanel",
                    "CodeEditor",
                    "Toolbar",
                    "StatusBar",
                    "PreviewPanel",
                    "ModelHealthPanel",
                    "DocumentationPanel",
                ]

                missing_components = []
                for component in required_components:
                    if component not in content:
                        missing_components.append(component)

                if missing_components:
                    self.log_test(
                        "IDE Layout Structure",
                        False,
                        f"Missing components in IDELayout: {missing_components}",
                    )
                    return False

                # Check for PanelGroup structure
                if "PanelGroup" not in content:
                    self.log_test(
                        "IDE Layout Structure",
                        False,
                        "PanelGroup not found in IDELayout",
                    )
                    return False

            self.log_test(
                "IDE Layout Structure", True, "IDELayout has proper structure"
            )
            return True

        except Exception as e:
            self.log_test("IDE Layout Structure", False, f"Error: {str(e)}")
            return False

    def test_file_manager_integration(self) -> bool:
        """Test that FileManager is properly integrated"""
        try:
            file_manager = self.project_root / "services/FileManager.ts"
            if not file_manager.exists():
                self.log_test(
                    "FileManager Integration", False, "FileManager.ts not found"
                )
                return False

            with open(file_manager, "r") as f:
                content = f.read()

                # Check for required methods
                required_methods = [
                    "getAllFiles",
                    "setActiveFile",
                    "updateFile",
                    "saveFile",
                    "createFile",
                ]

                missing_methods = []
                for method in required_methods:
                    if method not in content:
                        missing_methods.append(method)

                if missing_methods:
                    self.log_test(
                        "FileManager Integration",
                        False,
                        f"Missing methods in FileManager: {missing_methods}",
                    )
                    return False

            self.log_test(
                "FileManager Integration", True, "FileManager properly integrated"
            )
            return True

        except Exception as e:
            self.log_test("FileManager Integration", False, f"Error: {str(e)}")
            return False

    def test_ai_service_integration(self) -> bool:
        """Test that AIService is properly integrated"""
        try:
            ai_service = self.project_root / "services/AIService.ts"
            if not ai_service.exists():
                self.log_test("AIService Integration", False, "AIService.ts not found")
                return False

            with open(ai_service, "r") as f:
                content = f.read()

                # Check for local Ollama models
                local_models = [
                    "deepseek-coder:1.3b",
                    "yi-coder:1.5b",
                    "qwen2.5-coder:3b",
                    "starcoder2:3b",
                    "mistral:7b-instruct-q4_0",
                ]

                missing_models = []
                for model in local_models:
                    if model not in content:
                        missing_models.append(model)

                if missing_models:
                    self.log_test(
                        "AIService Integration",
                        False,
                        f"Missing local models in AIService: {missing_models}",
                    )
                    return False

            self.log_test(
                "AIService Integration",
                True,
                "AIService properly integrated with local models",
            )
            return True

        except Exception as e:
            self.log_test("AIService Integration", False, f"Error: {str(e)}")
            return False

    def test_monaco_editor_integration(self) -> bool:
        """Test that Monaco Editor is properly integrated"""
        try:
            code_editor = self.project_root / "components/ide/CodeEditor.tsx"
            if not code_editor.exists():
                self.log_test(
                    "Monaco Editor Integration", False, "CodeEditor.tsx not found"
                )
                return False

            with open(code_editor, "r") as f:
                content = f.read()

                if "@monaco-editor/react" not in content:
                    self.log_test(
                        "Monaco Editor Integration", False, "Monaco Editor not imported"
                    )
                    return False

                if "Editor" not in content:
                    self.log_test(
                        "Monaco Editor Integration",
                        False,
                        "Monaco Editor component not used",
                    )
                    return False

            self.log_test(
                "Monaco Editor Integration", True, "Monaco Editor properly integrated"
            )
            return True

        except Exception as e:
            self.log_test("Monaco Editor Integration", False, f"Error: {str(e)}")
            return False

    def test_resizable_panels(self) -> bool:
        """Test that resizable panels are properly configured"""
        try:
            layout_file = self.project_root / "components/ide/IDELayout.tsx"
            if not layout_file.exists():
                self.log_test("Resizable Panels", False, "IDELayout.tsx not found")
                return False

            with open(layout_file, "r") as f:
                content = f.read()

                if "react-resizable-panels" not in content:
                    self.log_test(
                        "Resizable Panels", False, "react-resizable-panels not imported"
                    )
                    return False

                if "PanelResizeHandle" not in content:
                    self.log_test(
                        "Resizable Panels", False, "PanelResizeHandle not used"
                    )
                    return False

            self.log_test(
                "Resizable Panels", True, "Resizable panels properly configured"
            )
            return True

        except Exception as e:
            self.log_test("Resizable Panels", False, f"Error: {str(e)}")
            return False

    def run_all_tests(self) -> bool:
        """Run all IDE functionality tests"""
        print("🧪 Testing IDE Functionality...")
        print("=" * 50)

        tests = [
            self.test_ide_components_exist,
            self.test_ide_services_exist,
            self.test_ide_page_exists,
            self.test_navigation_integration,
            self.test_app_routing,
            self.test_index_redirect,
            self.test_ide_layout_structure,
            self.test_file_manager_integration,
            self.test_ai_service_integration,
            self.test_monaco_editor_integration,
            self.test_resizable_panels,
        ]

        for test in tests:
            test()

        print("\n" + "=" * 50)
        print(
            f"📊 Test Results: {len([r for r in self.test_results if r['success']])}/{len(self.test_results)} tests passed"
        )

        if self.overall_success:
            print("🎉 All IDE functionality tests passed!")
        else:
            print("❌ Some tests failed. Please review the issues above.")

        return self.overall_success


async def main():
    """Main test function"""
    tester = IDEFunctionalityTester()
    success = tester.run_all_tests()

    # Save test results
    results_file = Path(__file__).parent / "ide_functionality_test_results.json"
    with open(results_file, "w") as f:
        json.dump(
            {
                "timestamp": str(Path().cwd()),
                "overall_success": success,
                "test_results": tester.test_results,
            },
            f,
            indent=2,
        )

    print(f"\n📄 Test results saved to: {results_file}")

    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(asyncio.run(main()))
