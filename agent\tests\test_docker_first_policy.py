#!/usr/bin/env python3
"""
Tests for Docker-First Policy Enforcement
"""
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest

from agent.core.docker_first_policy import (
    DockerFirstPolicyEnforcer,
    DockerFirstViolationError,
    docker_first_required,
    scan_for_host_execution_attempts,
    validate_container_security,
)


@pytest.fixture
def policy_enforcer():
    return DockerFirstPolicyEnforcer()


def test_policy_enforcer_initialization(policy_enforcer):
    """Test policy enforcer initialization"""
    assert len(policy_enforcer.violations) == 0
    assert "development_tools" in policy_enforcer.allowed_host_operations
    assert "testing_frameworks" in policy_enforcer.allowed_host_operations


def test_website_operation_detection(policy_enforcer):
    """Test detection of website/service operations"""
    # Test operation type detection
    assert policy_enforcer._is_website_service_operation("website_creation", {})
    assert policy_enforcer._is_website_service_operation("web_server_start", {})
    assert policy_enforcer._is_website_service_operation("api_deployment", {})
    assert not policy_enforcer._is_website_service_operation("file_backup", {})
    
    # Test context detection
    context_with_website = {"files": ["index.html", "app.js"]}
    assert policy_enforcer._is_website_service_operation("deploy", context_with_website)
    
    context_without_website = {"files": ["backup.txt", "data.csv"]}
    assert not policy_enforcer._is_website_service_operation("deploy", context_without_website)


def test_website_file_detection(policy_enforcer):
    """Test detection of website-related files"""
    # Test website files
    assert policy_enforcer._is_website_file("index.html")
    assert policy_enforcer._is_website_file("app.js")
    assert policy_enforcer._is_website_file("styles.css")
    assert policy_enforcer._is_website_file("package.json")
    assert policy_enforcer._is_website_file("requirements.txt")
    assert policy_enforcer._is_website_file("server.py")
    
    # Test non-website files
    assert not policy_enforcer._is_website_file("data.txt")
    assert not policy_enforcer._is_website_file("backup.sql")
    assert not policy_enforcer._is_website_file("config.ini")


def test_site_container_manager_detection(policy_enforcer):
    """Test detection of SiteContainerManager usage"""
    context_with_scm = {
        "operation": "create_container",
        "uses_site_container_manager": True,
        "containerized": True
    }
    assert policy_enforcer._uses_site_container_manager(context_with_scm)
    
    context_without_scm = {
        "operation": "start_server",
        "host_execution": True
    }
    assert not policy_enforcer._uses_site_container_manager(context_without_scm)


def test_host_execution_detection(policy_enforcer):
    """Test detection of host execution attempts"""
    context_with_host_exec = {
        "command": "python -m http.server 8000",
        "execution_type": "host_execution"
    }
    assert policy_enforcer._attempts_host_execution(context_with_host_exec)
    
    context_without_host_exec = {
        "command": "docker run -p 8000:80 nginx",
        "execution_type": "containerized"
    }
    assert not policy_enforcer._attempts_host_execution(context_without_host_exec)


def test_valid_container_operation(policy_enforcer):
    """Test validation of valid container operations"""
    context = {
        "operation": "create_website",
        "uses_site_container_manager": True,
        "containerized": True
    }
    
    result = policy_enforcer.validate_container_operation("website_creation", context)
    assert result is True
    assert len(policy_enforcer.violations) == 0


def test_docker_first_violation(policy_enforcer):
    """Test Docker-First policy violation detection"""
    context = {
        "operation": "create_website",
        "files": ["index.html", "app.js"],
        "host_execution": True
    }
    
    result = policy_enforcer.validate_container_operation("website_creation", context)
    assert result is False
    assert len(policy_enforcer.violations) > 0
    assert any("docker_first_violation" in v["type"] for v in policy_enforcer.violations)


def test_host_execution_violation(policy_enforcer):
    """Test host execution violation detection"""
    context = {
        "operation": "start_server",
        "command": "flask run --host=0.0.0.0",
        "host_execution": True
    }
    
    result = policy_enforcer.validate_container_operation("server_start", context)
    assert result is False
    assert len(policy_enforcer.violations) > 0
    assert any("host_execution_violation" in v["type"] for v in policy_enforcer.violations)


def test_enforce_docker_first_strict_mode(policy_enforcer):
    """Test strict mode enforcement"""
    # Add a violation
    policy_enforcer.violations.append({
        "type": "docker_first_violation",
        "message": "Test violation"
    })
    
    with pytest.raises(DockerFirstViolationError):
        policy_enforcer.enforce_docker_first(strict=True)


def test_enforce_docker_first_warning_mode(policy_enforcer):
    """Test warning mode enforcement"""
    # Add a violation
    policy_enforcer.violations.append({
        "type": "docker_first_violation",
        "message": "Test violation"
    })
    
    result = policy_enforcer.enforce_docker_first(strict=False)
    assert result["violations_found"] == 1
    assert result["enforcement_level"] == "warning"
    assert "Logged violations as warnings" in result["actions_taken"]


def test_violation_report(policy_enforcer):
    """Test violation report generation"""
    # Add test violations
    policy_enforcer.violations.extend([
        {"type": "docker_first_violation", "message": "Violation 1"},
        {"type": "docker_first_violation", "message": "Violation 2"},
        {"type": "host_execution_violation", "message": "Violation 3"}
    ])
    
    report = policy_enforcer.get_violation_report()
    
    assert report["total_violations"] == 3
    assert report["violations_by_type"]["docker_first_violation"] == 2
    assert report["violations_by_type"]["host_execution_violation"] == 1
    assert len(report["recommendations"]) > 0


def test_docker_first_required_decorator():
    """Test docker_first_required decorator"""
    @docker_first_required("website_creation")
    def create_website(name, files):
        return f"Created website {name} with files {files}"
    
    # Test with valid context (should pass)
    with patch.object(DockerFirstPolicyEnforcer, 'validate_container_operation', return_value=True):
        result = create_website("test_site", ["index.html"])
        assert result == "Created website test_site with files ['index.html']"
    
    # Test with invalid context (should raise exception)
    with patch.object(DockerFirstPolicyEnforcer, 'validate_container_operation', return_value=False):
        with pytest.raises(DockerFirstViolationError):
            create_website("test_site", ["index.html"])


def test_validate_container_security():
    """Test container security validation"""
    # Test secure configuration
    secure_config = {
        "user": "1000:1000",
        "environment": {"NODE_ENV": "production"},
        "deploy": {
            "resources": {
                "limits": {"cpus": "1.0", "memory": "512M"}
            }
        },
        "healthcheck": {
            "test": ["CMD", "curl", "-f", "http://localhost/health"]
        },
        "restart": "unless-stopped"
    }
    
    result = validate_container_security(secure_config)
    assert result["compliant"] is True
    assert result["security_score"] == 100
    assert len(result["violations"]) == 0
    
    # Test insecure configuration
    insecure_config = {
        "user": "root",
        "environment": {"SECRET_KEY": "exposed_secret"},
        "restart": "always"
    }
    
    result = validate_container_security(insecure_config)
    assert result["compliant"] is False
    assert result["security_score"] < 100
    assert len(result["violations"]) > 0


def test_scan_for_host_execution_attempts():
    """Test scanning for host execution attempts"""
    with tempfile.TemporaryDirectory() as tmpdir:
        # Create test files with host execution patterns
        test_py_file = Path(tmpdir) / "test_server.py"
        test_py_file.write_text("""
import subprocess
subprocess.run(['python', '-m', 'http.server', '8000'])
""")
        
        test_sh_file = Path(tmpdir) / "start_server.sh"
        test_sh_file.write_text("""
#!/bin/bash
npm start
flask run --host=0.0.0.0
""")
        
        # Change to temp directory for scanning
        import os
        original_cwd = os.getcwd()
        try:
            os.chdir(tmpdir)
            result = scan_for_host_execution_attempts()
            
            assert result["violations_found"] > 0
            assert any(v["type"] == "potential_host_execution" for v in result["violations"])
            assert len(result["recommendations"]) > 0
            
        finally:
            os.chdir(original_cwd)


def test_policy_enforcer_with_allowed_operations(policy_enforcer):
    """Test policy enforcer with allowed host operations"""
    context = {
        "operation": "run_tests",
        "operation_type": "testing_frameworks",
        "host_execution": True
    }
    
    # This should not trigger violations for allowed operations
    result = policy_enforcer.validate_container_operation("testing", context)
    # Note: Current implementation doesn't check allowed operations,
    # but this test documents expected behavior


def test_policy_enforcer_error_handling(policy_enforcer):
    """Test error handling in policy enforcer"""
    # Test with malformed context
    malformed_context = None
    
    result = policy_enforcer.validate_container_operation("test_operation", malformed_context)
    # Should handle gracefully and return False for safety
    assert result is False


def test_container_security_edge_cases():
    """Test container security validation edge cases"""
    # Test empty configuration
    empty_config = {}
    result = validate_container_security(empty_config)
    assert result["compliant"] is False
    assert len(result["violations"]) > 0
    
    # Test configuration with None values
    none_config = {
        "user": None,
        "environment": None,
        "deploy": None
    }
    result = validate_container_security(none_config)
    assert result["compliant"] is False
    
    # Test configuration with empty environment
    empty_env_config = {
        "user": "1000:1000",
        "environment": {},
        "deploy": {"resources": {"limits": {"cpus": "1.0"}}},
        "healthcheck": {"test": ["CMD", "true"]},
        "restart": "unless-stopped"
    }
    result = validate_container_security(empty_env_config)
    assert result["compliant"] is True


def test_integration_with_site_container_manager():
    """Test integration with SiteContainerManager"""
    from agent.core.site_container_manager import SiteContainerManager
    
    # Test that SiteContainerManager has Docker-First enforcer
    manager = SiteContainerManager()
    assert hasattr(manager, 'docker_first_enforcer')
    assert isinstance(manager.docker_first_enforcer, DockerFirstPolicyEnforcer)
    
    # Test policy enforcement methods
    assert hasattr(manager, 'enforce_docker_first_policy')
    assert hasattr(manager, 'get_monitoring_status')
