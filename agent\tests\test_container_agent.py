import unittest
from unittest.mock import MagicMock, patch

from agent.core.agents.container_agent import ContainerAgent


class TestContainerAgent(unittest.TestCase):

    @patch("core.agents.container_agent.SiteContainerManager")
    def test_create_site_container(self, MockSiteContainerManager):
        # Arrange
        mock_manager = MockSiteContainerManager.return_value
        mock_manager.create_site_container.return_value = {
            "status": "success",
            "container_id": "12345",
        }

        agent = ContainerAgent()
        agent.site_container_manager = mock_manager

        # Act
        result = agent.site_container_manager.create_site_container("test_site")

        # Assert
        mock_manager.create_site_container.assert_called_once_with("test_site")
        self.assertEqual(result, {"status": "success", "container_id": "12345"})


if __name__ == "__main__":
    unittest.main()
