"""
Basic tests for ArchitectAgent
Simple tests to verify core functionality
"""

import asyncio

import pytest

from agent.core.agents.architect_agent import ArchitectAgent, TaskPriority, TaskStatus


class TestArchitectAgentBasic:
    """Basic tests for ArchitectAgent"""

    @pytest.mark.asyncio
    async def test_agent_initialization(self):
        """Test that the architect agent initializes properly"""
        agent = ArchitectAgent("config/architect_agent_config.json")

        # Check that all agents are initialized
        assert agent.frontend_agent is not None
        assert agent.backend_agent is not None
        assert agent.container_agent is not None
        assert agent.learning_agent is not None
        assert agent.shell_ops_agent is not None
        assert agent.security_agent is not None
        assert agent.monitoring_agent is not None

        # Check configuration
        assert agent.config is not None
        assert "task_management" in agent.config
        assert "agent_coordination" in agent.config

        # Cleanup
        await agent.shutdown()

    @pytest.mark.asyncio
    async def test_command_processing(self):
        """Test basic command processing"""
        agent = ArchitectAgent("config/architect_agent_config.json")

        try:
            # Test command processing
            result = await agent.process_user_command(
                "Create a simple portfolio website", priority=TaskPriority.MEDIUM
            )

            assert result["success"] is True
            assert "task_id" in result
            assert result["status"] in ["pending", "in_progress", "completed", "failed"]
            assert result["sub_tasks_count"] > 0

            # Wait a bit for task to start processing
            await asyncio.sleep(0.1)
        finally:
            # Cleanup
            await agent.shutdown()

    @pytest.mark.asyncio
    async def test_task_status_retrieval(self):
        """Test task status retrieval"""
        agent = ArchitectAgent("config/architect_agent_config.json")

        try:
            # Create a task
            result = await agent.process_user_command("Test task status")
            assert result["success"] is True
            task_id = result["task_id"]

            # Get task status
            status_result = await agent.get_task_status(task_id)
            assert status_result["success"] is True
            assert status_result["task_id"] == task_id
            assert "sub_tasks" in status_result

            # Wait a bit for task to start processing
            await asyncio.sleep(0.1)
        finally:
            # Cleanup
            await agent.shutdown()

    @pytest.mark.asyncio
    async def test_all_tasks_retrieval(self):
        """Test all tasks retrieval"""
        agent = ArchitectAgent("config/architect_agent_config.json")

        try:
            # Get all tasks
            all_tasks = await agent.get_all_tasks()
            assert all_tasks["success"] is True
            assert "active_tasks" in all_tasks
            assert "completed_tasks" in all_tasks
        finally:
            # Cleanup
            await agent.shutdown()

    @pytest.mark.asyncio
    async def test_task_cancellation(self):
        """Test task cancellation"""
        agent = ArchitectAgent("config/architect_agent_config.json")

        try:
            # Create a task
            result = await agent.process_user_command("Test cancellation")
            assert result["success"] is True
            task_id = result["task_id"]

            # Cancel the task
            cancel_result = await agent.cancel_task(task_id)
            assert cancel_result["success"] is True
            assert cancel_result["task_id"] == task_id

            # Wait a bit for cancellation to process
            await asyncio.sleep(0.1)
        finally:
            # Cleanup
            await agent.shutdown()

    @pytest.mark.asyncio
    async def test_error_handling(self):
        """Test error handling"""
        agent = ArchitectAgent("config/architect_agent_config.json")

        try:
            # Test with empty command
            result = await agent.process_user_command("")
            assert result["success"] is False
            assert "error" in result

            # Test nonexistent task
            result = await agent.get_task_status("nonexistent_task")
            assert result["success"] is False
            assert "error" in result
        finally:
            # Cleanup
            await agent.shutdown()

    @pytest.mark.asyncio
    async def test_task_priority_handling(self):
        """Test different task priority levels"""
        agent = ArchitectAgent("config/architect_agent_config.json")

        try:
            priorities = [
                TaskPriority.LOW,
                TaskPriority.MEDIUM,
                TaskPriority.HIGH,
                TaskPriority.CRITICAL,
            ]

            for priority in priorities:
                result = await agent.process_user_command(
                    f"Test {priority.value} priority task", priority=priority
                )
                assert result["success"] is True
                assert result["priority"] == priority.value

            # Wait a bit for tasks to start processing
            await asyncio.sleep(0.1)
        finally:
            # Cleanup
            await agent.shutdown()

    @pytest.mark.asyncio
    async def test_complex_command_parsing(self):
        """Test parsing of complex commands"""
        agent = ArchitectAgent("config/architect_agent_config.json")

        try:
            complex_commands = [
                "Build a React ecommerce site with Node.js backend",
                "Create a portfolio website with blog functionality",
                "Develop a social media platform with real-time chat",
            ]

            for command in complex_commands:
                result = await agent.process_user_command(command)
                assert result["success"] is True
                assert "task_id" in result
                assert result["sub_tasks_count"] > 0

            # Wait a bit for tasks to start processing
            await asyncio.sleep(0.1)
        finally:
            # Cleanup
            await agent.shutdown()

    @pytest.mark.asyncio
    async def test_task_id_uniqueness(self):
        """Test that task IDs are unique"""
        agent = ArchitectAgent("config/architect_agent_config.json")
        task_ids = set()

        try:
            for i in range(3):
                result = await agent.process_user_command(f"Task {i}")
                assert result["success"] is True
                task_id = result["task_id"]
                assert task_id not in task_ids
                task_ids.add(task_id)

            # Wait a bit for tasks to start processing
            await asyncio.sleep(0.1)
        finally:
            # Cleanup
            await agent.shutdown()

    @pytest.mark.asyncio
    async def test_comprehensive_workflow(self):
        """Test a comprehensive workflow"""
        agent = ArchitectAgent("config/architect_agent_config.json")

        try:
            # 1. Create a task
            result = await agent.process_user_command("Comprehensive workflow test")
            assert result["success"] is True
            task_id = result["task_id"]

            # 2. Check initial status
            status1 = await agent.get_task_status(task_id)
            assert status1["success"] is True

            # 3. Create another task
            result2 = await agent.process_user_command("Second comprehensive task")
            assert result2["success"] is True
            task_id2 = result2["task_id"]

            # 4. Check all tasks
            all_tasks = await agent.get_all_tasks()
            assert all_tasks["success"] is True

            # 5. Cancel one task
            cancel_result = await agent.cancel_task(task_id)
            assert cancel_result["success"] is True

            # 6. Check final status
            final_status = await agent.get_task_status(task_id2)
            assert final_status["success"] is True

            # Wait a bit for tasks to process
            await asyncio.sleep(0.1)
        finally:
            # Cleanup
            await agent.shutdown()
