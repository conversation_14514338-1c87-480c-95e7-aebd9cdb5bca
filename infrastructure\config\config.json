{"logging": {"level": "INFO", "file": "logs/agent.log"}, "security": {"enabled": true, "mfa": {"enabled": true}, "oauth2": {"enabled": true}, "audit": {"enabled": true}, "compliance": {"enabled": true}, "threat_detection": {"enabled": true}}, "llm_security": {"enabled": true, "models": {"threat_analysis": "deepseek-coder:1.3b", "code_review": "deepseek-coder:1.3b", "compliance": "mistral:7b-instruct-q4_0", "general": "qwen2.5-coder:3b", "fallback": "yi-coder:1.5b"}, "settings": {"max_tokens": 2048, "temperature": 0.3, "confidence_threshold": 0.7, "max_processing_time": 30.0}, "tasks": {"threat_analysis": {"enabled": true, "auto_trigger": true, "priority": "high"}, "anomaly_detection": {"enabled": true, "auto_trigger": true, "priority": "medium"}, "code_security_review": {"enabled": true, "auto_trigger": false, "priority": "high"}, "compliance_audit": {"enabled": true, "auto_trigger": false, "priority": "medium"}, "security_recommendations": {"enabled": true, "auto_trigger": false, "priority": "low"}}}, "automated_security": {"enabled": true, "llm_enabled": true, "auto_patch_enabled": true, "auto_block_enabled": true, "notification_enabled": true, "monitoring_interval": 60, "threat_threshold": 0.7, "vulnerability_threshold": 0.6, "features": {"threat_detection": true, "vulnerability_scanning": true, "system_monitoring": true, "code_analysis": true, "compliance_checking": true}, "auto_actions": {"block_malicious_ips": true, "enable_rate_limiting": true, "update_dependencies": true, "restart_services": false, "backup_data": true}}, "ai_optimization": {"enabled": true, "optimization": {"target_response_time_ms": 2000.0, "max_memory_usage_mb": 4096.0, "max_cpu_usage_percent": 80.0, "max_gpu_usage_percent": 90.0, "min_success_rate": 0.95, "max_error_rate": 0.05, "optimization_interval_minutes": 30, "auto_optimize": true, "optimization_strategies": ["model_switching", "parameter_tuning", "resource_allocation", "caching_optimization"]}, "monitoring": {"enabled": true, "metrics_collection_interval": 60, "performance_thresholds": {"response_time_ms": 3000.0, "memory_usage_percent": 85.0, "cpu_usage_percent": 90.0, "error_rate_percent": 5.0}}}, "learning": {"enabled": true, "data_dir": "data/learning", "db_path": "data/learning/learning.db", "learning_interval": 300, "pattern_analysis_enabled": true, "user_preference_learning": true, "performance_insight_generation": true, "best_practices_enforcement": true, "auto_start": true, "background_processing": true, "logging": {"level": "INFO", "file": "logs/learning.log"}, "features": {"code_pattern_learning": true, "user_interaction_learning": true, "performance_learning": true, "best_practices_learning": true, "recommendation_generation": true}, "thresholds": {"pattern_confidence": 0.7, "preference_confidence": 0.8, "insight_confidence": 0.6, "recommendation_confidence": 0.5}}, "notifications": {"enabled": true, "channels": {"email": true, "webhook": false, "slack": false, "discord": false, "telegram": false, "console": true, "dashboard": true}, "email": {"smtp_server": "smtp.gmail.com", "smtp_port": 587, "username": "<EMAIL>", "password": "your-app-password", "from_email": "<EMAIL>"}, "webhook": {"url": "https://your-webhook-url.com/security"}, "slack": {"webhook_url": "https://hooks.slack.com/services/YOUR/SLACK/WEBHOOK"}, "discord": {"webhook_url": "https://discord.com/api/webhooks/YOUR/DISCORD/WEBHOOK"}, "telegram": {"bot_token": "your-bot-token", "chat_id": "your-chat-id"}, "settings": {"quiet_hours_start": "22:00", "quiet_hours_end": "08:00", "digest_enabled": true, "digest_interval": 3600, "urgent_override_quiet_hours": true}}, "database": {"url": "sqlite:///database/ai_coding_agent.db"}, "models": {"default": "deepseek-coder:1.3b"}, "api": {"host": "localhost", "port": 8000}}