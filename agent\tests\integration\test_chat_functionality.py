#!/usr/bin/env python3
"""
Test Chat Functionality Script
Comprehensive testing of the enhanced chat API endpoints.
"""

import json
import time
from datetime import datetime

import requests


def test_chat_endpoints():
    """Test all chat-related endpoints"""
    base_url = "http://127.0.0.1:8000"

    print("🧪 TESTING ENHANCED CHAT FUNCTIONALITY")
    print("=" * 60)

    # Test 1: Get available models
    print("\n1. Testing Models Endpoint...")
    try:
        response = requests.get(f"{base_url}/api/v1/chat/models", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Models endpoint working")
            print(f"   📊 Available models: {data.get('count', 0)}")
            print(f"   🤖 Models: {', '.join(data.get('models', [])[:5])}...")
        else:
            print(f"   ❌ Models endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Models endpoint error: {e}")

    # Test 2: Test chat endpoint (without auth - should return 401)
    print("\n2. Testing Chat Endpoint (No Auth)...")
    try:
        response = requests.post(
            f"{base_url}/api/v1/chat",
            json={
                "prompt": "Hello, can you help me with coding?",
                "context": {"file": "test.py"},
                "intent": "code_generation",
                "history": [],
            },
            timeout=10,
        )
        if response.status_code == 401:
            print("   ✅ Chat endpoint working (Authentication required as expected)")
        else:
            print(f"   ⚠️  Chat endpoint returned: {response.status_code}")
            if response.status_code == 200:
                data = response.json()
                print(
                    f"   📝 Response: {data.get('response', {}).get('content', 'No content')[:100]}..."
                )
    except Exception as e:
        print(f"   ❌ Chat endpoint error: {e}")

    # Test 3: Test chat test endpoint
    print("\n3. Testing Chat Test Endpoint...")
    try:
        response = requests.post(
            f"{base_url}/api/v1/chat/test",
            json={"prompt": "Hello, this is a test message"},
            timeout=10,
        )
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Chat test endpoint working")
            print(
                f"   📝 Response: {data.get('response', {}).get('content', 'No content')}"
            )
            print(f"   🧪 Test flag: {data.get('test', False)}")
        else:
            print(f"   ❌ Chat test endpoint failed: {response.status_code}")
            print(f"   📝 Error: {response.text}")
    except Exception as e:
        print(f"   ❌ Chat test endpoint error: {e}")

    # Test 4: Test AI models health
    print("\n4. Testing AI Models Health...")
    try:
        response = requests.get(f"{base_url}/api/v1/ai/models/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("   ✅ AI models health endpoint working")
            models = data.get("models", {})
            healthy_models = [name for name, status in models.items() if status]
            print(f"   🟢 Healthy models: {len(healthy_models)}")
            print(f"   📊 Total models: {len(models)}")
        else:
            print(f"   ❌ AI models health failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ AI models health error: {e}")

    # Test 5: Test root endpoint for chat documentation
    print("\n5. Testing Root Endpoint...")
    try:
        response = requests.get(f"{base_url}/", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("   ✅ Root endpoint working")
            endpoints = data.get("endpoints", {})
            if "chat" in endpoints:
                print("   📚 Chat endpoints documented in root")
                if isinstance(endpoints["chat"], dict):
                    print(f"   🔗 Chat endpoints: {list(endpoints['chat'].keys())}")
                else:
                    print(f"   🔗 Chat endpoint: {endpoints['chat']}")
            else:
                print("   ⚠️  Chat endpoints not documented in root")
        else:
            print(f"   ❌ Root endpoint failed: {response.status_code}")
    except Exception as e:
        print(f"   ❌ Root endpoint error: {e}")

    print("\n" + "=" * 60)
    print("🎉 Chat functionality testing completed!")


if __name__ == "__main__":
    test_chat_endpoints()
