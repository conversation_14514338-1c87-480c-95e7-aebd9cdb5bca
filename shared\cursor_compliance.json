{"last_check": "2025-08-08T04:05:59.821063", "violations": ["Missing requirements.txt file"], "compliance_score": 81.81818181818183, "rule_checks": {"file_organization": {"status": "passed", "violations": [], "warnings": ["Script file validation_service.py should be in scripts/ directory"]}, "todo_completion": {"status": "passed", "violations": [], "warnings": [], "incomplete_todos": []}, "test_success": {"status": "passed", "violations": [], "warnings": ["Could not run tests: argument of type 'NoneType' is not iterable"], "test_results": {}}, "dependency_management": {"status": "failed", "violations": ["requirements.txt not found in config/ directory"], "warnings": []}, "cli_api_compliance": {"status": "failed", "violations": ["CLI directory not found", "API directory not found"], "warnings": []}, "ai_model_compliance": {"status": "passed", "violations": [], "warnings": ["Cloud model usage detected in projects\\content\\cms_content_manager.py (future mode will allow this)", "Cloud model usage detected in projects\\content\\cms_content_manager.py (future mode will allow this)", "Cloud model usage detected in agent\\api\\main.py (future mode will allow this)", "Cloud model usage detected in agent\\api\\main.py (future mode will allow this)", "Cloud model usage detected in agent\\api\\main.py (future mode will allow this)", "Cloud model usage detected in agent\\core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in agent\\core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in agent\\core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in agent\\core\\cursor_rules_enforcer.py (future mode will allow this)", "Cloud model usage detected in agent\\fine_tuning\\evaluator.py (future mode will allow this)", "Cloud model usage detected in agent\\fine_tuning\\pipeline.py (future mode will allow this)", "Cloud model usage detected in agent\\fine_tuning\\trainer.py (future mode will allow this)", "Cloud model usage detected in agent\\scripts\\train_model.py (future mode will allow this)", "Cloud model usage detected in agent\\tests\\test_fine_tuning.py (future mode will allow this)", "Cloud model usage detected in agent\\tests\\test_ollama_integration.py (future mode will allow this)", "Cloud model usage detected in agent\\tests\\test_ollama_integration.py (future mode will allow this)", "Cloud model usage detected in agent\\tests\\test_pippy.py (future mode will allow this)"], "current_mode": "local_only"}, "git_workflow": {"status": "passed", "violations": [], "warnings": ["Repository should be in G:\\AICodingAgent, found in F:/NasShare/AICodingAgent", "There are uncommitted changes"]}, "security_compliance": {"status": "passed", "violations": [], "warnings": ["Potential hardcoded secret in agent\\api\\hf_api.py"]}, "file_cleanup": {"status": "passed", "violations": [], "warnings": ["Found 17 potential duplicate files", "Found 1 potentially obsolete files"], "duplicates": 17, "obsolete_files": 1}, "mock_data_cleanup": {"status": "passed", "violations": [], "warnings": ["Found 282 potential mock data files"], "mock_files": 282}, "virtual_environment": {"status": "passed", "violations": [], "warnings": []}}, "warnings": []}