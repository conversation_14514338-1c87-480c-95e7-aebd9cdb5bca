#!/usr/bin/env python3
"""
Persona System Demo
Demonstrates the complete persona-enhanced error handling and user interaction system
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from agent.core.persona_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentType, TaskCategory
from agent.core.site_container_manager import SiteContainerManager


async def demo_persona_enhanced_error_handling():
    """Demonstrate persona-enhanced error handling"""
    
    print("🎭 Persona-Enhanced Error Handling Demo")
    print("=" * 60)
    print("See how different AI specialists handle various types of errors\n")
    
    container_manager = SiteContainerManager()
    
    # Scenario 1: Frontend Error - Handled by Frontend Specialist
    print("📋 Scenario 1: Frontend JavaScript Error")
    print("-" * 50)
    
    frontend_error = {
        "title": "React Component Rendering Error",
        "description": "Cannot read property 'map' of undefined in ProductList component",
        "category": "frontend",
        "severity": "medium"
    }
    
    print(f"🔍 Error Detected: {frontend_error['title']}")
    print(f"   Category: {frontend_error['category']}")
    print(f"   Severity: {frontend_error['severity']}")
    
    result = await container_manager.handle_error_with_user_escalation(
        site_name="demo-ecommerce",
        error_details=frontend_error
    )
    
    if result.get("success"):
        print(f"\n🎨 Frontend Specialist: '{result.get('agent_message', 'Fixed successfully!')}'")
        print(f"   Agent Used: {result.get('agent_used', 'unknown')}")
        print(f"   Fix Applied: {result.get('fix_applied', 'unknown')}")
    elif result.get("requires_user_input"):
        print(f"\n🎨 Frontend Specialist needs your help:")
        print(f"   Agent: {result.get('agent_type', 'unknown')}")
        print(f"   Message preview: {result.get('user_message', '')[:100]}...")
    
    # Scenario 2: Database Error - Handled by Database Specialist
    print(f"\n📋 Scenario 2: Database Performance Issue")
    print("-" * 50)
    
    database_error = {
        "title": "Slow Query Performance",
        "description": "Product search queries taking over 3 seconds to complete",
        "category": "database",
        "severity": "high"
    }
    
    print(f"🔍 Error Detected: {database_error['title']}")
    print(f"   Category: {database_error['category']}")
    print(f"   Severity: {database_error['severity']}")
    
    result = await container_manager.handle_error_with_user_escalation(
        site_name="demo-ecommerce",
        error_details=database_error
    )
    
    if result.get("success"):
        print(f"\n🗄️ Database Specialist: '{result.get('agent_message', 'Optimized successfully!')}'")
        print(f"   Agent Used: {result.get('agent_used', 'unknown')}")
        print(f"   Fix Applied: {result.get('fix_applied', 'unknown')}")
    elif result.get("requires_user_input"):
        print(f"\n🗄️ Database Specialist needs your help:")
        print(f"   Agent: {result.get('agent_type', 'unknown')}")
        print(f"   Message preview: {result.get('user_message', '')[:100]}...")
    
    # Scenario 3: Security Error - Handled by Security Specialist
    print(f"\n📋 Scenario 3: Security Vulnerability")
    print("-" * 50)
    
    security_error = {
        "title": "Potential SQL Injection Vulnerability",
        "description": "User input not properly sanitized in search functionality",
        "category": "security",
        "severity": "critical"
    }
    
    print(f"🔍 Error Detected: {security_error['title']}")
    print(f"   Category: {security_error['category']}")
    print(f"   Severity: {security_error['severity']}")
    
    result = await container_manager.handle_error_with_user_escalation(
        site_name="demo-ecommerce",
        error_details=security_error
    )
    
    if result.get("success"):
        print(f"\n🛡️ Security Specialist: '{result.get('agent_message', 'Secured successfully!')}'")
        print(f"   Agent Used: {result.get('agent_used', 'unknown')}")
        print(f"   Fix Applied: {result.get('fix_applied', 'unknown')}")
    elif result.get("requires_user_input"):
        print(f"\n🛡️ Security Specialist needs your help:")
        print(f"   Agent: {result.get('agent_type', 'unknown')}")
        print(f"   Message preview: {result.get('user_message', '')[:100]}...")


async def demo_agent_personalities():
    """Demonstrate different agent personalities and communication styles"""
    
    print(f"\n🎭 Agent Personality Showcase")
    print("=" * 40)
    
    persona_manager = PersonaManager()
    
    agents_to_demo = [
        (AgentType.ARCHITECT, "I coordinate your entire project"),
        (AgentType.FRONTEND, "I make your site beautiful and user-friendly"),
        (AgentType.BACKEND, "I optimize your server performance"),
        (AgentType.SECURITY, "I protect your users and data"),
        (AgentType.DATABASE, "I manage your data efficiently")
    ]
    
    for agent_type, description in agents_to_demo:
        persona = persona_manager.personas.get(agent_type)
        if persona:
            print(f"\n🤖 {persona.name}")
            print(f"   Role: {description}")
            print(f"   Personality: {', '.join(persona.personality_traits[:3])}")
            print(f"   Communication: {persona.conversation_style[:80]}...")
            print(f"   Expertise: {', '.join(persona.expertise_areas[:3])}")
            
            # Show communication patterns
            if persona.communication_patterns:
                pattern_key = list(persona.communication_patterns.keys())[0]
                pattern_value = persona.communication_patterns[pattern_key]
                print(f"   Example: \"{pattern_value}\"")


async def demo_intelligent_routing():
    """Demonstrate intelligent task routing to appropriate agents"""
    
    print(f"\n🧠 Intelligent Task Routing Demo")
    print("=" * 40)
    
    persona_manager = PersonaManager()
    
    routing_scenarios = [
        ("User wants to improve site design", TaskCategory.FRONTEND_DEV, "Frontend Specialist"),
        ("Database is running slowly", TaskCategory.DATABASE_OPS, "Database Specialist"),
        ("Security vulnerability found", TaskCategory.SECURITY_REVIEW, "Security Specialist"),
        ("API endpoints need optimization", TaskCategory.BACKEND_DEV, "Backend Specialist"),
        ("General project planning", TaskCategory.PLANNING, "Architect"),
        ("User has a question", TaskCategory.CONVERSATION, "Architect")
    ]
    
    for scenario, task_category, expected_agent in routing_scenarios:
        actual_agent = persona_manager.get_agent_for_task(task_category)
        agent_name = persona_manager.personas[actual_agent].name
        
        print(f"\n📝 Scenario: {scenario}")
        print(f"   Task Category: {task_category.value}")
        print(f"   Routed to: {agent_name}")
        print(f"   Model: {persona_manager.get_model_for_agent_task(actual_agent)}")
        
        # Show transition message
        if actual_agent != AgentType.ARCHITECT:
            transition = persona_manager._generate_transition_message(
                AgentType.ARCHITECT, actual_agent, persona_manager.personas[actual_agent]
            )
            if transition:
                print(f"   Transition: \"{transition}\"")


async def demo_adaptive_communication():
    """Demonstrate adaptive communication based on user expertise"""
    
    print(f"\n🎯 Adaptive Communication Demo")
    print("=" * 40)
    
    persona_manager = PersonaManager()
    
    expertise_levels = ["beginner", "intermediate", "advanced"]
    
    for level in expertise_levels:
        persona_manager.update_user_expertise_level(level)
        style = persona_manager.get_adaptive_explanation_style(AgentType.ARCHITECT)
        
        print(f"\n👤 User Expertise Level: {level.title()}")
        print(f"   Use analogies: {style.get('use_analogies', 'unknown')}")
        print(f"   Explain jargon: {style.get('explain_jargon', 'unknown')}")
        print(f"   Step-by-step: {style.get('step_by_step', 'unknown')}")
        print(f"   Encourage questions: {style.get('encourage_questions', 'unknown')}")
        print(f"   Tone: {style.get('tone', 'unknown')}")


async def demo_conversation_continuity():
    """Demonstrate conversation continuity across agent switches"""
    
    print(f"\n🔄 Conversation Continuity Demo")
    print("=" * 40)
    
    persona_manager = PersonaManager()
    
    print("🤖 Architect: \"I'll help you build an amazing e-commerce site!\"")
    print("👤 User: \"Great! I want it to look modern and professional.\"")
    
    # Switch to frontend specialist
    switch_result = persona_manager.switch_agent_context(
        AgentType.FRONTEND, TaskCategory.FRONTEND_DEV
    )
    
    print(f"\n🎨 {switch_result['transition_message']}")
    print("🎨 Frontend Specialist: \"Perfect! I'll create a stunning, modern design that your customers will love. Let's focus on clean layouts, beautiful typography, and smooth animations.\"")
    
    print("\n👤 User: \"Sounds great! What about the backend performance?\"")
    
    # Switch to backend specialist
    switch_result = persona_manager.switch_agent_context(
        AgentType.BACKEND, TaskCategory.BACKEND_DEV
    )
    
    print(f"\n⚙️ {switch_result['transition_message']}")
    print("⚙️ Backend Specialist: \"Excellent question! I'll ensure your e-commerce platform is lightning-fast and can handle thousands of customers. We'll optimize database queries, implement caching, and set up efficient API endpoints.\"")
    
    print("\n👤 User: \"What about security for customer data?\"")
    
    # Switch to security specialist
    switch_result = persona_manager.switch_agent_context(
        AgentType.SECURITY, TaskCategory.SECURITY_REVIEW
    )
    
    print(f"\n🛡️ {switch_result['transition_message']}")
    print("🛡️ Security Specialist: \"Security is absolutely critical for e-commerce! I'll implement enterprise-grade protection: encrypted customer data, secure payment processing, protection against attacks, and full compliance with privacy regulations.\"")
    
    # Back to architect
    switch_result = persona_manager.switch_agent_context(
        AgentType.ARCHITECT, TaskCategory.CONVERSATION
    )
    
    print(f"\n🏗️ {switch_result['transition_message']}")
    print("🏗️ Architect: \"Perfect! Our specialists have covered design, performance, and security. I'll coordinate everything to deliver your complete e-commerce solution. You'll have a beautiful, fast, and secure online store!\"")


async def main():
    """Run the complete persona system demo"""
    
    print("🎭 AI Agent Persona System - Complete Demo")
    print("=" * 60)
    print("Experience how specialized AI agents work together with natural conversations\n")
    
    # Demo 1: Persona-enhanced error handling
    await demo_persona_enhanced_error_handling()
    
    # Demo 2: Agent personalities
    await demo_agent_personalities()
    
    # Demo 3: Intelligent routing
    await demo_intelligent_routing()
    
    # Demo 4: Adaptive communication
    await demo_adaptive_communication()
    
    # Demo 5: Conversation continuity
    await demo_conversation_continuity()
    
    # Summary
    print(f"\n🎉 Demo Summary")
    print("=" * 20)
    print(f"✅ Specialized AI agents for different expertise areas")
    print(f"✅ Natural, conversational communication maintained")
    print(f"✅ Intelligent routing based on task requirements")
    print(f"✅ Adaptive explanations based on user expertise")
    print(f"✅ Seamless conversation continuity across agent switches")
    print(f"✅ Persona-specific error handling and messaging")
    print(f"✅ Consistent user experience regardless of underlying LLM")
    
    print(f"\n🎯 Key Benefits:")
    print(f"   🤝 Feel like working with a team of expert colleagues")
    print(f"   🧠 Get specialized knowledge for each type of problem")
    print(f"   💬 Enjoy natural conversations that adapt to your level")
    print(f"   🔄 Experience seamless coordination between specialists")
    print(f"   🚀 Benefit from the best AI model for each specific task")
    print(f"   🎭 Interact with consistent personalities you can trust")
    
    return True


if __name__ == "__main__":
    # Run the demo
    result = asyncio.run(main())
    
    print(f"\n{'🎉 Persona system demo completed successfully!' if result else '❌ Demo failed'}")
    exit(0 if result else 1)
