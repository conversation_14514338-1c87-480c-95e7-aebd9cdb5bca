// Test website JavaScript
document.addEventListener('DOMContentLoaded', function() {
    console.log('Test website loaded successfully');

    // Add some interactivity
    const status = document.querySelector('.status');
    if (status) {
        status.addEventListener('click', function() {
            this.style.background = '#d1ecf1';
            this.style.borderColor = '#bee5eb';
            this.style.color = '#0c5460';
        });
    }

    // Display current time
    const timeElement = document.createElement('p');
    timeElement.textContent = 'Page loaded at: ' + new Date().toLocaleString();
    timeElement.style.textAlign = 'center';
    timeElement.style.color = '#6c757d';
    timeElement.style.fontSize = '14px';

    document.querySelector('.container').appendChild(timeElement);
});