#!/usr/bin/env python3
"""
Tests for Database Error Parser & Remediation System
"""

import pytest
from unittest.mock import Mock, patch
from sqlalchemy.exc import IntegrityError, OperationalError, ProgrammingError
import psycopg2

from agent.core.db.error_parser import (
    DatabaseErrorParser,
    ErrorCategory,
    RemediationAction,
    ErrorAnalysis
)


class TestDatabaseErrorParser:
    """Test cases for DatabaseErrorParser"""

    def setup_method(self):
        """Set up test fixtures"""
        self.parser = DatabaseErrorParser()

    def test_postgresql_table_missing(self):
        """Test PostgreSQL table missing error"""
        error_msg = 'relation "users" does not exist'
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.TABLE_MISSING
        assert analysis.confidence >= 0.9
        assert analysis.extracted_info["table_name"] == "users"
        assert analysis.auto_fixable is True
        assert len(analysis.remediation_steps) > 0
        assert analysis.remediation_steps[0].action == RemediationAction.CREATE_TABLE

    def test_sqlite_table_missing(self):
        """Test SQLite table missing error"""
        error_msg = "no such table: products"
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.TABLE_MISSING
        assert analysis.confidence >= 0.9
        assert analysis.extracted_info["table_name"] == "products"
        assert analysis.auto_fixable is True

    def test_mysql_table_missing(self):
        """Test MySQL table missing error"""
        error_msg = "Table 'mydb.orders' doesn't exist"
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.TABLE_MISSING
        assert analysis.confidence >= 0.9
        assert analysis.extracted_info["table_name"] == "mydb.orders"

    def test_postgresql_column_missing(self):
        """Test PostgreSQL column missing error"""
        error_msg = 'column "email" of relation "users" does not exist'
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.COLUMN_MISSING
        assert analysis.confidence >= 0.9
        assert analysis.extracted_info["column_name"] == "email"
        assert analysis.extracted_info["table_name"] == "users"
        assert analysis.auto_fixable is True
        assert analysis.remediation_steps[0].action == RemediationAction.ADD_COLUMN

    def test_sqlite_column_missing(self):
        """Test SQLite column missing error"""
        error_msg = "no such column: users.phone"
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.COLUMN_MISSING
        assert analysis.confidence >= 0.9
        assert analysis.extracted_info["table_name"] == "users"
        assert analysis.extracted_info["column_name"] == "phone"

    def test_postgresql_unique_constraint_violation(self):
        """Test PostgreSQL unique constraint violation"""
        error_msg = 'duplicate key value violates unique constraint "users_email_key"'
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.CONSTRAINT_VIOLATION
        assert analysis.confidence >= 0.8
        assert analysis.extracted_info["constraint_name"] == "users_email_key"
        assert analysis.auto_fixable is False  # Requires manual intervention

    def test_sqlite_unique_constraint_violation(self):
        """Test SQLite unique constraint violation"""
        error_msg = "UNIQUE constraint failed: users.email"
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.CONSTRAINT_VIOLATION
        assert analysis.confidence >= 0.8
        assert analysis.extracted_info["column_info"] == "users.email"

    def test_postgresql_not_null_constraint(self):
        """Test PostgreSQL NOT NULL constraint violation"""
        error_msg = 'null value in column "name" violates not-null constraint'
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.NOT_NULL_CONSTRAINT
        assert analysis.confidence >= 0.9
        assert analysis.extracted_info["column_name"] == "name"
        assert len(analysis.remediation_steps) >= 2  # Update data or modify column

    def test_sqlite_not_null_constraint(self):
        """Test SQLite NOT NULL constraint violation"""
        error_msg = "NOT NULL constraint failed: users.name"
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.NOT_NULL_CONSTRAINT
        assert analysis.confidence >= 0.9
        assert analysis.extracted_info["column_info"] == "users.name"

    def test_postgresql_foreign_key_violation(self):
        """Test PostgreSQL foreign key constraint violation"""
        error_msg = 'insert or update on table "orders" violates foreign key constraint "orders_user_id_fkey"'
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.FOREIGN_KEY_ERROR
        assert analysis.confidence >= 0.8
        assert analysis.extracted_info["table_name"] == "orders"
        assert analysis.extracted_info["constraint_name"] == "orders_user_id_fkey"

    def test_connection_error(self):
        """Test database connection error"""
        error_msg = "could not connect to server: Connection refused"
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.CONNECTION_ERROR
        assert analysis.confidence >= 0.8
        assert analysis.remediation_steps[0].action == RemediationAction.RESTART_CONNECTION

    def test_sqlite_database_locked(self):
        """Test SQLite database locked error"""
        error_msg = "database is locked"
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.CONNECTION_ERROR
        assert analysis.confidence >= 0.9

    def test_permission_error(self):
        """Test database permission error"""
        error_msg = 'permission denied for relation "sensitive_data"'
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.PERMISSION_ERROR
        assert analysis.confidence >= 0.9
        assert analysis.extracted_info["table_name"] == "sensitive_data"
        assert analysis.remediation_steps[0].action == RemediationAction.CHECK_PERMISSIONS

    def test_data_type_error(self):
        """Test data type conversion error"""
        error_msg = 'invalid input syntax for type integer: "not_a_number"'
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.DATA_TYPE_ERROR
        assert analysis.confidence >= 0.8
        assert analysis.extracted_info["data_type"] == "integer"
        assert analysis.extracted_info["value"] == "not_a_number"

    def test_unknown_error(self):
        """Test unknown/unrecognized error"""
        error_msg = "Some completely unknown database error"
        analysis = self.parser.parse_error(error_msg)

        assert analysis.category == ErrorCategory.UNKNOWN
        assert analysis.confidence == 0.0
        assert analysis.auto_fixable is False
        assert analysis.remediation_steps[0].action == RemediationAction.MANUAL_INTERVENTION

    def test_error_with_context(self):
        """Test error parsing with additional context"""
        error_msg = 'relation "users" does not exist'
        context = {"operation": "SELECT", "schema": "public"}
        analysis = self.parser.parse_error(error_msg, context)

        assert analysis.category == ErrorCategory.TABLE_MISSING
        assert analysis.extracted_info["table_name"] == "users"
        assert analysis.extracted_info["operation"] == "SELECT"
        assert analysis.extracted_info["schema"] == "public"

    def test_sqlalchemy_exception_parsing(self):
        """Test parsing SQLAlchemy exceptions"""
        # Mock SQLAlchemy OperationalError
        mock_error = OperationalError(
            statement="SELECT * FROM users",
            params={},
            orig=Mock(args=('relation "users" does not exist',))
        )

        analysis = self.parser.parse_error(mock_error)
        assert analysis.category == ErrorCategory.TABLE_MISSING

    def test_psycopg2_exception_parsing(self):
        """Test parsing psycopg2 exceptions"""
        # Create a mock psycopg2 error that behaves like a real exception
        mock_error = Mock()
        mock_error.__str__ = Mock(return_value='duplicate key value violates unique constraint "users_email_key"')
        mock_error.args = ('duplicate key value violates unique constraint "users_email_key"',)
        # Ensure no 'orig' attribute to avoid SQLAlchemy path
        del mock_error.orig

        analysis = self.parser.parse_error(mock_error)
        assert analysis.category == ErrorCategory.CONSTRAINT_VIOLATION

    def test_migration_sql_generation(self):
        """Test migration SQL generation for auto-fixable errors"""
        error_msg = 'relation "users" does not exist'
        analysis = self.parser.parse_error(error_msg)

        migration_sql = self.parser.suggest_migration_sql(analysis)
        assert migration_sql is not None
        assert "CREATE TABLE users" in migration_sql
        assert "id SERIAL PRIMARY KEY" in migration_sql

    def test_migration_sql_not_generated_for_non_fixable(self):
        """Test that migration SQL is not generated for non-auto-fixable errors"""
        error_msg = 'duplicate key value violates unique constraint "users_email_key"'
        analysis = self.parser.parse_error(error_msg)

        migration_sql = self.parser.suggest_migration_sql(analysis)
        assert migration_sql is None

    def test_remediation_step_customization(self):
        """Test that remediation steps are properly customized with extracted info"""
        error_msg = 'column "email" of relation "users" does not exist'
        analysis = self.parser.parse_error(error_msg)

        step = analysis.remediation_steps[0]
        assert "email" in step.description
        assert "users" in step.description
        assert step.sql_template is not None
        assert "{table_name}" not in step.sql_template  # Should be replaced
        assert "{column_name}" not in step.sql_template  # Should be replaced

    def test_risk_level_assessment(self):
        """Test risk level assessment for different error types"""
        # Low risk: Adding column
        error_msg = 'column "email" of relation "users" does not exist'
        analysis = self.parser.parse_error(error_msg)
        assert analysis.remediation_steps[0].risk_level == "low"

        # High risk: Constraint violation
        error_msg = 'duplicate key value violates unique constraint "users_email_key"'
        analysis = self.parser.parse_error(error_msg)
        assert any(step.risk_level == "high" for step in analysis.remediation_steps)

    def test_priority_ordering(self):
        """Test that remediation steps are properly prioritized"""
        error_msg = 'null value in column "name" violates not-null constraint'
        analysis = self.parser.parse_error(error_msg)

        # Should have multiple steps with different priorities
        priorities = [step.priority for step in analysis.remediation_steps]
        assert len(priorities) >= 2
        assert min(priorities) == 1  # Should have at least one high-priority step

    def test_rollback_sql_generation(self):
        """Test rollback SQL generation for reversible operations"""
        error_msg = 'column "email" of relation "users" does not exist'
        analysis = self.parser.parse_error(error_msg)

        step = analysis.remediation_steps[0]
        assert step.rollback_sql is not None
        assert "DROP COLUMN" in step.rollback_sql
        assert "email" in step.rollback_sql


class TestErrorParserIntegration:
    """Integration tests for error parser with real database scenarios"""

    def setup_method(self):
        """Set up test fixtures"""
        self.parser = DatabaseErrorParser()

    def test_common_migration_scenarios(self):
        """Test common database migration error scenarios"""
        scenarios = [
            {
                "error": 'relation "new_table" does not exist',
                "expected_category": ErrorCategory.TABLE_MISSING,
                "expected_fixable": True
            },
            {
                "error": 'column "new_column" of relation "existing_table" does not exist',
                "expected_category": ErrorCategory.COLUMN_MISSING,
                "expected_fixable": True
            },
            {
                "error": 'duplicate key value violates unique constraint "idx_unique"',
                "expected_category": ErrorCategory.CONSTRAINT_VIOLATION,
                "expected_fixable": False
            }
        ]

        for scenario in scenarios:
            analysis = self.parser.parse_error(scenario["error"])
            assert analysis.category == scenario["expected_category"]
            assert analysis.auto_fixable == scenario["expected_fixable"]

    def test_multi_database_compatibility(self):
        """Test compatibility across different database engines"""
        table_missing_errors = [
            ('relation "users" does not exist', "postgresql"),
            ("no such table: users", "sqlite"),
            ("Table 'db.users' doesn't exist", "mysql")
        ]

        for error_msg, engine in table_missing_errors:
            analysis = self.parser.parse_error(error_msg)
            assert analysis.category == ErrorCategory.TABLE_MISSING
            assert analysis.confidence >= 0.9
            assert "users" in analysis.extracted_info.get("table_name", "")


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
