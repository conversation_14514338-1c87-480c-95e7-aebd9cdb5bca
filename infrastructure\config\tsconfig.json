{"compilerOptions": {"target": "es5", "lib": ["dom", "dom.iterable", "es6"], "allowJs": true, "skipLibCheck": true, "strict": true, "forceConsistentCasingInFileNames": true, "noEmit": true, "esModuleInterop": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "isolatedModules": true, "jsx": "preserve", "incremental": true, "plugins": [{"name": "next"}], "baseUrl": ".", "paths": {"@/*": ["./*"], "@/components/*": ["./components/*"], "@/pages/*": ["./pages/*"], "@/styles/*": ["./styles/*"], "@/lib/*": ["./lib/*"], "@/utils/*": ["./utils/*"], "@/types/*": ["./types/*"], "@/services/*": ["./services/*"], "@/store/*": ["./store/*"], "@/contexts/*": ["./contexts/*"], "@/hooks/*": ["./hooks/*"]}}, "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", "**/*.js", "**/*.jsx"], "exclude": ["node_modules", ".next", "coverage", "test_dist", ".venv"]}