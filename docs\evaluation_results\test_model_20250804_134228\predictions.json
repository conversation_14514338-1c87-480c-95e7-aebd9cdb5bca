[{"id": 0, "prompt": "Write a Python function to sort a list", "expected": "", "predicted": "def example_function():\n    return 'Hello World'", "timestamp": "2025-08-04T13:42:28.807757"}, {"id": 1, "prompt": "Create a React component for a button", "expected": "", "predicted": "function MyComponent() {\n    return <div>Hello World</div>;\n}", "timestamp": "2025-08-04T13:42:28.807774"}, {"id": 2, "prompt": "Review this code for best practices", "expected": "", "predicted": "", "timestamp": "2025-08-04T13:42:28.807783"}]