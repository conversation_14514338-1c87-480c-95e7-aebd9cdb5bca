#!/usr/bin/env python3
"""
Tests for logger module
"""

import logging
import os
import tempfile
from pathlib import Path
from unittest.mock import <PERSON><PERSON><PERSON>, Mo<PERSON>, patch

import pytest

from agent.utils.logger import AILogger


class TestAILogger:
    """Test AILogger class"""

    def test_ai_logger_creation(self):
        """Test creating an AILogger"""
        logger = AILogger()
        assert logger is not None
        assert hasattr(logger, "loggers")
        assert hasattr(logger, "log_dir")

    def test_ai_logger_singleton_pattern(self):
        """Test that AILogger is a singleton"""
        logger1 = AILogger()
        logger2 = AILogger()
        assert logger1 is logger2

    def test_ai_logger_get_logger(self):
        """Test getting a logger"""
        logger = AILogger()
        ai_logger = logger.get_logger("test")
        assert ai_logger is not None
        assert isinstance(ai_logger, logging.Logger)

    def test_ai_logger_get_existing_logger(self):
        """Test getting an existing logger"""
        logger = AILogger()
        ai_logger1 = logger.get_logger("test")
        ai_logger2 = logger.get_logger("test")
        assert ai_logger1 is ai_logger2

    def test_ai_logger_configure_from_config(self):
        """Test configuring logger from config"""
        logger = AILogger()
        config = {"logging": {"level": "DEBUG"}}
        logger.configure_from_config(config)
        # This should not raise an exception

    def test_ai_logger_close(self):
        """Test closing logger"""
        logger = AILogger()
        test_logger = logger.get_logger("test")

        # Add a handler to the logger
        handler = logging.StreamHandler()
        test_logger.addHandler(handler)

        # Close the logger
        logger.close()

        # Verify handlers are removed
        assert len(test_logger.handlers) == 0

    def test_ai_logger_log_levels(self):
        """Test different log levels"""
        logger = AILogger()
        test_logger = logger.get_logger("test")

        # Test that all log levels work
        test_logger.debug("Debug message")
        test_logger.info("Info message")
        test_logger.warning("Warning message")
        test_logger.error("Error message")
        test_logger.critical("Critical message")

    def test_ai_logger_multiple_loggers(self):
        """Test multiple loggers"""
        logger = AILogger()
        logger1 = logger.get_logger("logger1")
        logger2 = logger.get_logger("logger2")

        assert logger1 is not logger2
        assert logger1.name == "logger1"
        assert logger2.name == "logger2"

    def test_ai_logger_log_format(self):
        """Test log format"""
        logger = AILogger()
        test_logger = logger.get_logger("test")

        # Test that log messages have the expected format
        with patch("sys.stdout") as mock_stdout:
            test_logger.info("Test message")
            # Verify that the log message was formatted correctly

    def test_ai_logger_error_handling(self):
        """Test error handling in logger"""
        logger = AILogger()
        test_logger = logger.get_logger("test")

        # Test logging an exception
        try:
            raise ValueError("Test exception")
        except ValueError as e:
            test_logger.exception("Exception occurred")

    def test_ai_logger_cleanup(self):
        """Test logger cleanup"""
        logger = AILogger()
        test_logger = logger.get_logger("test")

        # Add multiple handlers
        handler1 = logging.StreamHandler()
        handler2 = logging.FileHandler("test.log")
        test_logger.addHandler(handler1)
        test_logger.addHandler(handler2)

        # Close logger
        logger.close()

        # Verify all handlers are removed
        assert len(test_logger.handlers) == 0

    def test_ai_logger_with_invalid_config(self):
        """Test logger with invalid config"""
        logger = AILogger()

        # Test with invalid log level
        config = {"logging": {"level": "INVALID_LEVEL"}}
        logger.configure_from_config(config)

        # Test with missing log_dir
        config = {"logging": {}}
        logger.configure_from_config(config)


if __name__ == "__main__":
    pytest.main([__file__])
