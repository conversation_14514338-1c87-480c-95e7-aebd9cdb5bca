{"train": {"total_samples": 3, "avg_instruction_length": 58.333333333333336, "avg_output_length": 58.333333333333336, "min_instruction_length": 56, "max_instruction_length": 61, "min_output_length": 35, "max_output_length": 90, "total_characters": 350}, "val": {"total_samples": 1, "avg_instruction_length": 46.0, "avg_output_length": 95.0, "min_instruction_length": 46, "max_instruction_length": 46, "min_output_length": 95, "max_output_length": 95, "total_characters": 141}, "test": {"total_samples": 1, "avg_instruction_length": 52.0, "avg_output_length": 25.0, "min_instruction_length": 52, "max_instruction_length": 52, "min_output_length": 25, "max_output_length": 25, "total_characters": 77}}