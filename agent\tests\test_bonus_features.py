"""
Tests for ArchitectAgent Bonus Features
Tests deferred tasks, retry mechanism, clarifications, and learning integration
"""

import asyncio
from datetime import datetime, timedelta
from unittest.mock import Mock, patch

import pytest

from agent.core.agents.architect_agent import ArchitectAgent, TaskPriority
from agent.core.learning_integration import LearningIntegration, TaskOutcome
from agent.core.retry_manager import clarification_manager, retry_manager
from agent.core.task_queue import TaskPriority as QueueTaskPriority
from agent.core.task_queue import task_queue_manager


class TestBonusFeatures:
    """Test suite for bonus features"""

    @pytest.fixture
    async def architect_agent(self):
        """Create ArchitectAgent instance for testing"""
        agent = ArchitectAgent("config/architect_agent_config.json")
        return agent

    @pytest.mark.asyncio
    async def test_deferred_task_scheduling(self, architect_agent):
        """Test scheduling deferred tasks"""
        agent = await architect_agent
        scheduled_at = datetime.now() + timedelta(minutes=5)

        result = await agent.schedule_deferred_task(
            user_command="Create a test website",
            scheduled_at=scheduled_at,
            priority=TaskPriority.MEDIUM,
        )

        assert result["success"] is True
        assert "task_id" in result
        assert "queue_task_id" in result
        assert result["scheduled_at"] == scheduled_at.isoformat()
        assert result["priority"] == "medium"

    @pytest.mark.asyncio
    async def test_queue_status(self, architect_agent):
        """Test getting queue status"""
        agent = await architect_agent
        status = await agent.get_queue_status()

        assert "queued_tasks" in status
        assert "running_tasks" in status
        assert "completed_tasks" in status
        assert "max_concurrent_tasks" in status
        assert "is_running" in status
        assert isinstance(status["queued_tasks"], int)
        assert isinstance(status["running_tasks"], int)

    @pytest.mark.asyncio
    async def test_pending_clarifications(self, architect_agent):
        """Test getting pending clarifications"""
        agent = await architect_agent
        clarifications = await agent.get_pending_clarifications()

        assert clarifications["success"] is True
        assert "clarifications" in clarifications
        assert "count" in clarifications
        assert isinstance(clarifications["clarifications"], list)
        assert isinstance(clarifications["count"], int)

    @pytest.mark.asyncio
    async def test_provide_clarification(self, architect_agent):
        """Test providing clarification"""
        # First create a clarification request
        from agent.core.retry_manager import ClarificationType

        request_id = await clarification_manager.request_clarification(
            task_id="test_task",
            clarification_type=ClarificationType.MISSING_INFORMATION,
            message="What color scheme do you prefer?",
            options=["Light", "Dark", "Auto"],
            timeout_minutes=30,
        )

        # Provide clarification
        response = {"choice": "Dark", "comment": "I prefer dark themes"}
        result = await architect_agent.provide_clarification(request_id, response)

        assert result["success"] is True
        assert result["request_id"] == request_id

    @pytest.mark.asyncio
    async def test_learning_insights(self, architect_agent):
        """Test getting learning insights"""
        insights = await architect_agent.get_learning_insights()

        # Should return insights or message about no data
        assert isinstance(insights, dict)
        if "message" in insights:
            assert "No learning data available" in insights["message"]
        else:
            assert "total_tasks" in insights
            assert "success_rate" in insights

    @pytest.mark.asyncio
    async def test_pattern_recommendations(self, architect_agent):
        """Test getting pattern recommendations"""
        recommendations = await architect_agent.get_pattern_recommendations(
            "Create a React website"
        )

        assert recommendations["success"] is True
        assert "recommendations" in recommendations
        assert "count" in recommendations
        assert isinstance(recommendations["recommendations"], list)
        assert isinstance(recommendations["count"], int)

    @pytest.mark.asyncio
    async def test_learn_from_feedback(self, architect_agent):
        """Test learning from user feedback"""
        feedback = {
            "satisfaction": 4,
            "feedback": "Great work! The website looks amazing.",
            "task_id": "test_task_123",
        }

        result = await architect_agent.learn_from_feedback(feedback)

        assert result["success"] is True
        assert "learning_updated" in result
        assert "insights_generated" in result

    @pytest.mark.asyncio
    async def test_retry_configuration(self, architect_agent):
        """Test retry mechanism configuration"""
        # Configure retry settings
        retry_config = {
            "max_retries": 3,
            "base_delay": 1.0,
            "max_delay": 10.0,
            "exponential_backoff": True,
        }

        result = await retry_manager.configure_retry(retry_config)

        assert result["success"] is True
        assert result["max_retries"] == 3
        assert result["base_delay"] == 1.0

    @pytest.mark.asyncio
    async def test_retry_logic(self, architect_agent):
        """Test retry mechanism logic"""
        # Create a task that will fail initially
        task_id = "test_retry_task"

        # Simulate a task that fails twice then succeeds
        failure_count = 0

        async def failing_task():
            nonlocal failure_count
            failure_count += 1
            if failure_count < 3:
                raise Exception(f"Task failed attempt {failure_count}")
            return {"success": True, "result": "Task completed"}

        # Execute with retry
        result = await retry_manager.execute_with_retry(
            task_id=task_id, task_func=failing_task, max_retries=3
        )

        assert result["success"] is True
        assert result["attempts"] == 3
        assert result["result"]["result"] == "Task completed"

    @pytest.mark.asyncio
    async def test_task_outcome_recording(self, architect_agent):
        """Test recording task outcomes for learning"""
        task_id = "test_outcome_task"
        outcome = TaskOutcome(
            task_id=task_id,
            success=True,
            duration=120.5,
            user_satisfaction=4,
            feedback="Excellent work on the website design",
            patterns_used=["responsive_design", "modern_ui"],
            performance_metrics={"load_time": 2.1, "accessibility_score": 95},
        )

        result = await LearningIntegration.record_task_outcome(outcome)

        assert result["success"] is True
        assert result["outcome_id"] is not None
        assert result["learning_updated"] is True

    @pytest.mark.asyncio
    async def test_clarification_timeout(self, architect_agent):
        """Test clarification timeout handling"""
        # Request clarification with short timeout
        request_id = await clarification_manager.request_clarification(
            task_id="test_timeout_task",
            clarification_type=ClarificationType.MISSING_INFORMATION,
            message="What is your preferred color scheme?",
            options=["Light", "Dark", "Auto"],
            timeout_minutes=0.1,  # Very short timeout for testing
        )

        # Wait for timeout
        await asyncio.sleep(7)  # Wait 7 seconds for 0.1 minute timeout

        # Check if clarification expired
        status = await clarification_manager.get_clarification_status(request_id)

        # Should be expired or handled
        assert status["request_id"] == request_id
        assert status["status"] in ["expired", "handled", "pending"]

    @pytest.mark.asyncio
    async def test_error_analysis_for_clarification(self, architect_agent):
        """Test error analysis for clarification requests"""
        # Simulate an error that requires clarification
        error_context = {
            "error_type": "missing_information",
            "error_message": "Color scheme not specified",
            "task_context": "Create a website with modern design",
            "suggested_clarifications": [
                "What color scheme do you prefer?",
                "Do you want a light or dark theme?",
                "Any specific brand colors to include?",
            ],
        }

        result = await architect_agent.analyze_error_for_clarification(error_context)

        assert result["success"] is True
        assert "clarification_requested" in result
        assert "request_id" in result
        assert result["analysis"]["error_type"] == "missing_information"

    @pytest.mark.asyncio
    async def test_task_queue_priority(self, architect_agent):
        """Test task queue priority handling"""
        # Add tasks with different priorities
        high_priority_task = await task_queue_manager.add_task(
            task_id="high_priority_task",
            task_func=lambda: {"success": True},
            priority=QueueTaskPriority.HIGH,
        )

        low_priority_task = await task_queue_manager.add_task(
            task_id="low_priority_task",
            task_func=lambda: {"success": True},
            priority=QueueTaskPriority.LOW,
        )

        # Get queue status
        status = await task_queue_manager.get_status()

        assert status["queued_tasks"] >= 2
        assert "high_priority_task" in [task["task_id"] for task in status["task_list"]]
        assert "low_priority_task" in [task["task_id"] for task in status["task_list"]]

    @pytest.mark.asyncio
    async def test_comprehensive_bonus_workflow(self, architect_agent):
        """Test comprehensive bonus features workflow"""
        # 1. Schedule a deferred task
        scheduled_at = datetime.now() + timedelta(minutes=1)
        deferred_result = await architect_agent.schedule_deferred_task(
            user_command="Create a portfolio website",
            scheduled_at=scheduled_at,
            priority=TaskPriority.HIGH,
        )

        assert deferred_result["success"] is True

        # 2. Get queue status
        queue_status = await architect_agent.get_queue_status()
        assert "queued_tasks" in queue_status

        # 3. Get learning insights
        insights = await architect_agent.get_learning_insights()
        assert isinstance(insights, dict)

        # 4. Get pattern recommendations
        recommendations = await architect_agent.get_pattern_recommendations(
            "portfolio website"
        )
        assert recommendations["success"] is True

        # 5. Record task outcome
        outcome = TaskOutcome(
            task_id="comprehensive_test_task",
            success=True,
            duration=180.0,
            user_satisfaction=5,
            feedback="Perfect implementation of all features",
            patterns_used=["portfolio_design", "responsive_layout"],
            performance_metrics={"load_time": 1.8, "accessibility_score": 98},
        )

        outcome_result = await LearningIntegration.record_task_outcome(outcome)
        assert outcome_result["success"] is True

        # 6. Learn from feedback
        feedback_result = await architect_agent.learn_from_feedback(
            {
                "satisfaction": 5,
                "feedback": "Excellent comprehensive test",
                "task_id": "comprehensive_test_task",
            }
        )

        assert feedback_result["success"] is True

        print("✅ All bonus features working correctly!")
