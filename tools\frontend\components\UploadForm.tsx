import React, { useState } from 'react';

interface UploadFormProps {
  onSubmit: (data: { targetName?: string; reviewFirst: boolean; cleanupAfter: boolean }) => void;
  disabled?: boolean;
}

export const UploadForm: React.FC<UploadFormProps> = ({ onSubmit, disabled = false }) => {
  const [targetName, setTargetName] = useState('');
  const [reviewFirst, setReviewFirst] = useState(true);
  const [cleanupAfter, setCleanupAfter] = useState(false);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    onSubmit({ targetName, reviewFirst, cleanupAfter });
  };

  const validateTargetName = (name: string) => {
    return /^[a-zA-Z0-9_-]*$/.test(name);
  };

  return (
    <div className="upload-form-container">
      <form onSubmit={handleSubmit} className="upload-form">
        <div className="form-section">
          <h4>Import Options</h4>

          <div className="form-group">
            <label htmlFor="targetName">Target Name (optional):</label>
            <input
              id="targetName"
              type="text"
              value={targetName}
              onChange={(e) => setTargetName(e.target.value)}
              placeholder="my-imported-site"
              pattern="[a-zA-Z0-9_-]+"
              className={targetName && !validateTargetName(targetName) ? 'invalid' : ''}
              disabled={!!disabled || (!!targetName && !validateTargetName(targetName))}
            />
            <small className="help-text">
              Only letters, numbers, underscores, and hyphens allowed.
              If empty, a timestamped name will be generated.
            </small>
            {targetName && !validateTargetName(targetName) && (
              <small className="error-text">
                ❌ Invalid characters. Use only letters, numbers, underscores, and hyphens.
              </small>
            )}
          </div>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={reviewFirst}
                onChange={(e) => setReviewFirst(e.target.checked)}
                disabled={!!disabled}
              />
              <span className="checkmark"></span>
              Generate review report before import
            </label>
            <small className="help-text">
              Shows security analysis and framework detection before importing
            </small>
          </div>

          <div className="form-group checkbox-group">
            <label className="checkbox-label">
              <input
                type="checkbox"
                checked={cleanupAfter}
                onChange={(e) => setCleanupAfter(e.target.checked)}
                disabled={!!disabled}
              />
              <span className="checkmark"></span>
              Clean up upload files after import
            </label>
            <small className="help-text">
              Removes temporary upload files after successful import
            </small>
          </div>
        </div>

        <div className="form-actions">
          <button
            type="submit"
            className="submit-button"
            disabled={!!disabled || (!!targetName && !validateTargetName(targetName))}
          >
            {disabled ? 'Processing...' : 'Import Site'}
          </button>
        </div>
      </form>

      <style jsx>{`
        .upload-form-container {
          width: 100%;
          max-width: 500px;
          margin: 20px auto;
        }

        .upload-form {
          background: white;
          border-radius: 8px;
          padding: 24px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .form-section h4 {
          margin: 0 0 20px 0;
          color: #333;
          font-size: 18px;
        }

        .form-group {
          margin-bottom: 20px;
        }

        .form-group label {
          display: block;
          margin-bottom: 8px;
          font-weight: 500;
          color: #333;
        }

        .form-group input[type="text"] {
          width: 100%;
          padding: 12px;
          border: 2px solid #e1e5e9;
          border-radius: 6px;
          font-size: 14px;
          transition: border-color 0.3s ease;
        }

        .form-group input[type="text"]:focus {
          outline: none;
          border-color: #007bff;
        }

        .form-group input[type="text"].invalid {
          border-color: #dc3545;
        }

        .help-text {
          display: block;
          margin-top: 6px;
          font-size: 12px;
          color: #6c757d;
        }

        .error-text {
          display: block;
          margin-top: 6px;
          font-size: 12px;
          color: #dc3545;
        }

        .checkbox-group {
          margin-bottom: 16px;
        }

        .checkbox-label {
          display: flex;
          align-items: flex-start;
          cursor: pointer;
          font-weight: normal;
        }

        .checkbox-label input[type="checkbox"] {
          margin-right: 12px;
          margin-top: 2px;
        }

        .form-actions {
          margin-top: 24px;
          padding-top: 20px;
          border-top: 1px solid #e1e5e9;
        }

        .submit-button {
          width: 100%;
          padding: 12px 24px;
          background: #007bff;
          color: white;
          border: none;
          border-radius: 6px;
          font-size: 16px;
          font-weight: 500;
          cursor: pointer;
          transition: background-color 0.3s ease;
        }

        .submit-button:hover:not(:disabled) {
          background: #0056b3;
        }

        .submit-button:disabled {
          background: #6c757d;
          cursor: not-allowed;
        }
      `}</style>
    </div>
  );
};
