#!/usr/bin/env python3
"""
Docker Management Script for AI Coding Agent
Provides comprehensive Docker operations and management.
"""

import argparse
import asyncio
import json
import logging
import subprocess
import sys
import time
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import psutil

# Configure UTF-8 encoding for Windows
if hasattr(sys.stdout, "reconfigure"):
    sys.stdout.reconfigure(encoding="utf-8", errors="replace")

# Setup logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


class DockerManager:
    """Comprehensive Docker management for AI Coding Agent"""

    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.docker_compose_file = (
            self.project_root / "containers" / "docker-compose.yml"
        )
        self.dockerfile = self.project_root / "Dockerfile"

    def check_docker_installed(self) -> bool:
        """Check if Docker is installed and running"""
        try:
            result = subprocess.run(
                ["docker", "--version"], capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                logger.info(f"✅ Docker found: {result.stdout.strip()}")
                return True
            else:
                logger.error(f"❌ Docker not found: {result.stderr}")
                return False
        except FileNotFoundError:
            logger.error("❌ Docker not installed")
            return False
        except subprocess.TimeoutExpired:
            logger.error("❌ Docker command timed out")
            return False

    def check_docker_compose_installed(self) -> bool:
        """Check if Docker Compose is installed"""
        try:
            result = subprocess.run(
                ["docker-compose", "--version"],
                capture_output=True,
                text=True,
                timeout=10,
            )
            if result.returncode == 0:
                logger.info(f"✅ Docker Compose found: {result.stdout.strip()}")
                return True
            else:
                logger.error(f"❌ Docker Compose not found: {result.stderr}")
                return False
        except FileNotFoundError:
            logger.error("❌ Docker Compose not installed")
            return False
        except subprocess.TimeoutExpired:
            logger.error("❌ Docker Compose command timed out")
            return False

    def build_images(self) -> bool:
        """Build all Docker images"""
        try:
            logger.info("🔨 Building Docker images...")
            result = subprocess.run(
                ["docker-compose", "build", "--no-cache"],
                cwd=self.project_root / "containers",
                capture_output=True,
                text=True,
                timeout=600,  # 10 minutes
            )

            if result.returncode == 0:
                logger.info("✅ Docker images built successfully")
                return True
            else:
                logger.error(f"❌ Failed to build Docker images: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("❌ Docker build timed out")
            return False
        except Exception as e:
            logger.error(f"❌ Error building Docker images: {e}")
            return False

    def start_services(self) -> bool:
        """Start all Docker services"""
        try:
            logger.info("🚀 Starting Docker services...")
            result = subprocess.run(
                ["docker-compose", "up", "-d"],
                cwd=self.project_root / "containers",
                capture_output=True,
                text=True,
                timeout=300,  # 5 minutes
            )

            if result.returncode == 0:
                logger.info("✅ Docker services started successfully")

                # Start cursor rules monitor as background process
                self._start_cursor_monitor()

                return True
            else:
                logger.error(f"❌ Failed to start Docker services: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("❌ Docker start timed out")
            return False
        except Exception as e:
            logger.error(f"❌ Error starting Docker services: {e}")
            return False

    def _start_cursor_monitor(self):
        """Start cursor rules monitor as background process"""
        try:
            logger.info("🔍 Starting cursor rules monitor...")

            # Check if monitor is already running
            pid_file = Path("logs/cursor_rules_monitor.pid")
            if pid_file.exists():
                try:
                    with open(pid_file, "r") as f:
                        pid = int(f.read().strip())
                    process = psutil.Process(pid)
                    if process.is_running():
                        logger.info(
                            f"✅ Cursor rules monitor already running (PID: {pid})"
                        )
                        return
                except (ValueError, psutil.NoSuchProcess):
                    # PID file exists but process is dead, remove it
                    pid_file.unlink()

            # Start monitor in background
            monitor_script = self.project_root / "scripts" / "cursor_rules_monitor.py"
            if monitor_script.exists():
                # Use subprocess.Popen to start in background
                process = subprocess.Popen(
                    [sys.executable, str(monitor_script), "--daemon", "--strict"],
                    cwd=self.project_root,
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    start_new_session=True,
                )

                # Wait a moment for the process to start
                time.sleep(2)

                # Check if process is still running
                if process.poll() is None:
                    logger.info(
                        f"✅ Cursor rules monitor started successfully (PID: {process.pid})"
                    )
                else:
                    logger.error("❌ Failed to start cursor rules monitor")
            else:
                logger.error(f"❌ Monitor script not found: {monitor_script}")

        except Exception as e:
            logger.error(f"❌ Error starting cursor rules monitor: {e}")

    def stop_services(self) -> bool:
        """Stop all Docker services"""
        try:
            logger.info("🛑 Stopping Docker services...")
            result = subprocess.run(
                ["docker-compose", "down"],
                cwd=self.project_root / "containers",
                capture_output=True,
                text=True,
                timeout=120,  # 2 minutes
            )

            if result.returncode == 0:
                logger.info("✅ Docker services stopped successfully")

                # Stop cursor rules monitor
                self._stop_cursor_monitor()

                return True
            else:
                logger.error(f"❌ Failed to stop Docker services: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("❌ Docker stop timed out")
            return False
        except Exception as e:
            logger.error(f"❌ Error stopping Docker services: {e}")
            return False

    def _stop_monitoring_services(self):
        """Stop monitoring services"""
        try:
            logger.info("🛑 Stopping monitoring services...")
            # Monitoring services have been removed
            logger.info("✅ Monitoring services stopped")

        except Exception as e:
            logger.error(f"❌ Error stopping cursor rules monitor: {e}")

    def restart_services(self) -> bool:
        """Restart all Docker services"""
        try:
            logger.info("🔄 Restarting Docker services...")
            result = subprocess.run(
                ["docker-compose", "restart"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300,  # 5 minutes
            )

            if result.returncode == 0:
                logger.info("✅ Docker services restarted successfully")
                return True
            else:
                logger.error(f"❌ Failed to restart Docker services: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("❌ Docker restart timed out")
            return False
        except Exception as e:
            logger.error(f"❌ Error restarting Docker services: {e}")
            return False

    def get_service_status(self) -> Dict[str, Any]:
        """Get status of all Docker services"""
        try:
            result = subprocess.run(
                ["docker-compose", "ps"],
                cwd=self.project_root / "containers",
                capture_output=True,
                text=True,
                timeout=30,
            )

            if result.returncode == 0:
                logger.info("📊 Docker services status:")
                logger.info(result.stdout)
                return {"success": True, "output": result.stdout}
            else:
                logger.error(f"❌ Failed to get service status: {result.stderr}")
                return {"success": False, "error": result.stderr}

        except subprocess.TimeoutExpired:
            logger.error("❌ Status check timed out")
            return {"success": False, "error": "Timeout"}
        except Exception as e:
            logger.error(f"❌ Error getting service status: {e}")
            return {"success": False, "error": str(e)}

    def get_service_logs(
        self, service_name: str = None, tail: int = 100
    ) -> Dict[str, Any]:
        """Get logs from Docker services"""
        try:
            cmd = ["docker-compose", "logs", "--tail", str(tail)]
            if service_name:
                cmd.append(service_name)

            result = subprocess.run(
                cmd, cwd=self.project_root, capture_output=True, text=True, timeout=60
            )

            if result.returncode == 0:
                logger.info(f"📋 Logs for {service_name or 'all services'}:")
                logger.info(result.stdout)
                return {"success": True, "output": result.stdout}
            else:
                logger.error(f"❌ Failed to get logs: {result.stderr}")
                return {"success": False, "error": result.stderr}

        except subprocess.TimeoutExpired:
            logger.error("❌ Log retrieval timed out")
            return {"success": False, "error": "Timeout"}
        except Exception as e:
            logger.error(f"❌ Error getting logs: {e}")
            return {"success": False, "error": str(e)}

    def scale_service(self, service_name: str, replicas: int) -> bool:
        """Scale a specific service"""
        try:
            logger.info(f"📈 Scaling {service_name} to {replicas} replicas...")
            result = subprocess.run(
                ["docker-compose", "up", "-d", "--scale", f"{service_name}={replicas}"],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300,
            )

            if result.returncode == 0:
                logger.info(f"✅ {service_name} scaled to {replicas} replicas")
                return True
            else:
                logger.error(f"❌ Failed to scale {service_name}: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            logger.error("❌ Scale operation timed out")
            return False
        except Exception as e:
            logger.error(f"❌ Error scaling service: {e}")
            return False

    def cleanup(self) -> bool:
        """Clean up Docker resources"""
        try:
            logger.info("🧹 Cleaning up Docker resources...")

            # Stop and remove containers
            subprocess.run(["docker-compose", "down", "-v"], cwd=self.project_root)

            # Remove unused images
            subprocess.run(["docker", "image", "prune", "-f"])

            # Remove unused volumes
            subprocess.run(["docker", "volume", "prune", "-f"])

            # Remove unused networks
            subprocess.run(["docker", "network", "prune", "-f"])

            logger.info("✅ Docker cleanup completed")
            return True

        except Exception as e:
            logger.error(f"❌ Error during cleanup: {e}")
            return False

    def health_check(self) -> Dict[str, Any]:
        """Perform health check on all services"""
        try:
            logger.info("🏥 Performing health check...")

            # Get service status
            status = self.get_service_status()
            if not status["success"]:
                return {"success": False, "error": "Failed to get service status"}

            # Check main service health
            try:
                import requests

                response = requests.get("http://localhost:8000/health", timeout=10)
                if response.status_code == 200:
                    health_data = response.json()
                    logger.info("✅ Main service health check passed")
                else:
                    logger.warning(
                        f"⚠️ Main service health check failed: {response.status_code}"
                    )
                    health_data = {"status": "unhealthy"}
            except Exception as e:
                logger.warning(f"⚠️ Main service health check failed: {e}")
                health_data = {"status": "unreachable"}

            return {
                "success": True,
                "service_status": status,
                "health_data": health_data,
            }

        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
            return {"success": False, "error": str(e)}

    def backup_volumes(self) -> bool:
        """Backup Docker volumes"""
        try:
            logger.info("💾 Backing up Docker volumes...")

            backup_dir = self.project_root / "backups" / "docker"
            backup_dir.mkdir(parents=True, exist_ok=True)

            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Backup postgres data
            subprocess.run(
                [
                    "docker",
                    "run",
                    "--rm",
                    "-v",
                    "ai-coding-agent_postgres_data:/data",
                    "-v",
                    f"{backup_dir}:/backup",
                    "alpine",
                    "tar",
                    "czf",
                    f"/backup/postgres_backup_{timestamp}.tar.gz",
                    "-C",
                    "/data",
                    ".",
                ]
            )

            # Backup redis data
            subprocess.run(
                [
                    "docker",
                    "run",
                    "--rm",
                    "-v",
                    "ai-coding-agent_redis_data:/data",
                    "-v",
                    f"{backup_dir}:/backup",
                    "alpine",
                    "tar",
                    "czf",
                    f"/backup/redis_backup_{timestamp}.tar.gz",
                    "-C",
                    "/data",
                    ".",
                ]
            )

            logger.info("✅ Docker volumes backed up successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Error backing up volumes: {e}")
            return False

    def restore_volumes(self, backup_timestamp: str) -> bool:
        """Restore Docker volumes from backup"""
        try:
            logger.info(f"🔄 Restoring Docker volumes from {backup_timestamp}...")

            backup_dir = self.project_root / "backups" / "docker"

            # Stop services first
            self.stop_services()

            # Restore postgres data
            subprocess.run(
                [
                    "docker",
                    "run",
                    "--rm",
                    "-v",
                    "ai-coding-agent_postgres_data:/data",
                    "-v",
                    f"{backup_dir}:/backup",
                    "alpine",
                    "tar",
                    "xzf",
                    f"/backup/postgres_backup_{backup_timestamp}.tar.gz",
                    "-C",
                    "/data",
                ]
            )

            # Restore redis data
            subprocess.run(
                [
                    "docker",
                    "run",
                    "--rm",
                    "-v",
                    "ai-coding-agent_redis_data:/data",
                    "-v",
                    f"{backup_dir}:/backup",
                    "alpine",
                    "tar",
                    "xzf",
                    f"/backup/redis_backup_{backup_timestamp}.tar.gz",
                    "-C",
                    "/data",
                ]
            )

            # Start services
            self.start_services()

            logger.info("✅ Docker volumes restored successfully")
            return True

        except Exception as e:
            logger.error(f"❌ Error restoring volumes: {e}")
            return False


def start_container_agent():
    """Starts the container agent service."""
    run_command("docker-compose -f containers/docker-compose.yml up -d container_agent")


def stop_container_agent():
    """Stops the container agent service."""
    run_command("docker-compose -f containers/docker-compose.yml stop container_agent")


def container_agent_status():
    """Gets the status of the container agent service."""
    run_command("docker-compose -f containers/docker-compose.yml ps container_agent")


async def main():
    """Main function for Docker management"""
    docker_manager = DockerManager()

    # Check Docker installation
    if not docker_manager.check_docker_installed():
        logger.error("❌ Docker is not installed. Please install Docker Desktop first.")
        return

    if not docker_manager.check_docker_compose_installed():
        logger.error(
            "❌ Docker Compose is not installed. Please install Docker Compose first."
        )
        return

    # Parse command line arguments
    if len(sys.argv) < 2:
        print("Usage: python docker_management.py <command>")
        print(
            "Commands: build, start, stop, restart, status, logs, scale, cleanup, health, backup, restore"
        )
        return

    command = sys.argv[1].lower()

    if command == "build":
        docker_manager.build_images()
    elif command == "start":
        docker_manager.start_services()
    elif command == "stop":
        docker_manager.stop_services()
    elif command == "restart":
        docker_manager.restart_services()
    elif command == "status":
        docker_manager.get_service_status()
    elif command == "logs":
        service_name = sys.argv[2] if len(sys.argv) > 2 else None
        docker_manager.get_service_logs(service_name)
    elif command == "scale":
        if len(sys.argv) < 4:
            print("Usage: python docker_management.py scale <service> <replicas>")
            return
        service_name = sys.argv[2]
        replicas = int(sys.argv[3])
        docker_manager.scale_service(service_name, replicas)
    elif command == "cleanup":
        docker_manager.cleanup()
    elif command == "health":
        docker_manager.health_check()
    elif command == "backup":
        docker_manager.backup_volumes()
    elif command == "restore":
        if len(sys.argv) < 3:
            print("Usage: python docker_management.py restore <timestamp>")
            return
        timestamp = sys.argv[2]
        docker_manager.restore_volumes(timestamp)
    elif command == "container-agent:start":
        start_container_agent()
    elif command == "container-agent:stop":
        stop_container_agent()
    elif command == "container-agent:status":
        container_agent_status()
    else:
        print(f"Unknown command: {command}")
        print(
            "Available commands: build, start, stop, restart, status, logs, scale, cleanup, health, backup, restore"
        )


if __name__ == "__main__":
    asyncio.run(main())
