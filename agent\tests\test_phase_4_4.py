#!/usr/bin/env python3
"""
Phase 4.4 Site Management Frontend Test Script
Tests the complete site management frontend implementation
"""

import json
import os
import sys
import time
from pathlib import Path
from typing import Any, Dict, List

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


def test_phase_4_4_implementation():
    """Test Phase 4.4 Site Management Frontend Implementation"""

    print("=" * 80)
    print("PHASE 4.4 SITE MANAGEMENT FRONTEND TEST")
    print("=" * 80)

    test_results = {
        "phase": "4.4",
        "title": "Site Management Frontend",
        "status": "PASSED",
        "tests": [],
        "summary": {
            "total_tests": 0,
            "passed": 0,
            "failed": 0,
            "components_tested": [],
        },
    }

    def add_test(name: str, status: str, details: str = ""):
        test_results["tests"].append(
            {"name": name, "status": status, "details": details}
        )
        test_results["summary"]["total_tests"] += 1
        if status == "PASSED":
            test_results["summary"]["passed"] += 1
        else:
            test_results["summary"]["failed"] += 1

    # Test 1: Check TypeScript types for site management
    print("\n1. Testing TypeScript types for site management...")
    types_file = Path("src/types/index.ts")
    if types_file.exists():
        types_content = types_file.read_text()
        required_types = [
            "Site",
            "SiteCreate",
            "SiteUpdate",
            "SiteDeployment",
            "SiteTemplate",
            "SiteTheme",
            "SiteStats",
            "SiteBackup",
            "SiteHealth",
        ]

        missing_types = []
        for type_name in required_types:
            if (
                f"interface {type_name}" not in types_content
                and f"type {type_name}" not in types_content
            ):
                missing_types.append(type_name)

        if not missing_types:
            add_test(
                "TypeScript Types",
                "PASSED",
                f"All {len(required_types)} site management types found",
            )
            test_results["summary"]["components_tested"].append("TypeScript Types")
        else:
            add_test(
                "TypeScript Types",
                "FAILED",
                f"Missing types: {', '.join(missing_types)}",
            )
    else:
        add_test("TypeScript Types", "FAILED", "types/index.ts file not found")

    # Test 2: Check API client for site management endpoints
    print("\n2. Testing API client for site management endpoints...")
    api_file = Path("src/lib/api.ts")
    if api_file.exists():
        api_content = api_file.read_text()
        required_endpoints = [
            "sites:",
            "list:",
            "get:",
            "create:",
            "update:",
            "delete:",
            "deploy:",
            "getStats:",
            "getBackups:",
            "createBackup:",
            "downloadBackup:",
            "restoreBackup:",
            "getHealth:",
            "getTemplates:",
            "getThemes:",
        ]

        missing_endpoints = []
        for endpoint in required_endpoints:
            if endpoint not in api_content:
                missing_endpoints.append(endpoint)

        if not missing_endpoints:
            add_test(
                "API Client Endpoints",
                "PASSED",
                f"All {len(required_endpoints)} site management endpoints found",
            )
            test_results["summary"]["components_tested"].append("API Client")
        else:
            add_test(
                "API Client Endpoints",
                "FAILED",
                f"Missing endpoints: {', '.join(missing_endpoints)}",
            )
    else:
        add_test("API Client Endpoints", "FAILED", "api.ts file not found")

    # Test 3: Check SiteList component
    print("\n3. Testing SiteList component...")
    site_list_file = Path("src/components/sites/SiteList.tsx")
    if site_list_file.exists():
        site_list_content = site_list_file.read_text()
        required_features = [
            "useQuery",
            "useMemo",
            "SiteListProps",
            "SiteStatus",
            "viewMode",
            "searchTerm",
            "statusFilter",
            "selectedSites",
            "Grid",
            "List",
            "Search",
            "Filter",
            "Button",
        ]

        missing_features = []
        for feature in required_features:
            if feature not in site_list_content:
                missing_features.append(feature)

        if not missing_features:
            add_test(
                "SiteList Component",
                "PASSED",
                f"All {len(required_features)} features implemented",
            )
            test_results["summary"]["components_tested"].append("SiteList")
        else:
            add_test(
                "SiteList Component",
                "FAILED",
                f"Missing features: {', '.join(missing_features)}",
            )
    else:
        add_test("SiteList Component", "FAILED", "SiteList.tsx file not found")

    # Test 4: Check SiteDetail component
    print("\n4. Testing SiteDetail component...")
    site_detail_file = Path("src/components/sites/SiteDetail.tsx")
    if site_detail_file.exists():
        site_detail_content = site_detail_file.read_text()
        required_features = [
            "useQuery",
            "useMutation",
            "SiteDetailProps",
            "activeTab",
            "overview",
            "deployment",
            "backups",
            "health",
            "settings",
            "Site Information",
            "Statistics",
            "Quick Actions",
            "Deployment Status",
        ]

        missing_features = []
        for feature in required_features:
            if feature not in site_detail_content:
                missing_features.append(feature)

        if not missing_features:
            add_test(
                "SiteDetail Component",
                "PASSED",
                f"All {len(required_features)} features implemented",
            )
            test_results["summary"]["components_tested"].append("SiteDetail")
        else:
            add_test(
                "SiteDetail Component",
                "FAILED",
                f"Missing features: {', '.join(missing_features)}",
            )
    else:
        add_test("SiteDetail Component", "FAILED", "SiteDetail.tsx file not found")

    # Test 5: Check SiteCreationWizard component
    print("\n5. Testing SiteCreationWizard component...")
    wizard_file = Path("src/components/sites/SiteCreationWizard.tsx")
    if wizard_file.exists():
        wizard_content = wizard_file.read_text()
        required_features = [
            "useState",
            "SiteCreationWizardProps",
            "currentStep",
            "formData",
            "template",
            "theme",
            "configuration",
            "review",
            "WizardStep",
            "steps",
            "canProceed",
            "renderStepIndicator",
        ]

        missing_features = []
        for feature in required_features:
            if feature not in wizard_content:
                missing_features.append(feature)

        if not missing_features:
            add_test(
                "SiteCreationWizard Component",
                "PASSED",
                f"All {len(required_features)} features implemented",
            )
            test_results["summary"]["components_tested"].append("SiteCreationWizard")
        else:
            add_test(
                "SiteCreationWizard Component",
                "FAILED",
                f"Missing features: {', '.join(missing_features)}",
            )
    else:
        add_test(
            "SiteCreationWizard Component",
            "FAILED",
            "SiteCreationWizard.tsx file not found",
        )

    # Test 6: Check Badge component
    print("\n6. Testing Badge component...")
    badge_file = Path("src/components/ui/Badge.tsx")
    if badge_file.exists():
        badge_content = badge_file.read_text()
        required_features = [
            "BadgeProps",
            "variant",
            "size",
            "className",
            "cn",
            "default",
            "secondary",
            "destructive",
            "outline",
        ]

        missing_features = []
        for feature in required_features:
            if feature not in badge_content:
                missing_features.append(feature)

        if not missing_features:
            add_test(
                "Badge Component",
                "PASSED",
                f"All {len(required_features)} features implemented",
            )
            test_results["summary"]["components_tested"].append("Badge")
        else:
            add_test(
                "Badge Component",
                "FAILED",
                f"Missing features: {', '.join(missing_features)}",
            )
    else:
        add_test("Badge Component", "FAILED", "Badge.tsx file not found")

    # Test 7: Check main sites page
    print("\n7. Testing main sites page...")
    sites_page_file = Path("src/pages/sites.tsx")
    if sites_page_file.exists():
        sites_page_content = sites_page_file.read_text()
        required_features = [
            "ViewMode",
            "useState",
            "SiteList",
            "SiteDetail",
            "SiteCreationWizard",
            "viewMode",
            "selectedSiteId",
            "handleSiteSelect",
            "handleCreateSite",
            "Site Management",
            "handleBackToList",
        ]

        missing_features = []
        for feature in required_features:
            if feature not in sites_page_content:
                missing_features.append(feature)

        if not missing_features:
            add_test(
                "Sites Page",
                "PASSED",
                f"All {len(required_features)} features implemented",
            )
            test_results["summary"]["components_tested"].append("Sites Page")
        else:
            add_test(
                "Sites Page",
                "FAILED",
                f"Missing features: {', '.join(missing_features)}",
            )
    else:
        add_test("Sites Page", "FAILED", "sites.tsx file not found")

    # Test 8: Check sidebar navigation update
    print("\n8. Testing sidebar navigation update...")
    sidebar_file = Path("src/components/layout/Sidebar.tsx")
    if sidebar_file.exists():
        sidebar_content = sidebar_file.read_text()
        if (
            "Sites" in sidebar_content
            and "Globe" in sidebar_content
            and "/sites" in sidebar_content
        ):
            add_test(
                "Sidebar Navigation",
                "PASSED",
                "Sites navigation link added with Globe icon",
            )
            test_results["summary"]["components_tested"].append("Sidebar Navigation")
        else:
            add_test(
                "Sidebar Navigation",
                "FAILED",
                "Sites navigation link not properly added",
            )
    else:
        add_test("Sidebar Navigation", "FAILED", "Sidebar.tsx file not found")

    # Test 9: Check file structure
    print("\n9. Testing file structure...")
    required_files = [
        "src/components/sites/SiteList.tsx",
        "src/components/sites/SiteDetail.tsx",
        "src/components/sites/SiteCreationWizard.tsx",
        "src/components/ui/Badge.tsx",
        "src/pages/sites.tsx",
        "src/lib/utils.ts",
    ]

    missing_files = []
    for file_path in required_files:
        if not Path(file_path).exists():
            missing_files.append(file_path)

    if not missing_files:
        add_test(
            "File Structure",
            "PASSED",
            f"All {len(required_files)} required files exist",
        )
        test_results["summary"]["components_tested"].append("File Structure")
    else:
        add_test(
            "File Structure", "FAILED", f"Missing files: {', '.join(missing_files)}"
        )

    # Test 10: Check component integration
    print("\n10. Testing component integration...")
    integration_checks = [
        (
            "SiteList imports",
            "src/components/sites/SiteList.tsx",
            ["useQuery", "Site", "SiteStatus", "Button", "Input", "Badge"],
        ),
        (
            "SiteDetail imports",
            "src/components/sites/SiteDetail.tsx",
            [
                "useQuery",
                "useMutation",
                "Site",
                "SiteStats",
                "SiteBackup",
                "SiteHealth",
            ],
        ),
        (
            "Sites page imports",
            "src/pages/sites.tsx",
            ["SiteList", "SiteDetail", "SiteCreationWizard", "Button"],
        ),
        (
            "API integration",
            "src/lib/api.ts",
            ["sites:", "list", "get", "create", "update", "delete"],
        ),
    ]

    for check_name, file_name, required_imports in integration_checks:
        file_path = Path(file_name)
        if file_path.exists():
            content = file_path.read_text()
            missing_imports = [imp for imp in required_imports if imp not in content]
            if not missing_imports:
                add_test(
                    f"{check_name}", "PASSED", f"All imports and integrations found"
                )
            else:
                add_test(
                    f"{check_name}", "FAILED", f"Missing: {', '.join(missing_imports)}"
                )
        else:
            add_test(f"{check_name}", "FAILED", f"File {file_name} not found")

    # Determine overall status
    if test_results["summary"]["failed"] > 0:
        test_results["status"] = "FAILED"

    # Print results
    print("\n" + "=" * 80)
    print("TEST RESULTS SUMMARY")
    print("=" * 80)

    print(f"\nPhase: {test_results['phase']}")
    print(f"Title: {test_results['title']}")
    print(f"Overall Status: {test_results['status']}")

    print(f"\nTests: {test_results['summary']['total_tests']} total")
    print(f"  ✓ Passed: {test_results['summary']['passed']}")
    print(f"  ✗ Failed: {test_results['summary']['failed']}")

    print(f"\nComponents Tested:")
    for component in test_results["summary"]["components_tested"]:
        print(f"  ✓ {component}")

    print(f"\nDetailed Test Results:")
    for test in test_results["tests"]:
        status_icon = "✓" if test["status"] == "PASSED" else "✗"
        print(f"  {status_icon} {test['name']}: {test['status']}")
        if test["details"]:
            print(f"    Details: {test['details']}")

    # Save results
    results_file = Path("test_reports/phase_4_4_frontend_test_results.json")
    results_file.parent.mkdir(exist_ok=True)

    with open(results_file, "w") as f:
        json.dump(test_results, f, indent=2)

    print(f"\nResults saved to: {results_file}")

    # Print next steps
    print("\n" + "=" * 80)
    print("NEXT STEPS")
    print("=" * 80)

    if test_results["status"] == "PASSED":
        print("✓ Phase 4.4 Site Management Frontend implementation is complete!")
        print("\nNext steps:")
        print("1. Test the frontend in a browser environment")
        print("2. Integrate with backend API endpoints")
        print("3. Add real-time updates for deployment status")
        print("4. Implement error handling and loading states")
        print("5. Add unit tests for React components")
        print("6. Update project documentation")
    else:
        print("✗ Some tests failed. Please review and fix the issues above.")
        print("\nCommon fixes:")
        print("1. Ensure all required files exist")
        print("2. Check TypeScript types are properly defined")
        print("3. Verify API client methods are implemented")
        print("4. Confirm component imports and exports")
        print("5. Validate navigation updates")

    return test_results["status"] == "PASSED"


if __name__ == "__main__":
    success = test_phase_4_4_implementation()
    sys.exit(0 if success else 1)
