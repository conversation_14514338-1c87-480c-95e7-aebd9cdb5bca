#!/usr/bin/env python3
"""
Test script for IDE interface and chat function integration
"""

import json
import time
from datetime import datetime

import requests


def test_backend_health():
    """Test backend health endpoint"""
    print("🔍 Testing Backend Health...")
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend Health: {data['status']}")
            return True
        else:
            print(f"❌ Backend Health Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend Health Error: {e}")
        return False


def test_model_health():
    """Test AI model health endpoint"""
    print("\n🤖 Testing AI Model Health...")


def main():
    """Run all tests"""
    print("🚀 IDE & Chat Function Integration Test")
    print("=" * 50)
    print(f"Test Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    tests = [
        ("Backend Health", test_backend_health),
        ("Model Health", test_model_health),
        ("Chat Endpoint", test_chat_endpoint),
        ("Frontend Access", test_frontend_access),
        ("IDE Page", test_ide_page),
        ("API Proxy", test_api_proxy),
    ]

    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} Test Error: {e}")
            results.append((test_name, False))

    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")

    print(f"\nOverall: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 ALL TESTS PASSED - IDE & Chat Function Ready!")
    elif passed >= total * 0.8:
        print("🟡 MOSTLY WORKING - Minor issues to address")
    else:
        print("🔴 CRITICAL ISSUES - Immediate attention required")


if __name__ == "__main__":
    main()
