import React from 'react';

interface FrameworkInfo {
  framework: string;
  confidence: number;
  version?: string;
  build_tool?: string;
}

interface SecurityReport {
  status: string;
  issues: string[];
  warnings: string[];
  recommendations: string[];
}

interface ProjectManifest {
  name: string;
  framework_info: FrameworkInfo;
  security_report: SecurityReport;
  status: string;
  uploaded_at: string;
  file_count: number;
  total_size_mb: number;
  project_hash?: string;
  detected_languages?: string[];
  build_scripts?: string[];
  dependencies?: {
    [key: string]: string;
  };
  dev_dependencies?: {
    [key: string]: string;
  };
  scripts?: {
    [key: string]: string;
  };
  metadata?: {
    [key: string]: any;
  };
}

interface ProjectManifestProps {
  manifest: ProjectManifest;
  onAction?: (action: string, data?: any) => void;
}

export const ProjectManifest: React.FC<ProjectManifestProps> = ({
  manifest,
  onAction
}) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'safe': return '#28a745';
      case 'warning': return '#ffc107';
      case 'needs_review': return '#dc3545';
      default: return '#6c757d';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'safe': return '✅';
      case 'warning': return '⚠️';
      case 'needs_review': return '❌';
      default: return '❓';
    }
  };

  const getFrameworkIcon = (framework: string) => {
    switch (framework.toLowerCase()) {
      case 'react': return '⚛️';
      case 'next.js': return '▲';
      case 'vue': return '💚';
      case 'angular': return '🅰️';
      case 'flask': return '🍶';
      case 'django': return '🐍';
      case 'fastapi': return '⚡';
      case 'express': return '🚂';
      case 'static': return '📄';
      default: return '📦';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  const formatConfidence = (confidence: number) => {
    return Math.round(confidence * 100);
  };

  return (
    <div className="project-manifest-container">
      <div className="manifest-header">
        <h3>📋 Project Manifest</h3>
        <div className="manifest-status">
          <span
            className="status-badge"
            style={{ backgroundColor: getStatusColor(manifest.security_report.status) }}
          >
            {getStatusIcon(manifest.security_report.status)} {manifest.security_report.status.toUpperCase()}
          </span>
        </div>
      </div>

      <div className="manifest-content">
        {/* Basic Info */}
        <div className="manifest-section">
          <h4>📊 Basic Information</h4>
          <div className="info-grid">
            <div className="info-item">
              <span className="info-label">Project Name:</span>
              <span className="info-value">{manifest.name}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Uploaded:</span>
              <span className="info-value">{formatDate(manifest.uploaded_at)}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Files:</span>
              <span className="info-value">{manifest.file_count}</span>
            </div>
            <div className="info-item">
              <span className="info-label">Size:</span>
              <span className="info-value">{manifest.total_size_mb.toFixed(2)} MB</span>
            </div>
            {manifest.project_hash && (
              <div className="info-item">
                <span className="info-label">Hash:</span>
                <span className="info-value hash">{manifest.project_hash.substring(0, 8)}...</span>
              </div>
            )}
          </div>
        </div>

        {/* Framework Detection */}
        <div className="manifest-section">
          <h4>🤖 Framework Detection</h4>
          <div className="framework-info">
            <div className="framework-main">
              <span className="framework-icon">
                {getFrameworkIcon(manifest.framework_info.framework)}
              </span>
              <div className="framework-details">
                <span className="framework-name">{manifest.framework_info.framework}</span>
                <span className="framework-confidence">
                  {formatConfidence(manifest.framework_info.confidence)}% confidence
                </span>
                {manifest.framework_info.version && (
                  <span className="framework-version">v{manifest.framework_info.version}</span>
                )}
              </div>
            </div>
            {manifest.framework_info.build_tool && (
              <div className="build-tool">
                <span className="build-label">Build Tool:</span>
                <span className="build-value">{manifest.framework_info.build_tool}</span>
              </div>
            )}
          </div>
        </div>

        {/* Detected Languages */}
        {manifest.detected_languages && manifest.detected_languages.length > 0 && (
          <div className="manifest-section">
            <h4>🔤 Detected Languages</h4>
            <div className="languages-grid">
              {manifest.detected_languages.map((lang, index) => (
                <span key={index} className="language-badge">
                  {lang}
                </span>
              ))}
            </div>
          </div>
        )}

        {/* Dependencies */}
        {manifest.dependencies && Object.keys(manifest.dependencies).length > 0 && (
          <div className="manifest-section">
            <h4>📦 Dependencies</h4>
            <div className="dependencies-list">
              {Object.entries(manifest.dependencies).slice(0, 10).map(([name, version]) => (
                <div key={name} className="dependency-item">
                  <span className="dependency-name">{name}</span>
                  <span className="dependency-version">{version}</span>
                </div>
              ))}
              {Object.keys(manifest.dependencies).length > 10 && (
                <div className="dependency-more">
                  +{Object.keys(manifest.dependencies).length - 10} more...
                </div>
              )}
            </div>
          </div>
        )}

        {/* Build Scripts */}
        {manifest.scripts && Object.keys(manifest.scripts).length > 0 && (
          <div className="manifest-section">
            <h4>🔧 Build Scripts</h4>
            <div className="scripts-grid">
              {Object.entries(manifest.scripts).map(([name, script]) => (
                <div key={name} className="script-item">
                  <span className="script-name">{name}</span>
                  <span className="script-command">{script}</span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Security Summary */}
        <div className="manifest-section">
          <h4>🔒 Security Summary</h4>
          <div className="security-summary">
            <div className="security-stats">
              <div className="security-stat">
                <span className="stat-number">{manifest.security_report.issues.length}</span>
                <span className="stat-label">Issues</span>
              </div>
              <div className="security-stat">
                <span className="stat-number">{manifest.security_report.warnings.length}</span>
                <span className="stat-label">Warnings</span>
              </div>
              <div className="security-stat">
                <span className="stat-number">{manifest.security_report.recommendations.length}</span>
                <span className="stat-label">Recommendations</span>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        {onAction && (
          <div className="manifest-actions">
            <button
              onClick={() => onAction('preview')}
              className="action-button preview"
            >
              🔍 Preview Project
            </button>
            <button
              onClick={() => onAction('edit')}
              className="action-button edit"
            >
              📝 Open in Editor
            </button>
            <button
              onClick={() => onAction('validate')}
              className="action-button validate"
            >
              ✅ Validate Project
            </button>
          </div>
        )}
      </div>

      <style jsx>{`
        .project-manifest-container {
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }

        .manifest-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 20px 24px;
          background: #f8f9fa;
          border-bottom: 1px solid #e9ecef;
        }

        .manifest-header h3 {
          margin: 0;
          color: #333;
          font-size: 20px;
        }

        .status-badge {
          padding: 6px 12px;
          border-radius: 16px;
          color: white;
          font-size: 12px;
          font-weight: 500;
        }

        .manifest-content {
          padding: 24px;
        }

        .manifest-section {
          margin-bottom: 24px;
        }

        .manifest-section:last-child {
          margin-bottom: 0;
        }

        .manifest-section h4 {
          margin: 0 0 16px 0;
          color: #333;
          font-size: 16px;
          font-weight: 600;
        }

        .info-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
          gap: 12px;
        }

        .info-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          background: #f8f9fa;
          border-radius: 6px;
        }

        .info-label {
          font-weight: 500;
          color: #666;
        }

        .info-value {
          color: #333;
          font-weight: 600;
        }

        .info-value.hash {
          font-family: monospace;
          font-size: 12px;
        }

        .framework-info {
          background: #f8f9fa;
          padding: 16px;
          border-radius: 8px;
        }

        .framework-main {
          display: flex;
          align-items: center;
          gap: 12px;
          margin-bottom: 12px;
        }

        .framework-icon {
          font-size: 24px;
        }

        .framework-details {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .framework-name {
          font-weight: 600;
          color: #333;
          font-size: 16px;
        }

        .framework-confidence {
          font-size: 12px;
          color: #007bff;
        }

        .framework-version {
          font-size: 12px;
          color: #6c757d;
        }

        .build-tool {
          display: flex;
          align-items: center;
          gap: 8px;
          font-size: 14px;
        }

        .build-label {
          color: #666;
        }

        .build-value {
          color: #333;
          font-weight: 500;
        }

        .languages-grid {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }

        .language-badge {
          background: #007bff;
          color: white;
          padding: 4px 8px;
          border-radius: 12px;
          font-size: 12px;
          font-weight: 500;
        }

        .dependencies-list {
          max-height: 200px;
          overflow-y: auto;
        }

        .dependency-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 6px 12px;
          border-bottom: 1px solid #f1f3f4;
        }

        .dependency-name {
          font-weight: 500;
          color: #333;
        }

        .dependency-version {
          color: #6c757d;
          font-size: 12px;
        }

        .dependency-more {
          text-align: center;
          padding: 8px;
          color: #6c757d;
          font-style: italic;
        }

        .scripts-grid {
          display: grid;
          gap: 8px;
        }

        .script-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: 8px 12px;
          background: #f8f9fa;
          border-radius: 6px;
        }

        .script-name {
          font-weight: 500;
          color: #333;
        }

        .script-command {
          color: #6c757d;
          font-family: monospace;
          font-size: 12px;
        }

        .security-summary {
          background: #f8f9fa;
          padding: 16px;
          border-radius: 8px;
        }

        .security-stats {
          display: flex;
          justify-content: space-around;
          text-align: center;
        }

        .security-stat {
          display: flex;
          flex-direction: column;
          gap: 4px;
        }

        .stat-number {
          font-size: 24px;
          font-weight: 700;
          color: #333;
        }

        .stat-label {
          font-size: 12px;
          color: #666;
        }

        .manifest-actions {
          display: flex;
          gap: 12px;
          margin-top: 24px;
          padding-top: 20px;
          border-top: 1px solid #e9ecef;
        }

        .action-button {
          flex: 1;
          padding: 10px 16px;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.3s ease;
        }

        .action-button.preview {
          background: #17a2b8;
          color: white;
        }

        .action-button.preview:hover {
          background: #138496;
        }

        .action-button.edit {
          background: #007bff;
          color: white;
        }

        .action-button.edit:hover {
          background: #0056b3;
        }

        .action-button.validate {
          background: #28a745;
          color: white;
        }

        .action-button.validate:hover {
          background: #218838;
        }
      `}</style>
    </div>
  );
};
