import React, { useState } from 'react';

interface FileNode {
  name: string;
  type: 'file' | 'directory';
  size?: number;
  path: string;
  children?: FileNode[];
  language?: string;
  framework?: string;
}

interface FileTreeProps {
  files: FileNode[];
  onFileSelect?: (file: FileNode) => void;
  maxDepth?: number;
}

export const FileTree: React.FC<FileTreeProps> = ({
  files,
  onFileSelect,
  maxDepth = 3
}) => {
  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());

  const toggleNode = (path: string) => {
    const newExpanded = new Set(expandedNodes);
    if (newExpanded.has(path)) {
      newExpanded.delete(path);
    } else {
      newExpanded.add(path);
    }
    setExpandedNodes(newExpanded);
  };

  const getFileIcon = (node: FileNode) => {
    if (node.type === 'directory') {
      return expandedNodes.has(node.path) ? '📂' : '📁';
    }

    const ext = node.name.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'html': return '🌐';
      case 'css': return '🎨';
      case 'js': return '📜';
      case 'ts': return '📘';
      case 'jsx': return '⚛️';
      case 'tsx': return '⚛️';
      case 'py': return '🐍';
      case 'json': return '📋';
      case 'md': return '📝';
      case 'txt': return '📄';
      case 'png': case 'jpg': case 'jpeg': case 'gif': case 'svg': return '🖼️';
      case 'ico': return '🎯';
      case 'xml': return '📄';
      case 'yml': case 'yaml': return '⚙️';
      case 'lock': return '🔒';
      case 'gitignore': return '🚫';
      case 'env': return '🔐';
      default: return '📄';
    }
  };

  const getFileColor = (node: FileNode) => {
    if (node.type === 'directory') return '#007bff';

    const ext = node.name.split('.').pop()?.toLowerCase();
    switch (ext) {
      case 'html': return '#e34c26';
      case 'css': return '#264de4';
      case 'js': return '#f7df1e';
      case 'ts': return '#3178c6';
      case 'jsx': case 'tsx': return '#61dafb';
      case 'py': return '#3776ab';
      case 'json': return '#000000';
      case 'md': return '#ff6b6b';
      default: return '#6c757d';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
  };

  const renderNode = (node: FileNode, depth: number = 0) => {
    const isExpanded = expandedNodes.has(node.path);
    const hasChildren = node.children && node.children.length > 0;
    const canExpand = node.type === 'directory' && hasChildren && depth < maxDepth;

    return (
      <div key={node.path} className="file-tree-node">
        <div
          className={`file-tree-item ${node.type} ${canExpand ? 'expandable' : ''}`}
          style={{ paddingLeft: `${depth * 20}px` }}
          onClick={() => {
            if (canExpand) {
              toggleNode(node.path);
            } else if (node.type === 'file' && onFileSelect) {
              onFileSelect(node);
            }
          }}
        >
          <div className="file-icon">
            {getFileIcon(node)}
          </div>
          <div className="file-info">
            <span
              className="file-name"
              style={{ color: getFileColor(node) }}
            >
              {node.name}
            </span>
            {node.type === 'file' && node.size && (
              <span className="file-size">
                {formatFileSize(node.size)}
              </span>
            )}
          </div>
          {node.language && (
            <div className="file-language">
              {node.language}
            </div>
          )}
          {canExpand && (
            <div className="expand-icon">
              {isExpanded ? '▼' : '▶'}
            </div>
          )}
        </div>

        {isExpanded && hasChildren && (
          <div className="file-tree-children">
            {node.children!.map(child => renderNode(child, depth + 1))}
          </div>
        )}
      </div>
    );
  };

  return (
    <div className="file-tree-container">
      <div className="file-tree-header">
        <h4>📁 Project Structure</h4>
        <div className="file-tree-stats">
          <span>📄 {files.filter(f => f.type === 'file').length} files</span>
          <span>📁 {files.filter(f => f.type === 'directory').length} folders</span>
        </div>
      </div>

      <div className="file-tree-content">
        {files.map(node => renderNode(node))}
      </div>

      <style jsx>{`
        .file-tree-container {
          background: white;
          border-radius: 8px;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          overflow: hidden;
        }

        .file-tree-header {
          padding: 16px 20px;
          background: #f8f9fa;
          border-bottom: 1px solid #e9ecef;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }

        .file-tree-header h4 {
          margin: 0;
          color: #333;
          font-size: 16px;
        }

        .file-tree-stats {
          display: flex;
          gap: 16px;
          font-size: 12px;
          color: #6c757d;
        }

        .file-tree-content {
          max-height: 400px;
          overflow-y: auto;
          padding: 8px 0;
        }

        .file-tree-node {
          width: 100%;
        }

        .file-tree-item {
          display: flex;
          align-items: center;
          padding: 8px 20px;
          cursor: pointer;
          transition: background-color 0.2s ease;
          border-bottom: 1px solid #f8f9fa;
        }

        .file-tree-item:hover {
          background: #f8f9fa;
        }

        .file-tree-item.expandable:hover {
          background: #e3f2fd;
        }

        .file-icon {
          margin-right: 12px;
          font-size: 16px;
          width: 20px;
          text-align: center;
        }

        .file-info {
          flex: 1;
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .file-name {
          font-weight: 500;
          font-size: 14px;
        }

        .file-size {
          font-size: 12px;
          color: #6c757d;
          background: #f8f9fa;
          padding: 2px 6px;
          border-radius: 4px;
        }

        .file-language {
          font-size: 11px;
          color: white;
          background: #007bff;
          padding: 2px 6px;
          border-radius: 4px;
          margin-right: 8px;
        }

        .expand-icon {
          font-size: 12px;
          color: #6c757d;
          margin-left: 8px;
        }

        .file-tree-children {
          border-left: 2px solid #e9ecef;
          margin-left: 10px;
        }

        .file-tree-item.directory {
          font-weight: 600;
        }

        .file-tree-item.file {
          font-weight: normal;
        }
      `}</style>
    </div>
  );
};
