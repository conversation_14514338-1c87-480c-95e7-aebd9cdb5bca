#!/usr/bin/env python3
"""
Test script to verify active file content is included in prompt context
"""

import json
import time

import requests


def test_active_file_context():
    """Test that active file content is included in prompt context"""

    print("🧪 TESTING ACTIVE FILE CONTEXT INCLUSION")
    print("=" * 60)

    # Test cases with different scenarios
    test_cases = [
        {
            "name": "Code Modification Request",
            "prompt": "modify the header",
            "context": {
                "currentProject": "Test Project",
                "activeFiles": ["index.html"],
                "recentActions": ["Created project"],
                "conversationHistory": ["Previous message"],
                "code": "<!DOCTYPE html>\n<html>\n<head>\n    <title>Test Page</title>\n</head>\n<body>\n    <header>\n        <h1>Welcome</h1>\n    </header>\n</body>\n</html>",
            },
            "expected_code_included": True,
        },
        {
            "name": "Code Generation Request",
            "prompt": "create a new component",
            "context": {
                "currentProject": "Test Project",
                "activeFiles": ["App.jsx"],
                "recentActions": ["Created project"],
                "conversationHistory": ["Previous message"],
                "code": "import React from 'react';\n\nfunction App() {\n  return (\n    <div>\n      <h1>Hello World</h1>\n    </div>\n  );\n}\n\nexport default App;",
            },
            "expected_code_included": True,
        },
        {
            "name": "General Query",
            "prompt": "hello",
            "context": {
                "currentProject": "Test Project",
                "activeFiles": [],
                "recentActions": ["Created project"],
                "conversationHistory": ["Previous message"],
            },
            "expected_code_included": False,
        },
        {
            "name": "Debugging Request",
            "prompt": "fix the error",
            "context": {
                "currentProject": "Test Project",
                "activeFiles": ["script.js"],
                "recentActions": ["Created project"],
                "conversationHistory": ["Previous message"],
                "code": "function test() {\n    console.log('Hello');\n    // Error: missing semicolon\n    return true\n}",
            },
            "expected_code_included": True,
        },
    ]

    results = []

    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. Testing: {test_case['name']}")
        print(f"   Input: '{test_case['prompt']}'")
        print(f"   Expected Code Included: {test_case['expected_code_included']}")

        try:
            # Test the chat endpoint with the context
            response = requests.post(
                "http://127.0.0.1:8000/api/v1/chat/test",
                headers={"Content-Type": "application/json"},
                json={
                    "prompt": test_case["prompt"],
                    "context": test_case["context"],
                    "intent": {
                        "type": "create",
                        "confidence": 0.8,
                        "action": "test",
                        "target": "website",
                    },
                    "history": [
                        {"role": "user", "content": "Previous message"},
                        {"role": "assistant", "content": "Previous response"},
                    ],
                },
                timeout=10,
            )

            if response.status_code == 200:
                data = response.json()
                print(f"   ✅ Response received")

                # Check if context was properly sent
                response_text = data.get("response", "")
                if isinstance(response_text, dict):
                    response_text = response_text.get("content", "")
                response_text = str(response_text)

                # Check if code context is mentioned in response
                code_indicators = [
                    "html",
                    "javascript",
                    "react",
                    "component",
                    "function",
                    "header",
                    "error",
                ]
                code_mentioned = any(
                    indicator in response_text.lower() for indicator in code_indicators
                )

                print(f"   📝 Response: {response_text[:100]}...")
                print(f"   🔍 Code context mentioned: {code_mentioned}")

                # Verify context structure
                context_included = (
                    "code" in test_case["context"] and test_case["context"]["code"]
                )
                print(f"   📁 Context includes code: {context_included}")

                results.append(
                    {
                        "test": test_case["name"],
                        "status": "PASS",
                        "context_included": context_included,
                        "expected_included": test_case["expected_code_included"],
                        "code_mentioned": code_mentioned,
                    }
                )

            else:
                print(f"   ❌ HTTP {response.status_code}: {response.text}")
                results.append(
                    {
                        "test": test_case["name"],
                        "status": "FAIL",
                        "error": f"HTTP {response.status_code}",
                    }
                )

        except requests.exceptions.Timeout:
            print(f"   ⏰ Timeout - server not responding")
            results.append(
                {
                    "test": test_case["name"],
                    "status": "TIMEOUT",
                    "error": "Request timeout",
                }
            )
        except Exception as e:
            print(f"   ❌ Error: {str(e)}")
            results.append(
                {"test": test_case["name"], "status": "ERROR", "error": str(e)}
            )

    # Summary
    print(f"\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)

    passed = 0
    failed = 0
    context_correct = 0

    for result in results:
        if result["status"] == "PASS":
            passed += 1
            if result.get("context_included") == result.get("expected_included"):
                context_correct += 1

        status_icon = "✅" if result["status"] == "PASS" else "❌"
        print(f"{status_icon} {result['test']}: {result['status']}")
        if result.get("context_included") is not None:
            print(
                f"   Context included: {result['context_included']} (expected: {result['expected_included']})"
            )
            print(f"   Code mentioned: {result.get('code_mentioned', False)}")

    print(f"\n📈 SUMMARY:")
    print(f"   ✅ Passed: {passed}/{len(results)}")
    print(f"   ❌ Failed: {failed}/{len(results)}")
    print(
        f"   🎯 Context Accuracy: {context_correct}/{passed}"
        if passed > 0
        else "   🎯 Context Accuracy: N/A"
    )

    if passed == len(results):
        print(f"\n🎉 ALL TESTS PASSED! Active file context is working correctly.")
    else:
        print(f"\n⚠️  Some tests failed. Check the implementation.")

    return passed == len(results)


def test_context_structure():
    """Test the structure of the context payload"""

    print(f"\n🔧 TESTING CONTEXT PAYLOAD STRUCTURE")
    print("=" * 60)

    # Test payload structure
    test_payload = {
        "prompt": "modify the header",
        "context": {
            "currentProject": "Test Project",
            "activeFiles": ["index.html", "style.css"],
            "recentActions": ["Created project", "Modified header"],
            "conversationHistory": ["Previous message"],
            "code": "<!DOCTYPE html>\n<html>\n<head>\n    <title>Test</title>\n</head>\n<body>\n    <header>\n        <h1>Welcome</h1>\n    </header>\n</body>\n</html>",
        },
        "intent": {
            "type": "modify",
            "confidence": 0.8,
            "action": "modify_header",
            "target": "header",
        },
        "history": [
            {"role": "user", "content": "I want to modify the header"},
            {"role": "assistant", "content": "I'll help you modify the header"},
        ],
    }

    print("📋 Testing payload structure:")
    print(f"   ✅ Prompt: {test_payload['prompt']}")
    print(f"   ✅ Context: {len(test_payload['context'])} fields")
    print(f"   ✅ Active Files: {test_payload['context']['activeFiles']}")
    print(f"   ✅ Code Length: {len(test_payload['context']['code'])} chars")
    print(f"   ✅ Intent: {test_payload['intent']['type']}")
    print(f"   ✅ History: {len(test_payload['history'])} messages")

    try:
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/chat/test",
            headers={"Content-Type": "application/json"},
            json=test_payload,
            timeout=10,
        )

        if response.status_code == 200:
            data = response.json()
            print(f"   ✅ Backend accepted payload structure")

            # Check if backend processed the context
            response_text = data.get("response", "")
            if isinstance(response_text, dict):
                response_text = response_text.get("content", "")

            print(f"   📝 Response: {response_text}")

        else:
            print(f"   ❌ Backend rejected payload: {response.status_code}")

    except Exception as e:
        print(f"   ❌ Error: {str(e)}")


if __name__ == "__main__":
    print("🚀 Starting Active File Context Verification")
    print("=" * 60)

    # Check if server is running
    try:
        response = requests.get("http://127.0.0.1:8000/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend server is running")
        else:
            print("❌ Backend server not responding properly")
            exit(1)
    except Exception as e:
        print(f"❌ Cannot connect to backend server: {e}")
        print("Please start the backend server first:")
        print(
            "  .\\.venv\\Scripts\\Activate.ps1 && python src/dashboard/minimal_api.py"
        )
        exit(1)

    # Run tests
    success = test_active_file_context()
    test_context_structure()

    print(f"\n" + "=" * 60)
    if success:
        print("🎉 ACTIVE FILE CONTEXT VERIFICATION COMPLETE - ALL TESTS PASSED!")
    else:
        print("⚠️  ACTIVE FILE CONTEXT VERIFICATION COMPLETE - SOME ISSUES FOUND")
    print("=" * 60)
