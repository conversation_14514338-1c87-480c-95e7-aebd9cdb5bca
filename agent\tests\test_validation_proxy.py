"""
Tests for validation proxy functions.
"""

import warnings
from unittest.mock import MagicMock, patch

import pytest

# Import ValidationError from core.error_handling since that's what the validation functions use
from agent.core.error_handling import ValidationError

# Update imports to use consolidated validation functions
from agent.core.validation import (
    validate_directory_exists,
    validate_email,
    validate_enum,
    validate_file_exists,
    validate_not_none,
    validate_numeric_range,
    validate_port,
    validate_positive,
    validate_string_length,
    validate_url,
)


class TestValidationProxy:
    """Test cases for validation proxy functions"""

    def test_validate_not_none_valid(self):
        """Test validate_not_none with valid input"""
        validate_not_none("test", "value")  # Should not raise

    def test_validate_not_none_invalid(self):
        """Test validate_not_none with invalid input"""
        with pytest.raises(ValidationError) as exc_info:
            validate_not_none("test", None)
        assert "Value cannot be None" in str(exc_info.value)

    def test_validate_positive_valid(self):
        """Test validate_positive with valid input"""
        validate_positive("test", 5)  # Should not raise

    def test_validate_positive_invalid(self):
        """Test validate_positive with invalid input"""
        with pytest.raises(ValidationError) as exc_info:
            validate_positive("test", -1)
        assert "Value must be positive" in str(exc_info.value)

    def test_validate_enum_valid(self):
        """Test validate_enum with valid input"""
        validate_enum("test", "red", ["red", "green", "blue"])  # Should not raise

    def test_validate_enum_invalid(self):
        """Test validate_enum with invalid input"""
        with pytest.raises(ValidationError) as exc_info:
            validate_enum("test", "yellow", ["red", "green", "blue"])
        assert "must be one of: red, green, blue" in str(exc_info.value)

    def test_validate_string_length_valid(self):
        """Test validate_string_length with valid input"""
        validate_string_length("test", "hello", 1, 10)  # Should not raise

    def test_validate_string_length_invalid(self):
        """Test validate_string_length with invalid input"""
        with pytest.raises(ValidationError) as exc_info:
            validate_string_length("test", "hi", 5, 10)
        assert "must be at least 5 characters" in str(exc_info.value)

    def test_validate_file_exists(self, tmp_path):
        """Test validate_file_exists with existing file"""
        test_file = tmp_path / "test.txt"
        test_file.write_text("test content")
        validate_file_exists("test", str(test_file))  # Should not raise

    def test_validate_file_exists_invalid(self, tmp_path):
        """Test validate_file_exists with non-existent file"""
        test_file = tmp_path / "nonexistent.txt"
        with pytest.raises(ValidationError) as exc_info:
            validate_file_exists("test", str(test_file))
        assert "File does not exist" in str(exc_info.value)

    def test_validate_directory_exists(self, tmp_path):
        """Test validate_directory_exists with existing directory"""
        test_dir = tmp_path / "test_dir"
        test_dir.mkdir()
        validate_directory_exists("test", str(test_dir))  # Should not raise

    def test_validate_directory_exists_invalid(self, tmp_path):
        """Test validate_directory_exists with non-existent directory"""
        test_dir = tmp_path / "nonexistent_dir"
        with pytest.raises(ValidationError) as exc_info:
            validate_directory_exists("test", str(test_dir))
        assert "Directory does not exist" in str(exc_info.value)

    def test_validate_url_valid(self):
        """Test validate_url with valid URL"""
        validate_url("test", "https://example.com")  # Should not raise

    def test_validate_url_invalid(self):
        """Test validate_url with invalid URL"""
        with pytest.raises(ValidationError) as exc_info:
            validate_url("test", "not-a-url")
        assert "Invalid URL format" in str(exc_info.value)

    def test_validate_email_valid(self):
        """Test validate_email with valid email"""
        validate_email("test", "<EMAIL>")  # Should not raise

    def test_validate_email_invalid(self):
        """Test validate_email with invalid email"""
        with pytest.raises(ValidationError) as exc_info:
            validate_email("test", "invalid-email")
        assert "Invalid email format" in str(exc_info.value)

    def test_validate_numeric_range_valid(self):
        """Test validate_numeric_range with valid input"""
        validate_numeric_range("test", 50, 10, 100)  # Should not raise

    def test_validate_numeric_range_invalid(self):
        """Test validate_numeric_range with invalid input"""
        with pytest.raises(ValidationError) as exc_info:
            validate_numeric_range("test", 5, 10, 100)
        assert "must be at least 10" in str(exc_info.value)

    def test_validate_port_valid(self):
        """Test validate_port with valid port"""
        validate_port("test", 8080)  # Should not raise

    def test_validate_port_invalid(self):
        """Test validate_port with invalid port"""
        with pytest.raises(ValidationError) as exc_info:
            validate_port("test", 99999)
        assert "Port must be between 1 and 65535" in str(exc_info.value)
