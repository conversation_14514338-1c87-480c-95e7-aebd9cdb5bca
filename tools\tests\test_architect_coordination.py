#!/usr/bin/env python3
"""
Test Architect Coordination System
Verifies that the architect properly coordinates specialists with clear prompts and verification
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from agent.core.persona_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentType, TaskCategory
from agent.core.site_container_manager import SiteContainerManager


async def test_architect_delegation():
    """Test architect's delegation to specialists"""
    
    print("🏗️ Testing Architect Delegation System")
    print("=" * 50)
    
    container_manager = SiteContainerManager()
    
    test_results = {
        "prompt_creation": False,
        "specialist_delegation": False,
        "output_verification": False,
        "fix_application": False,
        "user_escalation": False
    }
    
    # Test 1: Prompt Creation
    try:
        specialist_prompt = await container_manager._create_specialist_prompt(
            AgentType.FRONTEND,
            {
                "title": "React Component Error",
                "description": "Component not rendering properly",
                "category": "frontend",
                "severity": "medium"
            },
            "test-site",
            []
        )
        
        required_fields = [
            "task_type", "specialist_role", "clear_objective", 
            "specific_instructions", "context", "success_criteria"
        ]
        
        if all(field in specialist_prompt for field in required_fields):
            print("✅ Specialist prompt creation working")
            print(f"   Objective: {specialist_prompt['clear_objective'][:80]}...")
            print(f"   Instructions: {len(specialist_prompt['specific_instructions'])} steps")
            print(f"   Success criteria: {len(specialist_prompt['success_criteria'])} criteria")
            test_results["prompt_creation"] = True
        else:
            missing = [f for f in required_fields if f not in specialist_prompt]
            print(f"❌ Prompt missing fields: {missing}")
            
    except Exception as e:
        print(f"❌ Prompt creation failed: {e}")
    
    # Test 2: Specialist Delegation
    try:
        delegation_result = await container_manager._delegate_to_specialist(
            AgentType.FRONTEND,
            {"test": "prompt"},
            {"category": "frontend", "title": "Test Error"}
        )
        
        if delegation_result.get("success") and delegation_result.get("fix_description"):
            print("✅ Specialist delegation working")
            print(f"   Fix: {delegation_result['fix_description'][:80]}...")
            print(f"   Changes: {len(delegation_result.get('changes_made', []))} changes")
            test_results["specialist_delegation"] = True
        else:
            print(f"❌ Delegation failed: {delegation_result.get('error', 'Unknown error')}")
            
    except Exception as e:
        print(f"❌ Specialist delegation failed: {e}")
    
    # Test 3: Output Verification
    try:
        mock_specialist_output = {
            "success": True,
            "fix_description": "Fixed React component state initialization and error handling",
            "changes_made": ["Added null checks", "Implemented error boundary"],
            "testing_performed": ["Unit tests", "Integration tests"],
            "rollback_instructions": "Revert to commit abc123",
            "verification_steps": ["Load page", "Test interactions"]
        }
        
        verification_result = await container_manager._architect_verify_output(
            mock_specialist_output,
            {"title": "Test Error"},
            "test-site"
        )
        
        if verification_result.get("verified"):
            print("✅ Output verification working")
            print(f"   Quality score: {verification_result.get('quality_score', 0):.1f}%")
            print(f"   Checks passed: {len(verification_result.get('checks_performed', []))}")
            test_results["output_verification"] = True
        else:
            print(f"❌ Verification failed: {verification_result.get('reason', 'Unknown')}")
            
    except Exception as e:
        print(f"❌ Output verification failed: {e}")
    
    # Test 4: Fix Application
    try:
        application_result = await container_manager._architect_apply_fix(
            "test-site",
            mock_specialist_output,
            {"title": "Test Error"}
        )
        
        if application_result.get("success"):
            print("✅ Fix application working")
            print(f"   Changes applied: {application_result.get('changes_applied', 0)}")
            print(f"   Verifications passed: {application_result.get('verifications_passed', 0)}")
            test_results["fix_application"] = True
        else:
            print(f"❌ Fix application failed: {application_result.get('error', 'Unknown')}")
            
    except Exception as e:
        print(f"❌ Fix application failed: {e}")
    
    # Test 5: User Escalation
    try:
        escalation_result = await container_manager._architect_escalate_to_user(
            "test-site",
            {"title": "Complex Error", "description": "Needs user input"},
            [{"fix_type": "test_fix", "success": False, "error": "Failed"}]
        )
        
        if escalation_result.get("requires_user_input") and escalation_result.get("architect_message"):
            print("✅ User escalation working")
            print(f"   Message length: {len(escalation_result['architect_message'])} chars")
            print(f"   Suggestions: {len(escalation_result.get('suggested_actions', []))}")
            test_results["user_escalation"] = True
        else:
            print(f"❌ User escalation failed: {escalation_result.get('error', 'Unknown')}")
            
    except Exception as e:
        print(f"❌ User escalation failed: {e}")
    
    return test_results


async def test_end_to_end_coordination():
    """Test complete end-to-end architect coordination"""
    
    print(f"\n🔄 Testing End-to-End Coordination")
    print("=" * 40)
    
    container_manager = SiteContainerManager()
    
    coordination_results = {
        "frontend_coordination": False,
        "database_coordination": False,
        "security_coordination": False,
        "architect_messaging": False
    }
    
    # Test scenarios
    test_scenarios = [
        {
            "name": "Frontend Error",
            "error": {
                "title": "React Component Not Rendering",
                "description": "ProductList component shows blank screen",
                "category": "frontend",
                "severity": "medium"
            },
            "result_key": "frontend_coordination"
        },
        {
            "name": "Database Performance",
            "error": {
                "title": "Slow Database Queries",
                "description": "Search queries taking over 3 seconds",
                "category": "database", 
                "severity": "high"
            },
            "result_key": "database_coordination"
        },
        {
            "name": "Security Vulnerability",
            "error": {
                "title": "SQL Injection Risk",
                "description": "User input not properly sanitized",
                "category": "security",
                "severity": "critical"
            },
            "result_key": "security_coordination"
        }
    ]
    
    for scenario in test_scenarios:
        try:
            print(f"\n📋 Testing: {scenario['name']}")
            
            result = await container_manager.handle_error_with_user_escalation(
                site_name="test-coordination",
                error_details=scenario["error"]
            )
            
            if result.get("success"):
                print(f"✅ {scenario['name']} coordinated successfully")
                print(f"   Architect message: {result.get('architect_message', 'No message')[:60]}...")
                print(f"   Fix applied: {result.get('fix_applied', 'Unknown')[:60]}...")
                coordination_results[scenario["result_key"]] = True
            elif result.get("requires_user_input"):
                print(f"⚠️ {scenario['name']} escalated to user (expected for complex issues)")
                print(f"   Escalation reason: {result.get('escalation_reason', 'Unknown')}")
                coordination_results[scenario["result_key"]] = True  # Escalation is also success
            else:
                print(f"❌ {scenario['name']} coordination failed: {result.get('error', 'Unknown')}")
                
        except Exception as e:
            print(f"❌ {scenario['name']} coordination error: {e}")
    
    # Test architect messaging
    try:
        architect_message = container_manager._generate_architect_success_message(
            AgentType.FRONTEND,
            {"fix_description": "Fixed React component rendering issue"}
        )
        
        if "architect" in architect_message.lower() and len(architect_message) > 50:
            print(f"\n✅ Architect messaging working")
            print(f"   Sample message: {architect_message[:80]}...")
            coordination_results["architect_messaging"] = True
        else:
            print(f"\n❌ Architect messaging insufficient")
            
    except Exception as e:
        print(f"\n❌ Architect messaging failed: {e}")
    
    return coordination_results


async def test_prompt_quality():
    """Test the quality and specificity of prompts created for specialists"""
    
    print(f"\n📝 Testing Prompt Quality")
    print("=" * 30)
    
    container_manager = SiteContainerManager()
    
    quality_results = {
        "clear_objectives": False,
        "specific_instructions": False,
        "comprehensive_context": False,
        "success_criteria": False,
        "validation_requirements": False
    }
    
    try:
        # Test prompt for frontend specialist
        prompt = await container_manager._create_specialist_prompt(
            AgentType.FRONTEND,
            {
                "title": "Complex UI Bug",
                "description": "Multiple components not rendering correctly",
                "category": "frontend",
                "severity": "high"
            },
            "complex-site",
            [{"fix_type": "previous_attempt", "success": False}]
        )
        
        # Check objective clarity
        objective = prompt.get("clear_objective", "")
        if len(objective) > 50 and "frontend" in objective.lower():
            print("✅ Clear objectives provided")
            quality_results["clear_objectives"] = True
        else:
            print("❌ Objectives unclear or too brief")
        
        # Check instruction specificity
        instructions = prompt.get("specific_instructions", [])
        if len(instructions) >= 6 and all(len(inst) > 20 for inst in instructions[:3]):
            print("✅ Specific instructions provided")
            print(f"   Instruction count: {len(instructions)}")
            quality_results["specific_instructions"] = True
        else:
            print("❌ Instructions too vague or insufficient")
        
        # Check context comprehensiveness
        context = prompt.get("context", {})
        required_context = ["site_name", "error_details", "available_tools", "site_context"]
        if all(key in context for key in required_context):
            print("✅ Comprehensive context provided")
            print(f"   Available tools: {len(context.get('available_tools', []))}")
            quality_results["comprehensive_context"] = True
        else:
            missing = [key for key in required_context if key not in context]
            print(f"❌ Context missing: {missing}")
        
        # Check success criteria
        success_criteria = prompt.get("success_criteria", [])
        if len(success_criteria) >= 4:
            print("✅ Success criteria defined")
            print(f"   Criteria count: {len(success_criteria)}")
            quality_results["success_criteria"] = True
        else:
            print("❌ Success criteria insufficient")
        
        # Check validation requirements
        validation_reqs = prompt.get("validation_requirements", [])
        if len(validation_reqs) >= 3:
            print("✅ Validation requirements specified")
            quality_results["validation_requirements"] = True
        else:
            print("❌ Validation requirements missing")
            
    except Exception as e:
        print(f"❌ Prompt quality test failed: {e}")
    
    return quality_results


async def main():
    """Run all architect coordination tests"""
    
    print("🏗️ Architect Coordination System Test Suite")
    print("=" * 60)
    print("Testing the architect's ability to coordinate specialists with")
    print("clear prompts, verification, and proper user communication\n")
    
    # Test 1: Delegation System
    delegation_results = await test_architect_delegation()
    
    # Test 2: End-to-End Coordination
    coordination_results = await test_end_to_end_coordination()
    
    # Test 3: Prompt Quality
    quality_results = await test_prompt_quality()
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 30)
    
    print(f"\n🏗️ Delegation System:")
    for test, result in delegation_results.items():
        status = "✅" if result else "❌"
        print(f"   {test.replace('_', ' ').title()}: {status}")
    
    print(f"\n🔄 End-to-End Coordination:")
    for test, result in coordination_results.items():
        status = "✅" if result else "❌"
        print(f"   {test.replace('_', ' ').title()}: {status}")
    
    print(f"\n📝 Prompt Quality:")
    for test, result in quality_results.items():
        status = "✅" if result else "❌"
        print(f"   {test.replace('_', ' ').title()}: {status}")
    
    # Calculate overall score
    all_results = {**delegation_results, **coordination_results, **quality_results}
    total_tests = len(all_results)
    passed_tests = sum(all_results.values())
    score = (passed_tests / total_tests) * 100
    
    print(f"\n🎯 Overall Coordination Score: {score:.1f}%")
    
    if score >= 90:
        print(f"🎉 EXCELLENT - Architect coordination system fully functional!")
        status = "excellent"
    elif score >= 75:
        print(f"✅ GOOD - Most coordination features working well")
        status = "good"
    elif score >= 50:
        print(f"⚠️ FAIR - Basic coordination working, needs improvement")
        status = "fair"
    else:
        print(f"❌ POOR - Coordination system needs significant work")
        status = "poor"
    
    print(f"\n💡 Key Benefits Achieved:")
    print(f"   🏗️ Architect is the single point of contact with users")
    print(f"   📋 Clear, specific prompts guide specialist work")
    print(f"   ✅ Comprehensive verification ensures quality output")
    print(f"   🔧 Proper tool access for each specialist type")
    print(f"   🤝 Natural escalation to users when needed")
    print(f"   📊 Feedback loops improve reliability over time")
    
    return status in ["excellent", "good"]


if __name__ == "__main__":
    # Run the coordination tests
    result = asyncio.run(main())
    
    print(f"\n{'🎉 Architect coordination system working perfectly!' if result else '⚠️ Coordination system needs improvement'}")
    exit(0 if result else 1)
