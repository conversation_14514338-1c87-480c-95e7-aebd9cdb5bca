#!/usr/bin/env python3
"""
Test Auto Security Patch Script
Verifies that the auto security patch functionality works correctly.
"""

import asyncio
import json
import shutil
import sys
import tempfile
from pathlib import Path

# Add src to path for imports
sys.path.append(str(Path(__file__).parent.parent))

from agent.scripts.auto_security_patch import (
    AutoSecurityPatcher,
    NotificationSender,
    SecurityPatcher,
    VulnerabilityScanner,
)


async def test_vulnerability_scanner():
    """Test vulnerability scanner functionality"""
    print("🧪 Testing VulnerabilityScanner...")

    try:
        scanner = VulnerabilityScanner(".")

        # Test Python vulnerability scanning
        print("  Testing Python vulnerability scanning...")
        python_results = await scanner.scan_python_vulnerabilities()
        assert python_results["scan_type"] == "python"
        assert "status" in python_results
        print(f"  ✅ Python scan completed: {python_results['status']}")

        # Test Node.js vulnerability scanning
        print("  Testing Node.js vulnerability scanning...")
        node_results = await scanner.scan_node_vulnerabilities()
        assert node_results["scan_type"] == "nodejs"
        assert "status" in node_results
        print(f"  ✅ Node.js scan completed: {node_results['status']}")

        return True
    except Exception as e:
        print(f"  ❌ VulnerabilityScanner test failed: {e}")
        return False


async def test_security_patcher():
    """Test security patcher functionality"""
    print("🧪 Testing SecurityPatcher...")

    try:
        patcher = SecurityPatcher(".")

        # Test with empty vulnerability list
        print("  Testing with empty vulnerability list...")
        empty_python_result = await patcher.patch_python_vulnerabilities([])
        assert empty_python_result["patch_type"] == "python"
        assert empty_python_result["total_vulnerabilities"] == 0
        print("  ✅ Empty Python vulnerability list handled correctly")

        empty_node_result = await patcher.patch_node_vulnerabilities([])
        assert empty_node_result["patch_type"] == "nodejs"
        assert empty_node_result["total_vulnerabilities"] == 0
        print("  ✅ Empty Node.js vulnerability list handled correctly")

        return True
    except Exception as e:
        print(f"  ❌ SecurityPatcher test failed: {e}")
        return False


def test_notification_sender():
    """Test notification sender functionality"""
    print("🧪 Testing NotificationSender...")

    try:
        notifier = NotificationSender()

        # Test summary generation
        mock_report = {
            "session_info": {
                "session_id": "test-session-123",
                "total_critical_vulnerabilities": 5,
            },
            "python_patching": {"successfully_patched": 2, "failed_patches": 1},
            "nodejs_patching": {"successfully_patched": 1, "failed_patches": 1},
        }

        summary = notifier._generate_summary(mock_report)
        assert isinstance(summary, str)
        assert "5" in summary  # Should mention total critical vulnerabilities
        print("  ✅ Summary generation works correctly")

        # Test console notification (should not fail)
        notifier._send_console_notification(summary, mock_report)
        print("  ✅ Console notification works correctly")

        return True
    except Exception as e:
        print(f"  ❌ NotificationSender test failed: {e}")
        return False


async def test_auto_security_patcher():
    """Test the main AutoSecurityPatcher class"""
    print("🧪 Testing AutoSecurityPatcher...")

    try:
        patcher = AutoSecurityPatcher(".")

        # Test initialization
        assert patcher.session_id is not None
        assert patcher.start_time is not None
        print("  ✅ AutoSecurityPatcher initialization works")

        # Test directory creation
        patcher.ensure_logs_directory()
        logs_dir = patcher.project_root / "logs"
        assert logs_dir.exists()
        print("  ✅ Logs directory creation works")

        # Test vulnerability scanning
        print("  Testing vulnerability scanning...")
        scan_results = await patcher._scan_vulnerabilities()
        assert "python_scan" in scan_results
        assert "nodejs_scan" in scan_results
        assert "total_critical_vulnerabilities" in scan_results
        print("  ✅ Vulnerability scanning works")

        # Test patch report generation
        print("  Testing patch report generation...")
        mock_patch_results = {
            "python_patching": {"successfully_patched": 0, "failed_patches": 0},
            "nodejs_patching": {"successfully_patched": 0, "failed_patches": 0},
        }

        patch_report = patcher._generate_patch_report(scan_results, mock_patch_results)
        assert "session_info" in patch_report
        assert "scan_results" in patch_report
        assert "patch_results" in patch_report
        assert "summary" in patch_report
        print("  ✅ Patch report generation works")

        return True
    except Exception as e:
        print(f"  ❌ AutoSecurityPatcher test failed: {e}")
        return False


async def test_dry_run_functionality():
    """Test dry-run functionality"""
    print("🧪 Testing dry-run functionality...")

    try:
        patcher = AutoSecurityPatcher(".")

        # Test dry-run (scanning only)
        scan_results = await patcher._scan_vulnerabilities()

        # Verify scan results structure
        assert isinstance(scan_results, dict)
        assert "python_scan" in scan_results
        assert "nodejs_scan" in scan_results
        assert "total_critical_vulnerabilities" in scan_results

        print(
            f"  ✅ Dry-run scan found {scan_results['total_critical_vulnerabilities']} critical vulnerabilities"
        )

        return True
    except Exception as e:
        print(f"  ❌ Dry-run test failed: {e}")
        return False


async def test_error_handling():
    """Test error handling capabilities"""
    print("🧪 Testing error handling...")

    try:
        # Test with non-existent directory
        with tempfile.TemporaryDirectory() as temp_dir:
            temp_path = Path(temp_dir) / "nonexistent"

            # This should not crash but handle the error gracefully
            patcher = AutoSecurityPatcher(str(temp_path))

            # The scanner should handle missing directories gracefully
            scanner = VulnerabilityScanner(str(temp_path))

            # Test that it doesn't crash on missing package.json/requirements.txt
            python_results = await scanner.scan_python_vulnerabilities()
            node_results = await scanner.scan_node_vulnerabilities()

            # Results should indicate appropriate status
            assert "status" in python_results
            assert "status" in node_results

        print("  ✅ Error handling works correctly")
        return True
    except Exception as e:
        print(f"  ❌ Error handling test failed: {e}")
        return False


async def test_report_saving():
    """Test report saving functionality"""
    print("🧪 Testing report saving...")

    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            patcher = AutoSecurityPatcher(temp_dir)

            # Create a mock report
            mock_report = {
                "session_info": {
                    "session_id": patcher.session_id,
                    "status": "completed",
                },
                "scan_results": {"total_critical_vulnerabilities": 0},
                "patch_results": {},
                "summary": {"total_critical_vulnerabilities_found": 0},
            }

            # Save the report
            report_path = patcher._save_patch_report(mock_report)

            # Verify the report was saved
            assert report_path.exists()
            assert report_path.suffix == ".json"

            # Verify the report content
            with open(report_path, "r") as f:
                saved_report = json.load(f)

            assert saved_report["session_info"]["session_id"] == patcher.session_id

        print("  ✅ Report saving works correctly")
        return True
    except Exception as e:
        print(f"  ❌ Report saving test failed: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 TESTING AUTO SECURITY PATCH FUNCTIONALITY")
    print("=" * 60)

    tests = [
        ("Vulnerability Scanner", test_vulnerability_scanner),
        ("Security Patcher", test_security_patcher),
        ("Notification Sender", test_notification_sender),
        ("Auto Security Patcher", test_auto_security_patcher),
        ("Dry Run Functionality", test_dry_run_functionality),
        ("Error Handling", test_error_handling),
        ("Report Saving", test_report_saving),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n🧪 Running {test_name}...")
        try:
            if asyncio.iscoroutinefunction(test_func):
                success = await test_func()
            else:
                success = test_func()
            results.append((test_name, success))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))

    # Print summary
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)

    passed = 0
    failed = 0

    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{test_name:<25} {status}")
        if success:
            passed += 1
        else:
            failed += 1

    print(f"\nTotal: {passed + failed} tests")
    print(f"Passed: {passed}")
    print(f"Failed: {failed}")
    print(f"Success Rate: {(passed / (passed + failed) * 100):.1f}%")

    if failed == 0:
        print(
            "\n🎉 ALL TESTS PASSED! Auto security patch functionality is working correctly."
        )
        print("\n📋 Next Steps:")
        print(
            "1. Test the script manually: python scripts/auto_security_patch.py --dry-run"
        )
        print(
            "2. Set up Slack webhook URL in environment: export SLACK_WEBHOOK_URL=your_webhook_url"
        )
        print("3. Test with actual patching: python scripts/auto_security_patch.py")
        print(
            "4. Configure CI pipeline using .github/workflows/nightly-security-patch.yml"
        )
    else:
        print(f"\n⚠️ {failed} test(s) failed. Please check the implementation.")

    return failed == 0


if __name__ == "__main__":
    success = asyncio.run(main())
    sys.exit(0 if success else 1)
