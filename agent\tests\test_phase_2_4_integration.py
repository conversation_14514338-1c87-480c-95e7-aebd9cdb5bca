#!/usr/bin/env python3
"""
Phase 2.4: Maintenance Engine Integration Test
Tests the integration between Phase 2.3 CMS & Content and Phase 2.4 Maintenance Engine.
"""

import os
import sys
import time
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

try:
    from projects.content.cms_content_manager import ContentManager
    from agent.core.maintenance_engine import (
        <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        DependencyManager,
        MaintenanceEngine,
    )

    print("✅ Successfully imported Phase 2.4 Maintenance Engine modules")
except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)


def test_phase_2_4_integration():
    """Test Phase 2.4 Maintenance Engine integration with Phase 2.3 CMS"""
    print("\n🚀 Testing Phase 2.4: Maintenance Engine Integration")
    print("=" * 70)

    try:
        # Initialize Maintenance Engine
        print("\n📋 Initializing Maintenance Engine...")
        engine = MaintenanceEngine("config/maintenance_config.json")
        print("✅ Maintenance Engine initialized successfully")

        # Initialize CMS Manager
        print("\n📋 Initializing CMS Manager...")
        cms_manager = ContentManager("config/cms_config.json")
        print("✅ CMS Manager initialized successfully")

        # Connect CMS to Maintenance Engine
        print("\n🔗 Connecting CMS to Maintenance Engine...")
        engine.link_checker.cms_manager = cms_manager
        print("✅ CMS integration enabled")

        # Test 1: Create CMS content with links
        print("\n📝 Test 1: Creating CMS content with links...")
        print("-" * 50)

        # Create content with internal and external links
        content_with_links = """<article>
<h1>Test Article with Links</h1>
<p>This is a test article to demonstrate link checking integration.</p>

<h2>Internal Links</h2>
<ul>
<li><a href="/about">About Page</a></li>
<li><a href="/contact">Contact Page</a></li>
<li><a href="/blog">Blog Index</a></li>
</ul>

<h2>External Links</h2>
<ul>
<li><a href="https://www.google.com">Google</a></li>
<li><a href="https://www.github.com">GitHub</a></li>
<li><a href="https://www.example.com/nonexistent">Broken Link</a></li>
</ul>

<h2>Media Links</h2>
<ul>
<li><img src="/images/test.jpg" alt="Test Image"></li>
<li><a href="/documents/test.pdf">Test Document</a></li>
</ul>

<p>This content will be used to test the maintenance engine's link checking capabilities.</p>
</article>"""

        article = cms_manager.create_content(
            title="Test Article with Links - Phase 2.4 Integration",
            content=content_with_links,
            content_type="article",
            author="Integration Test",
            tags=["integration", "phase-2-4", "link-test", "maintenance"],
        )

        print(f"✅ Created test article: {article.id}")
        print(f"   Title: {article.title}")
        print(f"   Status: {article.status}")

        # Test 2: Link checking with CMS integration
        print("\n🔍 Test 2: Link checking with CMS integration...")
        print("-" * 50)

        # Extract links from CMS content
        links = engine.link_checker.extract_links_from_content(
            article.content, article.id, article.file_path
        )

        print(f"✅ Extracted {len(links)} links from CMS content")
        for link in links:
            print(f"   - {link['url']} ({link['link_type']})")

        # Check links
        print("\n🔍 Checking links...")
        results = engine.link_checker.check_all_links([article])

        print(f"✅ Link check completed")
        print(f"📊 Results:")
        print(f"   Total links checked: {len(results)}")
        print(f"   Valid links: {len([r for r in results if r.is_valid])}")
        print(f"   Broken links: {len([r for r in results if not r.is_valid])}")

        # Show broken links
        broken_links = [r for r in results if not r.is_valid]
        if broken_links:
            print(f"\n❌ Broken links found:")
            for result in broken_links:
                print(f"   - {result.url}")
                print(f"     Status: {result.status_code}")
                print(f"     Error: {result.error_message}")
                print(f"     Source: {result.source_content_id}")

        # Test 3: Dependency checking
        print("\n📦 Test 3: Dependency checking...")
        print("-" * 50)

        # Check for dependency updates
        updates = engine.dependency_manager.check_python_dependencies()

        print(f"✅ Dependency check completed")
        print(f"📊 Results:")
        print(f"   Total updates found: {len(updates)}")

        if updates:
            print(f"\n📦 Available updates:")
            for update in updates[:5]:  # Show first 5
                print(f"   - {update.package_name}")
                print(f"     Current: {update.current_version}")
                print(f"     Available: {update.available_version}")
                print(f"     Type: {update.update_type}")
                print(
                    f"     Approval: {'Required' if update.approval_required else 'Auto'}"
                )
        else:
            print("✅ All dependencies are up to date!")

        # Test 4: Maintenance task scheduling
        print("\n📅 Test 4: Maintenance task scheduling...")
        print("-" * 50)

        # Schedule maintenance tasks
        link_task_id = engine.schedule_link_check("medium")
        dep_task_id = engine.schedule_dependency_check("medium")

        print(f"✅ Scheduled maintenance tasks:")
        print(f"   Link check: {link_task_id}")
        print(f"   Dependency check: {dep_task_id}")

        # Test 5: Maintenance status
        print("\n📊 Test 5: Maintenance status...")
        print("-" * 50)

        status = engine.get_maintenance_status()

        print(f"✅ Maintenance Status:")
        print(f"   Last updated: {status.get('last_updated', 'Unknown')}")

        # Link health
        link_health = status.get("link_health", {})
        if link_health:
            print(f"\n🔗 Link Health:")
            print(f"   Total links: {link_health.get('total_links', 0)}")
            print(f"   Valid links: {link_health.get('valid_links', 0)}")
            print(f"   Broken links: {link_health.get('broken_links', 0)}")
            print(f"   Health: {link_health.get('health_percentage', 0):.1f}%")

        # Pending updates
        pending_updates = status.get("pending_dependency_updates", 0)
        print(f"\n📦 Pending dependency updates: {pending_updates}")

        # Test 6: CMS content audit
        print("\n📝 Test 6: CMS content audit...")
        print("-" * 50)

        # Get all CMS content
        all_content = cms_manager.list_content()

        print(f"✅ CMS Content Audit:")
        print(f"   Total content items: {len(all_content)}")

        # Content type breakdown
        type_counts = {}
        for item in all_content:
            type_counts[item.content_type] = type_counts.get(item.content_type, 0) + 1

        print(f"\n📋 Content by type:")
        for content_type, count in type_counts.items():
            print(f"   {content_type}: {count}")

        # Status breakdown
        status_counts = {}
        for item in all_content:
            status_counts[item.status] = status_counts.get(item.status, 0) + 1

        print(f"\n📋 Content by status:")
        for status_name, count in status_counts.items():
            print(f"   {status_name}: {count}")

        # Test 7: Integration workflow
        print("\n🔄 Test 7: Integration workflow...")
        print("-" * 50)

        # Simulate a complete maintenance workflow
        print("📋 Step 1: Creating content with potential issues...")

        # Create content with potential maintenance issues
        problematic_content = """<article>
<h1>Content with Potential Issues</h1>
<p>This content has some potential maintenance issues.</p>

<h2>Short Content Section</h2>
<p>Too short.</p>

<h2>Links Section</h2>
<ul>
<li><a href="https://www.example.com/broken">Broken external link</a></li>
<li><a href="/nonexistent-page">Broken internal link</a></li>
<li><a href="https://www.google.com">Valid link</a></li>
</ul>

<p>This content demonstrates how the maintenance engine can identify and report issues.</p>
</article>"""

        problematic_article = cms_manager.create_content(
            title="Content with Maintenance Issues",
            content=problematic_content,
            content_type="article",
            author="Integration Test",
            tags=["maintenance", "issues", "test"],
        )

        print(f"✅ Created problematic content: {problematic_article.id}")

        # Check for issues
        print("\n📋 Step 2: Running maintenance checks...")

        # Link check
        link_results = engine.link_checker.check_all_links([problematic_article])
        broken_count = len([r for r in link_results if not r.is_valid])

        print(f"   Link check: {broken_count} broken links found")

        # Content audit
        content_issues = []
        if len(problematic_article.content) < 200:
            content_issues.append("Content too short")
        if not problematic_article.tags:
            content_issues.append("No tags")

        print(f"   Content audit: {len(content_issues)} issues found")

        # Generate report
        print("\n📋 Step 3: Generating maintenance report...")

        report = {
            "generated_at": time.strftime("%Y-%m-%d %H:%M:%S"),
            "content_items": len(all_content),
            "broken_links": broken_count,
            "content_issues": len(content_issues),
            "pending_updates": len(updates),
            "overall_health": (
                "good"
                if broken_count == 0 and len(content_issues) == 0
                else "needs_attention"
            ),
        }

        print(f"✅ Maintenance report generated:")
        print(f"   Content items: {report['content_items']}")
        print(f"   Broken links: {report['broken_links']}")
        print(f"   Content issues: {report['content_issues']}")
        print(f"   Pending updates: {report['pending_updates']}")
        print(f"   Overall health: {report['overall_health']}")

        print(
            "\n🎉 Phase 2.4 Maintenance Engine integration test completed successfully!"
        )
        print("=" * 70)
        print("\n📋 Integration Summary:")
        print("✅ CMS content creation and management")
        print("✅ Link extraction from CMS content")
        print("✅ Automated link checking with CMS integration")
        print("✅ Dependency update checking")
        print("✅ Maintenance task scheduling")
        print("✅ Comprehensive status reporting")
        print("✅ Content audit and issue detection")
        print("✅ Integrated maintenance workflow")

        return True

    except Exception as e:
        print(f"❌ Error during Phase 2.4 integration test: {e}")
        import traceback

        traceback.print_exc()
        return False


def test_standalone_maintenance():
    """Test maintenance engine without CMS integration"""
    print("\n�� Testing Standalone Maintenance Engine")
    print("=" * 50)

    try:
        # Initialize engine without CMS
        engine = MaintenanceEngine("config/maintenance_config.json")

        # Test link checking on static files
        print("\n🔍 Testing link checking on static files...")
        results = engine.link_checker.check_all_links()

        print(f"✅ Standalone link check completed")
        print(f"   Total links checked: {len(results)}")
        print(f"   Valid links: {len([r for r in results if r.is_valid])}")
        print(f"   Broken links: {len([r for r in results if not r.is_valid])}")

        # Test dependency checking
        print("\n📦 Testing dependency checking...")
        updates = engine.dependency_manager.check_python_dependencies()

        print(f"✅ Dependency check completed")
        print(f"   Updates found: {len(updates)}")

        # Test maintenance status
        print("\n📊 Testing maintenance status...")
        status = engine.get_maintenance_status()

        print(f"✅ Maintenance status retrieved")
        print(f"   Last updated: {status.get('last_updated', 'Unknown')}")

        return True

    except Exception as e:
        print(f"❌ Error during standalone maintenance test: {e}")
        return False


if __name__ == "__main__":
    print("🚀 Starting Phase 2.4 Maintenance Engine Integration Tests")

    # Test integration
    integration_success = test_phase_2_4_integration()

    # Test standalone functionality
    standalone_success = test_standalone_maintenance()

    # Overall result
    if integration_success and standalone_success:
        print("\n🎉 All Phase 2.4 integration tests passed successfully!")
        sys.exit(0)
    else:
        print("\n❌ Some Phase 2.4 integration tests failed!")
        sys.exit(1)
