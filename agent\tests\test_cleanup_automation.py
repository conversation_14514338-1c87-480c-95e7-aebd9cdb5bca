#!/usr/bin/env python3
"""
Tests for automated cleanup functionality in CursorRulesEnforcer
"""
import tempfile
from pathlib import Path

import pytest

from agent.core.cursor_rules_enforcer import CursorRulesEnforcer


def test_detect_duplicate_files(tmp_path: Path):
    """Test duplicate file detection"""
    enforcer = CursorRulesEnforcer(str(tmp_path))
    
    # Create duplicate files
    file1 = tmp_path / "module1.py"
    file2 = tmp_path / "module2.py"
    
    content = "def hello():\n    return 'world'"
    file1.write_text(content)
    file2.write_text(content)
    
    duplicates = enforcer._detect_duplicate_files()
    assert len(duplicates) == 1
    assert (file1, file2) in duplicates or (file2, file1) in duplicates


def test_detect_obsolete_files(tmp_path: Path):
    """Test obsolete file detection"""
    enforcer = CursorRulesEnforcer(str(tmp_path))
    
    # Create obsolete files
    obsolete_files = [
        tmp_path / "old_module.py.bak",
        tmp_path / "temp_file.tmp",
        tmp_path / "backup_data.old",
    ]
    
    for file_path in obsolete_files:
        file_path.write_text("obsolete content")
    
    detected = enforcer._detect_obsolete_files()
    assert len(detected) == 3
    assert all(f in detected for f in obsolete_files)


def test_detect_legacy_files(tmp_path: Path):
    """Test legacy file detection"""
    enforcer = CursorRulesEnforcer(str(tmp_path))
    
    # Create legacy files by naming pattern
    legacy_file1 = tmp_path / "module_v1.py"
    legacy_file1.write_text("# Old version")
    
    # Create legacy file by content marker
    legacy_file2 = tmp_path / "deprecated_module.py"
    legacy_file2.write_text("# @deprecated\ndef old_function():\n    pass")
    
    detected = enforcer._detect_legacy_files()
    assert len(detected) >= 2
    assert legacy_file1 in detected
    assert legacy_file2 in detected


def test_detect_temp_files(tmp_path: Path):
    """Test temporary file detection"""
    enforcer = CursorRulesEnforcer(str(tmp_path))
    
    # Create temp files
    temp_files = [
        tmp_path / "temp_data.tmp",
        tmp_path / "output.log",
        tmp_path / "cache.temp",
    ]
    
    for file_path in temp_files:
        file_path.write_text("temp content")
    
    # Create temp directory
    pycache_dir = tmp_path / "__pycache__"
    pycache_dir.mkdir()
    
    detected = enforcer._detect_temp_files()
    assert len(detected) >= 4  # 3 files + 1 directory
    assert all(f in detected for f in temp_files)
    assert pycache_dir in detected


def test_generate_cleanup_actions(tmp_path: Path):
    """Test cleanup action generation"""
    enforcer = CursorRulesEnforcer(str(tmp_path))
    
    # Mock data
    duplicates = [(tmp_path / "file1.py", tmp_path / "file2.py")]
    obsolete_files = [tmp_path / "old.bak"]
    legacy_files = [tmp_path / "legacy_v1.py"]
    temp_files = [tmp_path / "temp.tmp"]
    
    actions = enforcer._generate_cleanup_actions(duplicates, obsolete_files, legacy_files, temp_files)
    
    assert len(actions) == 4
    
    # Check action types
    action_types = {action["type"] for action in actions}
    expected_types = {"consolidate_duplicate", "remove_obsolete", "archive_legacy", "clean_temp"}
    assert action_types == expected_types
    
    # Check priorities
    priorities = {action["priority"] for action in actions}
    assert "high" in priorities
    assert "medium" in priorities
    assert "low" in priorities


def test_file_cleanup_integration(tmp_path: Path):
    """Test the integrated file cleanup check"""
    enforcer = CursorRulesEnforcer(str(tmp_path))
    
    # Create various problematic files
    # Duplicates
    file1 = tmp_path / "dup1.py"
    file2 = tmp_path / "dup2.py"
    content = "def test(): pass"
    file1.write_text(content)
    file2.write_text(content)
    
    # Obsolete
    obsolete = tmp_path / "old.bak"
    obsolete.write_text("old content")
    
    # Legacy
    legacy = tmp_path / "legacy_v1.py"
    legacy.write_text("# @deprecated\ndef old_func(): pass")
    
    # Temp
    temp = tmp_path / "temp.tmp"
    temp.write_text("temp content")
    
    result = enforcer._check_file_cleanup()
    
    # Should have violations for duplicates and obsolete files
    assert result["status"] == "failed"
    assert len(result["violations"]) >= 2
    assert result["duplicates"] >= 1
    assert result["obsolete_files"] >= 1
    assert result["legacy_files"] >= 1
    assert result["temp_files"] >= 1
    assert len(result["cleanup_actions"]) >= 4
