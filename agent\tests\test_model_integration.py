#!/usr/bin/env python3
"""
Test script to verify model integration in AI Coding Agent
Tests deepseek-coder:1.3b and qwen2.5-coder:3b integration
"""

import asyncio
import os
import sys
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agent.models.model_manager import model_manager
from agent.models.model_router import ModelRouter


async def test_model_integration():
    """Test that models are properly integrated"""
    print("🔍 Testing Model Integration in AI Coding Agent")
    print("=" * 60)

    try:
        # Initialize model router
        print("📋 Initializing Model Router...")
        router = ModelRouter()

        # Check available models
        print("\n📋 Available models from router:")
        available_models = router.get_available_models()
        for model in available_models:
            print(f"   ✅ {model}")

        # Check if our target models are available
        target_models = ["deepseek-coder:6.7b-instruct", "qwen2.5:3b"]
        missing_models = []

        for model in target_models:
            if model in available_models:
                print(f"   ✅ {model} - AVAILABLE")
            else:
                print(f"   ❌ {model} - NOT FOUND")
                missing_models.append(model)

        if missing_models:
            print(f"\n⚠️  Missing models: {missing_models}")
            return False

        # Test model selection for different task types
        print("\n🤖 Testing Model Selection:")

        # Test code generation (should use deepseek-coder:1.3b)
        code_model = router.select_model("code_generation")
        print(f"   Code Generation: {code_model}")

        # Test content creation (should use qwen2.5-coder:3b)
        content_model = router.select_model("content_creation")
        print(f"   Content Creation: {content_model}")

        # Test direct model calls
        print("\n🧪 Testing Direct Model Calls:")

        # Test deepseek-coder:6.7b-instruct
        print("   Testing deepseek-coder:6.7b-instruct...")
        try:
            response = await router.generate_response(
                "deepseek-coder:6.7b-instruct",
                "Write a simple Python function to calculate fibonacci numbers.",
            )
            print(f"   ✅ deepseek-coder:6.7b-instruct response: {response[:100]}...")
        except Exception as e:
            print(f"   ❌ deepseek-coder:6.7b-instruct failed: {str(e)}")

        # Test qwen2.5:3b
        print("   Testing qwen2.5:3b...")
        try:
            response = await router.generate_response(
                "qwen2.5:3b", "Explain what is machine learning in simple terms."
            )
            print(f"   ✅ qwen2.5:3b response: {response[:100]}...")
        except Exception as e:
            print(f"   ❌ qwen2.5:3b failed: {str(e)}")

        # Test model manager directly
        print("\n🔧 Testing Model Manager:")
        try:
            response = await model_manager.generate(
                "Write a hello world function in Python.",
                model_name="deepseek-coder:6.7b-instruct",
            )
            print(
                f"   ✅ Model Manager with deepseek-coder:6.7b-instruct: {response[:100]}..."
            )
        except Exception as e:
            print(f"   ❌ Model Manager failed: {str(e)}")

        print("\n✅ Model integration test completed successfully!")
        return True

    except Exception as e:
        print(f"\n❌ Model integration test failed: {str(e)}")
        return False


async def test_model_configuration():
    """Test model configuration loading"""
    print("\n📋 Testing Model Configuration:")
    print("=" * 40)

    try:
        router = ModelRouter()

        # Check model configuration
        print("   Model configuration loaded successfully")
        print(f"   Number of configured models: {len(router.config['models'])}")

        # Check specific model configs
        for model_name in ["deepseek-coder:6.7b-instruct", "qwen2.5:3b"]:
            if model_name in router.config["models"]:
                config = router.config["models"][model_name]
                print(f"   ✅ {model_name}:")
                print(f"      - Type: {config.get('type', 'unknown')}")
                print(f"      - Use cases: {config.get('use_cases', [])}")
                print(f"      - Endpoint: {config.get('endpoint', 'unknown')}")
            else:
                print(f"   ❌ {model_name}: Not found in configuration")

        return True

    except Exception as e:
        print(f"   ❌ Configuration test failed: {str(e)}")
        return False


async def main():
    """Main test function"""
    print("🚀 AI Coding Agent Model Integration Test")
    print("=" * 60)

    # Test configuration
    config_success = await test_model_configuration()

    # Test integration
    integration_success = await test_model_integration()

    # Summary
    print("\n" + "=" * 60)
    print("📊 Test Summary:")
    print(f"   Configuration: {'✅ PASS' if config_success else '❌ FAIL'}")
    print(f"   Integration: {'✅ PASS' if integration_success else '❌ FAIL'}")

    if config_success and integration_success:
        print("\n🎉 All tests passed! Models are properly integrated.")
        return 0
    else:
        print("\n⚠️  Some tests failed. Check the output above for details.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
    sys.exit(exit_code)
