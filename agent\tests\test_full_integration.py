#!/usr/bin/env python3
"""
Full Integration Test for Advanced Learning Enhancements

This script tests the complete integration of advanced learning enhancements
including CLI commands, API routes, and local LLM integration.
"""

import asyncio
import json
import os
import sys
import time
from datetime import datetime
from pathlib import Path

import requests

# Fix Windows console encoding for emoji support
if hasattr(sys.stdout, "reconfigure"):
    # on Windows, switch the console to UTF-8 output to support emojis
    sys.stdout.reconfigure(encoding="utf-8", errors="replace")

# Add project root to Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))


def test_api_server_running():
    """Test if API server is running and accessible"""
    print("🧪 Testing API Server")
    print("=" * 50)

    try:
        # Test basic health endpoint
        response = requests.get("http://127.0.0.1:8000/health", timeout=30)
        if response.status_code == 200:
            print("✅ API server is running and accessible")
            health_data = response.json()
            print(f"   Status: {health_data.get('status', 'unknown')}")
            return True
        else:
            print(f"❌ API server returned status {response.status_code}")
            return False
    except requests.exceptions.ConnectionError:
        print("❌ API server is not running")
        print(
            "   Start the server with: uvicorn api.main:app --host 127.0.0.1 --port 8000"
        )
        return False
    except Exception as e:
        print(f"❌ Error testing API server: {e}")
        return False


def test_advanced_learning_endpoints():
    """Test advanced learning endpoints"""
    print("\n🧪 Testing Advanced Learning Endpoints")
    print("=" * 50)

    try:
        # Test learning summary endpoint
        response = requests.get(
            "http://127.0.0.1:8000/api/v1/advanced-learning/learning/summary",
            timeout=30,
        )
        if response.status_code == 200:
            print("✅ Advanced learning summary endpoint working")
            data = response.json()
            print(f"   Success: {data.get('success', False)}")
        else:
            print(
                f"⚠️ Advanced learning summary endpoint returned {response.status_code}"
            )

        # Test learning recommendations endpoint
        response = requests.get(
            "http://127.0.0.1:8000/api/v1/advanced-learning/learning/recommendations?query=test",
            timeout=30,
        )
        if response.status_code == 200:
            print("✅ Advanced learning recommendations endpoint working")
        else:
            print(
                f"⚠️ Advanced learning recommendations endpoint returned {response.status_code}"
            )

        return True
    except Exception as e:
        print(f"❌ Error testing advanced learning endpoints: {e}")
        return False


def test_advanced_learning_enhancements_endpoints():
    """Test advanced learning enhancements endpoints"""
    print("\n🧪 Testing Advanced Learning Enhancements Endpoints")
    print("=" * 50)

    try:
        # Test health endpoint
        response = requests.get(
            "http://127.0.0.1:8000/api/v1/advanced-learning-enhancements/health",
            timeout=30,
        )
        if response.status_code == 200:
            print("✅ Advanced learning enhancements health endpoint working")
            data = response.json()
            print(f"   Status: {data.get('status', 'unknown')}")
            print(f"   Version: {data.get('version', 'unknown')}")
            print(f"   Components: {len(data.get('components', []))}")
        else:
            print(
                f"❌ Advanced learning enhancements health endpoint returned {response.status_code}"
            )
            return False

        # Test status endpoint
        response = requests.get(
            "http://127.0.0.1:8000/api/v1/advanced-learning-enhancements/status",
            timeout=30,
        )
        if response.status_code == 200:
            print("✅ Advanced learning enhancements status endpoint working")
            data = response.json()
            if data.get("success"):
                components = data.get("components_status", {})
                print(
                    f"   Available components: {sum(1 for c in components.values() if c.get('available'))}/{len(components)}"
                )
        else:
            print(
                f"⚠️ Advanced learning enhancements status endpoint returned {response.status_code}"
            )

        # Test meta learning optimize endpoint
        test_data = {
            "model_performance": {
                "model1": [0.8, 0.85, 0.9],
                "model2": [0.7, 0.75, 0.8],
            }
        }
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/advanced-learning-enhancements/meta-learning/optimize",
            json=test_data,
            timeout=10,
        )
        if response.status_code == 200:
            print("✅ Meta learning optimize endpoint working")
            data = response.json()
            print(f"   Success: {data.get('success', False)}")
        else:
            print(f"⚠️ Meta learning optimize endpoint returned {response.status_code}")

        # Test Pareto solutions endpoint
        test_data = {
            "objectives": ["speed", "quality"],
            "current_performance": {"speed": 0.8, "quality": 0.9},
            "constraints": {"max_memory": 4096},
        }
        response = requests.post(
            "http://127.0.0.1:8000/api/v1/advanced-learning-enhancements/pareto/solutions",
            json=test_data,
            timeout=10,
        )
        if response.status_code == 200:
            print("✅ Pareto solutions endpoint working")
            data = response.json()
            print(f"   Success: {data.get('success', False)}")
        else:
            print(f"⚠️ Pareto solutions endpoint returned {response.status_code}")

        return True
    except Exception as e:
        print(f"❌ Error testing advanced learning enhancements endpoints: {e}")
        return False


async def test_cli_commands_integration():
    """Test CLI commands integration"""
    print("\n🧪 Testing CLI Commands Integration")
    print("=" * 50)

    try:
        from agent.cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        # Create a mock agent for testing
        class MockAgent:
            def __init__(self):
                # Mock components that return expected data
                class MockMetaLearningOptimizer:
                    def optimize_learning_rates(self, model_performance):
                        return {"model1": 0.001, "model2": 0.002}

                class MockParetoOptimizer:
                    def find_pareto_optimal_routing(
                        self, objectives, current_performance, constraints
                    ):
                        return [{"solution": 1}, {"solution": 2}, {"solution": 3}]

                class MockWorkloadPredictor:
                    def predict_demand_spikes(
                        self, historical_patterns, external_factors
                    ):
                        class MockForecast:
                            def __init__(self):
                                self.predicted_demand = 1.0
                                self.confidence = 0.85
                                self.time_horizon = 3600
                                self.risk_level = "low"
                                self.recommendations = ["scale_up"]

                        return MockForecast()

                class MockCascadePredictor:
                    def detect_cascade_risk(self, current_loads, failure_patterns):
                        class MockRiskAssessment:
                            def __init__(self):
                                self.risk_level = "medium"
                                self.risk_score = 0.6
                                self.vulnerable_components = ["model1"]
                                self.mitigation_strategies = ["load_balancing"]

                        return MockRiskAssessment()

                self.meta_learning_optimizer = MockMetaLearningOptimizer()
                self.pareto_optimizer = MockParetoOptimizer()
                self.workload_predictor = MockWorkloadPredictor()
                self.cascade_predictor = MockCascadePredictor()

        mock_agent = MockAgent()
        commands = AdvancedLearningEnhancementsCommands(mock_agent)

        # Test CLI commands
        result = await commands.optimize_learning_rates(
            {"model1": [0.8, 0.85, 0.9], "model2": [0.7, 0.75, 0.8]}
        )
        if result["success"]:
            print("✅ CLI optimize_learning_rates command working")
        else:
            print(
                f"❌ CLI optimize_learning_rates command failed: {result.get('error')}"
            )

        result = await commands.find_pareto_solutions(
            ["speed", "quality"], {"speed": 0.8, "quality": 0.9}, {"max_memory": 4096}
        )
        if result["success"]:
            print("✅ CLI find_pareto_solutions command working")
        else:
            print(f"❌ CLI find_pareto_solutions command failed: {result.get('error')}")

        result = await commands.predict_demand(
            {"requests_per_minute": [10, 15, 20, 25, 30]}, {"time_of_day": 0.8}
        )
        if result["success"]:
            print("✅ CLI predict_demand command working")
        else:
            print(f"❌ CLI predict_demand command failed: {result.get('error')}")

        result = await commands.detect_cascade_risk(
            {"model1": 0.8, "model2": 0.7}, {"model1": [0.1, 0.15, 0.2]}
        )
        if result["success"]:
            print("✅ CLI detect_cascade_risk command working")
        else:
            print(f"❌ CLI detect_cascade_risk command failed: {result.get('error')}")

        return True
    except Exception as e:
        print(f"❌ Error testing CLI commands integration: {e}")
        return False


async def test_local_llm_integration():
    """Test local LLM integration capabilities"""
    print("\n🧪 Testing Local LLM Integration")
    print("=" * 50)

    try:
        # Test that CLI commands return structured data for LLMs
        from agent.cli.advanced_learning_enhancements_commands import (
            AdvancedLearningEnhancementsCommands,
        )

        class MockAgent:
            def __init__(self):
                class MockComponent:
                    def optimize_learning_rates(self, model_performance):
                        return {"model1": 0.001, "model2": 0.002}

                self.meta_learning_optimizer = MockComponent()

        mock_agent = MockAgent()
        commands = AdvancedLearningEnhancementsCommands(mock_agent)

        # Test structured response format
        result = await commands.optimize_learning_rates(
            {"model1": [0.8, 0.85, 0.9], "model2": [0.7, 0.75, 0.8]}
        )

        # Check if response has LLM-friendly structure
        if isinstance(result, dict) and "success" in result:
            print("✅ CLI commands return structured responses for LLMs")
            print(f"   Success field: {result['success']}")
            if result.get("success"):
                print(f"   Models optimized: {result.get('models_optimized', 0)}")
                print(f"   Timestamp: {result.get('timestamp', 'N/A')}")
        else:
            print("❌ CLI commands don't return structured responses")
            return False

        # Test API endpoint for LLM integration
        response = requests.get(
            "http://127.0.0.1:8000/api/v1/advanced-learning-enhancements/health",
            timeout=30,
        )
        if response.status_code == 200:
            data = response.json()
            if isinstance(data, dict) and "status" in data:
                print("✅ API endpoints return structured responses for LLMs")
                print(f"   Status: {data['status']}")
                print(f"   Service: {data.get('service', 'N/A')}")
                print(f"   Version: {data.get('version', 'N/A')}")
            else:
                print("❌ API endpoints don't return structured responses")
                return False
        else:
            print(f"❌ API health endpoint not accessible: {response.status_code}")
            return False

        return True
    except Exception as e:
        print(f"❌ Error testing local LLM integration: {e}")
        return False


def test_documentation_access():
    """Test API documentation access"""
    print("\n🧪 Testing API Documentation")
    print("=" * 50)

    try:
        # Test Swagger UI
        response = requests.get("http://127.0.0.1:8000/docs", timeout=30)
        if response.status_code == 200:
            print("✅ Swagger UI documentation accessible")
        else:
            print(f"⚠️ Swagger UI returned {response.status_code}")

        # Test ReDoc
        response = requests.get("http://127.0.0.1:8000/redoc", timeout=30)
        if response.status_code == 200:
            print("✅ ReDoc documentation accessible")
        else:
            print(f"⚠️ ReDoc returned {response.status_code}")

        # Test OpenAPI JSON
        response = requests.get("http://127.0.0.1:8000/openapi.json", timeout=30)
        if response.status_code == 200:
            print("✅ OpenAPI JSON schema accessible")
            data = response.json()
            paths = data.get("paths", {})
            enhancement_paths = [
                path
                for path in paths.keys()
                if "advanced-learning-enhancements" in path
            ]
            print(
                f"   Found {len(enhancement_paths)} advanced learning enhancement endpoints"
            )
        else:
            print(f"⚠️ OpenAPI JSON returned {response.status_code}")

        return True
    except Exception as e:
        print(f"❌ Error testing API documentation: {e}")
        return False


async def main():
    """Run all integration tests"""
    print("🚀 Full Integration Test Suite")
    print("=" * 60)

    tests = [
        ("API Server", test_api_server_running),
        ("Advanced Learning Endpoints", test_advanced_learning_endpoints),
        (
            "Advanced Learning Enhancements Endpoints",
            test_advanced_learning_enhancements_endpoints,
        ),
        ("CLI Commands Integration", test_cli_commands_integration),
        ("Local LLM Integration", test_local_llm_integration),
        ("API Documentation", test_documentation_access),
    ]

    results = []

    for test_name, test_func in tests:
        print(f"\n📋 Running: {test_name}")
        print("-" * 40)

        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()

            results.append((test_name, result))

            if result:
                print(f"✅ {test_name}: PASSED")
            else:
                print(f"❌ {test_name}: FAILED")

        except Exception as e:
            print(f"❌ {test_name}: ERROR - {e}")
            results.append((test_name, False))

    # Summary
    print("\n📊 Integration Test Results Summary")
    print("=" * 40)

    passed = sum(1 for _, result in results if result)
    total = len(results)

    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status} {test_name}")

    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")

    if passed == total:
        print(
            "🎉 All integration tests passed! Advanced Learning Enhancements are fully integrated and working."
        )
        print("\n📋 Integration Summary:")
        print("✅ API server running and accessible")
        print("✅ All endpoints responding correctly")
        print("✅ CLI commands functional")
        print("✅ Local LLM integration ready")
        print("✅ Documentation accessible")
        print("✅ System ready for production use")

        print("\n🚀 Next Steps:")
        print("1. Start using CLI commands in your applications")
        print("2. Integrate API endpoints with your frontend")
        print("3. Connect local Ollama LLMs to the system")
        print("4. Monitor system performance and learning")
        print("5. Explore advanced features like federated learning")

        return True
    else:
        print("⚠️  Some integration tests failed. Please check the issues above.")
        print("\n🔧 Troubleshooting:")
        print(
            "1. Ensure API server is running: uvicorn api.main:app --host 127.0.0.1 --port 8000"
        )
        print("2. Check virtual environment is activated")
        print("3. Verify all dependencies are installed")
        print("4. Review error messages for specific issues")
        return False


if __name__ == "__main__":
    asyncio.run(main())
