"""
Unit tests for environment variable management utility
"""

import os
import tempfile
from pathlib import Path
from unittest.mock import mock_open, patch

import pytest

from agent.utils.environment import EnvironmentManager, get_env


class TestEnvironmentManager:
    """Test cases for EnvironmentManager"""

    def test_singleton_behavior(self):
        """Test that EnvironmentManager is a singleton"""
        env1 = EnvironmentManager()
        env2 = EnvironmentManager()
        assert env1 is env2

    def test_get_string_value(self):
        """Test getting string environment variable"""
        with patch.dict("os.environ", {"TEST_STRING": "hello_world"}):
            env = get_env()
            assert env.get("TEST_STRING") == "hello_world"

    def test_get_with_default(self):
        """Test getting variable with default value"""
        env = get_env()
        assert env.get("NONEXISTENT_VAR", "default") == "default"

    def test_get_required_exists(self):
        """Test getting required variable that exists"""
        with patch.dict("os.environ", {"REQUIRED_VAR": "exists"}):
            env = get_env()
            assert env.get_required("REQUIRED_VAR") == "exists"

    def test_get_required_missing(self):
        """Test getting required variable that doesn't exist"""
        env = get_env()
        with pytest.raises(
            ValueError, match="Required environment variable MISSING_VAR not found"
        ):
            env.get_required("MISSING_VAR")

    def test_get_bool_true_values(self):
        """Test boolean conversion for true values"""
        env = get_env()
        true_values = ["true", "True", "1", "yes", "on", "TRUE"]

        for value in true_values:
            with patch.dict("os.environ", {"TEST_BOOL": value}):
                assert env.get_bool("TEST_BOOL") is True

    def test_get_bool_false_values(self):
        """Test boolean conversion for false values"""
        env = get_env()
        false_values = ["false", "False", "0", "no", "off", "FALSE", "invalid"]

        for value in false_values:
            with patch.dict("os.environ", {"TEST_BOOL": value}):
                assert env.get_bool("TEST_BOOL") is False

    def test_get_bool_default(self):
        """Test boolean with default value"""
        env = get_env()
        assert env.get_bool("NONEXISTENT_BOOL", True) is True
        assert env.get_bool("NONEXISTENT_BOOL", False) is False

    def test_get_int_valid(self):
        """Test integer conversion with valid values"""
        with patch.dict("os.environ", {"TEST_INT": "42"}):
            env = get_env()
            assert env.get_int("TEST_INT") == 42

    def test_get_int_invalid(self):
        """Test integer conversion with invalid values"""
        with patch.dict("os.environ", {"TEST_INT": "invalid"}):
            env = get_env()
            assert env.get_int("TEST_INT", 100) == 100

    def test_get_int_default(self):
        """Test integer with default value"""
        env = get_env()
        assert env.get_int("NONEXISTENT_INT", 999) == 999

    def test_load_env_file(self, temp_dir):
        """Test loading .env file"""
        env_file = temp_dir / ".env"
        env_file.write_text("TEST_VAR=test_value\nANOTHER_VAR=another_value")

        with patch.dict("os.environ", clear=True):
            EnvironmentManager.reset()
            env = EnvironmentManager(str(env_file))

            assert os.environ.get("TEST_VAR") == "test_value"
            assert os.environ.get("ANOTHER_VAR") == "another_value"

    def test_get_config(self):
        """Test getting full configuration"""
        with patch.dict(
            "os.environ",
            {
                "DATABASE_URL": "postgresql://test",
                "SECRET_KEY": "test_secret",
                "DEBUG": "true",
                "PORT": "8080",
            },
        ):
            env = get_env()
            config = env.get_config()

            assert config["database_url"] == "postgresql://test"
            assert config["secret_key"] == "test_secret"
            assert config["debug"] is True
            assert config["port"] == 8080

    def test_empty_env_file(self, temp_dir):
        """Test handling empty .env file"""
        env_file = temp_dir / ".env"
        env_file.write_text("")

        with patch.dict("os.environ", clear=True):
            EnvironmentManager.reset()
            env = EnvironmentManager(str(env_file))
            # Should not crash

    def test_malformed_env_file(self, temp_dir):
        """Test handling malformed .env file"""
        env_file = temp_dir / ".env"
        env_file.write_text("INVALID_LINE_WITHOUT_EQUALS\nANOTHER_BAD_LINE")

        with patch.dict("os.environ", clear=True):
            EnvironmentManager.reset()
            env = EnvironmentManager(str(env_file))
            # Should not crash

    def test_env_file_with_comments(self, temp_dir):
        """Test .env file with comments"""
        env_file = temp_dir / ".env"
        env_file.write_text(
            """
        # This is a comment
        TEST_VAR=value
        # Another comment
        ANOTHER_VAR=another_value
        """
        )

        with patch.dict("os.environ", clear=True):
            EnvironmentManager.reset()
            env = EnvironmentManager(str(env_file))

            assert os.environ.get("TEST_VAR") == "value"
            assert os.environ.get("ANOTHER_VAR") == "another_value"

    def test_case_sensitivity(self):
        """Test case sensitivity of environment variables"""
        import platform

        with patch.dict(
            "os.environ", {"TEST_VAR": "uppercase", "test_var": "lowercase"}
        ):
            env = get_env()
            # On Windows, environment variables are case-insensitive
            if platform.system() == "Windows":
                # On Windows, the last one set wins
                assert env.get("TEST_VAR") == "lowercase"
                assert env.get("test_var") == "lowercase"
            else:
                assert env.get("TEST_VAR") == "uppercase"
                assert env.get("test_var") == "lowercase"

    def test_whitespace_handling(self):
        """Test handling of whitespace in values"""
        with patch.dict("os.environ", {"TEST_VAR": "  value with spaces  "}):
            env = get_env()
            assert env.get("TEST_VAR") == "  value with spaces  "

    def test_empty_values(self):
        """Test handling empty string values"""
        with patch.dict("os.environ", {"EMPTY_VAR": ""}):
            env = get_env()
            assert env.get("EMPTY_VAR") == ""

    def test_special_characters(self):
        """Test handling special characters in values"""
        with patch.dict(
            "os.environ", {"SPECIAL_VAR": "value!@#$%^&*()_+-=[]{}|;:,.<>?"}
        ):
            env = get_env()
            assert env.get("SPECIAL_VAR") == "value!@#$%^&*()_+-=[]{}|;:,.<>?"

    def test_unicode_values(self):
        """Test handling unicode values"""
        with patch.dict("os.environ", {"UNICODE_VAR": "测试值🔥"}):
            env = get_env()
            assert env.get("UNICODE_VAR") == "测试值🔥"


class TestEnvironmentIntegration:
    """Integration tests for environment management"""

    def test_full_configuration_workflow(self, temp_dir):
        """Test complete configuration workflow"""
        # Create .env file
        env_file = temp_dir / ".env"
        env_file.write_text(
            """
        DATABASE_URL=postgresql://user:pass@localhost/db
        SECRET_KEY=super_secret_key
        DEBUG=true
        PORT=5432
        """
        )

        with patch.dict("os.environ", clear=True):
            EnvironmentManager.reset()
            env = EnvironmentManager(str(env_file))

            config = env.get_config()

            assert config["database_url"] == "postgresql://user:pass@localhost/db"
            assert config["secret_key"] == "super_secret_key"
            assert config["debug"] is True
            assert config["port"] == 5432

    def test_override_precedence(self, temp_dir):
        """Test precedence of environment variables over .env file"""
        env_file = temp_dir / ".env"
        env_file.write_text("TEST_VAR=from_file")

        with patch.dict("os.environ", {"TEST_VAR": "from_env"}):
            EnvironmentManager.reset()
            env = EnvironmentManager(str(env_file))

            assert env.get("TEST_VAR") == "from_env"
