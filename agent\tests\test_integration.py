# tests/test_integration.py
"""
Integration tests for end-to-end workflows.
Tests complete workflows: site creation, monitoring, alerts, and CLI integration.
"""

import asyncio
import json
import pytest
import tempfile
from pathlib import Path
from unittest.mock import AsyncMock, MagicMock, patch

from agent.core.site_container_manager import SiteContainerManager
from agent.core.ai_container_manager import AIEnhancedContainerManager
from agent.monitoring.alerting_system import MonitoringManager
from agent.cli.external_hosting_commands import ExternalHostingCommands


@pytest.mark.integration
class TestEndToEndWorkflows:
    """Integration tests for complete workflows"""

    @pytest.fixture
    def temp_site_dir(self):
        """Create temporary site directory for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            site_path = Path(temp_dir) / "integration-test-site"
            site_path.mkdir()

            # Create a React site structure
            (site_path / "package.json").write_text(json.dumps({
                "name": "integration-test-site",
                "version": "1.0.0",
                "dependencies": {
                    "react": "^18.0.0",
                    "react-dom": "^18.0.0"
                },
                "scripts": {
                    "start": "react-scripts start",
                    "build": "react-scripts build"
                }
            }))

            (site_path / "src").mkdir()
            (site_path / "src" / "App.js").write_text("""
import React from 'react';

function App() {
  return (
    <div className="App">
      <h1>Integration Test Site</h1>
      <p>This is a test site for integration testing.</p>
    </div>
  );
}

export default App;
            """)

            (site_path / "public").mkdir()
            (site_path / "public" / "index.html").write_text("""
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8" />
    <title>Integration Test Site</title>
</head>
<body>
    <div id="root"></div>
</body>
</html>
            """)

            yield site_path

    @pytest.mark.asyncio
    async def test_complete_site_creation_workflow(self, temp_site_dir):
        """Test complete site creation workflow from start to finish"""
        site_name = "integration-test-site"

        # Initialize managers
        site_manager = SiteContainerManager()

        # Copy site to sites directory
        import shutil
        sites_dir = site_manager.sites_dir
        sites_dir.mkdir(exist_ok=True)
        target_site_dir = sites_dir / site_name
        if target_site_dir.exists():
            shutil.rmtree(target_site_dir)
        shutil.copytree(temp_site_dir, target_site_dir)

        with patch('docker.from_env') as mock_docker, \
             patch('subprocess.run') as mock_subprocess:
            # Mock Docker client
            mock_client = MagicMock()
            mock_docker.return_value = mock_client
            mock_client.containers.list.return_value = []
            mock_client.images.build.return_value = (MagicMock(), [])

            # Mock subprocess for build script
            mock_subprocess.return_value.returncode = 0
            mock_subprocess.return_value.stderr = ""

            # Test site creation
            site_config = {"port": 8080, "environment": "test"}
            result = await site_manager.create_site_container(
                site_name=site_name,
                site_config=site_config
            )

            assert result["success"] is True

            # Verify environment file was created
            env_file = Path(f".env.{site_name}")
            assert env_file.exists()

            # Verify compose file was created (may be in containers directory)
            compose_file = Path(f"docker-compose.{site_name}.yml")
            containers_compose_file = Path(f"containers/docker-compose.{site_name}.yml")

            # At least one should exist
            assert compose_file.exists() or containers_compose_file.exists()

            # Cleanup
            if env_file.exists():
                env_file.unlink()
            if compose_file.exists():
                compose_file.unlink()
            if containers_compose_file.exists():
                containers_compose_file.unlink()

    @pytest.mark.asyncio
    async def test_ai_enhanced_workflow(self, temp_site_dir):
        """Test AI-enhanced container creation workflow"""
        site_name = "ai-integration-test"

        # Initialize AI manager
        ai_manager = AIEnhancedContainerManager()

        # Copy site to sites directory
        import shutil
        sites_dir = ai_manager.sites_dir
        sites_dir.mkdir(exist_ok=True)
        target_site_dir = sites_dir / site_name
        if target_site_dir.exists():
            shutil.rmtree(target_site_dir)
        shutil.copytree(temp_site_dir, target_site_dir)

        # Test AI analysis (should work regardless of Ollama availability)
        analysis = await ai_manager.analyze_and_optimize_site(site_name)

        # Should either succeed with AI or fail gracefully
        assert isinstance(analysis, dict)
        assert "success" in analysis

        if analysis["success"]:
            assert "framework" in analysis
            assert "complexity_score" in analysis
        else:
            assert "error" in analysis

    @pytest.mark.asyncio
    async def test_monitoring_integration_workflow(self):
        """Test monitoring system integration"""
        # Initialize monitoring manager
        monitoring_manager = MonitoringManager(dashboard_port=8094)

        # Test status before starting
        status = monitoring_manager.get_monitoring_status()
        assert status["monitoring_active"] is False

        # Test that all components are initialized
        assert monitoring_manager.metrics_collector is not None
        assert monitoring_manager.health_system is not None
        assert monitoring_manager.alerting_system is not None
        assert monitoring_manager.dashboard is not None

    @pytest.mark.asyncio
    async def test_cli_integration_workflow(self, temp_site_dir):
        """Test CLI integration workflow"""
        # Initialize CLI commands with mock agent
        mock_agent = MagicMock()
        cli_commands = ExternalHostingCommands(mock_agent)

        # Test monitoring status
        status_result = await cli_commands.get_monitoring_status()
        assert status_result["success"] is True
        assert "monitoring_status" in status_result

        # Test AI analysis (if available)
        site_name = "cli-test-site"

        # Copy site to sites directory
        import shutil
        sites_dir = Path("sites")
        sites_dir.mkdir(exist_ok=True)
        target_site_dir = sites_dir / site_name
        if target_site_dir.exists():
            shutil.rmtree(target_site_dir)
        shutil.copytree(temp_site_dir, target_site_dir)

        # Test site performance analysis
        performance_result = await cli_commands.get_container_performance(
            site_name, minutes=60
        )

        # Should handle case where monitoring is not running
        assert isinstance(performance_result, dict)
        assert "success" in performance_result

    @pytest.mark.asyncio
    async def test_error_handling_workflow(self):
        """Test error handling in various scenarios"""
        site_manager = SiteContainerManager()

        # Test with non-existent site
        result = await site_manager.create_site_container(
            site_name="non-existent-site",
            site_config={"port": 8080}
        )

        assert result["success"] is False
        assert "error" in result
        assert "does not exist" in result["error"]

    @pytest.mark.asyncio
    async def test_port_allocation_workflow(self):
        """Test port allocation workflow"""
        site_manager = SiteContainerManager()

        # Test port allocation through port manager
        port1 = site_manager.port_manager.allocate_port("test-site-1")
        port2 = site_manager.port_manager.allocate_port("test-site-2")

        assert port1 != port2
        assert 8080 <= port1 <= 9000
        assert 8080 <= port2 <= 9000

        # Test port release
        site_manager.port_manager.release_port("test-site-1")
        site_manager.port_manager.release_port("test-site-2")

        # Test that ports are available again
        available_ports = site_manager.port_manager.get_available_ports()
        assert port1 in available_ports or not site_manager.port_manager._is_port_in_use(port1)
        assert port2 in available_ports or not site_manager.port_manager._is_port_in_use(port2)

    @pytest.mark.asyncio
    async def test_environment_file_workflow(self, temp_site_dir):
        """Test environment file creation and management"""
        site_manager = SiteContainerManager()
        site_name = "env-test-site"
        port = 8085

        # Test environment file creation
        env_file_path = await site_manager._create_site_env_file(site_name, port)

        assert env_file_path.exists()
        assert env_file_path.name == f".env.{site_name}"

        # Verify content
        env_content = env_file_path.read_text()
        assert f"SITE_NAME={site_name}" in env_content
        assert f"EXTERNAL_PORT={port}" in env_content
        assert "CONTAINER_PORT=80" in env_content

        # Cleanup
        env_file_path.unlink()

    @pytest.mark.asyncio
    async def test_dockerignore_generation_workflow(self, temp_site_dir):
        """Test .dockerignore generation workflow"""
        site_manager = SiteContainerManager()

        # Test characteristics detection using DockerfileGenerator
        from agent.core.site_container_manager import DockerfileGenerator
        dockerfile_gen = DockerfileGenerator()
        characteristics = dockerfile_gen._detect_site_characteristics(temp_site_dir)

        assert characteristics["framework"] == "react"
        assert characteristics["has_dependencies"] is True

        # Test .dockerignore generation
        dockerignore_path = await site_manager._create_site_dockerignore(
            temp_site_dir, characteristics
        )

        assert dockerignore_path.exists()
        dockerignore_content = dockerignore_path.read_text()

        # Verify common patterns
        assert "node_modules/" in dockerignore_content
        assert ".env*" in dockerignore_content
        assert "!.env.example" in dockerignore_content

    @pytest.mark.asyncio
    async def test_dockerfile_generation_workflow(self, temp_site_dir):
        """Test Dockerfile generation workflow"""
        site_manager = SiteContainerManager()
        site_name = "dockerfile-test"

        # Test Dockerfile generation
        dockerfile_path = await site_manager._create_site_dockerfile(site_name, temp_site_dir)

        assert dockerfile_path.exists()
        dockerfile_content = dockerfile_path.read_text()

        # Verify security features
        assert "USER appuser" in dockerfile_content
        assert "EXPOSE 80" in dockerfile_content

        # Verify health check
        assert "HEALTHCHECK" in dockerfile_content

    @pytest.mark.asyncio
    async def test_compose_generation_workflow(self):
        """Test docker-compose generation workflow"""
        site_manager = SiteContainerManager()
        site_name = "compose-test"
        port = 8086
        image_name = f"ai-coding-agent/{site_name}:latest"

        # Test compose file generation
        compose_path = await site_manager._create_site_compose(site_name, port, image_name)

        assert compose_path.exists()
        compose_content = compose_path.read_text()

        # Verify compose structure
        assert f"container_name: site-{site_name}" in compose_content
        assert f"{port}:80" in compose_content
        assert f".env.{site_name}" in compose_content
        assert "ai-coding-network" in compose_content

        # Verify resource limits
        assert "deploy:" in compose_content
        assert "resources:" in compose_content

        # Verify logging
        assert "logging:" in compose_content
        assert "max-size" in compose_content

        # Cleanup
        compose_path.unlink()


@pytest.mark.integration
class TestSystemIntegration:
    """System-level integration tests"""

    @pytest.mark.asyncio
    async def test_full_system_startup(self):
        """Test full system startup sequence"""
        # Test that all major components can be initialized
        site_manager = SiteContainerManager()
        ai_manager = AIEnhancedContainerManager()
        monitoring_manager = MonitoringManager()
        mock_agent = MagicMock()
        cli_commands = ExternalHostingCommands(mock_agent)

        # Verify all components are properly initialized
        assert site_manager is not None
        assert ai_manager is not None
        assert monitoring_manager is not None
        assert cli_commands is not None

        # Test basic functionality
        assert hasattr(site_manager, 'sites_dir')
        assert hasattr(ai_manager, 'ollama_available')
        assert hasattr(monitoring_manager, 'is_running')
        assert hasattr(cli_commands, 'monitoring_manager')

    @pytest.mark.asyncio
    async def test_configuration_consistency(self):
        """Test configuration consistency across components"""
        site_manager = SiteContainerManager()

        # Test port range consistency through port manager
        assert site_manager.port_manager.start_port == 8080
        assert site_manager.port_manager.end_port == 9000

        # Test sites directory consistency
        assert site_manager.sites_dir.name == "sites"

    @pytest.mark.asyncio
    async def test_error_propagation(self):
        """Test error propagation through the system"""
        mock_agent = MagicMock()
        cli_commands = ExternalHostingCommands(mock_agent)

        # Test error handling in CLI
        result = await cli_commands.get_container_performance("non-existent-site")

        # Should handle gracefully
        assert isinstance(result, dict)
        assert "success" in result


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-m", "integration"])
