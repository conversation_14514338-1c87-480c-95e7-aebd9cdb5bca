/**
 * Jest Configuration Test
 * Verifies that Jest is properly configured with all features
 */

import '@testing-library/jest-dom';
import { render, screen } from '@testing-library/react';
import userEvent from '@testing-library/user-event';

// Test component to verify React Testing Library
const TestComponent = ({ title, onClick }: { title: string; onClick: () => void }) => (
  <div>
    <h1 data-testid="title">{title}</h1>
    <button data-testid="button" onClick={onClick}>
      Click me
    </button>
  </div>
);

describe('Jest Configuration', () => {
  it('should have jest-dom matchers available', () => {
    const element = document.createElement('div');
    element.className = 'test-class';
    expect(element).toHaveClass('test-class');
    // Note: toBeInTheDocument() only works with elements that are actually in the DOM
    document.body.appendChild(element);
    expect(element).toBeInTheDocument();
    document.body.removeChild(element);
  });

  it('should be able to render React components', () => {
    const mockOnClick = jest.fn();
    render(<TestComponent title="Test Title" onClick={mockOnClick} />);

    expect(screen.getByTestId('title')).toBeInTheDocument();
    expect(screen.getByTestId('title')).toHaveTextContent('Test Title');
    expect(screen.getByTestId('button')).toBeInTheDocument();
  });

  it('should be able to simulate user interactions', async () => {
    const user = userEvent.setup();
    const mockOnClick = jest.fn();

    render(<TestComponent title="Test Title" onClick={mockOnClick} />);

    const button = screen.getByTestId('button');
    await user.click(button);

    expect(mockOnClick).toHaveBeenCalledTimes(1);
  });

  it('should have global test utilities available', () => {
    expect(global.testUtils).toBeDefined();
    expect(typeof global.testUtils.waitFor).toBe('function');
    expect(typeof global.testUtils.createMockData).toBe('function');
  });

  it('should be able to create mock data', () => {
    const mockUser = global.testUtils.createMockData('user', { name: 'Custom User' });
    expect(mockUser).toEqual({
      id: '1',
      name: 'Custom User',
      email: '<EMAIL>',
    });
  });

  it('should have Next.js router mocked', () => {
    // This test verifies that Next.js router is properly mocked
    expect(true).toBe(true);
  });

  it('should have window.matchMedia mocked', () => {
    const mediaQuery = window.matchMedia('(max-width: 768px)');
    expect(mediaQuery.matches).toBe(false);
    expect(typeof mediaQuery.addEventListener).toBe('function');
  });

  it('should have IntersectionObserver mocked', () => {
    const observer = new IntersectionObserver(() => {});
    expect(observer).toBeDefined();
    expect(typeof observer.observe).toBe('function');
    expect(typeof observer.unobserve).toBe('function');
  });

  it('should have ResizeObserver mocked', () => {
    const observer = new ResizeObserver(() => {});
    expect(observer).toBeDefined();
    expect(typeof observer.observe).toBe('function');
    expect(typeof observer.unobserve).toBe('function');
  });
});
