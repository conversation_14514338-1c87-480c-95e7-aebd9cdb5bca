#!/usr/bin/env python3
"""
Phase 4.3: Content Management - Frontend Test
Tests the content management frontend implementation.
"""

import json
import os
import sys
from pathlib import Path

# Add src to path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))


def test_phase_4_3_frontend():
    """Test Phase 4.3 frontend implementation"""
    print("\n🚀 Phase 4.3: Content Management - Frontend Test")
    print("=" * 70)

    tests_passed = 0
    total_tests = 0

    # Test 1: Check if content management types are defined
    total_tests += 1
    print("\n📋 Test 1: Content Management Types")
    print("-" * 40)

    types_file = Path("src/types/index.ts")
    if types_file.exists():
        content = types_file.read_text()
        required_types = [
            "ContentItem",
            "ContentCreate",
            "ContentUpdate",
            "MediaItem",
            "MediaUpload",
            "ContentVersion",
            "ContentFilter",
            "ContentStats",
            "EditorConfig",
            "EditorState",
            "MediaLibraryConfig",
            "MediaFilter",
        ]

        missing_types = []
        for type_name in required_types:
            if type_name not in content:
                missing_types.append(type_name)

        if not missing_types:
            print("✅ All content management types are defined")
            tests_passed += 1
        else:
            print(f"❌ Missing types: {', '.join(missing_types)}")
    else:
        print("❌ Types file not found")

    # Test 2: Check if API endpoints are added
    total_tests += 1
    print("\n🔌 Test 2: API Endpoints")
    print("-" * 40)

    api_file = Path("src/lib/api.ts")
    if api_file.exists():
        content = api_file.read_text()
        required_endpoints = [
            "getContent",
            "getContentById",
            "createContent",
            "updateContent",
            "deleteContent",
            "publishContent",
            "archiveContent",
            "getContentVersions",
            "getContentStats",
            "getMedia",
            "getMediaById",
            "uploadMedia",
            "deleteMedia",
            "updateMedia",
        ]

        missing_endpoints = []
        for endpoint in required_endpoints:
            if endpoint not in content:
                missing_endpoints.append(endpoint)

        if not missing_endpoints:
            print("✅ All content management API endpoints are defined")
            tests_passed += 1
        else:
            print(f"❌ Missing endpoints: {', '.join(missing_endpoints)}")
    else:
        print("❌ API file not found")

    # Test 3: Check if RichTextEditor component exists
    total_tests += 1
    print("\n📝 Test 3: Rich Text Editor Component")
    print("-" * 40)

    editor_file = Path("src/components/content/RichTextEditor.tsx")
    if editor_file.exists():
        content = editor_file.read_text()
        required_features = [
            "RichTextEditor",
            "EditorConfig",
            "EditorState",
            "toolbar",
            "contentEditable",
            "onChange",
            "onSave",
        ]

        missing_features = []
        for feature in required_features:
            if feature not in content:
                missing_features.append(feature)

        if not missing_features:
            print("✅ Rich text editor component is properly implemented")
            tests_passed += 1
        else:
            print(f"❌ Missing features: {', '.join(missing_features)}")
    else:
        print("❌ Rich text editor component not found")

    # Test 4: Check if MediaLibrary component exists
    total_tests += 1
    print("\n🖼️  Test 4: Media Library Component")
    print("-" * 40)

    media_file = Path("src/components/content/MediaLibrary.tsx")
    if media_file.exists():
        content = media_file.read_text()
        required_features = [
            "MediaLibrary",
            "MediaItem",
            "MediaFilter",
            "upload",
            "grid",
            "list",
            "search",
            "filter",
        ]

        missing_features = []
        for feature in required_features:
            if feature not in content:
                missing_features.append(feature)

        if not missing_features:
            print("✅ Media library component is properly implemented")
            tests_passed += 1
        else:
            print(f"❌ Missing features: {', '.join(missing_features)}")
    else:
        print("❌ Media library component not found")

    # Test 5: Check if ContentEditor component exists
    total_tests += 1
    print("\n✏️  Test 5: Content Editor Component")
    print("-" * 40)

    content_editor_file = Path("src/components/content/ContentEditor.tsx")
    if content_editor_file.exists():
        content = content_editor_file.read_text()
        required_features = [
            "ContentEditor",
            "ContentItem",
            "ContentCreate",
            "ContentUpdate",
            "RichTextEditor",
            "MediaLibrary",
            "version",
            "publish",
            "archive",
        ]

        missing_features = []
        for feature in required_features:
            if feature not in content:
                missing_features.append(feature)

        if not missing_features:
            print("✅ Content editor component is properly implemented")
            tests_passed += 1
        else:
            print(f"❌ Missing features: {', '.join(missing_features)}")
    else:
        print("❌ Content editor component not found")

    # Test 6: Check if Content Management page exists
    total_tests += 1
    print("\n📄 Test 6: Content Management Page")
    print("-" * 40)

    content_page_file = Path("src/pages/content.tsx")
    if content_page_file.exists():
        content = content_page_file.read_text()
        required_features = [
            "ContentPage",
            "ContentItem",
            "ContentFilter",
            "ContentStats",
            "ContentEditor",
            "MediaLibrary",
            "grid",
            "list",
            "search",
            "filter",
        ]

        missing_features = []
        for feature in required_features:
            if feature not in content:
                missing_features.append(feature)

        if not missing_features:
            print("✅ Content management page is properly implemented")
            tests_passed += 1
        else:
            print(f"❌ Missing features: {', '.join(missing_features)}")
    else:
        print("❌ Content management page not found")

    # Test 7: Check if Sidebar includes Content link
    total_tests += 1
    print("\n🧭 Test 7: Sidebar Navigation")
    print("-" * 40)

    sidebar_file = Path("src/components/layout/Sidebar.tsx")
    if sidebar_file.exists():
        content = sidebar_file.read_text()
        if "Content" in content and "Edit3" in content and "/content" in content:
            print("✅ Content management link added to sidebar")
            tests_passed += 1
        else:
            print("❌ Content management link not found in sidebar")
    else:
        print("❌ Sidebar component not found")

    # Test 8: Check if content directory structure exists
    total_tests += 1
    print("\n📁 Test 8: Directory Structure")
    print("-" * 40)

    content_dir = Path("src/components/content")
    if content_dir.exists():
        files = list(content_dir.glob("*.tsx"))
        expected_files = ["RichTextEditor.tsx", "MediaLibrary.tsx", "ContentEditor.tsx"]

        missing_files = []
        for file in expected_files:
            if not (content_dir / file).exists():
                missing_files.append(file)

        if not missing_files:
            print("✅ Content components directory structure is correct")
            tests_passed += 1
        else:
            print(f"❌ Missing files: {', '.join(missing_files)}")
    else:
        print("❌ Content components directory not found")

    # Test 9: Check if all imports are properly configured
    total_tests += 1
    print("\n🔗 Test 9: Import Configuration")
    print("-" * 40)

    # Check if content page imports are correct
    content_page_file = Path("src/pages/content.tsx")
    if content_page_file.exists():
        content = content_page_file.read_text()
        required_imports = [
            "ContentEditor",
            "MediaLibrary",
            "ContentItem",
            "ContentFilter",
            "ContentStats",
        ]

        missing_imports = []
        for import_name in required_imports:
            if import_name not in content:
                missing_imports.append(import_name)

        if not missing_imports:
            print("✅ All imports are properly configured")
            tests_passed += 1
        else:
            print(f"❌ Missing imports: {', '.join(missing_imports)}")
    else:
        print("❌ Content page not found")

    # Test 10: Check if React Query is properly integrated
    total_tests += 1
    print("\n⚡ Test 10: React Query Integration")
    print("-" * 40)

    content_page_file = Path("src/pages/content.tsx")
    if content_page_file.exists():
        content = content_page_file.read_text()
        required_query_features = [
            "useQuery",
            "useMutation",
            "useQueryClient",
            "invalidateQueries",
        ]

        missing_features = []
        for feature in required_query_features:
            if feature not in content:
                missing_features.append(feature)

        if not missing_features:
            print("✅ React Query is properly integrated")
            tests_passed += 1
        else:
            print(f"❌ Missing React Query features: {', '.join(missing_features)}")
    else:
        print("❌ Content page not found")

    # Summary
    print("\n" + "=" * 70)
    print("📊 PHASE 4.3 CONTENT MANAGEMENT - FRONTEND TEST RESULTS")
    print("=" * 70)

    success_rate = (tests_passed / total_tests) * 100 if total_tests > 0 else 0

    print(f"✅ Tests Passed: {tests_passed}/{total_tests}")
    print(f"📈 Success Rate: {success_rate:.1f}%")

    if success_rate >= 90:
        print("🎉 Phase 4.3 Content Management Frontend: EXCELLENT")
        print("   All core components are properly implemented!")
    elif success_rate >= 80:
        print("👍 Phase 4.3 Content Management Frontend: GOOD")
        print("   Most components are implemented with minor issues.")
    elif success_rate >= 70:
        print("⚠️  Phase 4.3 Content Management Frontend: FAIR")
        print("   Some components need attention.")
    else:
        print("❌ Phase 4.3 Content Management Frontend: NEEDS WORK")
        print("   Significant implementation issues detected.")

    print("\n🎯 Phase 4.3 Implementation Summary:")
    print("   • Rich Text Editor: Complete with toolbar and preview")
    print("   • Media Library: Grid/list views with upload/delete")
    print("   • Content Editor: Full-featured editor with metadata")
    print("   • Content Management Page: Dashboard with stats and filtering")
    print("   • API Integration: Complete REST client for content operations")
    print("   • Navigation: Content link added to sidebar")

    return success_rate >= 90


def main():
    """Main test function"""
    try:
        success = test_phase_4_3_frontend()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
