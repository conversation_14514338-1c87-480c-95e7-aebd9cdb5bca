#!/usr/bin/env python3
"""
Integration tests for AI Coding Agent orchestration system
"""
import asyncio
import json
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest

from agent.core.agents.architect_agent import ArchitectAgent
from agent.core.context.shared_context_manager import SharedContextManager
from agent.core.cursor_rules_enforcer import CursorRulesEnforcer
from agent.core.docker_first_policy import DockerFirstPolicyEnforcer
from agent.core.project_models import Roadmap, ProjectState
from agent.core.project_store import ProjectStore


@pytest.fixture
def temp_project_dir():
    with tempfile.TemporaryDirectory() as tmpdir:
        yield tmpdir


@pytest.fixture
def project_store(temp_project_dir):
    return ProjectStore(base_dir=temp_project_dir)


@pytest.fixture
def context_manager(temp_project_dir):
    return SharedContextManager(storage_dir=f"{temp_project_dir}/context")


@pytest.fixture
def sample_roadmap():
    return {
        "id": "test_roadmap",
        "title": "Test Project Roadmap",
        "description": "Integration test roadmap",
        "phases": [
            {
                "id": "phase_1",
                "name": "Planning",
                "description": "Project planning phase",
                "steps": [
                    {
                        "id": "step_1",
                        "name": "Requirements Analysis",
                        "description": "Analyze project requirements",
                        "tasks": [
                            {
                                "id": "task_1",
                                "name": "Gather Requirements",
                                "description": "Collect and document requirements",
                                "status": "pending",
                                "dependencies": [],
                                "estimated_hours": 4
                            }
                        ]
                    }
                ]
            }
        ]
    }


def test_project_store_integration(project_store, sample_roadmap):
    """Test project store integration with roadmap and state"""
    project_id = "integration_test_project"
    
    # Create and save roadmap
    roadmap = Roadmap.from_dict(sample_roadmap)
    project_store.save_roadmap(project_id, roadmap)
    
    # Create and save project state
    state = ProjectState(
        project_id=project_id,
        roadmap_id=sample_roadmap["id"],
        current_phase="planning",
        current_step="requirements",
        current_task="gather_requirements"
    )
    project_store.save_state(project_id, state)
    
    # Load and verify
    loaded_roadmap = project_store.load_roadmap(project_id)
    loaded_state = project_store.load_state(project_id)
    
    assert loaded_roadmap is not None
    assert loaded_roadmap.title == sample_roadmap["title"]
    assert loaded_state is not None
    assert loaded_state.project_id == project_id
    assert loaded_state.current_phase == "planning"


def test_context_manager_integration(context_manager):
    """Test context manager integration across multiple sessions"""
    # Create multiple sessions
    session1 = context_manager.create_session("session_1", {"type": "development"})
    session2 = context_manager.create_session("session_2", {"type": "testing"})
    
    # Set context in different sessions
    context_manager.set_context("session_1", "project_config", {"database": "postgresql"}, "architect_agent")
    context_manager.set_context("session_2", "test_config", {"framework": "pytest"}, "test_agent")
    
    # Set global context
    context_manager.set_context("session_1", "api_key", "secret_key", "security_agent", global_scope=True)
    
    # Verify session-specific context
    dev_config = context_manager.get_context("session_1", "project_config")
    test_config = context_manager.get_context("session_2", "test_config")
    
    assert dev_config["database"] == "postgresql"
    assert test_config["framework"] == "pytest"
    
    # Verify global context access from different sessions
    api_key_1 = context_manager.get_context("session_1", "api_key")
    api_key_2 = context_manager.get_context("session_2", "api_key")
    
    assert api_key_1 == "secret_key"
    assert api_key_2 == "secret_key"
    
    # Test search functionality
    search_results = context_manager.search_context("session_1", agent_id="architect_agent")
    assert len(search_results) >= 1
    assert any(entry.key == "project_config" for entry in search_results)


def test_docker_first_policy_integration():
    """Test Docker-First policy integration with enforcement"""
    enforcer = DockerFirstPolicyEnforcer()
    
    # Test valid containerized operation
    valid_context = {
        "operation": "create_website",
        "uses_site_container_manager": True,
        "containerized": True,
        "files": ["index.html", "app.js"]
    }
    
    result = enforcer.validate_container_operation("website_creation", valid_context)
    assert result is True
    assert len(enforcer.violations) == 0
    
    # Test invalid host execution
    invalid_context = {
        "operation": "start_server",
        "command": "python -m http.server 8000",
        "host_execution": True,
        "files": ["index.html"]
    }
    
    result = enforcer.validate_container_operation("server_start", invalid_context)
    assert result is False
    assert len(enforcer.violations) > 0
    
    # Test enforcement
    violation_report = enforcer.get_violation_report()
    assert violation_report["total_violations"] > 0
    assert len(violation_report["recommendations"]) > 0


def test_cursor_rules_enforcer_integration():
    """Test cursor rules enforcer integration with auto-fix"""
    enforcer = CursorRulesEnforcer()
    
    # Test compliance checking
    compliance_result = enforcer.check_compliance()
    assert "compliance_score" in compliance_result
    assert "violations" in compliance_result
    assert isinstance(compliance_result["compliance_score"], (int, float))
    
    # Test file cleanup checking
    cleanup_result = enforcer._check_file_cleanup()
    assert "status" in cleanup_result
    assert cleanup_result["status"] in ["passed", "failed"]
    
    # Test auto-fix functionality
    auto_fix_result = enforcer.auto_fix_cleanup_issues(dry_run=True)
    assert "success" in auto_fix_result
    assert "actions_taken" in auto_fix_result
    assert "dry_run" in auto_fix_result
    assert auto_fix_result["dry_run"] is True


@pytest.mark.asyncio
async def test_architect_agent_integration(temp_project_dir, sample_roadmap):
    """Test architect agent integration with project orchestration"""
    try:
        # Create mock config file
        config_path = Path(temp_project_dir) / "agent_config.json"
        config_data = {
            "model_settings": {
                "model_name": "test_model",
                "temperature": 0.1
            },
            "routing": {
                "strategy": "simple",
                "fallback_enabled": True
            }
        }
        config_path.write_text(json.dumps(config_data))
        
        # Initialize architect agent
        agent = ArchitectAgent(str(config_path))
        
        # Test roadmap-based start
        project_id = "integration_test_project"
        
        # Mock the actual orchestration to avoid complex dependencies
        with patch.object(agent, '_execute_roadmap_orchestration') as mock_execute:
            mock_execute.return_value = {
                "success": True,
                "project_id": project_id,
                "status": "started",
                "phases_created": 1
            }
            
            result = await agent.start_with_roadmap(project_id, sample_roadmap)
            
            assert result["success"] is True
            assert result["project_id"] == project_id
            mock_execute.assert_called_once()
        
        # Test project-based start
        with patch.object(agent, '_infer_and_execute_roadmap') as mock_infer:
            mock_infer.return_value = {
                "success": True,
                "project_id": project_id,
                "status": "started",
                "inferred_phases": 3
            }
            
            result = await agent.start_with_project(project_id, str(temp_project_dir))
            
            assert result["success"] is True
            assert result["project_id"] == project_id
            mock_infer.assert_called_once()
            
    except Exception as e:
        # If architect agent has complex dependencies, skip this test
        pytest.skip(f"Architect agent integration test skipped: {e}")


def test_end_to_end_project_workflow(project_store, context_manager, sample_roadmap):
    """Test end-to-end project workflow integration"""
    project_id = "e2e_test_project"
    
    # Step 1: Create project with roadmap
    roadmap = Roadmap.from_dict(sample_roadmap)
    project_store.save_roadmap(project_id, roadmap)
    
    # Step 2: Initialize project state
    state = ProjectState(
        project_id=project_id,
        roadmap_id=sample_roadmap["id"],
        current_phase="planning",
        current_step="requirements",
        current_task="gather_requirements"
    )
    project_store.save_state(project_id, state)
    
    # Step 3: Create context session for project
    session_id = f"project_{project_id}"
    session = context_manager.create_session(session_id, {
        "project_id": project_id,
        "type": "project_orchestration"
    })
    
    # Step 4: Set project context
    context_manager.set_context(
        session_id, 
        "project_roadmap", 
        sample_roadmap, 
        "architect_agent",
        tags={"project", "roadmap"}
    )
    
    context_manager.set_context(
        session_id,
        "current_state",
        {
            "phase": state.current_phase,
            "step": state.current_step,
            "task": state.current_task
        },
        "state_manager",
        tags={"project", "state"}
    )
    
    # Step 5: Verify project data integrity
    loaded_roadmap = project_store.load_roadmap(project_id)
    loaded_state = project_store.load_state(project_id)
    
    assert loaded_roadmap.title == sample_roadmap["title"]
    assert loaded_state.current_phase == "planning"
    
    # Step 6: Verify context data
    roadmap_context = context_manager.get_context(session_id, "project_roadmap")
    state_context = context_manager.get_context(session_id, "current_state")
    
    assert roadmap_context["title"] == sample_roadmap["title"]
    assert state_context["phase"] == "planning"
    
    # Step 7: Search project-related context
    project_entries = context_manager.search_context(session_id, tags={"project"})
    assert len(project_entries) >= 2
    
    # Step 8: Update project state
    state.current_step = "design"
    state.current_task = "create_architecture"
    project_store.save_state(project_id, state)
    
    # Step 9: Update context
    context_manager.set_context(
        session_id,
        "current_state",
        {
            "phase": state.current_phase,
            "step": state.current_step,
            "task": state.current_task
        },
        "state_manager",
        tags={"project", "state"}
    )
    
    # Step 10: Verify updates
    updated_state = project_store.load_state(project_id)
    updated_context = context_manager.get_context(session_id, "current_state")
    
    assert updated_state.current_step == "design"
    assert updated_context["step"] == "design"


def test_monitoring_integration():
    """Test monitoring system integration"""
    try:
        from agent.core.container_monitor_hooks import container_monitor_hooks
        
        # Test monitoring system initialization
        summary = container_monitor_hooks.get_monitoring_summary()
        assert "total_events" in summary
        assert "monitored_containers" in summary
        
        # Test event emission
        asyncio.run(container_monitor_hooks.emit_event(
            "test_event",
            "test_container",
            {"test": "data"}
        ))
        
        # Verify event was recorded
        events = container_monitor_hooks.get_container_events("test_container")
        assert len(events) >= 1
        assert events[-1].event_type == "test_event"
        
    except ImportError:
        pytest.skip("Container monitoring hooks not available")


def test_cli_api_integration():
    """Test CLI and API integration"""
    try:
        # Test CLI command loading
        from agent.cli.orchestration_commands import orchestration
        assert orchestration is not None
        
        # Test API routes loading
        from agent.api.orchestration_routes import router
        assert router is not None
        
        # Verify both provide similar functionality
        api_routes = [route.path for route in router.routes]
        
        # Check that major functionality is available in both
        assert any("projects" in route for route in api_routes)
        assert any("validation" in route for route in api_routes)
        assert any("context" in route for route in api_routes)
        
    except ImportError as e:
        pytest.skip(f"CLI/API integration test skipped: {e}")


def test_system_health_check():
    """Test overall system health and component integration"""
    health_status = {
        "components": {},
        "overall_status": "healthy"
    }
    
    # Test core components
    try:
        context_manager = SharedContextManager()
        health_status["components"]["context_manager"] = "healthy"
    except Exception as e:
        health_status["components"]["context_manager"] = f"unhealthy: {e}"
        health_status["overall_status"] = "degraded"
    
    try:
        enforcer = CursorRulesEnforcer()
        health_status["components"]["cursor_rules_enforcer"] = "healthy"
    except Exception as e:
        health_status["components"]["cursor_rules_enforcer"] = f"unhealthy: {e}"
        health_status["overall_status"] = "degraded"
    
    try:
        policy_enforcer = DockerFirstPolicyEnforcer()
        health_status["components"]["docker_first_policy"] = "healthy"
    except Exception as e:
        health_status["components"]["docker_first_policy"] = f"unhealthy: {e}"
        health_status["overall_status"] = "degraded"
    
    try:
        project_store = ProjectStore()
        health_status["components"]["project_store"] = "healthy"
    except Exception as e:
        health_status["components"]["project_store"] = f"unhealthy: {e}"
        health_status["overall_status"] = "degraded"
    
    # At least core components should be healthy
    healthy_components = [
        status for status in health_status["components"].values() 
        if status == "healthy"
    ]
    
    assert len(healthy_components) >= 3, f"Too many unhealthy components: {health_status}"
    
    print(f"System health check: {health_status['overall_status']}")
    for component, status in health_status["components"].items():
        print(f"  {component}: {status}")


if __name__ == "__main__":
    # Run basic integration tests
    print("Running integration tests...")
    
    test_system_health_check()
    
    print("Integration tests completed!")
