# Multi-stage build for Threat Detection Engine
FROM python:3.11-slim AS builder
ENV PIP_NO_CACHE_DIR=1
# Set working directory
WORKDIR /app

# Install system dependencies for building
RUN apt-get update && apt-get install -y --no-install-recommends curl \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
RUN python -m venv /opt/venv \
 && . /opt/venv/bin/activate \
 && pip install --upgrade pip \
 && pip install --no-cache-dir fastapi uvicorn aiohttp

# Production stage
FROM python:3.11-slim AS runtime
ENV PATH="/opt/venv/bin:$PATH" PYTHONUNBUFFERED=1 PYTHONPATH=/app
# Create non-root user for security
RUN addgroup -r threatdetection && adduser -r -g threatdetection threatdetection

# Set working directory
WORKDIR /app

# Install runtime dependencies
RUN apt-get update && apt-get install -y --no-install-recommends curl \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Copy Python packages from builder stage
COPY --from=builder /opt/venv /opt/venv

# Create necessary directories
RUN mkdir -p /app/data/threat-detection \
    /app/logs/threat-detection \
    /app/backups/threat-detection \
    /app/temp/threat-detection \
    /app/config

# Set ownership to non-root user
RUN chown -R threatdetection:threatdetection /app

# Switch to non-root user
USER threatdetection

# Set environment variables
ENV THREAT_DETECTION_ENABLED=true
ENV ENVIRONMENT=production
ENV LOG_LEVEL=INFO
ENV PORT=8085

# Expose port
EXPOSE 8085

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=40s --retries=3 \
    CMD curl -f http://localhost:8085/health || exit 1

# Create threat detection service entry point
RUN echo '#!/usr/bin/env python3\nfrom fastapi import FastAPI\nimport uvicorn\napp=FastAPI()\<EMAIL>("/health")\nasync def h():\n    return {"status":"healthy","service":"threat_detection_engine"}\nif __name__=="__main__":\n uvicorn.run("threat_detection_service:app",host="0.0.0.0",port=8085,log_level="info")' > /app/threat_detection_service.py

# Make the script executable
RUN chmod +x /app/threat_detection_service.py

# Set the entry point
ENTRYPOINT ["python", "/app/threat_detection_service.py"]
