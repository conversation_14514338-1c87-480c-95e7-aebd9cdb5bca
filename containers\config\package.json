{"name": "ai-coding-agent-frontend", "version": "1.0.0", "description": "Frontend for AI Coding Agent with upload functionality", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:ci": "jest --ci --coverage --watchAll=false", "test:debug": "jest --detect<PERSON><PERSON><PERSON><PERSON><PERSON> --forceExit", "test:update": "jest --updateSnapshot", "cypress:open": "cypress open", "cypress:run": "cypress run", "test:e2e": "cypress run --spec 'cypress/e2e/feedback_spec.cy.js'", "test:feedback": "cypress run --spec 'cypress/e2e/feedback_spec.cy.js' --headed"}, "dependencies": {"@monaco-editor/react": "^4.7.0", "@tailwindcss/postcss": "^4.1.11", "@tanstack/react-query": "^5.83.0", "autoprefixer": "^10.4.21", "axios": "^1.11.0", "clsx": "^2.1.1", "lucide-react": "^0.525.0", "next": "^14.0.0", "postcss": "^8.5.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.61.1", "react-hot-toast": "^2.5.2", "react-resizable-panels": "^3.0.3", "tailwind-merge": "^3.3.1", "tailwindcss": "^4.1.11", "typescript": "^5.0.0", "zustand": "^4.5.7"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.4", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/jest": "^30.0.0", "@types/node": "^20.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "babel-jest": "^30.0.5", "cypress": "^14.5.2", "cypress-file-upload": "^5.0.8", "eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "prettier": "^3.0.0"}, "engines": {"node": ">=18.0.0"}}