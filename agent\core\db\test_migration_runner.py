#!/usr/bin/env python3
"""
Tests for Alembic Migration Runner
"""

import pytest
import tempfile
import shutil
import os
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
import sqlalchemy as sa
from sqlalchemy import create_engine, MetaData, Table, Column, Integer, String
from sqlalchemy.orm import declarative_base

from agent.core.db.migration_runner import (
    MigrationRunner,
    MigrationStatus,
    MigrationInfo,
    MigrationResult
)


# Test models for migration testing
Base = declarative_base()

class UserModel(Base):
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False)
    email = Column(String(100), unique=True)


class TestMigrationRunner:
    """Test cases for MigrationRunner"""

    def setup_method(self):
        """Set up test fixtures"""
        # Create temporary directory for test projects
        self.temp_dir = tempfile.mkdtemp()
        self.projects_root = Path(self.temp_dir)
        self.project_name = "test_project"

        # Create migration runner with SQLite
        self.runner = MigrationRunner(
            project_name=self.project_name,
            projects_root=str(self.projects_root)
        )

    def teardown_method(self):
        """Clean up test fixtures"""
        # Close any open database connections
        try:
            if hasattr(self, 'runner') and self.runner:
                # Force close any SQLite connections
                import gc
                gc.collect()
        except:
            pass

        # Clean up temp directory with retry for Windows file locking
        if os.path.exists(self.temp_dir):
            try:
                shutil.rmtree(self.temp_dir)
            except PermissionError:
                # Retry after a short delay on Windows
                import time
                time.sleep(0.1)
                try:
                    shutil.rmtree(self.temp_dir)
                except:
                    pass  # Best effort cleanup

    def test_init_migration_runner(self):
        """Test migration runner initialization"""
        assert self.runner.project_name == self.project_name
        assert self.runner.projects_root == self.projects_root
        assert self.runner.project_dir == self.projects_root / self.project_name
        assert self.runner.migrations_dir == self.projects_root / self.project_name / "migrations"
        assert self.runner.database_url.startswith("sqlite:///")

    def test_ensure_project_structure(self):
        """Test project structure creation"""
        self.runner._ensure_project_structure()

        assert self.runner.project_dir.exists()
        assert self.runner.migrations_dir.exists()

    def test_init_migrations(self):
        """Test migration environment initialization"""
        result = self.runner.init_migrations()

        assert result.success is True
        assert "initialized" in result.message.lower()
        assert (self.runner.migrations_dir / "alembic.ini").exists()
        assert (self.runner.migrations_dir / "env.py").exists()

    def test_init_migrations_already_initialized(self):
        """Test initialization when already initialized"""
        # Initialize once
        self.runner.init_migrations()

        # Try to initialize again
        result = self.runner.init_migrations()

        assert result.success is True
        assert "already initialized" in result.message.lower()

    def test_generate_migration_without_init(self):
        """Test migration generation automatically initializes if needed"""
        result = self.runner.generate_migration("Initial migration", auto_generate=False)

        assert result.success is True
        assert result.revision is not None
        assert (self.runner.migrations_dir / "alembic.ini").exists()

    def test_generate_migration_with_message(self):
        """Test migration generation with custom message"""
        # Initialize first
        self.runner.init_migrations()

        result = self.runner.generate_migration("Add user table", auto_generate=False)

        assert result.success is True
        assert result.revision is not None
        assert "Add user table" in result.message

    def test_apply_migrations_empty(self):
        """Test applying migrations when none exist"""
        # Initialize migrations
        self.runner.init_migrations()

        result = self.runner.apply_migrations()

        assert result.success is True
        assert "head" in result.message

    def test_get_current_revision_no_database(self):
        """Test getting current revision when database doesn't exist"""
        revision = self.runner.get_current_revision()

        # Should return None for non-existent database
        assert revision is None

    def test_get_database_info(self):
        """Test getting database information"""
        info = self.runner.get_database_info()

        assert info["project_name"] == self.project_name
        assert info["database_url"] == self.runner.database_url
        assert "migrations_dir" in info
        assert "database_exists" in info

    def test_create_alembic_config(self):
        """Test Alembic configuration creation"""
        config = self.runner._create_alembic_config()

        assert config is not None
        assert config.get_main_option("sqlalchemy.url") == self.runner.database_url
        assert (self.runner.migrations_dir / "alembic.ini").exists()

    def test_migration_with_real_database(self):
        """Test complete migration workflow with real SQLite database"""
        # Initialize migrations
        init_result = self.runner.init_migrations()
        assert init_result.success

        # Create a simple migration
        migration_result = self.runner.generate_migration("Create users table", auto_generate=False)
        assert migration_result.success

        # Apply migrations
        apply_result = self.runner.apply_migrations()
        assert apply_result.success

        # Check current revision
        current_rev = self.runner.get_current_revision()
        assert current_rev is not None

        # Validate schema
        validation_result = self.runner.validate_database_schema()
        assert validation_result.success

    def test_migration_history_empty(self):
        """Test migration history when no migrations exist"""
        self.runner.init_migrations()

        history = self.runner.get_migration_history()

        assert isinstance(history, list)
        # May be empty or contain initial migration

    def test_database_backup_sqlite(self):
        """Test database backup for SQLite"""
        # Create a database first
        engine = create_engine(self.runner.database_url)
        with engine.connect() as conn:
            conn.execute(sa.text("CREATE TABLE test (id INTEGER)"))
            conn.commit()

        backup_path = self.runner.create_database_backup()

        assert backup_path is not None
        assert os.path.exists(backup_path)
        assert ".backup_" in backup_path

    def test_database_backup_non_sqlite(self):
        """Test database backup for non-SQLite databases"""
        # Create runner with PostgreSQL URL
        runner = MigrationRunner(
            project_name="test",
            database_url="postgresql://user:pass@localhost/db",
            projects_root=str(self.projects_root)
        )

        backup_path = runner.create_database_backup()

        assert backup_path is None

    def test_restore_database_backup(self):
        """Test database backup restoration"""
        # Create a database and backup
        engine = create_engine(self.runner.database_url)
        with engine.connect() as conn:
            conn.execute(sa.text("CREATE TABLE test (id INTEGER)"))
            conn.commit()

        backup_path = self.runner.create_database_backup()
        assert backup_path is not None

        # Modify database
        with engine.connect() as conn:
            conn.execute(sa.text("DROP TABLE test"))
            conn.commit()

        # Restore backup
        restore_result = self.runner.restore_database_backup(backup_path)
        assert restore_result is True

        # Verify restoration
        with engine.connect() as conn:
            tables = conn.execute(sa.text("SELECT name FROM sqlite_master WHERE type='table'")).fetchall()
            table_names = [table[0] for table in tables]
            assert "test" in table_names

    def test_cleanup_old_backups(self):
        """Test cleanup of old database backups"""
        # Create multiple backups
        engine = create_engine(self.runner.database_url)
        with engine.connect() as conn:
            conn.execute(sa.text("CREATE TABLE test (id INTEGER)"))
            conn.commit()

        backup_paths = []
        for i in range(7):  # Create 7 backups
            backup_path = self.runner.create_database_backup()
            if backup_path:
                backup_paths.append(backup_path)

        # Cleanup keeping only 3
        self.runner.cleanup_old_backups(keep_count=3)

        # Check that only 3 backups remain
        existing_backups = [path for path in backup_paths if os.path.exists(path)]
        assert len(existing_backups) <= 3

    def test_rollback_migrations(self):
        """Test migration rollback"""
        # Initialize and create migrations
        self.runner.init_migrations()

        # Generate and apply a migration
        gen_result = self.runner.generate_migration("Initial migration", auto_generate=False)
        assert gen_result.success

        apply_result = self.runner.apply_migrations()
        assert apply_result.success

        # Rollback to base
        rollback_result = self.runner.rollback_migrations("base")
        assert rollback_result.success

    def test_validate_database_schema_up_to_date(self):
        """Test schema validation when database is up to date"""
        # Initialize and apply all migrations
        self.runner.init_migrations()
        self.runner.apply_migrations()

        result = self.runner.validate_database_schema()

        assert result.success is True
        assert "up to date" in result.message.lower()

    def test_error_handling_invalid_database_url(self):
        """Test error handling with invalid database URL"""
        runner = MigrationRunner(
            project_name="test",
            database_url="invalid://url",
            projects_root=str(self.projects_root)
        )

        result = runner.apply_migrations()

        assert result.success is False
        assert result.error_analysis is not None

    def test_migration_info_dataclass(self):
        """Test MigrationInfo dataclass"""
        info = MigrationInfo(
            revision="abc123",
            description="Test migration",
            status=MigrationStatus.APPLIED,
            created_at="2023-01-01",
            applied_at="2023-01-02"
        )

        assert info.revision == "abc123"
        assert info.description == "Test migration"
        assert info.status == MigrationStatus.APPLIED
        assert info.created_at == "2023-01-01"
        assert info.applied_at == "2023-01-02"

    def test_migration_result_dataclass(self):
        """Test MigrationResult dataclass"""
        result = MigrationResult(
            success=True,
            message="Migration successful",
            revision="abc123",
            migrations_applied=["abc123", "def456"]
        )

        assert result.success is True
        assert result.message == "Migration successful"
        assert result.revision == "abc123"
        assert result.migrations_applied == ["abc123", "def456"]


class TestMigrationRunnerIntegration:
    """Integration tests for MigrationRunner"""

    def setup_method(self):
        """Set up test fixtures"""
        self.temp_dir = tempfile.mkdtemp()
        self.projects_root = Path(self.temp_dir)

    def teardown_method(self):
        """Clean up test fixtures"""
        if os.path.exists(self.temp_dir):
            # Force garbage collection to close any open database connections
            import gc
            import time
            gc.collect()
            time.sleep(0.1)  # Small delay to allow file handles to close
            try:
                shutil.rmtree(self.temp_dir)
            except PermissionError:
                # On Windows, sometimes files are still locked - try again after a short delay
                time.sleep(0.5)
                try:
                    shutil.rmtree(self.temp_dir)
                except PermissionError:
                    # If still locked, just log it - this is a test cleanup issue, not a functionality issue
                    print(f"Warning: Could not clean up temp directory {self.temp_dir} - files may be locked")

    def test_multiple_projects(self):
        """Test managing migrations for multiple projects"""
        project1 = MigrationRunner("project1", projects_root=str(self.projects_root))
        project2 = MigrationRunner("project2", projects_root=str(self.projects_root))

        # Initialize both projects
        result1 = project1.init_migrations()
        result2 = project2.init_migrations()

        assert result1.success
        assert result2.success

        # Verify separate migration directories
        assert project1.migrations_dir != project2.migrations_dir
        assert project1.migrations_dir.exists()
        assert project2.migrations_dir.exists()

    def test_custom_database_url(self):
        """Test migration runner with custom database URL"""
        custom_db_path = self.temp_dir + "/custom.db"
        custom_url = f"sqlite:///{custom_db_path}"

        runner = MigrationRunner(
            project_name="custom_project",
            database_url=custom_url,
            projects_root=str(self.projects_root)
        )

        assert runner.database_url == custom_url

        # Test that it works
        result = runner.init_migrations()
        assert result.success

    def test_migration_workflow_complete(self):
        """Test complete migration workflow from start to finish"""
        runner = MigrationRunner("workflow_test", projects_root=str(self.projects_root))

        # 1. Initialize
        init_result = runner.init_migrations()
        assert init_result.success

        # 2. Generate migration
        gen_result = runner.generate_migration("Create initial schema", auto_generate=False)
        assert gen_result.success

        # 3. Apply migrations
        apply_result = runner.apply_migrations()
        assert apply_result.success

        # 4. Check status
        current_rev = runner.get_current_revision()
        assert current_rev is not None

        # 5. Validate schema
        validation_result = runner.validate_database_schema()
        assert validation_result.success

        # 6. Get database info
        db_info = runner.get_database_info()
        assert db_info["project_name"] == "workflow_test"
        assert db_info["current_revision"] == current_rev


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
