"""
Tests for Enhanced WebsiteGenerator Async Implementation
"""

import asyncio
import json
import tempfile
import unittest
from datetime import datetime, timezone
from pathlib import Path
from typing import Any, Dict, List
from unittest.mock import AsyncMock, Mock, patch

import pytest

from agent.core.website_generator import (
    AssetPipeline,
    AsyncCacheManager,
    BuildProgress,
    BuildStatus,
    EnhancedWebsiteGenerator,
    ProgressCallback,
    WebsitePlugin,
)
from tools.templates.template_manager import TemplateManager


class MockTemplateManager:
    """Mock template manager for testing"""

    def get_template(self, template_name: str, theme_name: str) -> Dict[str, Any]:
        return {
            "name": template_name,
            "theme": theme_name,
            "files": {
                "index.html": "<!DOCTYPE html><html><head><title>Test</title></head><body><h1>Hello World</h1></body></html>",
                "style.css": "body { font-family: Arial, sans-serif; }",
                "script.js": "console.log('Hello World');",
            },
        }


class MockWebsitePlugin:
    """Mock website plugin for testing"""

    def __init__(self, name: str):
        self.name = name
        self.pre_build_called = False
        self.post_build_called = False
        self.error_called = False

    async def pre_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        self.pre_build_called = True
        return {"plugin": self.name, "action": "pre_build"}

    async def post_build(
        self, site_config: Dict[str, Any], site_dir: Path
    ) -> Dict[str, Any]:
        self.post_build_called = True
        return {"plugin": self.name, "action": "post_build"}

    async def on_error(self, error: Exception, site_config: Dict[str, Any]) -> None:
        self.error_called = True


class TestBuildProgress:
    """Test BuildProgress dataclass"""

    def test_build_progress_initialization(self):
        """Test BuildProgress initialization"""
        progress = BuildProgress()
        assert progress.status == BuildStatus.PENDING
        assert progress.progress_percent == 0.0
        assert progress.steps_completed == 0
        assert progress.total_steps == 0

    def test_build_progress_with_values(self):
        """Test BuildProgress with custom values"""
        progress = BuildProgress(
            status=BuildStatus.BUILDING,
            current_step="Testing",
            progress_percent=50.0,
            build_id="test_build",
            site_name="test_site",
            steps_completed=2,
            total_steps=4,
        )
        assert progress.status == BuildStatus.BUILDING
        assert progress.current_step == "Testing"
        assert progress.progress_percent == 50.0
        assert progress.build_id == "test_build"
        assert progress.site_name == "test_site"
        assert progress.steps_completed == 2
        assert progress.total_steps == 4


class TestAsyncCacheManager:
    """Test AsyncCacheManager"""

    @pytest.fixture
    def cache_manager(self):
        """Create a cache manager for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield AsyncCacheManager(cache_dir=temp_dir)

    def test_cache_key_generation(self, cache_manager):
        """Test cache key generation"""
        key = cache_manager.get_template_cache_key("modern", "dark")
        assert key == "modern_dark"

    @pytest.mark.asyncio
    async def test_cache_template_and_retrieve(self, cache_manager):
        """Test caching and retrieving templates"""
        template_data = {"name": "test", "content": "test content"}

        # Cache template
        await cache_manager.cache_template("test_template", "test_theme", template_data)

        # Retrieve template
        cached = await cache_manager.get_cached_template("test_template", "test_theme")
        assert cached == template_data

    @pytest.mark.asyncio
    async def test_get_nonexistent_template(self, cache_manager):
        """Test retrieving non-existent template"""
        cached = await cache_manager.get_cached_template("nonexistent", "theme")
        assert cached is None

    @pytest.mark.asyncio
    async def test_clear_cache(self, cache_manager):
        """Test clearing cache"""
        template_data = {"name": "test", "content": "test content"}

        # Cache template
        await cache_manager.cache_template("test_template", "test_theme", template_data)

        # Verify it's cached
        cached = await cache_manager.get_cached_template("test_template", "test_theme")
        assert cached == template_data

        # Clear cache
        await cache_manager.clear_cache()

        # Verify it's gone
        cached = await cache_manager.get_cached_template("test_template", "test_theme")
        assert cached is None


class TestAsyncAssetPipeline:
    """Test AssetPipeline"""

    @pytest.fixture
    def asset_pipeline(self):
        """Create an asset pipeline for testing"""
        with tempfile.TemporaryDirectory() as temp_dir:
            yield AssetPipeline(output_dir=temp_dir)

    @pytest.fixture
    def source_dir(self):
        """Create a source directory with test assets"""
        with tempfile.TemporaryDirectory() as temp_dir:
            source_path = Path(temp_dir)

            # Create test CSS file
            css_file = source_path / "style.css"
            css_file.write_text("body { font-family: Arial, sans-serif; }")

            # Create test JS file
            js_file = source_path / "script.js"
            js_file.write_text("console.log('Hello World');")

            yield source_path

    @pytest.mark.asyncio
    async def test_optimize_assets_async(self, asset_pipeline, source_dir):
        """Test asset optimization"""
        results = await asset_pipeline.optimize_assets_async(source_dir, "test_site")

        assert "images" in results
        assert "styles" in results
        assert "scripts" in results
        assert "optimization_time" in results
        assert results["optimization_time"] > 0

    @pytest.mark.asyncio
    async def test_optimize_styles_async(self, asset_pipeline, source_dir):
        """Test CSS optimization"""
        target_dir = Path(asset_pipeline.output_dir) / "test_site"
        target_dir.mkdir(exist_ok=True)

        results = await asset_pipeline._optimize_styles_async(source_dir, target_dir)

        assert "optimized" in results
        assert "total_size" in results
        assert "files_processed" in results
        assert results["files_processed"] >= 1

    @pytest.mark.asyncio
    async def test_optimize_scripts_async(self, asset_pipeline, source_dir):
        """Test JavaScript optimization"""
        target_dir = Path(asset_pipeline.output_dir) / "test_site"
        target_dir.mkdir(exist_ok=True)

        results = await asset_pipeline._optimize_scripts_async(source_dir, target_dir)

        assert "optimized" in results
        assert "total_size" in results
        assert "files_processed" in results
        assert results["files_processed"] >= 1

    @pytest.mark.asyncio
    async def test_cleanup(self, asset_pipeline):
        """Test cleanup"""
        await asset_pipeline.cleanup()
        # Should not raise any exceptions


class TestEnhancedWebsiteGenerator:
    """Test EnhancedWebsiteGenerator"""

    @pytest.fixture
    def generator(self):
        """Create a generator for testing"""
        template_manager = MockTemplateManager()
        return EnhancedWebsiteGenerator(
            template_manager, max_workers=2, max_concurrent_builds=2
        )

    @pytest.fixture
    def site_config(self):
        """Create a site configuration for testing"""
        return {"name": "test_site", "template": "modern", "theme": "default"}

    def test_generator_initialization(self, generator):
        """Test generator initialization"""
        assert generator.max_workers == 2
        assert generator.max_concurrent_builds == 2
        assert len(generator.plugins) == 0
        assert len(generator.progress_callbacks) == 0

    def test_add_plugin(self, generator):
        """Test adding plugins"""
        plugin = MockWebsitePlugin("test_plugin")
        generator.add_plugin(plugin)
        assert len(generator.plugins) == 1
        assert generator.plugins[0] == plugin

    def test_add_progress_callback(self, generator):
        """Test adding progress callbacks"""

        async def callback(progress: BuildProgress):
            pass

        generator.add_progress_callback(callback)
        assert len(generator.progress_callbacks) == 1
        assert callback in generator.progress_callbacks

    def test_remove_progress_callback(self, generator):
        """Test removing progress callbacks"""

        async def callback(progress: BuildProgress):
            pass

        generator.add_progress_callback(callback)
        assert len(generator.progress_callbacks) == 1

        generator.remove_progress_callback(callback)
        assert len(generator.progress_callbacks) == 0

    @pytest.mark.asyncio
    async def test_validate_site_config_async_valid(self, generator, site_config):
        """Test valid site configuration validation"""
        await generator._validate_site_config_async(site_config)
        # Should not raise any exceptions

    @pytest.mark.asyncio
    async def test_validate_site_config_async_invalid(self, generator):
        """Test invalid site configuration validation"""
        invalid_config = {"name": "test"}  # Missing template

        with pytest.raises(ValueError, match="Missing required field: template"):
            await generator._validate_site_config_async(invalid_config)

    @pytest.mark.asyncio
    async def test_create_site_directory_async(self, generator, site_config):
        """Test site directory creation"""
        # Test that the method runs without errors and returns a Path
        site_dir = await generator._create_site_directory_async(site_config)
        assert isinstance(site_dir, Path)
        assert site_dir.name == "test_site"
        assert site_dir.parent.name == "sites"

    @pytest.mark.asyncio
    async def test_run_pre_build_plugins_async(self, generator, site_config):
        """Test pre-build plugins execution"""
        plugin = MockWebsitePlugin("test_plugin")
        generator.add_plugin(plugin)

        with tempfile.TemporaryDirectory() as temp_dir:
            site_dir = Path(temp_dir)
            await generator._run_pre_build_plugins_async(site_config, site_dir)

            assert plugin.pre_build_called
            assert not plugin.post_build_called
            assert not plugin.error_called

    @pytest.mark.asyncio
    async def test_run_post_build_plugins_async(self, generator, site_config):
        """Test post-build plugins execution"""
        plugin = MockWebsitePlugin("test_plugin")
        generator.add_plugin(plugin)

        with tempfile.TemporaryDirectory() as temp_dir:
            site_dir = Path(temp_dir)
            await generator._run_post_build_plugins_async(site_config, site_dir)

            assert not plugin.pre_build_called
            assert plugin.post_build_called
            assert not plugin.error_called

    @pytest.mark.asyncio
    async def test_run_error_plugins_async(self, generator, site_config):
        """Test error plugins execution"""
        plugin = MockWebsitePlugin("test_plugin")
        generator.add_plugin(plugin)

        error = Exception("Test error")
        await generator._run_error_plugins_async(error, site_config)

        assert not plugin.pre_build_called
        assert not plugin.post_build_called
        assert plugin.error_called

    @pytest.mark.asyncio
    async def test_get_build_status(self, generator):
        """Test getting build status"""
        build_id = "test_build"
        progress = BuildProgress(build_id=build_id)
        generator.active_builds[build_id] = progress

        result = await generator.get_build_status(build_id)
        assert result == progress

        # Test non-existent build
        result = await generator.get_build_status("nonexistent")
        assert result is None

    @pytest.mark.asyncio
    async def test_cancel_build(self, generator):
        """Test build cancellation"""
        build_id = "test_build"
        progress = BuildProgress(build_id=build_id, status=BuildStatus.BUILDING)
        generator.active_builds[build_id] = progress

        # Cancel build
        result = await generator.cancel_build(build_id)
        assert result is True
        assert progress.status == BuildStatus.CANCELLED

        # Test cancelling non-existent build
        result = await generator.cancel_build("nonexistent")
        assert result is False

    @pytest.mark.asyncio
    async def test_list_active_builds(self, generator):
        """Test listing active builds"""
        build_id = "test_build"
        progress = BuildProgress(build_id=build_id)
        generator.active_builds[build_id] = progress

        result = await generator.list_active_builds()
        assert build_id in result
        assert result[build_id] == progress

    @pytest.mark.asyncio
    async def test_create_website_async_success(self, generator, site_config):
        """Test successful website creation"""
        # Mock template manager
        with patch.object(
            generator.template_manager, "get_template"
        ) as mock_get_template:
            mock_get_template.return_value = {
                "name": "modern",
                "theme": "default",
                "files": {"index.html": "<html></html>"},
            }

            # Mock file operations
            with patch("pathlib.Path.mkdir"), patch(
                "pathlib.Path.exists", return_value=False
            ), patch("shutil.rmtree"), patch("builtins.open", create=True), patch(
                "json.dump"
            ):

                result = await generator.create_website_async(site_config)

                assert result["success"] is True
                assert result["site_name"] == "test_site"
                assert "site_path" in result
                assert "manifest" in result
                assert "asset_results" in result

    @pytest.mark.asyncio
    async def test_create_website_async_with_progress_callback(
        self, generator, site_config
    ):
        """Test website creation with progress callback"""
        progress_updates = []

        async def progress_callback(progress: BuildProgress):
            progress_updates.append(progress)

        # Mock template manager
        with patch.object(
            generator.template_manager, "get_template"
        ) as mock_get_template:
            mock_get_template.return_value = {
                "name": "modern",
                "theme": "default",
                "files": {"index.html": "<html></html>"},
            }

            # Mock file operations
            with patch("pathlib.Path.mkdir"), patch(
                "pathlib.Path.exists", return_value=False
            ), patch("shutil.rmtree"), patch("builtins.open", create=True), patch(
                "json.dump"
            ):

                result = await generator.create_website_async(
                    site_config, progress_callback
                )

                assert result["success"] is True
                assert len(progress_updates) > 0

                # Check that progress was tracked
                for progress in progress_updates:
                    assert isinstance(progress, BuildProgress)
                    assert progress.build_id != ""
                    assert progress.site_name == "test_site"

    @pytest.mark.asyncio
    async def test_create_website_async_with_plugins(self, generator, site_config):
        """Test website creation with plugins"""
        plugin = MockWebsitePlugin("test_plugin")
        generator.add_plugin(plugin)

        # Mock template manager
        with patch.object(
            generator.template_manager, "get_template"
        ) as mock_get_template:
            mock_get_template.return_value = {
                "name": "modern",
                "theme": "default",
                "files": {"index.html": "<html></html>"},
            }

            # Mock file operations
            with patch("pathlib.Path.mkdir"), patch(
                "pathlib.Path.exists", return_value=False
            ), patch("shutil.rmtree"), patch("builtins.open", create=True), patch(
                "json.dump"
            ):

                result = await generator.create_website_async(site_config)

                assert result["success"] is True
                assert plugin.pre_build_called
                assert plugin.post_build_called
                assert not plugin.error_called

    @pytest.mark.asyncio
    async def test_create_website_async_error(self, generator, site_config):
        """Test website creation with error"""
        # Mock template manager to raise an error
        with patch.object(
            generator.template_manager, "get_template"
        ) as mock_get_template:
            mock_get_template.side_effect = Exception("Template not found")

            # Mock file operations to prevent other errors
            with patch("pathlib.Path.mkdir"), patch(
                "pathlib.Path.exists", return_value=False
            ), patch("shutil.rmtree"):

                with pytest.raises(Exception, match="Template not found"):
                    await generator.create_website_async(site_config)

    @pytest.mark.asyncio
    async def test_cleanup(self, generator):
        """Test cleanup"""
        await generator.cleanup()
        # Should not raise any exceptions


class TestConcurrentBuilds:
    """Test concurrent build functionality"""

    @pytest.mark.asyncio
    async def test_concurrent_builds(self):
        """Test multiple concurrent builds"""
        template_manager = MockTemplateManager()
        generator = EnhancedWebsiteGenerator(template_manager, max_concurrent_builds=2)

        site_configs = [
            {"name": f"site_{i}", "template": "modern", "theme": "default"}
            for i in range(3)
        ]

        # Mock template manager
        with patch.object(
            generator.template_manager, "get_template"
        ) as mock_get_template:
            mock_get_template.return_value = {
                "name": "modern",
                "theme": "default",
                "files": {"index.html": "<html></html>"},
            }

            # Mock file operations
            with patch("pathlib.Path.mkdir"), patch(
                "pathlib.Path.exists", return_value=False
            ), patch("shutil.rmtree"), patch("builtins.open", create=True), patch(
                "json.dump"
            ):

                # Start multiple builds concurrently
                tasks = [
                    generator.create_website_async(config) for config in site_configs
                ]

                results = await asyncio.gather(*tasks, return_exceptions=True)

                # Check that all builds completed
                assert len(results) == 3
                for result in results:
                    if isinstance(result, Exception):
                        pytest.fail(f"Build failed with exception: {result}")
                    else:
                        assert result["success"] is True


if __name__ == "__main__":
    pytest.main([__file__])
