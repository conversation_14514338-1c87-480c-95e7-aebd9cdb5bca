"""
Common test functions used across multiple test files.
This module consolidates duplicate test functionality to eliminate code duplication.
"""

import json
import time
from datetime import datetime
from typing import Any, Dict, Optional

import requests


def test_backend_health(base_url: str = "http://127.0.0.1:8000") -> bool:
    """
    Unified backend health test - replaces 3 duplicate implementations.

    Args:
        base_url: Base URL for the backend service

    Returns:
        bool: True if backend is healthy, False otherwise
    """
    print("🔍 Testing Backend Health...")
    try:
        response = requests.get(f"{base_url}/health", timeout=5)
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Backend Health: {data['status']}")
            return True
        else:
            print(f"❌ Backend Health Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Backend Health Error: {e}")
        return False


def test_model_health(base_url: str = "http://127.0.0.1:8000") -> bool:
    """
    Unified model health test - replaces 3 duplicate implementations.

    Args:
        base_url: Base URL for the backend service

    Returns:
        bool: True if models are available, False otherwise
    """
    print("\n🤖 Testing AI Model Health...")
    try:
        response = requests.get(f"{base_url}/api/v1/ai/models/health", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ Model Health Endpoint Working")
            models = data.get("models", {})
            available_models = [model for model, status in models.items() if status]
            if available_models:
                print(f"✅ Available Models: {', '.join(available_models)}")
            else:
                print("⚠️  No AI models available (Ollama not running)")
            return True
        else:
            print(f"❌ Model Health Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Model Health Error: {e}")
        return False


def test_chat_endpoint(
    base_url: str = "http://127.0.0.1:8000",
    prompt: str = "Hello, can you help me create a website?",
) -> bool:
    """
    Unified chat endpoint test - replaces 3 duplicate implementations.

    Args:
        base_url: Base URL for the backend service
        prompt: Test prompt to send

    Returns:
        bool: True if endpoint responds correctly, False otherwise
    """
    print("\n💬 Testing Chat Endpoint...")
    try:
        response = requests.post(
            f"{base_url}/api/v1/chat", json={"prompt": prompt}, timeout=10
        )
        if response.status_code == 401:
            print("✅ Chat Endpoint Working (Authentication required as expected)")
            return True
        elif response.status_code == 200:
            data = response.json()
            print("✅ Chat Endpoint Working (No auth required)")
            content = data.get("response", {}).get("content", "No content")
            print(f"Response: {content[:100]}...")
            return True
        else:
            print(f"❌ Chat Endpoint Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Chat Endpoint Error: {e}")
        return False


def test_frontend_access(frontend_url: str = "http://localhost:3000") -> bool:
    """
    Unified frontend access test - replaces duplicate implementations.

    Args:
        frontend_url: Frontend URL to test

    Returns:
        bool: True if frontend is accessible, False otherwise
    """
    print("\n🌐 Testing Frontend Access...")
    try:
        response = requests.get(f"{frontend_url}/", timeout=5)
        if response.status_code == 200:
            print("✅ Frontend Homepage Accessible")
            return True
        else:
            print(f"❌ Frontend Access Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Frontend Access Error: {e}")
        return False


def test_ide_page(frontend_url: str = "http://localhost:3000") -> bool:
    """
    Unified IDE page test - replaces duplicate implementations.

    Args:
        frontend_url: Frontend URL to test

    Returns:
        bool: True if IDE page is accessible, False otherwise
    """
    print("\n💻 Testing IDE Page...")
    try:
        response = requests.get(f"{frontend_url}/ide", timeout=5)
        if response.status_code == 200:
            print("✅ IDE Page Accessible")
            return True
        else:
            print(f"❌ IDE Page Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ IDE Page Error: {e}")
        return False


def test_api_proxy(base_url: str = "http://127.0.0.1:8000") -> bool:
    """
    Unified API proxy test - replaces duplicate implementations.

    Args:
        base_url: Base URL for the backend service

    Returns:
        bool: True if API proxy is working, False otherwise
    """
    print("\n🔗 Testing API Proxy...")
    try:
        response = requests.get(f"{base_url}/api/v1/chat/models", timeout=10)
        if response.status_code == 200:
            data = response.json()
            print("✅ API Proxy Working")
            print(f"📊 Available models: {data.get('count', 0)}")
            return True
        else:
            print(f"❌ API Proxy Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ API Proxy Error: {e}")
        return False


def run_comprehensive_health_check(
    backend_url: str = "http://127.0.0.1:8000",
    frontend_url: str = "http://localhost:3000",
) -> Dict[str, bool]:
    """
    Run all health checks and return results.

    Args:
        backend_url: Backend service URL
        frontend_url: Frontend service URL

    Returns:
        dict: Results of all health checks
    """
    print("🧪 RUNNING COMPREHENSIVE HEALTH CHECK")
    print("=" * 60)

    results = {
        "backend_health": test_backend_health(backend_url),
        "model_health": test_model_health(backend_url),
        "chat_endpoint": test_chat_endpoint(backend_url),
        "frontend_access": test_frontend_access(frontend_url),
        "ide_page": test_ide_page(frontend_url),
        "api_proxy": test_api_proxy(backend_url),
    }

    # Summary
    passed = sum(results.values())
    total = len(results)
    print(f"\n📊 SUMMARY: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed!")
    else:
        failed_tests = [test for test, result in results.items() if not result]
        print(f"❌ Failed tests: {', '.join(failed_tests)}")

    return results


def test_authentication_endpoints(base_url: str = "http://127.0.0.1:8000") -> bool:
    """
    Test authentication-related endpoints.

    Args:
        base_url: Base URL for the backend service

    Returns:
        bool: True if authentication endpoints work correctly
    """
    print("\n🔐 Testing Authentication Endpoints...")

    auth_tests = [
        ("/api/auth/login", "POST"),
        ("/api/auth/logout", "POST"),
        ("/api/auth/verify", "GET"),
    ]

    passed = 0
    total = len(auth_tests)

    for endpoint, method in auth_tests:
        try:
            if method == "GET":
                response = requests.get(f"{base_url}{endpoint}", timeout=5)
            else:
                response = requests.post(f"{base_url}{endpoint}", json={}, timeout=5)

            # We expect 401 or 422 for unauthorized/invalid requests
            if response.status_code in [401, 422, 200]:
                print(f"✅ {endpoint} responds correctly ({response.status_code})")
                passed += 1
            else:
                print(f"❌ {endpoint} unexpected response: {response.status_code}")
        except Exception as e:
            print(f"❌ {endpoint} error: {e}")

    success = passed == total
    print(f"📊 Authentication tests: {passed}/{total} passed")
    return success


def wait_for_service(url: str, timeout: int = 30, interval: int = 2) -> bool:
    """
    Wait for a service to become available.

    Args:
        url: Service URL to check
        timeout: Maximum time to wait in seconds
        interval: Check interval in seconds

    Returns:
        bool: True if service becomes available, False if timeout
    """
    print(f"⏳ Waiting for service at {url}...")

    start_time = time.time()
    while time.time() - start_time < timeout:
        try:
            response = requests.get(url, timeout=5)
            if response.status_code == 200:
                print(f"✅ Service available at {url}")
                return True
        except requests.exceptions.RequestException:
            pass

        time.sleep(interval)

    print(f"❌ Service not available at {url} after {timeout}s")
    return False
