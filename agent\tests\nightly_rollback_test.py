#!/usr/bin/env python3
"""
Nightly Rollback Test Script
Runs automated rollback testing of previous deployments.

This script can be scheduled as a cron job to run nightly:
0 2 * * * /path/to/python /path/to/scripts/nightly_rollback_test.py

Or run manually for testing:
python scripts/nightly_rollback_test.py
"""

import asyncio
import json
import logging
import sys
from datetime import datetime
from pathlib import Path

# Fix Windows console encoding for emoji support
if hasattr(sys.stdout, "reconfigure"):
    # on Windows, switch the console to UTF-8 output to support emojis
    sys.stdout.reconfigure(encoding="utf-8", errors="replace")

# Add the project root to the Python path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from agent.core.home_server import HomeServer


def setup_logging():
    """Setup logging for the nightly rollback test"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)

    # Create a timestamp for the log file
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_dir / f"nightly_rollback_test_{timestamp}.log"

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
        handlers=[logging.FileHandler(log_file), logging.StreamHandler(sys.stdout)],
    )

    # Fix logging handler encoding for Windows
    for handler in logging.root.handlers:
        if hasattr(handler.stream, "reconfigure"):
            handler.stream.reconfigure(encoding="utf-8", errors="replace")

    return logging.getLogger("nightly_rollback_test")


async def run_nightly_rollback_test():
    """Run the nightly rollback test"""
    logger = setup_logging()

    print("🚀 Starting nightly rollback test")
    logger.info("🚀 Starting nightly rollback test")
    logger.info(f"Timestamp: {datetime.now().isoformat()}")

    try:
        # Initialize the pipeline
        print("📋 Initializing pipeline...")
        pipeline = HomeServerPipeline()
        logger.info("✅ Pipeline initialized")
        print("✅ Pipeline initialized")

        # Run the rollback test
        print("🔄 Running rollback test...")
        logger.info("🔄 Running rollback test...")
        result = await pipeline.run_nightly_rollback_test()

        # Log results
        print(f"📊 Test Summary:")
        logger.info(f"📊 Test Summary:")
        print(f"   Deployments tested: {result.get('deployments_tested', 0)}")
        print(f"   Tests passed: {result.get('tests_passed', 0)}")
        print(f"   Tests failed: {result.get('tests_failed', 0)}")
        logger.info(f"   Deployments tested: {result.get('deployments_tested', 0)}")
        logger.info(f"   Tests passed: {result.get('tests_passed', 0)}")
        logger.info(f"   Tests failed: {result.get('tests_failed', 0)}")

        if result.get("tests_failed", 0) > 0:
            print("❌ Rollback test FAILED")
            logger.error("❌ Rollback test FAILED")
            print(f"   Error: {result.get('error', 'Unknown error')}")
            logger.error(f"   Error: {result.get('error', 'Unknown error')}")

            # Log alerts
            for alert in result.get("alerts", []):
                print(f"   Alert: {alert.get('message', 'Unknown alert')}")
                logger.error(f"   Alert: {alert.get('message', 'Unknown alert')}")

            # Exit with error code for cron job monitoring
            sys.exit(1)
        else:
            print("✅ Rollback test PASSED")
            logger.info("✅ Rollback test PASSED")

            # Log test details
            for test_result in result.get("results", []):
                deployment = test_result.get("deployment", {})
                site_name = deployment.get("site", "unknown")
                status = test_result.get("overall_status", "unknown")
                print(f"   {site_name}: {status}")
                logger.info(f"   {site_name}: {status}")

        # Save detailed results
        results_dir = Path("logs/rollback_tests")
        results_dir.mkdir(exist_ok=True)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = results_dir / f"nightly_rollback_test_{timestamp}.json"

        with open(results_file, "w") as f:
            json.dump(result, f, indent=2)

        print(f"📄 Detailed results saved to: {results_file}")
        logger.info(f"📄 Detailed results saved to: {results_file}")

        # Get test history
        history = pipeline.get_rollback_test_history(limit=5)
        print(f"📈 Recent test history: {len(history)} tests")
        logger.info(f"📈 Recent test history: {len(history)} tests")

        for i, hist_result in enumerate(history):
            timestamp = hist_result.get("start_time", "unknown")
            passed = hist_result.get("tests_passed", 0)
            failed = hist_result.get("tests_failed", 0)
            print(f"   {i+1}. {timestamp}: {passed} passed, {failed} failed")
            logger.info(f"   {i+1}. {timestamp}: {passed} passed, {failed} failed")

        print("🎉 Nightly rollback test completed successfully")
        logger.info("🎉 Nightly rollback test completed successfully")

    except Exception as e:
        print(f"❌ Nightly rollback test failed: {e}")
        logger.error(f"❌ Nightly rollback test failed: {e}")
        logger.exception("Full traceback:")
        sys.exit(1)


def main():
    """Main entry point"""
    print("🚀 Nightly Rollback Test Script Starting...")
    try:
        asyncio.run(run_nightly_rollback_test())
    except KeyboardInterrupt:
        print("\n⚠️  Nightly rollback test interrupted by user")
        sys.exit(130)
    except Exception as e:
        print(f"❌ Fatal error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
