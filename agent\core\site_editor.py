import asyncio
import json
import shutil
from pathlib import Path
from typing import Any, Dict

from agent.core.website_generator import SafetyValidator

from agent.core.agents import BackendAgent, FrontendAgent
from agent.core.managers import BackupManager


class SiteEditor:
    """Safely edits websites in the sites/ directory."""

    def __init__(self):
        self.sites_dir = Path("sites")
        self.backup_manager = BackupManager.create_simple("backups")
        self.safety_validator = SafetyValidator()
        self.frontend_agent = FrontendAgent()
        self.backend_agent = BackendAgent()

    async def edit_site(self, site_name: str, edit_plan: str) -> Dict[str, Any]:
        """
        Loads a site, validates it, backs it up, and then applies an edit plan.

        Args:
            site_name: The name of the site directory in `sites/`.
            edit_plan: A natural language description of the edits to perform.

        Returns:
            A dictionary with the status and a message.
        """
        try:
            site_path = self.sites_dir / site_name

            # 1. Validate the path
            self.safety_validator.validate_target_path(str(site_path))
            if not site_path.exists():
                raise FileNotFoundError(f"Site '{site_name}' not found at {site_path}")

            # 2. Create a pre-edit backup
            backup_path = self.backup_manager.create_site_backup(
                site_path, f"pre-edit-{site_name}"
            )
            print(f"Created pre-edit backup at {backup_path}")

            # 3. Load manifest to determine how to edit
            manifest_path = site_path / "site.config.json"
            if not manifest_path.exists():
                # Fallback to upload_manifest if primary is missing
                manifest_path = site_path / "upload_manifest.json"

            if not manifest_path.exists():
                raise FileNotFoundError(f"No manifest found for site '{site_name}'")

            with open(manifest_path, "r", encoding="utf-8") as f:
                manifest = json.load(f)

            framework = manifest.get("framework", "static")
            print(f"Identified framework '{framework}' for site '{site_name}'")

            # 4. Dispatch to the appropriate editor
            print(f"Executing edit plan for '{site_name}': {edit_plan}")

            if framework in ["nextjs", "react", "static"]:
                # Use the new FrontendAgent for frontend frameworks
                task_id = f"edit_{site_name}_{int(asyncio.get_event_loop().time())}"
                result = await self.frontend_agent.execute_task(edit_plan, task_id)

                if result["success"]:
                    print(f"Frontend edit completed: {result['message']}")
                else:
                    print(f"Frontend edit failed: {result['error']}")

            elif framework == "flask":
                # Use the new BackendAgent for backend frameworks
                task_id = f"edit_{site_name}_{int(asyncio.get_event_loop().time())}"
                result = await self.backend_agent.execute_task(edit_plan, task_id)

                if result["success"]:
                    print(f"Backend edit completed: {result['message']}")
                else:
                    print(f"Backend edit failed: {result['error']}")
            else:
                print(
                    f"Warning: No specific editor for framework '{framework}'. Performing a generic edit."
                )
                await self._simulate_edit(site_path, edit_plan)

            return {
                "status": "success",
                "message": f"Successfully applied edit plan to '{site_name}'.",
                "site_path": str(site_path),
                "backup_path": str(backup_path),
            }

        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def _simulate_edit(self, site_path: Path, edit_plan: str):
        """A placeholder to simulate an edit."""
        edit_log_path = site_path / "edit_history.log"
        with open(edit_log_path, "a", encoding="utf-8") as f:
            f.write(f"EDIT_PLAN: {edit_plan}\n")
        print(f"Simulated edit by appending to {edit_log_path}")

    async def shutdown(self):
        """Shutdown the site editor and its agents"""
        await self.frontend_agent.shutdown()
        await self.backend_agent.shutdown()
