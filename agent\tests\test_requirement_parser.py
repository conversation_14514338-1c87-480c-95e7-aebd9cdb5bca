import json
import unittest

from agent.utils.requirement_parser import CommandSchemaError, RequirementParser


class TestRequirementParser(unittest.TestCase):
    def setUp(self):
        self.parser = RequirementParser()

    def test_valid_command(self):
        raw_command = json.dumps(
            {
                "type": "create",
                "target": "webpage",
                "parameters": {"title": "My Website", "template": "modern"},
            }
        )
        command = self.parser.parse_command(raw_command)
        self.assertEqual(command.type.value, "create")
        self.assertEqual(command.target, "webpage")
        self.assertIn("title", command.parameters)

    def test_missing_required_field(self):
        raw_command = json.dumps(
            {"target": "webpage", "parameters": {"title": "My Website"}}
        )
        with self.assertRaises(CommandSchemaError):
            self.parser.parse_command(raw_command)

    def test_invalid_command_type(self):
        raw_command = json.dumps(
            {"type": "invalid_type", "target": "webpage", "parameters": {}}
        )
        with self.assertRaises(CommandSchemaError):
            self.parser.parse_command(raw_command)

    def test_session_generation(self):
        commands = [
            self.parser.parse_command(
                json.dumps({"type": "create", "target": "webpage", "parameters": {}})
            ),
            self.parser.parse_command(
                json.dumps({"type": "modify", "target": "webpage", "parameters": {}})
            ),
        ]
        session = self.parser.generate_session(commands)
        self.assertEqual(len(session["commands"]), 2)
        self.assertEqual(session["status"], "pending")


if __name__ == "__main__":
    unittest.main()
