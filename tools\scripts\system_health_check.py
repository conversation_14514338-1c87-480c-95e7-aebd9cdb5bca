#!/usr/bin/env python3
"""
System Health Check
Comprehensive check of the error handling and user escalation system
"""

import asyncio
import sys
from pathlib import Path
from datetime import datetime

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))


async def check_system_components():
    """Check all system components are working"""
    
    print("🔍 System Component Health Check")
    print("=" * 50)
    
    components = {
        "SiteContainerManager": False,
        "ErrorDetectionSystem": False,
        "CLI Integration": False,
        "Error Escalation": False,
        "User Suggestions": False
    }
    
    # Test 1: Core Imports
    try:
        from agent.core.site_container_manager import SiteContainerManager
        from agent.core.error_detection_system import ErrorDetectionSystem
        from agent.cli.site_container_commands import SiteContainerCommands
        
        print("✅ All core imports successful")
        components["SiteContainerManager"] = True
        components["ErrorDetectionSystem"] = True
        components["CLI Integration"] = True
        
    except ImportError as e:
        print(f"❌ Import failed: {e}")
        return components
    
    # Test 2: Component Instantiation
    try:
        container_manager = SiteContainerManager()
        error_detector = ErrorDetectionSystem()
        cli_commands = SiteContainerCommands(agent=None)
        
        print("✅ All components instantiated successfully")
        
    except Exception as e:
        print(f"❌ Instantiation failed: {e}")
        return components
    
    # Test 3: Error Escalation Methods
    try:
        required_methods = [
            "handle_error_with_user_escalation",
            "apply_user_suggested_fix",
            "_escalate_to_user"
        ]
        
        missing_methods = []
        for method in required_methods:
            if not hasattr(container_manager, method):
                missing_methods.append(method)
        
        if not missing_methods:
            print("✅ All error escalation methods available")
            components["Error Escalation"] = True
        else:
            print(f"❌ Missing methods: {missing_methods}")
            
    except Exception as e:
        print(f"❌ Method check failed: {e}")
    
    # Test 4: CLI Integration Methods
    try:
        cli_methods = [
            "handle_error_with_escalation",
            "apply_user_suggested_fix",
            "get_error_status"
        ]
        
        missing_cli_methods = []
        for method in cli_methods:
            if not hasattr(cli_commands, method):
                missing_cli_methods.append(method)
        
        if not missing_cli_methods:
            print("✅ All CLI integration methods available")
        else:
            print(f"❌ Missing CLI methods: {missing_cli_methods}")
            
    except Exception as e:
        print(f"❌ CLI method check failed: {e}")
    
    # Test 5: User Suggestion Processing
    try:
        test_suggestions = [
            "restart the container",
            "run a diagnostic",
            "rollback to previous version"
        ]
        
        suggestion_results = []
        for suggestion in test_suggestions:
            try:
                result = await container_manager.apply_user_suggested_fix(
                    site_name="test-site",
                    user_suggestion=suggestion
                )
                suggestion_results.append(result.get("success", False))
            except Exception:
                suggestion_results.append(False)
        
        if any(suggestion_results):
            print("✅ User suggestion processing working")
            components["User Suggestions"] = True
        else:
            print("❌ User suggestion processing failed")
            
    except Exception as e:
        print(f"❌ User suggestion test failed: {e}")
    
    return components


async def check_error_workflow():
    """Test the complete error workflow"""
    
    print(f"\n🔄 Error Workflow Test")
    print("=" * 30)
    
    try:
        from agent.core.site_container_manager import SiteContainerManager
        
        container_manager = SiteContainerManager()
        
        # Test different error scenarios
        test_scenarios = [
            {
                "name": "Low Severity Error",
                "error": {
                    "title": "Minor CSS Issue",
                    "description": "Button styling incorrect",
                    "category": "frontend",
                    "severity": "low"
                },
                "expected": "automatic"
            },
            {
                "name": "High Severity Error",
                "error": {
                    "title": "Database Connection Failed",
                    "description": "Cannot connect to database",
                    "category": "database", 
                    "severity": "high"
                },
                "expected": "escalation"
            },
            {
                "name": "Critical Error",
                "error": {
                    "title": "Security Vulnerability",
                    "description": "SQL injection detected",
                    "category": "security",
                    "severity": "critical"
                },
                "expected": "escalation"
            }
        ]
        
        workflow_results = []
        
        for scenario in test_scenarios:
            print(f"\n   Testing: {scenario['name']}")
            
            try:
                result = await container_manager.handle_error_with_user_escalation(
                    site_name="test-workflow",
                    error_details=scenario["error"]
                )
                
                if scenario["expected"] == "automatic" and result.get("success"):
                    print(f"   ✅ Auto-resolved as expected")
                    workflow_results.append(True)
                elif scenario["expected"] == "escalation" and result.get("requires_user_input"):
                    print(f"   ✅ Escalated to user as expected")
                    workflow_results.append(True)
                elif scenario["expected"] == "escalation" and result.get("success"):
                    print(f"   ⚠️ Auto-resolved (expected escalation)")
                    workflow_results.append(True)  # Still working, just different behavior
                else:
                    print(f"   ❌ Unexpected result: {result}")
                    workflow_results.append(False)
                    
            except Exception as e:
                print(f"   ❌ Error: {e}")
                workflow_results.append(False)
        
        success_rate = sum(workflow_results) / len(workflow_results) * 100
        print(f"\n   Workflow Success Rate: {success_rate:.1f}%")
        
        return success_rate >= 80  # 80% success rate is acceptable
        
    except Exception as e:
        print(f"❌ Workflow test failed: {e}")
        return False


async def check_integration_points():
    """Check integration between components"""
    
    print(f"\n🔗 Integration Points Check")
    print("=" * 35)
    
    integration_results = {
        "Error Detection → Container Manager": False,
        "Container Manager → CLI": False,
        "CLI → User Interface": False,
        "Error Patterns → Auto-Fix": False
    }
    
    try:
        # Test 1: Error Detection to Container Manager
        from agent.core.error_detection_system import ErrorDetectionSystem, DetectedError, ErrorCategory, ErrorSeverity
        
        error_detector = ErrorDetectionSystem()
        test_error = DetectedError(
            id="integration_test",
            timestamp=datetime.now(),
            category=ErrorCategory.DATABASE,
            severity=ErrorSeverity.HIGH,
            title="Integration Test Error",
            description="Testing integration",
            source="integration_test",
            context={"site_name": "test-integration"}
        )
        
        # This should escalate to container manager
        await error_detector._escalate_to_user(test_error)
        print("✅ Error Detection → Container Manager")
        integration_results["Error Detection → Container Manager"] = True
        
    except Exception as e:
        print(f"❌ Error Detection → Container Manager: {e}")
    
    try:
        # Test 2: Container Manager to CLI
        from agent.cli.site_container_commands import SiteContainerCommands
        
        cli_commands = SiteContainerCommands(agent=None)
        
        result = await cli_commands.handle_error_with_escalation(
            site_name="test-cli",
            error_title="CLI Test Error",
            error_description="Testing CLI integration"
        )
        
        if result.get("success") or result.get("requires_user_input"):
            print("✅ Container Manager → CLI")
            integration_results["Container Manager → CLI"] = True
        else:
            print(f"❌ Container Manager → CLI: {result}")
            
    except Exception as e:
        print(f"❌ Container Manager → CLI: {e}")
    
    try:
        # Test 3: CLI to User Interface (simulated)
        cli_result = await cli_commands.get_error_status("test-site")
        
        if cli_result.get("success"):
            print("✅ CLI → User Interface")
            integration_results["CLI → User Interface"] = True
        else:
            print(f"❌ CLI → User Interface: {cli_result}")
            
    except Exception as e:
        print(f"❌ CLI → User Interface: {e}")
    
    try:
        # Test 4: Error Patterns to Auto-Fix
        from agent.core.site_container_manager import SiteContainerManager
        
        container_manager = SiteContainerManager()
        
        # Test auto-fixable error pattern
        auto_fix_result = await container_manager._attempt_automatic_fix(
            site_name="test-autofix",
            error_details={
                "description": "container not responding",
                "category": "infrastructure"
            },
            previous_attempts=[]
        )
        
        if auto_fix_result.get("success") or auto_fix_result.get("fix_type"):
            print("✅ Error Patterns → Auto-Fix")
            integration_results["Error Patterns → Auto-Fix"] = True
        else:
            print(f"❌ Error Patterns → Auto-Fix: {auto_fix_result}")
            
    except Exception as e:
        print(f"❌ Error Patterns → Auto-Fix: {e}")
    
    return integration_results


async def main():
    """Run complete system health check"""
    
    print("🏥 Error Handling System Health Check")
    print("=" * 60)
    print("Checking all components of the error detection and user escalation system\n")
    
    # Check 1: System Components
    components = await check_system_components()
    
    # Check 2: Error Workflow
    workflow_ok = await check_error_workflow()
    
    # Check 3: Integration Points
    integrations = await check_integration_points()
    
    # Summary
    print(f"\n📊 Health Check Summary")
    print("=" * 30)
    
    print(f"\n🔧 Component Status:")
    for component, status in components.items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {component}")
    
    print(f"\n🔄 Workflow Status:")
    workflow_icon = "✅" if workflow_ok else "❌"
    print(f"   {workflow_icon} Error Workflow")
    
    print(f"\n🔗 Integration Status:")
    for integration, status in integrations.items():
        status_icon = "✅" if status else "❌"
        print(f"   {status_icon} {integration}")
    
    # Overall health score
    total_checks = len(components) + 1 + len(integrations)
    passed_checks = sum(components.values()) + (1 if workflow_ok else 0) + sum(integrations.values())
    health_score = (passed_checks / total_checks) * 100
    
    print(f"\n🎯 Overall System Health: {health_score:.1f}%")
    
    if health_score >= 90:
        print(f"🎉 EXCELLENT - System is fully operational!")
        status = "excellent"
    elif health_score >= 75:
        print(f"✅ GOOD - System is working well with minor issues")
        status = "good"
    elif health_score >= 50:
        print(f"⚠️ FAIR - System has some issues that need attention")
        status = "fair"
    else:
        print(f"❌ POOR - System needs significant fixes")
        status = "poor"
    
    print(f"\n💡 Recommendations:")
    if health_score < 100:
        print(f"   • Address any failed component checks")
        print(f"   • Test error workflows manually")
        print(f"   • Verify integration points")
    
    if health_score >= 75:
        print(f"   • System is ready for production use")
        print(f"   • Monitor error handling in real scenarios")
        print(f"   • Collect user feedback on escalation experience")
    
    return status == "excellent" or status == "good"


if __name__ == "__main__":
    # Run the health check
    result = asyncio.run(main())
    
    print(f"\n{'🎉 System health check completed successfully!' if result else '⚠️ System needs attention before production use'}")
    exit(0 if result else 1)
