#!/usr/bin/env python3
import os
import json
from pathlib import Path

import pytest

from agent.core.project_models import Roadmap, PhaseSpec, StepSpec, TaskSpec, ItemStatus, ProjectState
from agent.core.project_store import ProjectStore


def test_project_store_roundtrip(tmp_path: Path):
    base = tmp_path / "projects"
    store = ProjectStore(base_dir=str(base))

    roadmap = Roadmap(
        id="rm1",
        title="Demo",
        phases=[
            PhaseSpec(
                id="p1",
                title="Planning",
                steps=[
                    StepSpec(
                        id="s1",
                        title="Env Setup",
                        tasks=[
                            TaskSpec(
                                id="t1",
                                title="Init repo",
                                description="git init",
                                agent_type="shell",
                                dependencies=[],
                            )
                        ],
                    )
                ],
            )
        ],
    )

    project_id = "proj123"
    store.save_roadmap(project_id, roadmap)

    loaded = store.load_roadmap(project_id)
    assert loaded is not None
    assert loaded.title == "Demo"
    assert loaded.phases[0].steps[0].tasks[0].agent_type == "shell"

    state = ProjectState(project_id=project_id, roadmap_id=roadmap.id, current_phase="p1")
    store.save_state(project_id, state)

    loaded_state = store.load_state(project_id)
    assert loaded_state is not None
    assert loaded_state.project_id == project_id
    assert loaded_state.current_phase == "p1"

