{"netlify": {"enabled": false, "api_token": "", "team_id": "", "site_id": "", "deploy_hook": "", "custom_domain": "", "environment_variables": {"NODE_VERSION": "18", "NPM_VERSION": "9"}}, "github_pages": {"enabled": false, "repository": "", "branch": "gh-pages", "token": "", "username": "", "email": "", "custom_domain": "", "cname_file": false}, "vercel": {"enabled": false, "api_token": "", "project_id": "", "team_id": "", "custom_domain": "", "environment_variables": {"NODE_VERSION": "18"}}, "aws_s3": {"enabled": false, "access_key_id": "", "secret_access_key": "", "bucket_name": "", "region": "us-east-1", "cloudfront_distribution": "", "custom_domain": ""}, "cloudflare_pages": {"enabled": false, "api_token": "", "account_id": "", "project_name": "", "custom_domain": ""}, "export_settings": {"default_provider": "static_export", "build_optimization": true, "minification": true, "compression": true, "source_maps": false, "cache_busting": true, "preload_critical_resources": true, "lazy_loading": true, "image_optimization": {"enabled": true, "quality": 85, "formats": ["webp", "avif"], "max_width": 1920}, "css_optimization": {"enabled": true, "purge_unused": true, "minify": true, "autoprefixer": true}, "js_optimization": {"enabled": true, "minify": true, "tree_shaking": true, "code_splitting": true}}, "deployment_settings": {"auto_deploy": false, "deploy_on_push": false, "preview_deployments": true, "rollback_enabled": true, "max_deployments": 10, "deployment_timeout": 300, "health_check_after_deploy": true, "notifications": {"email": false, "slack": false, "webhook": ""}}, "security_settings": {"https_redirect": true, "security_headers": {"X-Frame-Options": "DENY", "X-Content-Type-Options": "nosniff", "X-XSS-Protection": "1; mode=block", "Referrer-Policy": "strict-origin-when-cross-origin", "Content-Security-Policy": "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"}, "rate_limiting": {"enabled": true, "requests_per_minute": 100, "burst_limit": 10}}}