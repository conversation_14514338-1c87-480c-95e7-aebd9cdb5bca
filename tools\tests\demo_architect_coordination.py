#!/usr/bin/env python3
"""
Architect Coordination Demo
Demonstrates how the architect coordinates specialists behind the scenes
while maintaining single point of contact with users
"""

import asyncio
import sys
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from agent.core.persona_manager import <PERSON><PERSON><PERSON><PERSON><PERSON>, AgentType, TaskCategory
from agent.core.site_container_manager import SiteContainerManager


async def demo_architect_user_interaction():
    """Demonstrate architect as single point of contact with user"""
    
    print("🏗️ Architect-User Interaction Demo")
    print("=" * 50)
    print("See how the architect handles user requests while coordinating specialists behind the scenes\n")
    
    container_manager = SiteContainerManager()
    
    print("👤 User: \"My e-commerce site has a problem - the product list isn't showing up!\"")
    print()
    
    # Architect receives the error
    frontend_error = {
        "title": "Product List Not Displaying",
        "description": "React ProductList component shows blank screen instead of products",
        "category": "frontend",
        "severity": "high"
    }
    
    print("🏗️ Architect: \"I'll investigate this right away! Let me check what's happening with your product display.\"")
    print()
    print("🔍 [Behind the scenes: Architect analyzing the error...]")
    
    # Show the coordination process
    result = await container_manager.handle_error_with_user_escalation(
        site_name="demo-ecommerce",
        error_details=frontend_error
    )
    
    if result.get("success"):
        print("✅ [Behind the scenes: Architect coordinated with Frontend Specialist]")
        print("✅ [Behind the scenes: Specialist output verified and applied]")
        print()
        print(f"🏗️ Architect: \"{result.get('architect_message', 'Fixed successfully!')}\"")
        print()
        print("👤 User: \"Wow, that was fast! How did you fix it?\"")
        print()
        print("🏗️ Architect: \"I worked with our frontend specialist to identify the issue - your React component")
        print("    wasn't handling empty data properly. We added proper null checks and error boundaries.")
        print("    Your product list should now display beautifully, even when data is loading!\"")
    else:
        print("⚠️ [Behind the scenes: Issue requires user input]")
        print()
        print(f"🏗️ Architect: \"{result.get('architect_message', 'I need your help with this one.')}\"")


async def demo_behind_the_scenes_coordination():
    """Show what happens behind the scenes during coordination"""
    
    print(f"\n🎭 Behind-the-Scenes Coordination Demo")
    print("=" * 50)
    print("See the detailed coordination process that happens invisibly to the user\n")
    
    container_manager = SiteContainerManager()
    
    # Complex database issue
    database_error = {
        "title": "Database Performance Degradation",
        "description": "Product search queries taking 5+ seconds, users complaining about slow site",
        "category": "database",
        "severity": "critical"
    }
    
    print("📋 Step 1: Architect Receives Error Report")
    print(f"   Error: {database_error['title']}")
    print(f"   Category: {database_error['category']}")
    print(f"   Severity: {database_error['severity']}")
    
    print("\n🧠 Step 2: Architect Analyzes and Routes to Specialist")
    task_category = container_manager._map_error_to_task_category(database_error["category"])
    specialist_agent = container_manager.persona_manager.get_agent_for_task(task_category, database_error)
    print(f"   Task Category: {task_category.value}")
    print(f"   Specialist Selected: {specialist_agent.value}")
    
    print("\n📝 Step 3: Architect Creates Detailed Prompt for Specialist")
    specialist_prompt = await container_manager._create_specialist_prompt(
        specialist_agent, database_error, "demo-ecommerce", []
    )
    
    print(f"   Objective: {specialist_prompt['clear_objective'][:80]}...")
    print(f"   Instructions: {len(specialist_prompt['specific_instructions'])} detailed steps")
    print(f"   Success Criteria: {len(specialist_prompt['success_criteria'])} requirements")
    print(f"   Available Tools: {len(specialist_prompt['context']['available_tools'])} tools")
    
    print("\n🔧 Step 4: Specialist Performs Work")
    specialist_result = await container_manager._delegate_to_specialist(
        specialist_agent, specialist_prompt, database_error
    )
    
    print(f"   Fix Applied: {specialist_result['fix_description'][:80]}...")
    print(f"   Changes Made: {len(specialist_result.get('changes_made', []))} changes")
    print(f"   Testing Done: {len(specialist_result.get('testing_performed', []))} tests")
    
    print("\n✅ Step 5: Architect Verifies Specialist Output")
    verification_result = await container_manager._architect_verify_output(
        specialist_result, database_error, "demo-ecommerce"
    )
    
    print(f"   Verification Status: {'✅ PASSED' if verification_result['verified'] else '❌ FAILED'}")
    print(f"   Quality Score: {verification_result.get('quality_score', 0):.1f}%")
    print(f"   Checks Performed: {len(verification_result.get('checks_performed', []))}")
    
    print("\n🚀 Step 6: Architect Applies Verified Fix")
    application_result = await container_manager._architect_apply_fix(
        "demo-ecommerce", specialist_result, database_error
    )
    
    print(f"   Application Status: {'✅ SUCCESS' if application_result['success'] else '❌ FAILED'}")
    print(f"   Changes Applied: {application_result.get('changes_applied', 0)}")
    print(f"   Verifications Passed: {application_result.get('verifications_passed', 0)}")
    
    print("\n💬 Step 7: Architect Reports to User")
    architect_message = container_manager._generate_architect_success_message(
        specialist_agent, application_result
    )
    print(f"   User Message: {architect_message}")


async def demo_prompt_quality():
    """Demonstrate the quality and specificity of specialist prompts"""
    
    print(f"\n📋 Specialist Prompt Quality Demo")
    print("=" * 40)
    print("See how the architect creates clear, specific, context-rich prompts\n")
    
    container_manager = SiteContainerManager()
    
    # Security vulnerability scenario
    security_error = {
        "title": "SQL Injection Vulnerability Detected",
        "description": "Security scanner found potential SQL injection in user search functionality",
        "category": "security",
        "severity": "critical"
    }
    
    print("🛡️ Creating Prompt for Security Specialist")
    print("-" * 45)
    
    prompt = await container_manager._create_specialist_prompt(
        AgentType.SECURITY, security_error, "production-site", 
        [{"fix_type": "quick_patch", "success": False, "error": "Incomplete fix"}]
    )
    
    print(f"🎯 **Clear Objective:**")
    print(f"   {prompt['clear_objective']}")
    
    print(f"\n📝 **Specific Instructions:** ({len(prompt['specific_instructions'])} steps)")
    for i, instruction in enumerate(prompt['specific_instructions'][:5], 1):
        print(f"   {i}. {instruction}")
    if len(prompt['specific_instructions']) > 5:
        print(f"   ... and {len(prompt['specific_instructions']) - 5} more steps")
    
    print(f"\n🎯 **Success Criteria:** ({len(prompt['success_criteria'])} criteria)")
    for i, criteria in enumerate(prompt['success_criteria'][:3], 1):
        print(f"   {i}. {criteria}")
    if len(prompt['success_criteria']) > 3:
        print(f"   ... and {len(prompt['success_criteria']) - 3} more criteria")
    
    print(f"\n🔧 **Available Tools:** ({len(prompt['context']['available_tools'])} tools)")
    tools = prompt['context']['available_tools']
    print(f"   Base tools: {', '.join(tools[:5])}")
    if len(tools) > 5:
        print(f"   Specialist tools: {', '.join(tools[5:])}")
    
    print(f"\n⚠️ **Constraints:** ({len(prompt.get('constraints', []))} constraints)")
    constraints = prompt.get('constraints', [])
    for i, constraint in enumerate(constraints[:3], 1):
        print(f"   {i}. {constraint}")
    if len(constraints) > 3:
        print(f"   ... and {len(constraints) - 3} more constraints")
    
    print(f"\n📊 **Expected Output Format:**")
    output_format = prompt['expected_output_format']
    for key, description in list(output_format.items())[:4]:
        print(f"   {key}: {description}")
    if len(output_format) > 4:
        print(f"   ... and {len(output_format) - 4} more fields")


async def demo_verification_process():
    """Demonstrate the architect's verification process"""
    
    print(f"\n🔍 Verification Process Demo")
    print("=" * 35)
    print("See how the architect verifies specialist output for quality\n")
    
    container_manager = SiteContainerManager()
    
    # Mock specialist outputs - good and bad examples
    good_output = {
        "success": True,
        "fix_description": "Implemented comprehensive SQL injection protection using parameterized queries and input validation",
        "changes_made": [
            "Replaced all string concatenation with parameterized queries",
            "Added input sanitization for all user inputs",
            "Implemented prepared statements for database operations"
        ],
        "testing_performed": [
            "SQL injection vulnerability testing with OWASP test suite",
            "Input validation testing with malicious payloads",
            "Security audit of all database interaction points"
        ],
        "rollback_instructions": "Revert database access layer to commit sha256:abc123 and restart application",
        "verification_steps": [
            "Test normal search functionality",
            "Attempt SQL injection attacks to verify protection",
            "Review security logs for any bypass attempts"
        ]
    }
    
    bad_output = {
        "success": True,
        "fix_description": "Fixed it",
        "changes_made": ["Changed some code"],
        "testing_performed": [],
        "rollback_instructions": "Undo if needed"
    }
    
    print("✅ **Verifying Good Specialist Output:**")
    good_verification = await container_manager._architect_verify_output(
        good_output, {"title": "Security Issue"}, "test-site"
    )
    
    print(f"   Status: {'✅ VERIFIED' if good_verification['verified'] else '❌ REJECTED'}")
    print(f"   Quality Score: {good_verification.get('quality_score', 0):.1f}%")
    print(f"   Checks Passed:")
    for check in good_verification.get('checks_performed', []):
        print(f"     {check}")
    
    print("\n❌ **Verifying Poor Specialist Output:**")
    bad_verification = await container_manager._architect_verify_output(
        bad_output, {"title": "Security Issue"}, "test-site"
    )
    
    print(f"   Status: {'✅ VERIFIED' if bad_verification['verified'] else '❌ REJECTED'}")
    print(f"   Reason: {bad_verification.get('reason', 'Unknown')}")
    print(f"   Checks Performed:")
    for check in bad_verification.get('checks_performed', []):
        print(f"     {check}")


async def main():
    """Run the complete architect coordination demo"""
    
    print("🏗️ Architect Coordination System - Complete Demo")
    print("=" * 60)
    print("Experience how the architect coordinates specialists behind the scenes")
    print("while maintaining natural user conversations\n")
    
    # Demo 1: User interaction
    await demo_architect_user_interaction()
    
    # Demo 2: Behind the scenes coordination
    await demo_behind_the_scenes_coordination()
    
    # Demo 3: Prompt quality
    await demo_prompt_quality()
    
    # Demo 4: Verification process
    await demo_verification_process()
    
    # Summary
    print(f"\n🎉 Demo Summary")
    print("=" * 20)
    print(f"✅ Architect is the single point of contact with users")
    print(f"✅ Clear, specific, context-rich prompts guide specialists")
    print(f"✅ Comprehensive verification ensures output quality")
    print(f"✅ Proper tool access enables effective specialist work")
    print(f"✅ Natural escalation when user input is needed")
    print(f"✅ Feedback loops improve system reliability")
    
    print(f"\n🎯 Key Improvements:")
    print(f"   🎭 Users only interact with the friendly architect")
    print(f"   📋 Specialists receive unambiguous, detailed instructions")
    print(f"   ✅ All specialist work is verified before application")
    print(f"   🔧 Each specialist has access to appropriate tools")
    print(f"   🔄 Continuous improvement through validation feedback")
    print(f"   🤝 Seamless user experience with expert coordination")
    
    return True


if __name__ == "__main__":
    # Run the demo
    result = asyncio.run(main())
    
    print(f"\n{'🎉 Architect coordination demo completed successfully!' if result else '❌ Demo failed'}")
    exit(0 if result else 1)
