# Cursor Verification Prompts for AI Coding Agent Implementation

## Phase 1: Website Upload & Basic Integration Verification

### Cursor Prompt for Phase 1 Verification

```
PHASE 1 VERIFICATION: Website Upload & Basic Integration

Please analyze the codebase and verify that the following Phase 1 components have been properly implemented:

## 1. FILE UPLOAD SYSTEM
- [ ] Verify upload endpoint exists and handles multiple file formats (.html, .css, .js, .zip, .tar.gz)
- [ ] Check file validation (size limits, security scanning, malicious file detection)
- [ ] Confirm upload progress tracking and error handling
- [ ] Test file extraction for compressed uploads
- [ ] Validate temporary storage and cleanup mechanisms

## 2. WEBSITE ANALYSIS ENGINE
- [ ] Verify framework detection (React, Vue, Angular, vanilla HTML, WordPress, etc.)
- [ ] Check dependency analysis (package.json, requirements.txt, etc.)
- [ ] Confirm architecture pattern recognition (SPA, MPA, static site, etc.)
- [ ] Test code quality assessment integration
- [ ] Validate security vulnerability scanning

## 3. COMPATIBILITY CHECKER
- [ ] Verify hosting environment compatibility assessment
- [ ] Check resource requirement analysis (memory, CPU, storage)
- [ ] Confirm dependency conflict detection
- [ ] Test migration complexity scoring
- [ ] Validate compatibility report generation

## 4. BASIC HOSTING INTEGRATION
- [ ] Verify uploaded sites can be deployed to local hosting infrastructure
- [ ] Check URL generation and routing for uploaded sites
- [ ] Confirm static asset serving and optimization
- [ ] Test SSL certificate generation for uploaded sites
- [ ] Validate domain/subdomain assignment

## 5. MONITORING EXTENSION
- [ ] Verify monitoring hooks are added to uploaded websites
- [ ] Check performance tracking integration
- [ ] Confirm uptime monitoring for uploaded sites
- [ ] Test error logging and alerting
- [ ] Validate resource usage tracking

## 6. DATA PERSISTENCE
- [ ] Verify uploaded website metadata storage
- [ ] Check file versioning and backup systems
- [ ] Confirm user ownership and permissions
- [ ] Test database schema for uploaded sites
- [ ] Validate data integrity and recovery mechanisms

## VERIFICATION COMMANDS TO RUN:
1. `.\.venv\Scripts\Activate.ps1; python -m pytest tests/test_upload_system.py -v`
2. `.\.venv\Scripts\Activate.ps1; python -m pytest tests/test_website_analysis.py -v`
3. `.\.venv\Scripts\Activate.ps1; python -m pytest tests/test_compatibility_checker.py -v`
4. `.\.venv\Scripts\Activate.ps1; python -m pytest tests/test_hosting_integration.py -v`
5. `python scripts/verify_phase1_implementation.py`

## INTEGRATION TESTS TO PERFORM:
1. Upload a WordPress site and verify it deploys correctly
2. Upload a React app and confirm build process integration
3. Upload a static HTML site and test hosting functionality
4. Test concurrent uploads and resource handling
5. Verify rollback functionality if upload fails

Please provide a detailed report of what's implemented, what's missing, and any issues found.
```

---

## Phase 2: Advanced Maintenance Verification

### Cursor Prompt for Phase 2 Verification

```
PHASE 2 VERIFICATION: Advanced Maintenance

Please analyze the codebase and verify that the following Phase 2 advanced maintenance components have been properly implemented:

## 1. AI-POWERED OPTIMIZATION SUGGESTIONS
- [ ] Verify performance optimization recommendation engine
- [ ] Check SEO improvement suggestion system
- [ ] Confirm accessibility enhancement recommendations
- [ ] Test security improvement suggestions
- [ ] Validate code quality improvement recommendations

## 2. SECURITY MONITORING & UPDATES
- [ ] Verify dependency vulnerability scanning (npm audit, safety, etc.)
- [ ] Check automated security patch application
- [ ] Confirm malware and intrusion detection
- [ ] Test security policy enforcement
- [ ] Validate security incident response automation

## 3. PERFORMANCE OPTIMIZATION AUTOMATION
- [ ] Verify image optimization and compression
- [ ] Check CSS/JS minification and bundling
- [ ] Confirm CDN integration and asset optimization
- [ ] Test database query optimization (if applicable)
- [ ] Validate caching strategy implementation

## 4. PREDICTIVE MAINTENANCE
- [ ] Verify traffic spike prediction algorithms
- [ ] Check resource usage forecasting
- [ ] Confirm failure prediction models
- [ ] Test proactive scaling mechanisms
- [ ] Validate maintenance scheduling optimization

## 5. AUTOMATED UPDATE SYSTEM
- [ ] Verify framework and dependency update automation
- [ ] Check backup creation before updates
- [ ] Confirm rollback mechanisms for failed updates
- [ ] Test update conflict resolution
- [ ] Validate update notification and approval workflows

## 6. HEALTH MONITORING DASHBOARD
- [ ] Verify real-time website health metrics
- [ ] Check performance trend analysis
- [ ] Confirm alerting and notification systems
- [ ] Test maintenance history tracking
- [ ] Validate cost optimization recommendations

## VERIFICATION COMMANDS TO RUN:
1. `.\.venv\Scripts\Activate.ps1; python -m pytest tests/test_optimization_engine.py -v`
2. `.\.venv\Scripts\Activate.ps1; python -m pytest tests/test_security_monitoring.py -v`
3. `.\.venv\Scripts\Activate.ps1; python -m pytest tests/test_performance_automation.py -v`
4. `.\.venv\Scripts\Activate.ps1; python -m pytest tests/test_predictive_maintenance.py -v`
5. `python scripts/verify_phase2_implementation.py`

## INTEGRATION TESTS TO PERFORM:
1. Trigger a security vulnerability and verify automated patching
2. Simulate traffic spike and test predictive scaling
3. Test performance optimization on a slow website
4. Verify maintenance scheduling doesn't conflict with high-traffic periods
5. Test rollback functionality after a failed automated update

## METRICS TO VALIDATE:
- Security patch application time < 1 hour for critical vulnerabilities
- Performance optimization improvements > 20% average
- Predictive maintenance accuracy > 85%
- Zero-downtime deployment success rate > 99%
- User satisfaction with automated suggestions > 80%

Please provide a comprehensive report including performance benchmarks and any optimization recommendations.
```

---

## Phase 3: Hybrid Development Verification

### Cursor Prompt for Phase 3 Verification

```
PHASE 3 VERIFICATION: Hybrid Development

Please analyze the codebase and verify that the following Phase 3 hybrid development components have been properly implemented:

## 1. COLLABORATIVE EDITING ENVIRONMENT
- [ ] Verify real-time code editor integration
- [ ] Check version control and conflict resolution
- [ ] Confirm multi-user editing capabilities
- [ ] Test AI suggestion integration in editor
- [ ] Validate code completion and IntelliSense

## 2. AI-HUMAN COLLABORATION SYSTEM
- [ ] Verify AI code generation suggestions in real-time
- [ ] Check human modification learning algorithms
- [ ] Confirm design pattern recognition and suggestions
- [ ] Test code review and quality feedback
- [ ] Validate iterative improvement workflows

## 3. ADVANCED MIGRATION TOOLS
- [ ] Verify WordPress to custom site migration
- [ ] Check Wix/Squarespace import capabilities
- [ ] Confirm Shopify store migration tools
- [ ] Test custom platform migration adapters
- [ ] Validate SEO preservation during migration

## 4. LEARNING FROM USER MODIFICATIONS
- [ ] Verify user coding pattern analysis
- [ ] Check preference learning algorithms
- [ ] Confirm style guide generation from user code
- [ ] Test personalized suggestion improvement
- [ ] Validate knowledge base updates from user interactions

## 5. VISUAL DEVELOPMENT INTERFACE
- [ ] Verify drag-and-drop interface for non-coders
- [ ] Check visual component library and customization
- [ ] Confirm responsive design tools
- [ ] Test real-time preview and hot-reloading
- [ ] Validate design-to-code generation

## 6. ADVANCED DEPLOYMENT OPTIONS
- [ ] Verify multi-environment deployment (staging, production)
- [ ] Check blue-green deployment capabilities
- [ ] Confirm A/B testing infrastructure
- [ ] Test feature flag management
- [ ] Validate progressive deployment strategies

## 7. ECOSYSTEM INTEGRATION
- [ ] Verify third-party service integrations (analytics, CRM, etc.)
- [ ] Check API management and documentation generation
- [ ] Confirm webhook and automation capabilities
- [ ] Test marketplace for plugins and extensions
- [ ] Validate white-label and multi-tenant support

## VERIFICATION COMMANDS TO RUN:
1. `.\.venv\Scripts\Activate.ps1; python -m pytest tests/test_collaborative_editing.py -v`
2. `.\.venv\Scripts\Activate.ps1; python -m pytest tests/test_ai_human_collaboration.py -v`
3. `.\.venv\Scripts\Activate.ps1; python -m pytest tests/test_migration_tools.py -v`
4. `.\.venv\Scripts\Activate.ps1; python -m pytest tests/test_learning_algorithms.py -v`
5. `.\.venv\Scripts\Activate.ps1; python -m pytest tests/test_visual_development.py -v`
6. `python scripts/verify_phase3_implementation.py`

## INTEGRATION TESTS TO PERFORM:
1. Multiple users editing same website simultaneously
2. AI learning from user coding patterns and improving suggestions
3. Complete WordPress site migration with SEO preservation
4. Visual development of complex responsive layout
5. Deployment pipeline with A/B testing and rollback

## USER EXPERIENCE TESTS:
1. Non-technical user creates website using visual interface
2. Developer extends AI-generated site with custom functionality
3. Team collaboration on large website project
4. Migration of existing business website with zero downtime
5. Integration with existing business tools and workflows

## PERFORMANCE BENCHMARKS:
- Real-time collaboration latency < 100ms
- Migration completion time < 2 hours for average site
- AI suggestion accuracy improvement > 15% after user interactions
- Visual development speed 3x faster than traditional coding
- User satisfaction with hybrid development > 90%

## BUSINESS METRICS TO VALIDATE:
- User retention rate for hybrid development features
- Time-to-deployment reduction compared to traditional methods
- Customer support ticket reduction due to improved UX
- Revenue impact from advanced features adoption
- Platform scalability with increased user base

Please provide a comprehensive analysis including user experience feedback, performance metrics, and recommendations for further enhancement.
```

---

## Cross-Phase Integration Verification

### Cursor Prompt for Full System Integration Verification

```
FULL SYSTEM INTEGRATION VERIFICATION

Please analyze the entire codebase and verify that all three phases work together seamlessly:

## SYSTEM-WIDE INTEGRATION TESTS
- [ ] Verify end-to-end workflow: Upload → Analyze → Deploy → Maintain → Enhance
- [ ] Check data consistency across all phases
- [ ] Confirm user experience continuity between features
- [ ] Test system performance under full feature load
- [ ] Validate security across all integration points

## MULTI-MODEL AI COORDINATION
- [ ] Verify all AI models work together in complex scenarios
- [ ] Check fallback mechanisms across all phases
- [ ] Confirm quality consistency in end-to-end workflows
- [ ] Test learning system integration across phases
- [ ] Validate performance optimization across model usage

## SCALABILITY VERIFICATION
- [ ] Test system with 100+ concurrent websites
- [ ] Verify resource allocation and optimization
- [ ] Check database performance under load
- [ ] Test backup and disaster recovery procedures
- [ ] Validate monitoring and alerting at scale

## COMPLIANCE AND SECURITY
- [ ] Verify GDPR compliance for user data
- [ ] Check security audit trail completeness
- [ ] Confirm data encryption and protection
- [ ] Test incident response procedures
- [ ] Validate compliance reporting capabilities

## VERIFICATION COMMANDS:
1. `python scripts/full_system_integration_test.py`
2. `python scripts/stress_test_all_phases.py`
3. `python scripts/security_audit_complete_system.py`
4. `python scripts/compliance_verification.py`

Please provide a final comprehensive report on the complete system readiness for production deployment.
```

These prompts are designed to be copy-pasted directly into Cursor for thorough verification of each implementation phase. They include specific checkpoints, commands to run, and metrics to validate, ensuring nothing is missed in the verification process.
